{"ast": null, "code": "import { factory } from '../../utils/factory.js';\nvar name = 'invmod';\nvar dependencies = ['typed', 'config', 'BigNumber', 'xgcd', 'equal', 'smaller', 'mod', 'add', 'isInteger'];\nexport var createInvmod = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed,\n    config,\n    BigNumber,\n    xgcd,\n    equal,\n    smaller,\n    mod,\n    add,\n    isInteger\n  } = _ref;\n  /**\n   * Calculate the (modular) multiplicative inverse of a modulo b. Solution to the equation `ax ≣ 1 (mod b)`\n   * See https://en.wikipedia.org/wiki/Modular_multiplicative_inverse.\n   *\n   * Syntax:\n   *\n   *    math.invmod(a, b)\n   *\n   * Examples:\n   *\n   *    math.invmod(8, 12)             // returns NaN\n   *    math.invmod(7, 13)             // returns 2\n   *    math.invmod(15151, 15122)      // returns 10429\n   *\n   * See also:\n   *\n   *    gcd, xgcd\n   *\n   * @param {number | BigNumber} a  An integer number\n   * @param {number | BigNumber} b  An integer number\n   * @return {number | BigNumber }  Returns an integer number\n   *                              where `invmod(a,b)*a ≣ 1 (mod b)`\n   */\n\n  return typed(name, {\n    'number, number': invmod,\n    'BigNumber, BigNumber': invmod\n  });\n\n  function invmod(a, b) {\n    if (!isInteger(a) || !isInteger(b)) throw new Error('Parameters in function invmod must be integer numbers');\n    a = mod(a, b);\n    if (equal(b, 0)) throw new Error('Divisor must be non zero');\n    var res = xgcd(a, b);\n    res = res.valueOf();\n    var [gcd, inv] = res;\n    if (!equal(gcd, BigNumber(1))) return NaN;\n    inv = mod(inv, b);\n    if (smaller(inv, BigNumber(0))) inv = add(inv, b);\n    return inv;\n  }\n});", "map": null, "metadata": {}, "sourceType": "module"}