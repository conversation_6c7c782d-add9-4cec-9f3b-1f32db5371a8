{"ast": null, "code": "import { Subject } from '../Subject';\nimport { Subscription } from '../Subscription';\nimport { OuterSubscriber } from '../OuterSubscriber';\nimport { subscribeToResult } from '../util/subscribeToResult';\nexport function windowToggle(openings, closingSelector) {\n  return source => source.lift(new WindowToggleOperator(openings, closingSelector));\n}\n\nclass WindowToggleOperator {\n  constructor(openings, closingSelector) {\n    this.openings = openings;\n    this.closingSelector = closingSelector;\n  }\n\n  call(subscriber, source) {\n    return source.subscribe(new WindowToggleSubscriber(subscriber, this.openings, this.closingSelector));\n  }\n\n}\n\nclass WindowToggleSubscriber extends OuterSubscriber {\n  constructor(destination, openings, closingSelector) {\n    super(destination);\n    this.openings = openings;\n    this.closingSelector = closingSelector;\n    this.contexts = [];\n    this.add(this.openSubscription = subscribeToResult(this, openings, openings));\n  }\n\n  _next(value) {\n    const {\n      contexts\n    } = this;\n\n    if (contexts) {\n      const len = contexts.length;\n\n      for (let i = 0; i < len; i++) {\n        contexts[i].window.next(value);\n      }\n    }\n  }\n\n  _error(err) {\n    const {\n      contexts\n    } = this;\n    this.contexts = null;\n\n    if (contexts) {\n      const len = contexts.length;\n      let index = -1;\n\n      while (++index < len) {\n        const context = contexts[index];\n        context.window.error(err);\n        context.subscription.unsubscribe();\n      }\n    }\n\n    super._error(err);\n  }\n\n  _complete() {\n    const {\n      contexts\n    } = this;\n    this.contexts = null;\n\n    if (contexts) {\n      const len = contexts.length;\n      let index = -1;\n\n      while (++index < len) {\n        const context = contexts[index];\n        context.window.complete();\n        context.subscription.unsubscribe();\n      }\n    }\n\n    super._complete();\n  }\n\n  _unsubscribe() {\n    const {\n      contexts\n    } = this;\n    this.contexts = null;\n\n    if (contexts) {\n      const len = contexts.length;\n      let index = -1;\n\n      while (++index < len) {\n        const context = contexts[index];\n        context.window.unsubscribe();\n        context.subscription.unsubscribe();\n      }\n    }\n  }\n\n  notifyNext(outerValue, innerValue, outerIndex, innerIndex, innerSub) {\n    if (outerValue === this.openings) {\n      let closingNotifier;\n\n      try {\n        const {\n          closingSelector\n        } = this;\n        closingNotifier = closingSelector(innerValue);\n      } catch (e) {\n        return this.error(e);\n      }\n\n      const window = new Subject();\n      const subscription = new Subscription();\n      const context = {\n        window,\n        subscription\n      };\n      this.contexts.push(context);\n      const innerSubscription = subscribeToResult(this, closingNotifier, context);\n\n      if (innerSubscription.closed) {\n        this.closeWindow(this.contexts.length - 1);\n      } else {\n        innerSubscription.context = context;\n        subscription.add(innerSubscription);\n      }\n\n      this.destination.next(window);\n    } else {\n      this.closeWindow(this.contexts.indexOf(outerValue));\n    }\n  }\n\n  notifyError(err) {\n    this.error(err);\n  }\n\n  notifyComplete(inner) {\n    if (inner !== this.openSubscription) {\n      this.closeWindow(this.contexts.indexOf(inner.context));\n    }\n  }\n\n  closeWindow(index) {\n    if (index === -1) {\n      return;\n    }\n\n    const {\n      contexts\n    } = this;\n    const context = contexts[index];\n    const {\n      window,\n      subscription\n    } = context;\n    contexts.splice(index, 1);\n    window.complete();\n    subscription.unsubscribe();\n  }\n\n}", "map": {"version": 3, "names": ["Subject", "Subscription", "OuterSubscriber", "subscribeToResult", "windowToggle", "openings", "closingSelector", "source", "lift", "WindowToggleOperator", "constructor", "call", "subscriber", "subscribe", "WindowToggleSubscriber", "destination", "contexts", "add", "openSubscription", "_next", "value", "len", "length", "i", "window", "next", "_error", "err", "index", "context", "error", "subscription", "unsubscribe", "_complete", "complete", "_unsubscribe", "notifyNext", "outerValue", "innerValue", "outerIndex", "innerIndex", "innerSub", "closingNotifier", "e", "push", "innerSubscription", "closed", "closeWindow", "indexOf", "notifyError", "notifyComplete", "inner", "splice"], "sources": ["D:/work/joyserver/client/node_modules/@angular-slider/ngx-slider/node_modules/rxjs/_esm2015/internal/operators/windowToggle.js"], "sourcesContent": ["import { Subject } from '../Subject';\nimport { Subscription } from '../Subscription';\nimport { OuterSubscriber } from '../OuterSubscriber';\nimport { subscribeToResult } from '../util/subscribeToResult';\nexport function windowToggle(openings, closingSelector) {\n    return (source) => source.lift(new WindowToggleOperator(openings, closingSelector));\n}\nclass WindowToggleOperator {\n    constructor(openings, closingSelector) {\n        this.openings = openings;\n        this.closingSelector = closingSelector;\n    }\n    call(subscriber, source) {\n        return source.subscribe(new WindowToggleSubscriber(subscriber, this.openings, this.closingSelector));\n    }\n}\nclass WindowToggleSubscriber extends OuterSubscriber {\n    constructor(destination, openings, closingSelector) {\n        super(destination);\n        this.openings = openings;\n        this.closingSelector = closingSelector;\n        this.contexts = [];\n        this.add(this.openSubscription = subscribeToResult(this, openings, openings));\n    }\n    _next(value) {\n        const { contexts } = this;\n        if (contexts) {\n            const len = contexts.length;\n            for (let i = 0; i < len; i++) {\n                contexts[i].window.next(value);\n            }\n        }\n    }\n    _error(err) {\n        const { contexts } = this;\n        this.contexts = null;\n        if (contexts) {\n            const len = contexts.length;\n            let index = -1;\n            while (++index < len) {\n                const context = contexts[index];\n                context.window.error(err);\n                context.subscription.unsubscribe();\n            }\n        }\n        super._error(err);\n    }\n    _complete() {\n        const { contexts } = this;\n        this.contexts = null;\n        if (contexts) {\n            const len = contexts.length;\n            let index = -1;\n            while (++index < len) {\n                const context = contexts[index];\n                context.window.complete();\n                context.subscription.unsubscribe();\n            }\n        }\n        super._complete();\n    }\n    _unsubscribe() {\n        const { contexts } = this;\n        this.contexts = null;\n        if (contexts) {\n            const len = contexts.length;\n            let index = -1;\n            while (++index < len) {\n                const context = contexts[index];\n                context.window.unsubscribe();\n                context.subscription.unsubscribe();\n            }\n        }\n    }\n    notifyNext(outerValue, innerValue, outerIndex, innerIndex, innerSub) {\n        if (outerValue === this.openings) {\n            let closingNotifier;\n            try {\n                const { closingSelector } = this;\n                closingNotifier = closingSelector(innerValue);\n            }\n            catch (e) {\n                return this.error(e);\n            }\n            const window = new Subject();\n            const subscription = new Subscription();\n            const context = { window, subscription };\n            this.contexts.push(context);\n            const innerSubscription = subscribeToResult(this, closingNotifier, context);\n            if (innerSubscription.closed) {\n                this.closeWindow(this.contexts.length - 1);\n            }\n            else {\n                innerSubscription.context = context;\n                subscription.add(innerSubscription);\n            }\n            this.destination.next(window);\n        }\n        else {\n            this.closeWindow(this.contexts.indexOf(outerValue));\n        }\n    }\n    notifyError(err) {\n        this.error(err);\n    }\n    notifyComplete(inner) {\n        if (inner !== this.openSubscription) {\n            this.closeWindow(this.contexts.indexOf(inner.context));\n        }\n    }\n    closeWindow(index) {\n        if (index === -1) {\n            return;\n        }\n        const { contexts } = this;\n        const context = contexts[index];\n        const { window, subscription } = context;\n        contexts.splice(index, 1);\n        window.complete();\n        subscription.unsubscribe();\n    }\n}\n"], "mappings": "AAAA,SAASA,OAAT,QAAwB,YAAxB;AACA,SAASC,YAAT,QAA6B,iBAA7B;AACA,SAASC,eAAT,QAAgC,oBAAhC;AACA,SAASC,iBAAT,QAAkC,2BAAlC;AACA,OAAO,SAASC,YAAT,CAAsBC,QAAtB,EAAgCC,eAAhC,EAAiD;EACpD,OAAQC,MAAD,IAAYA,MAAM,CAACC,IAAP,CAAY,IAAIC,oBAAJ,CAAyBJ,QAAzB,EAAmCC,eAAnC,CAAZ,CAAnB;AACH;;AACD,MAAMG,oBAAN,CAA2B;EACvBC,WAAW,CAACL,QAAD,EAAWC,eAAX,EAA4B;IACnC,KAAKD,QAAL,GAAgBA,QAAhB;IACA,KAAKC,eAAL,GAAuBA,eAAvB;EACH;;EACDK,IAAI,CAACC,UAAD,EAAaL,MAAb,EAAqB;IACrB,OAAOA,MAAM,CAACM,SAAP,CAAiB,IAAIC,sBAAJ,CAA2BF,UAA3B,EAAuC,KAAKP,QAA5C,EAAsD,KAAKC,eAA3D,CAAjB,CAAP;EACH;;AAPsB;;AAS3B,MAAMQ,sBAAN,SAAqCZ,eAArC,CAAqD;EACjDQ,WAAW,CAACK,WAAD,EAAcV,QAAd,EAAwBC,eAAxB,EAAyC;IAChD,MAAMS,WAAN;IACA,KAAKV,QAAL,GAAgBA,QAAhB;IACA,KAAKC,eAAL,GAAuBA,eAAvB;IACA,KAAKU,QAAL,GAAgB,EAAhB;IACA,KAAKC,GAAL,CAAS,KAAKC,gBAAL,GAAwBf,iBAAiB,CAAC,IAAD,EAAOE,QAAP,EAAiBA,QAAjB,CAAlD;EACH;;EACDc,KAAK,CAACC,KAAD,EAAQ;IACT,MAAM;MAAEJ;IAAF,IAAe,IAArB;;IACA,IAAIA,QAAJ,EAAc;MACV,MAAMK,GAAG,GAAGL,QAAQ,CAACM,MAArB;;MACA,KAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGF,GAApB,EAAyBE,CAAC,EAA1B,EAA8B;QAC1BP,QAAQ,CAACO,CAAD,CAAR,CAAYC,MAAZ,CAAmBC,IAAnB,CAAwBL,KAAxB;MACH;IACJ;EACJ;;EACDM,MAAM,CAACC,GAAD,EAAM;IACR,MAAM;MAAEX;IAAF,IAAe,IAArB;IACA,KAAKA,QAAL,GAAgB,IAAhB;;IACA,IAAIA,QAAJ,EAAc;MACV,MAAMK,GAAG,GAAGL,QAAQ,CAACM,MAArB;MACA,IAAIM,KAAK,GAAG,CAAC,CAAb;;MACA,OAAO,EAAEA,KAAF,GAAUP,GAAjB,EAAsB;QAClB,MAAMQ,OAAO,GAAGb,QAAQ,CAACY,KAAD,CAAxB;QACAC,OAAO,CAACL,MAAR,CAAeM,KAAf,CAAqBH,GAArB;QACAE,OAAO,CAACE,YAAR,CAAqBC,WAArB;MACH;IACJ;;IACD,MAAMN,MAAN,CAAaC,GAAb;EACH;;EACDM,SAAS,GAAG;IACR,MAAM;MAAEjB;IAAF,IAAe,IAArB;IACA,KAAKA,QAAL,GAAgB,IAAhB;;IACA,IAAIA,QAAJ,EAAc;MACV,MAAMK,GAAG,GAAGL,QAAQ,CAACM,MAArB;MACA,IAAIM,KAAK,GAAG,CAAC,CAAb;;MACA,OAAO,EAAEA,KAAF,GAAUP,GAAjB,EAAsB;QAClB,MAAMQ,OAAO,GAAGb,QAAQ,CAACY,KAAD,CAAxB;QACAC,OAAO,CAACL,MAAR,CAAeU,QAAf;QACAL,OAAO,CAACE,YAAR,CAAqBC,WAArB;MACH;IACJ;;IACD,MAAMC,SAAN;EACH;;EACDE,YAAY,GAAG;IACX,MAAM;MAAEnB;IAAF,IAAe,IAArB;IACA,KAAKA,QAAL,GAAgB,IAAhB;;IACA,IAAIA,QAAJ,EAAc;MACV,MAAMK,GAAG,GAAGL,QAAQ,CAACM,MAArB;MACA,IAAIM,KAAK,GAAG,CAAC,CAAb;;MACA,OAAO,EAAEA,KAAF,GAAUP,GAAjB,EAAsB;QAClB,MAAMQ,OAAO,GAAGb,QAAQ,CAACY,KAAD,CAAxB;QACAC,OAAO,CAACL,MAAR,CAAeQ,WAAf;QACAH,OAAO,CAACE,YAAR,CAAqBC,WAArB;MACH;IACJ;EACJ;;EACDI,UAAU,CAACC,UAAD,EAAaC,UAAb,EAAyBC,UAAzB,EAAqCC,UAArC,EAAiDC,QAAjD,EAA2D;IACjE,IAAIJ,UAAU,KAAK,KAAKhC,QAAxB,EAAkC;MAC9B,IAAIqC,eAAJ;;MACA,IAAI;QACA,MAAM;UAAEpC;QAAF,IAAsB,IAA5B;QACAoC,eAAe,GAAGpC,eAAe,CAACgC,UAAD,CAAjC;MACH,CAHD,CAIA,OAAOK,CAAP,EAAU;QACN,OAAO,KAAKb,KAAL,CAAWa,CAAX,CAAP;MACH;;MACD,MAAMnB,MAAM,GAAG,IAAIxB,OAAJ,EAAf;MACA,MAAM+B,YAAY,GAAG,IAAI9B,YAAJ,EAArB;MACA,MAAM4B,OAAO,GAAG;QAAEL,MAAF;QAAUO;MAAV,CAAhB;MACA,KAAKf,QAAL,CAAc4B,IAAd,CAAmBf,OAAnB;MACA,MAAMgB,iBAAiB,GAAG1C,iBAAiB,CAAC,IAAD,EAAOuC,eAAP,EAAwBb,OAAxB,CAA3C;;MACA,IAAIgB,iBAAiB,CAACC,MAAtB,EAA8B;QAC1B,KAAKC,WAAL,CAAiB,KAAK/B,QAAL,CAAcM,MAAd,GAAuB,CAAxC;MACH,CAFD,MAGK;QACDuB,iBAAiB,CAAChB,OAAlB,GAA4BA,OAA5B;QACAE,YAAY,CAACd,GAAb,CAAiB4B,iBAAjB;MACH;;MACD,KAAK9B,WAAL,CAAiBU,IAAjB,CAAsBD,MAAtB;IACH,CAtBD,MAuBK;MACD,KAAKuB,WAAL,CAAiB,KAAK/B,QAAL,CAAcgC,OAAd,CAAsBX,UAAtB,CAAjB;IACH;EACJ;;EACDY,WAAW,CAACtB,GAAD,EAAM;IACb,KAAKG,KAAL,CAAWH,GAAX;EACH;;EACDuB,cAAc,CAACC,KAAD,EAAQ;IAClB,IAAIA,KAAK,KAAK,KAAKjC,gBAAnB,EAAqC;MACjC,KAAK6B,WAAL,CAAiB,KAAK/B,QAAL,CAAcgC,OAAd,CAAsBG,KAAK,CAACtB,OAA5B,CAAjB;IACH;EACJ;;EACDkB,WAAW,CAACnB,KAAD,EAAQ;IACf,IAAIA,KAAK,KAAK,CAAC,CAAf,EAAkB;MACd;IACH;;IACD,MAAM;MAAEZ;IAAF,IAAe,IAArB;IACA,MAAMa,OAAO,GAAGb,QAAQ,CAACY,KAAD,CAAxB;IACA,MAAM;MAAEJ,MAAF;MAAUO;IAAV,IAA2BF,OAAjC;IACAb,QAAQ,CAACoC,MAAT,CAAgBxB,KAAhB,EAAuB,CAAvB;IACAJ,MAAM,CAACU,QAAP;IACAH,YAAY,CAACC,WAAb;EACH;;AAxGgD"}, "metadata": {}, "sourceType": "module"}