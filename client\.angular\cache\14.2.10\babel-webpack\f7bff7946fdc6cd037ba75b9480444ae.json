{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nimport { format } from '../../utils/string.js';\nimport { typeOf } from '../../utils/is.js';\nimport { escapeLatex } from '../../utils/latex.js';\nimport { factory } from '../../utils/factory.js';\nvar name = 'ConstantNode';\nvar dependencies = ['Node'];\nexport var createConstantNode = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    Node\n  } = _ref;\n\n  class ConstantNode extends Node {\n    /**\n     * A ConstantNode holds a constant value like a number or string.\n     *\n     * Usage:\n     *\n     *     new ConstantNode(2.3)\n     *     new ConstantNode('hello')\n     *\n     * @param {*} value    Value can be any type (number, BigNumber, bigint, string, ...)\n     * @constructor ConstantNode\n     * @extends {Node}\n     */\n    constructor(value) {\n      super();\n      this.value = value;\n    }\n\n    get type() {\n      return name;\n    }\n\n    get isConstantNode() {\n      return true;\n    }\n    /**\n     * Compile a node into a JavaScript function.\n     * This basically pre-calculates as much as possible and only leaves open\n     * calculations which depend on a dynamic scope with variables.\n     * @param {Object} math     Math.js namespace with functions and constants.\n     * @param {Object} argNames An object with argument names as key and `true`\n     *                          as value. Used in the SymbolNode to optimize\n     *                          for arguments from user assigned functions\n     *                          (see FunctionAssignmentNode) or special symbols\n     *                          like `end` (see IndexNode).\n     * @return {function} Returns a function which can be called like:\n     *                        evalNode(scope: Object, args: Object, context: *)\n     */\n\n\n    _compile(math, argNames) {\n      var value = this.value;\n      return function evalConstantNode() {\n        return value;\n      };\n    }\n    /**\n     * Execute a callback for each of the child nodes of this node\n     * @param {function(child: Node, path: string, parent: Node)} callback\n     */\n\n\n    forEach(callback) {// nothing to do, we don't have any children\n    }\n    /**\n     * Create a new ConstantNode with children produced by the given callback.\n     * Trivial because there are no children.\n     * @param {function(child: Node, path: string, parent: Node) : Node} callback\n     * @returns {ConstantNode} Returns a clone of the node\n     */\n\n\n    map(callback) {\n      return this.clone();\n    }\n    /**\n     * Create a clone of this node, a shallow copy\n     * @return {ConstantNode}\n     */\n\n\n    clone() {\n      return new ConstantNode(this.value);\n    }\n    /**\n     * Get string representation\n     * @param {Object} options\n     * @return {string} str\n     */\n\n\n    _toString(options) {\n      return format(this.value, options);\n    }\n    /**\n     * Get HTML representation\n     * @param {Object} options\n     * @return {string} str\n     */\n\n\n    _toHTML(options) {\n      var value = this._toString(options);\n\n      switch (typeOf(this.value)) {\n        case 'number':\n        case 'bigint':\n        case 'BigNumber':\n        case 'Fraction':\n          return '<span class=\"math-number\">' + value + '</span>';\n\n        case 'string':\n          return '<span class=\"math-string\">' + value + '</span>';\n\n        case 'boolean':\n          return '<span class=\"math-boolean\">' + value + '</span>';\n\n        case 'null':\n          return '<span class=\"math-null-symbol\">' + value + '</span>';\n\n        case 'undefined':\n          return '<span class=\"math-undefined\">' + value + '</span>';\n\n        default:\n          return '<span class=\"math-symbol\">' + value + '</span>';\n      }\n    }\n    /**\n     * Get a JSON representation of the node\n     * @returns {Object}\n     */\n\n\n    toJSON() {\n      return {\n        mathjs: name,\n        value: this.value\n      };\n    }\n    /**\n     * Instantiate a ConstantNode from its JSON representation\n     * @param {Object} json  An object structured like\n     *                       `{\"mathjs\": \"SymbolNode\", value: 2.3}`,\n     *                       where mathjs is optional\n     * @returns {ConstantNode}\n     */\n\n\n    static fromJSON(json) {\n      return new ConstantNode(json.value);\n    }\n    /**\n     * Get LaTeX representation\n     * @param {Object} options\n     * @return {string} str\n     */\n\n\n    _toTex(options) {\n      var value = this._toString(options);\n\n      var type = typeOf(this.value);\n\n      switch (type) {\n        case 'string':\n          return '\\\\mathtt{' + escapeLatex(value) + '}';\n\n        case 'number':\n        case 'BigNumber':\n          {\n            var finite = type === 'BigNumber' ? this.value.isFinite() : isFinite(this.value);\n\n            if (!finite) {\n              return this.value.valueOf() < 0 ? '-\\\\infty' : '\\\\infty';\n            }\n\n            var index = value.toLowerCase().indexOf('e');\n\n            if (index !== -1) {\n              return value.substring(0, index) + '\\\\cdot10^{' + value.substring(index + 1) + '}';\n            }\n\n            return value;\n          }\n\n        case 'bigint':\n          {\n            return value.toString();\n          }\n\n        case 'Fraction':\n          return this.value.toLatex();\n\n        default:\n          return value;\n      }\n    }\n\n  }\n\n  _defineProperty(ConstantNode, \"name\", name);\n\n  return ConstantNode;\n}, {\n  isClass: true,\n  isNode: true\n});", "map": null, "metadata": {}, "sourceType": "module"}