{"ast": null, "code": "// Copyright (c) 2006-2024, <PERSON>, All Rights Reserved.\n// SPDX-License-Identifier: LGPL-2.1+\n// https://github.com/DrTimothyAldenDavis/SuiteSparse/tree/dev/CSparse/Source\nimport { csMarked } from './csMarked.js';\nimport { csMark } from './csMark.js';\nimport { csUnflip } from './csUnflip.js';\n/**\n * Depth-first search computes the nonzero pattern xi of the directed graph G (Matrix) starting\n * at nodes in B (see csReach()).\n *\n * @param {Number}  j               The starting node for the DFS algorithm\n * @param {Matrix}  g               The G matrix to search, ptr array modified, then restored\n * @param {Number}  top             Start index in stack xi[top..n-1]\n * @param {Number}  k               The kth column in B\n * @param {Array}   xi              The nonzero pattern xi[top] .. xi[n - 1], an array of size = 2 * n\n *                                  The first n entries is the nonzero pattern, the last n entries is the stack\n * @param {Array}   pinv            The inverse row permutation vector, must be null for L * x = b\n *\n * @return {Number}                 New value of top\n */\n\nexport function csDfs(j, g, top, xi, pinv) {\n  // g arrays\n  var index = g._index;\n  var ptr = g._ptr;\n  var size = g._size; // columns\n\n  var n = size[1]; // vars\n\n  var i, p, p2; // initialize head\n\n  var head = 0; // initialize the recursion stack\n\n  xi[0] = j; // loop\n\n  while (head >= 0) {\n    // get j from the top of the recursion stack\n    j = xi[head]; // apply permutation vector\n\n    var jnew = pinv ? pinv[j] : j; // check node j is marked\n\n    if (!csMarked(ptr, j)) {\n      // mark node j as visited\n      csMark(ptr, j); // update stack (last n entries in xi)\n\n      xi[n + head] = jnew < 0 ? 0 : csUnflip(ptr[jnew]);\n    } // node j done if no unvisited neighbors\n\n\n    var done = 1; // examine all neighbors of j, stack (last n entries in xi)\n\n    for (p = xi[n + head], p2 = jnew < 0 ? 0 : csUnflip(ptr[jnew + 1]); p < p2; p++) {\n      // consider neighbor node i\n      i = index[p]; // check we have visited node i, skip it\n\n      if (csMarked(ptr, i)) {\n        continue;\n      } // pause depth-first search of node j, update stack (last n entries in xi)\n\n\n      xi[n + head] = p; // start dfs at node i\n\n      xi[++head] = i; // node j is not done\n\n      done = 0; // break, to start dfs(i)\n\n      break;\n    } // check depth-first search at node j is done\n\n\n    if (done) {\n      // remove j from the recursion stack\n      head--; // and place in the output stack\n\n      xi[--top] = j;\n    }\n  }\n\n  return top;\n}", "map": {"version": 3, "names": ["csMarked", "csMark", "csUnflip", "csDfs", "j", "g", "top", "xi", "pinv", "index", "_index", "ptr", "_ptr", "size", "_size", "n", "i", "p", "p2", "head", "jnew", "done"], "sources": ["D:/work/joyserver/client/node_modules/mathjs/lib/esm/function/algebra/sparse/csDfs.js"], "sourcesContent": ["// Copyright (c) 2006-2024, <PERSON>, All Rights Reserved.\n// SPDX-License-Identifier: LGPL-2.1+\n// https://github.com/DrTimothyAldenDavis/SuiteSparse/tree/dev/CSparse/Source\nimport { csMarked } from './csMarked.js';\nimport { csMark } from './csMark.js';\nimport { csUnflip } from './csUnflip.js';\n\n/**\n * Depth-first search computes the nonzero pattern xi of the directed graph G (Matrix) starting\n * at nodes in B (see csReach()).\n *\n * @param {Number}  j               The starting node for the DFS algorithm\n * @param {Matrix}  g               The G matrix to search, ptr array modified, then restored\n * @param {Number}  top             Start index in stack xi[top..n-1]\n * @param {Number}  k               The kth column in B\n * @param {Array}   xi              The nonzero pattern xi[top] .. xi[n - 1], an array of size = 2 * n\n *                                  The first n entries is the nonzero pattern, the last n entries is the stack\n * @param {Array}   pinv            The inverse row permutation vector, must be null for L * x = b\n *\n * @return {Number}                 New value of top\n */\nexport function csDfs(j, g, top, xi, pinv) {\n  // g arrays\n  var index = g._index;\n  var ptr = g._ptr;\n  var size = g._size;\n  // columns\n  var n = size[1];\n  // vars\n  var i, p, p2;\n  // initialize head\n  var head = 0;\n  // initialize the recursion stack\n  xi[0] = j;\n  // loop\n  while (head >= 0) {\n    // get j from the top of the recursion stack\n    j = xi[head];\n    // apply permutation vector\n    var jnew = pinv ? pinv[j] : j;\n    // check node j is marked\n    if (!csMarked(ptr, j)) {\n      // mark node j as visited\n      csMark(ptr, j);\n      // update stack (last n entries in xi)\n      xi[n + head] = jnew < 0 ? 0 : csUnflip(ptr[jnew]);\n    }\n    // node j done if no unvisited neighbors\n    var done = 1;\n    // examine all neighbors of j, stack (last n entries in xi)\n    for (p = xi[n + head], p2 = jnew < 0 ? 0 : csUnflip(ptr[jnew + 1]); p < p2; p++) {\n      // consider neighbor node i\n      i = index[p];\n      // check we have visited node i, skip it\n      if (csMarked(ptr, i)) {\n        continue;\n      }\n      // pause depth-first search of node j, update stack (last n entries in xi)\n      xi[n + head] = p;\n      // start dfs at node i\n      xi[++head] = i;\n      // node j is not done\n      done = 0;\n      // break, to start dfs(i)\n      break;\n    }\n    // check depth-first search at node j is done\n    if (done) {\n      // remove j from the recursion stack\n      head--;\n      // and place in the output stack\n      xi[--top] = j;\n    }\n  }\n  return top;\n}"], "mappings": "AAAA;AACA;AACA;AACA,SAASA,QAAT,QAAyB,eAAzB;AACA,SAASC,MAAT,QAAuB,aAAvB;AACA,SAASC,QAAT,QAAyB,eAAzB;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,OAAO,SAASC,KAAT,CAAeC,CAAf,EAAkBC,CAAlB,EAAqBC,GAArB,EAA0BC,EAA1B,EAA8BC,IAA9B,EAAoC;EACzC;EACA,IAAIC,KAAK,GAAGJ,CAAC,CAACK,MAAd;EACA,IAAIC,GAAG,GAAGN,CAAC,CAACO,IAAZ;EACA,IAAIC,IAAI,GAAGR,CAAC,CAACS,KAAb,CAJyC,CAKzC;;EACA,IAAIC,CAAC,GAAGF,IAAI,CAAC,CAAD,CAAZ,CANyC,CAOzC;;EACA,IAAIG,CAAJ,EAAOC,CAAP,EAAUC,EAAV,CARyC,CASzC;;EACA,IAAIC,IAAI,GAAG,CAAX,CAVyC,CAWzC;;EACAZ,EAAE,CAAC,CAAD,CAAF,GAAQH,CAAR,CAZyC,CAazC;;EACA,OAAOe,IAAI,IAAI,CAAf,EAAkB;IAChB;IACAf,CAAC,GAAGG,EAAE,CAACY,IAAD,CAAN,CAFgB,CAGhB;;IACA,IAAIC,IAAI,GAAGZ,IAAI,GAAGA,IAAI,CAACJ,CAAD,CAAP,GAAaA,CAA5B,CAJgB,CAKhB;;IACA,IAAI,CAACJ,QAAQ,CAACW,GAAD,EAAMP,CAAN,CAAb,EAAuB;MACrB;MACAH,MAAM,CAACU,GAAD,EAAMP,CAAN,CAAN,CAFqB,CAGrB;;MACAG,EAAE,CAACQ,CAAC,GAAGI,IAAL,CAAF,GAAeC,IAAI,GAAG,CAAP,GAAW,CAAX,GAAelB,QAAQ,CAACS,GAAG,CAACS,IAAD,CAAJ,CAAtC;IACD,CAXe,CAYhB;;;IACA,IAAIC,IAAI,GAAG,CAAX,CAbgB,CAchB;;IACA,KAAKJ,CAAC,GAAGV,EAAE,CAACQ,CAAC,GAAGI,IAAL,CAAN,EAAkBD,EAAE,GAAGE,IAAI,GAAG,CAAP,GAAW,CAAX,GAAelB,QAAQ,CAACS,GAAG,CAACS,IAAI,GAAG,CAAR,CAAJ,CAAnD,EAAoEH,CAAC,GAAGC,EAAxE,EAA4ED,CAAC,EAA7E,EAAiF;MAC/E;MACAD,CAAC,GAAGP,KAAK,CAACQ,CAAD,CAAT,CAF+E,CAG/E;;MACA,IAAIjB,QAAQ,CAACW,GAAD,EAAMK,CAAN,CAAZ,EAAsB;QACpB;MACD,CAN8E,CAO/E;;;MACAT,EAAE,CAACQ,CAAC,GAAGI,IAAL,CAAF,GAAeF,CAAf,CAR+E,CAS/E;;MACAV,EAAE,CAAC,EAAEY,IAAH,CAAF,GAAaH,CAAb,CAV+E,CAW/E;;MACAK,IAAI,GAAG,CAAP,CAZ+E,CAa/E;;MACA;IACD,CA9Be,CA+BhB;;;IACA,IAAIA,IAAJ,EAAU;MACR;MACAF,IAAI,GAFI,CAGR;;MACAZ,EAAE,CAAC,EAAED,GAAH,CAAF,GAAYF,CAAZ;IACD;EACF;;EACD,OAAOE,GAAP;AACD"}, "metadata": {}, "sourceType": "module"}