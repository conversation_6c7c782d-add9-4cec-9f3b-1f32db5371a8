{"ast": null, "code": "export var distanceDocs = {\n  name: 'distance',\n  category: 'Geometry',\n  syntax: ['distance([x1, y1], [x2, y2])', 'distance([[x1, y1], [x2, y2]])'],\n  description: 'Calculates the Euclidean distance between two points.',\n  examples: ['distance([0,0], [4,4])', 'distance([[0,0], [4,4]])'],\n  seealso: []\n};", "map": {"version": 3, "names": ["distanceDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["D:/work/joyserver/client/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/geometry/distance.js"], "sourcesContent": ["export var distanceDocs = {\n  name: 'distance',\n  category: 'Geometry',\n  syntax: ['distance([x1, y1], [x2, y2])', 'distance([[x1, y1], [x2, y2]])'],\n  description: 'Calculates the Euclidean distance between two points.',\n  examples: ['distance([0,0], [4,4])', 'distance([[0,0], [4,4]])'],\n  seealso: []\n};"], "mappings": "AAAA,OAAO,IAAIA,YAAY,GAAG;EACxBC,IAAI,EAAE,UADkB;EAExBC,QAAQ,EAAE,UAFc;EAGxBC,MAAM,EAAE,CAAC,8BAAD,EAAiC,gCAAjC,CAHgB;EAIxBC,WAAW,EAAE,uDAJW;EAKxBC,QAAQ,EAAE,CAAC,wBAAD,EAA2B,0BAA3B,CALc;EAMxBC,OAAO,EAAE;AANe,CAAnB"}, "metadata": {}, "sourceType": "module"}