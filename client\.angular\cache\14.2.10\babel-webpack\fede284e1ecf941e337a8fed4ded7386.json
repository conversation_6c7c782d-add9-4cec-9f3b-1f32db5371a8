{"ast": null, "code": "import { clone } from '../../../utils/object.js';\nexport function createRealSymmetric(_ref) {\n  var {\n    config,\n    addScalar,\n    subtract,\n    abs,\n    atan,\n    cos,\n    sin,\n    multiplyScalar,\n    inv,\n    bignumber,\n    multiply,\n    add\n  } = _ref;\n  /**\n   * @param {number[] | BigNumber[]} arr\n   * @param {number} N\n   * @param {number} prec\n   * @param {'number' | 'BigNumber'} type\n   */\n\n  function main(arr, N) {\n    var prec = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : config.relTol;\n    var type = arguments.length > 3 ? arguments[3] : undefined;\n    var computeVectors = arguments.length > 4 ? arguments[4] : undefined;\n\n    if (type === 'number') {\n      return diag(arr, prec, computeVectors);\n    }\n\n    if (type === 'BigNumber') {\n      return diagBig(arr, prec, computeVectors);\n    }\n\n    throw TypeError('Unsupported data type: ' + type);\n  } // diagonalization implementation for number (efficient)\n\n\n  function diag(x, precision, computeVectors) {\n    var N = x.length;\n    var e0 = Math.abs(precision / N);\n    var psi;\n    var Sij;\n\n    if (computeVectors) {\n      Sij = new Array(N); // Sij is Identity Matrix\n\n      for (var i = 0; i < N; i++) {\n        Sij[i] = Array(N).fill(0);\n        Sij[i][i] = 1.0;\n      }\n    } // initial error\n\n\n    var Vab = getAij(x);\n\n    while (Math.abs(Vab[1]) >= Math.abs(e0)) {\n      var _i = Vab[0][0];\n      var j = Vab[0][1];\n      psi = getTheta(x[_i][_i], x[j][j], x[_i][j]);\n      x = x1(x, psi, _i, j);\n      if (computeVectors) Sij = Sij1(Sij, psi, _i, j);\n      Vab = getAij(x);\n    }\n\n    var Ei = Array(N).fill(0); // eigenvalues\n\n    for (var _i2 = 0; _i2 < N; _i2++) {\n      Ei[_i2] = x[_i2][_i2];\n    }\n\n    return sorting(clone(Ei), Sij, computeVectors);\n  } // diagonalization implementation for bigNumber\n\n\n  function diagBig(x, precision, computeVectors) {\n    var N = x.length;\n    var e0 = abs(precision / N);\n    var psi;\n    var Sij;\n\n    if (computeVectors) {\n      Sij = new Array(N); // Sij is Identity Matrix\n\n      for (var i = 0; i < N; i++) {\n        Sij[i] = Array(N).fill(0);\n        Sij[i][i] = 1.0;\n      }\n    } // initial error\n\n\n    var Vab = getAijBig(x);\n\n    while (abs(Vab[1]) >= abs(e0)) {\n      var _i3 = Vab[0][0];\n      var j = Vab[0][1];\n      psi = getThetaBig(x[_i3][_i3], x[j][j], x[_i3][j]);\n      x = x1Big(x, psi, _i3, j);\n      if (computeVectors) Sij = Sij1Big(Sij, psi, _i3, j);\n      Vab = getAijBig(x);\n    }\n\n    var Ei = Array(N).fill(0); // eigenvalues\n\n    for (var _i4 = 0; _i4 < N; _i4++) {\n      Ei[_i4] = x[_i4][_i4];\n    } // return [clone(Ei), clone(Sij)]\n\n\n    return sorting(clone(Ei), Sij, computeVectors);\n  } // get angle\n\n\n  function getTheta(aii, ajj, aij) {\n    var denom = ajj - aii;\n\n    if (Math.abs(denom) <= config.relTol) {\n      return Math.PI / 4.0;\n    } else {\n      return 0.5 * Math.atan(2.0 * aij / (ajj - aii));\n    }\n  } // get angle\n\n\n  function getThetaBig(aii, ajj, aij) {\n    var denom = subtract(ajj, aii);\n\n    if (abs(denom) <= config.relTol) {\n      return bignumber(-1).acos().div(4);\n    } else {\n      return multiplyScalar(0.5, atan(multiply(2.0, aij, inv(denom))));\n    }\n  } // update eigvec\n\n\n  function Sij1(Sij, theta, i, j) {\n    var N = Sij.length;\n    var c = Math.cos(theta);\n    var s = Math.sin(theta);\n    var Ski = Array(N).fill(0);\n    var Skj = Array(N).fill(0);\n\n    for (var k = 0; k < N; k++) {\n      Ski[k] = c * Sij[k][i] - s * Sij[k][j];\n      Skj[k] = s * Sij[k][i] + c * Sij[k][j];\n    }\n\n    for (var _k = 0; _k < N; _k++) {\n      Sij[_k][i] = Ski[_k];\n      Sij[_k][j] = Skj[_k];\n    }\n\n    return Sij;\n  } // update eigvec for overlap\n\n\n  function Sij1Big(Sij, theta, i, j) {\n    var N = Sij.length;\n    var c = cos(theta);\n    var s = sin(theta);\n    var Ski = Array(N).fill(bignumber(0));\n    var Skj = Array(N).fill(bignumber(0));\n\n    for (var k = 0; k < N; k++) {\n      Ski[k] = subtract(multiplyScalar(c, Sij[k][i]), multiplyScalar(s, Sij[k][j]));\n      Skj[k] = addScalar(multiplyScalar(s, Sij[k][i]), multiplyScalar(c, Sij[k][j]));\n    }\n\n    for (var _k2 = 0; _k2 < N; _k2++) {\n      Sij[_k2][i] = Ski[_k2];\n      Sij[_k2][j] = Skj[_k2];\n    }\n\n    return Sij;\n  } // update matrix\n\n\n  function x1Big(Hij, theta, i, j) {\n    var N = Hij.length;\n    var c = bignumber(cos(theta));\n    var s = bignumber(sin(theta));\n    var c2 = multiplyScalar(c, c);\n    var s2 = multiplyScalar(s, s);\n    var Aki = Array(N).fill(bignumber(0));\n    var Akj = Array(N).fill(bignumber(0)); // 2cs Hij\n\n    var csHij = multiply(bignumber(2), c, s, Hij[i][j]); //  Aii\n\n    var Aii = addScalar(subtract(multiplyScalar(c2, Hij[i][i]), csHij), multiplyScalar(s2, Hij[j][j]));\n    var Ajj = add(multiplyScalar(s2, Hij[i][i]), csHij, multiplyScalar(c2, Hij[j][j])); // 0  to i\n\n    for (var k = 0; k < N; k++) {\n      Aki[k] = subtract(multiplyScalar(c, Hij[i][k]), multiplyScalar(s, Hij[j][k]));\n      Akj[k] = addScalar(multiplyScalar(s, Hij[i][k]), multiplyScalar(c, Hij[j][k]));\n    } // Modify Hij\n\n\n    Hij[i][i] = Aii;\n    Hij[j][j] = Ajj;\n    Hij[i][j] = bignumber(0);\n    Hij[j][i] = bignumber(0); // 0  to i\n\n    for (var _k3 = 0; _k3 < N; _k3++) {\n      if (_k3 !== i && _k3 !== j) {\n        Hij[i][_k3] = Aki[_k3];\n        Hij[_k3][i] = Aki[_k3];\n        Hij[j][_k3] = Akj[_k3];\n        Hij[_k3][j] = Akj[_k3];\n      }\n    }\n\n    return Hij;\n  } // update matrix\n\n\n  function x1(Hij, theta, i, j) {\n    var N = Hij.length;\n    var c = Math.cos(theta);\n    var s = Math.sin(theta);\n    var c2 = c * c;\n    var s2 = s * s;\n    var Aki = Array(N).fill(0);\n    var Akj = Array(N).fill(0); //  Aii\n\n    var Aii = c2 * Hij[i][i] - 2 * c * s * Hij[i][j] + s2 * Hij[j][j];\n    var Ajj = s2 * Hij[i][i] + 2 * c * s * Hij[i][j] + c2 * Hij[j][j]; // 0  to i\n\n    for (var k = 0; k < N; k++) {\n      Aki[k] = c * Hij[i][k] - s * Hij[j][k];\n      Akj[k] = s * Hij[i][k] + c * Hij[j][k];\n    } // Modify Hij\n\n\n    Hij[i][i] = Aii;\n    Hij[j][j] = Ajj;\n    Hij[i][j] = 0;\n    Hij[j][i] = 0; // 0  to i\n\n    for (var _k4 = 0; _k4 < N; _k4++) {\n      if (_k4 !== i && _k4 !== j) {\n        Hij[i][_k4] = Aki[_k4];\n        Hij[_k4][i] = Aki[_k4];\n        Hij[j][_k4] = Akj[_k4];\n        Hij[_k4][j] = Akj[_k4];\n      }\n    }\n\n    return Hij;\n  } // get max off-diagonal value from Upper Diagonal\n\n\n  function getAij(Mij) {\n    var N = Mij.length;\n    var maxMij = 0;\n    var maxIJ = [0, 1];\n\n    for (var i = 0; i < N; i++) {\n      for (var j = i + 1; j < N; j++) {\n        if (Math.abs(maxMij) < Math.abs(Mij[i][j])) {\n          maxMij = Math.abs(Mij[i][j]);\n          maxIJ = [i, j];\n        }\n      }\n    }\n\n    return [maxIJ, maxMij];\n  } // get max off-diagonal value from Upper Diagonal\n\n\n  function getAijBig(Mij) {\n    var N = Mij.length;\n    var maxMij = 0;\n    var maxIJ = [0, 1];\n\n    for (var i = 0; i < N; i++) {\n      for (var j = i + 1; j < N; j++) {\n        if (abs(maxMij) < abs(Mij[i][j])) {\n          maxMij = abs(Mij[i][j]);\n          maxIJ = [i, j];\n        }\n      }\n    }\n\n    return [maxIJ, maxMij];\n  } // sort results\n\n\n  function sorting(E, S, computeVectors) {\n    var N = E.length;\n    var values = Array(N);\n    var vecs;\n\n    if (computeVectors) {\n      vecs = Array(N);\n\n      for (var k = 0; k < N; k++) {\n        vecs[k] = Array(N);\n      }\n    }\n\n    for (var i = 0; i < N; i++) {\n      var minID = 0;\n      var minE = E[0];\n\n      for (var j = 0; j < E.length; j++) {\n        if (abs(E[j]) < abs(minE)) {\n          minID = j;\n          minE = E[minID];\n        }\n      }\n\n      values[i] = E.splice(minID, 1)[0];\n\n      if (computeVectors) {\n        for (var _k5 = 0; _k5 < N; _k5++) {\n          vecs[i][_k5] = S[_k5][minID];\n\n          S[_k5].splice(minID, 1);\n        }\n      }\n    }\n\n    if (!computeVectors) return {\n      values\n    };\n    var eigenvectors = vecs.map((vector, i) => ({\n      value: values[i],\n      vector\n    }));\n    return {\n      values,\n      eigenvectors\n    };\n  }\n\n  return main;\n}", "map": {"version": 3, "names": ["clone", "createRealSymmetric", "_ref", "config", "addScalar", "subtract", "abs", "atan", "cos", "sin", "multiplyScalar", "inv", "bignumber", "multiply", "add", "main", "arr", "N", "prec", "arguments", "length", "undefined", "relTol", "type", "computeVectors", "diag", "diagBig", "TypeError", "x", "precision", "e0", "Math", "psi", "<PERSON><PERSON>", "Array", "i", "fill", "Vab", "get<PERSON><PERSON><PERSON>", "_i", "j", "get<PERSON><PERSON><PERSON>", "x1", "Sij1", "<PERSON>i", "_i2", "sorting", "getAijBig", "_i3", "getThetaBig", "x1Big", "Sij1Big", "_i4", "aii", "ajj", "aij", "denom", "PI", "acos", "div", "theta", "c", "s", "Ski", "Skj", "k", "_k", "_k2", "<PERSON>j", "c2", "s2", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "csHij", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "_k3", "_k4", "<PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "maxIJ", "E", "S", "values", "vecs", "minID", "minE", "splice", "_k5", "eigenvectors", "map", "vector", "value"], "sources": ["D:/work/joyserver/client/node_modules/mathjs/lib/esm/function/matrix/eigs/realSymmetric.js"], "sourcesContent": ["import { clone } from '../../../utils/object.js';\nexport function createRealSymmetric(_ref) {\n  var {\n    config,\n    addScalar,\n    subtract,\n    abs,\n    atan,\n    cos,\n    sin,\n    multiplyScalar,\n    inv,\n    bignumber,\n    multiply,\n    add\n  } = _ref;\n  /**\n   * @param {number[] | BigNumber[]} arr\n   * @param {number} N\n   * @param {number} prec\n   * @param {'number' | 'BigNumber'} type\n   */\n  function main(arr, N) {\n    var prec = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : config.relTol;\n    var type = arguments.length > 3 ? arguments[3] : undefined;\n    var computeVectors = arguments.length > 4 ? arguments[4] : undefined;\n    if (type === 'number') {\n      return diag(arr, prec, computeVectors);\n    }\n    if (type === 'BigNumber') {\n      return diagBig(arr, prec, computeVectors);\n    }\n    throw TypeError('Unsupported data type: ' + type);\n  }\n\n  // diagonalization implementation for number (efficient)\n  function diag(x, precision, computeVectors) {\n    var N = x.length;\n    var e0 = Math.abs(precision / N);\n    var psi;\n    var Sij;\n    if (computeVectors) {\n      Sij = new Array(N);\n      // Sij is Identity Matrix\n      for (var i = 0; i < N; i++) {\n        Sij[i] = Array(N).fill(0);\n        Sij[i][i] = 1.0;\n      }\n    }\n    // initial error\n    var Vab = getAij(x);\n    while (Math.abs(Vab[1]) >= Math.abs(e0)) {\n      var _i = Vab[0][0];\n      var j = Vab[0][1];\n      psi = getTheta(x[_i][_i], x[j][j], x[_i][j]);\n      x = x1(x, psi, _i, j);\n      if (computeVectors) Sij = Sij1(Sij, psi, _i, j);\n      Vab = getAij(x);\n    }\n    var Ei = Array(N).fill(0); // eigenvalues\n    for (var _i2 = 0; _i2 < N; _i2++) {\n      Ei[_i2] = x[_i2][_i2];\n    }\n    return sorting(clone(Ei), Sij, computeVectors);\n  }\n\n  // diagonalization implementation for bigNumber\n  function diagBig(x, precision, computeVectors) {\n    var N = x.length;\n    var e0 = abs(precision / N);\n    var psi;\n    var Sij;\n    if (computeVectors) {\n      Sij = new Array(N);\n      // Sij is Identity Matrix\n      for (var i = 0; i < N; i++) {\n        Sij[i] = Array(N).fill(0);\n        Sij[i][i] = 1.0;\n      }\n    }\n    // initial error\n    var Vab = getAijBig(x);\n    while (abs(Vab[1]) >= abs(e0)) {\n      var _i3 = Vab[0][0];\n      var j = Vab[0][1];\n      psi = getThetaBig(x[_i3][_i3], x[j][j], x[_i3][j]);\n      x = x1Big(x, psi, _i3, j);\n      if (computeVectors) Sij = Sij1Big(Sij, psi, _i3, j);\n      Vab = getAijBig(x);\n    }\n    var Ei = Array(N).fill(0); // eigenvalues\n    for (var _i4 = 0; _i4 < N; _i4++) {\n      Ei[_i4] = x[_i4][_i4];\n    }\n    // return [clone(Ei), clone(Sij)]\n    return sorting(clone(Ei), Sij, computeVectors);\n  }\n\n  // get angle\n  function getTheta(aii, ajj, aij) {\n    var denom = ajj - aii;\n    if (Math.abs(denom) <= config.relTol) {\n      return Math.PI / 4.0;\n    } else {\n      return 0.5 * Math.atan(2.0 * aij / (ajj - aii));\n    }\n  }\n\n  // get angle\n  function getThetaBig(aii, ajj, aij) {\n    var denom = subtract(ajj, aii);\n    if (abs(denom) <= config.relTol) {\n      return bignumber(-1).acos().div(4);\n    } else {\n      return multiplyScalar(0.5, atan(multiply(2.0, aij, inv(denom))));\n    }\n  }\n\n  // update eigvec\n  function Sij1(Sij, theta, i, j) {\n    var N = Sij.length;\n    var c = Math.cos(theta);\n    var s = Math.sin(theta);\n    var Ski = Array(N).fill(0);\n    var Skj = Array(N).fill(0);\n    for (var k = 0; k < N; k++) {\n      Ski[k] = c * Sij[k][i] - s * Sij[k][j];\n      Skj[k] = s * Sij[k][i] + c * Sij[k][j];\n    }\n    for (var _k = 0; _k < N; _k++) {\n      Sij[_k][i] = Ski[_k];\n      Sij[_k][j] = Skj[_k];\n    }\n    return Sij;\n  }\n  // update eigvec for overlap\n  function Sij1Big(Sij, theta, i, j) {\n    var N = Sij.length;\n    var c = cos(theta);\n    var s = sin(theta);\n    var Ski = Array(N).fill(bignumber(0));\n    var Skj = Array(N).fill(bignumber(0));\n    for (var k = 0; k < N; k++) {\n      Ski[k] = subtract(multiplyScalar(c, Sij[k][i]), multiplyScalar(s, Sij[k][j]));\n      Skj[k] = addScalar(multiplyScalar(s, Sij[k][i]), multiplyScalar(c, Sij[k][j]));\n    }\n    for (var _k2 = 0; _k2 < N; _k2++) {\n      Sij[_k2][i] = Ski[_k2];\n      Sij[_k2][j] = Skj[_k2];\n    }\n    return Sij;\n  }\n\n  // update matrix\n  function x1Big(Hij, theta, i, j) {\n    var N = Hij.length;\n    var c = bignumber(cos(theta));\n    var s = bignumber(sin(theta));\n    var c2 = multiplyScalar(c, c);\n    var s2 = multiplyScalar(s, s);\n    var Aki = Array(N).fill(bignumber(0));\n    var Akj = Array(N).fill(bignumber(0));\n    // 2cs Hij\n    var csHij = multiply(bignumber(2), c, s, Hij[i][j]);\n    //  Aii\n    var Aii = addScalar(subtract(multiplyScalar(c2, Hij[i][i]), csHij), multiplyScalar(s2, Hij[j][j]));\n    var Ajj = add(multiplyScalar(s2, Hij[i][i]), csHij, multiplyScalar(c2, Hij[j][j]));\n    // 0  to i\n    for (var k = 0; k < N; k++) {\n      Aki[k] = subtract(multiplyScalar(c, Hij[i][k]), multiplyScalar(s, Hij[j][k]));\n      Akj[k] = addScalar(multiplyScalar(s, Hij[i][k]), multiplyScalar(c, Hij[j][k]));\n    }\n    // Modify Hij\n    Hij[i][i] = Aii;\n    Hij[j][j] = Ajj;\n    Hij[i][j] = bignumber(0);\n    Hij[j][i] = bignumber(0);\n    // 0  to i\n    for (var _k3 = 0; _k3 < N; _k3++) {\n      if (_k3 !== i && _k3 !== j) {\n        Hij[i][_k3] = Aki[_k3];\n        Hij[_k3][i] = Aki[_k3];\n        Hij[j][_k3] = Akj[_k3];\n        Hij[_k3][j] = Akj[_k3];\n      }\n    }\n    return Hij;\n  }\n\n  // update matrix\n  function x1(Hij, theta, i, j) {\n    var N = Hij.length;\n    var c = Math.cos(theta);\n    var s = Math.sin(theta);\n    var c2 = c * c;\n    var s2 = s * s;\n    var Aki = Array(N).fill(0);\n    var Akj = Array(N).fill(0);\n    //  Aii\n    var Aii = c2 * Hij[i][i] - 2 * c * s * Hij[i][j] + s2 * Hij[j][j];\n    var Ajj = s2 * Hij[i][i] + 2 * c * s * Hij[i][j] + c2 * Hij[j][j];\n    // 0  to i\n    for (var k = 0; k < N; k++) {\n      Aki[k] = c * Hij[i][k] - s * Hij[j][k];\n      Akj[k] = s * Hij[i][k] + c * Hij[j][k];\n    }\n    // Modify Hij\n    Hij[i][i] = Aii;\n    Hij[j][j] = Ajj;\n    Hij[i][j] = 0;\n    Hij[j][i] = 0;\n    // 0  to i\n    for (var _k4 = 0; _k4 < N; _k4++) {\n      if (_k4 !== i && _k4 !== j) {\n        Hij[i][_k4] = Aki[_k4];\n        Hij[_k4][i] = Aki[_k4];\n        Hij[j][_k4] = Akj[_k4];\n        Hij[_k4][j] = Akj[_k4];\n      }\n    }\n    return Hij;\n  }\n\n  // get max off-diagonal value from Upper Diagonal\n  function getAij(Mij) {\n    var N = Mij.length;\n    var maxMij = 0;\n    var maxIJ = [0, 1];\n    for (var i = 0; i < N; i++) {\n      for (var j = i + 1; j < N; j++) {\n        if (Math.abs(maxMij) < Math.abs(Mij[i][j])) {\n          maxMij = Math.abs(Mij[i][j]);\n          maxIJ = [i, j];\n        }\n      }\n    }\n    return [maxIJ, maxMij];\n  }\n\n  // get max off-diagonal value from Upper Diagonal\n  function getAijBig(Mij) {\n    var N = Mij.length;\n    var maxMij = 0;\n    var maxIJ = [0, 1];\n    for (var i = 0; i < N; i++) {\n      for (var j = i + 1; j < N; j++) {\n        if (abs(maxMij) < abs(Mij[i][j])) {\n          maxMij = abs(Mij[i][j]);\n          maxIJ = [i, j];\n        }\n      }\n    }\n    return [maxIJ, maxMij];\n  }\n\n  // sort results\n  function sorting(E, S, computeVectors) {\n    var N = E.length;\n    var values = Array(N);\n    var vecs;\n    if (computeVectors) {\n      vecs = Array(N);\n      for (var k = 0; k < N; k++) {\n        vecs[k] = Array(N);\n      }\n    }\n    for (var i = 0; i < N; i++) {\n      var minID = 0;\n      var minE = E[0];\n      for (var j = 0; j < E.length; j++) {\n        if (abs(E[j]) < abs(minE)) {\n          minID = j;\n          minE = E[minID];\n        }\n      }\n      values[i] = E.splice(minID, 1)[0];\n      if (computeVectors) {\n        for (var _k5 = 0; _k5 < N; _k5++) {\n          vecs[i][_k5] = S[_k5][minID];\n          S[_k5].splice(minID, 1);\n        }\n      }\n    }\n    if (!computeVectors) return {\n      values\n    };\n    var eigenvectors = vecs.map((vector, i) => ({\n      value: values[i],\n      vector\n    }));\n    return {\n      values,\n      eigenvectors\n    };\n  }\n  return main;\n}"], "mappings": "AAAA,SAASA,KAAT,QAAsB,0BAAtB;AACA,OAAO,SAASC,mBAAT,CAA6BC,IAA7B,EAAmC;EACxC,IAAI;IACFC,MADE;IAEFC,SAFE;IAGFC,QAHE;IAIFC,GAJE;IAKFC,IALE;IAMFC,GANE;IAOFC,GAPE;IAQFC,cARE;IASFC,GATE;IAUFC,SAVE;IAWFC,QAXE;IAYFC;EAZE,IAaAZ,IAbJ;EAcA;AACF;AACA;AACA;AACA;AACA;;EACE,SAASa,IAAT,CAAcC,GAAd,EAAmBC,CAAnB,EAAsB;IACpB,IAAIC,IAAI,GAAGC,SAAS,CAACC,MAAV,GAAmB,CAAnB,IAAwBD,SAAS,CAAC,CAAD,CAAT,KAAiBE,SAAzC,GAAqDF,SAAS,CAAC,CAAD,CAA9D,GAAoEhB,MAAM,CAACmB,MAAtF;IACA,IAAIC,IAAI,GAAGJ,SAAS,CAACC,MAAV,GAAmB,CAAnB,GAAuBD,SAAS,CAAC,CAAD,CAAhC,GAAsCE,SAAjD;IACA,IAAIG,cAAc,GAAGL,SAAS,CAACC,MAAV,GAAmB,CAAnB,GAAuBD,SAAS,CAAC,CAAD,CAAhC,GAAsCE,SAA3D;;IACA,IAAIE,IAAI,KAAK,QAAb,EAAuB;MACrB,OAAOE,IAAI,CAACT,GAAD,EAAME,IAAN,EAAYM,cAAZ,CAAX;IACD;;IACD,IAAID,IAAI,KAAK,WAAb,EAA0B;MACxB,OAAOG,OAAO,CAACV,GAAD,EAAME,IAAN,EAAYM,cAAZ,CAAd;IACD;;IACD,MAAMG,SAAS,CAAC,4BAA4BJ,IAA7B,CAAf;EACD,CAhCuC,CAkCxC;;;EACA,SAASE,IAAT,CAAcG,CAAd,EAAiBC,SAAjB,EAA4BL,cAA5B,EAA4C;IAC1C,IAAIP,CAAC,GAAGW,CAAC,CAACR,MAAV;IACA,IAAIU,EAAE,GAAGC,IAAI,CAACzB,GAAL,CAASuB,SAAS,GAAGZ,CAArB,CAAT;IACA,IAAIe,GAAJ;IACA,IAAIC,GAAJ;;IACA,IAAIT,cAAJ,EAAoB;MAClBS,GAAG,GAAG,IAAIC,KAAJ,CAAUjB,CAAV,CAAN,CADkB,CAElB;;MACA,KAAK,IAAIkB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGlB,CAApB,EAAuBkB,CAAC,EAAxB,EAA4B;QAC1BF,GAAG,CAACE,CAAD,CAAH,GAASD,KAAK,CAACjB,CAAD,CAAL,CAASmB,IAAT,CAAc,CAAd,CAAT;QACAH,GAAG,CAACE,CAAD,CAAH,CAAOA,CAAP,IAAY,GAAZ;MACD;IACF,CAZyC,CAa1C;;;IACA,IAAIE,GAAG,GAAGC,MAAM,CAACV,CAAD,CAAhB;;IACA,OAAOG,IAAI,CAACzB,GAAL,CAAS+B,GAAG,CAAC,CAAD,CAAZ,KAAoBN,IAAI,CAACzB,GAAL,CAASwB,EAAT,CAA3B,EAAyC;MACvC,IAAIS,EAAE,GAAGF,GAAG,CAAC,CAAD,CAAH,CAAO,CAAP,CAAT;MACA,IAAIG,CAAC,GAAGH,GAAG,CAAC,CAAD,CAAH,CAAO,CAAP,CAAR;MACAL,GAAG,GAAGS,QAAQ,CAACb,CAAC,CAACW,EAAD,CAAD,CAAMA,EAAN,CAAD,EAAYX,CAAC,CAACY,CAAD,CAAD,CAAKA,CAAL,CAAZ,EAAqBZ,CAAC,CAACW,EAAD,CAAD,CAAMC,CAAN,CAArB,CAAd;MACAZ,CAAC,GAAGc,EAAE,CAACd,CAAD,EAAII,GAAJ,EAASO,EAAT,EAAaC,CAAb,CAAN;MACA,IAAIhB,cAAJ,EAAoBS,GAAG,GAAGU,IAAI,CAACV,GAAD,EAAMD,GAAN,EAAWO,EAAX,EAAeC,CAAf,CAAV;MACpBH,GAAG,GAAGC,MAAM,CAACV,CAAD,CAAZ;IACD;;IACD,IAAIgB,EAAE,GAAGV,KAAK,CAACjB,CAAD,CAAL,CAASmB,IAAT,CAAc,CAAd,CAAT,CAvB0C,CAuBf;;IAC3B,KAAK,IAAIS,GAAG,GAAG,CAAf,EAAkBA,GAAG,GAAG5B,CAAxB,EAA2B4B,GAAG,EAA9B,EAAkC;MAChCD,EAAE,CAACC,GAAD,CAAF,GAAUjB,CAAC,CAACiB,GAAD,CAAD,CAAOA,GAAP,CAAV;IACD;;IACD,OAAOC,OAAO,CAAC9C,KAAK,CAAC4C,EAAD,CAAN,EAAYX,GAAZ,EAAiBT,cAAjB,CAAd;EACD,CA/DuC,CAiExC;;;EACA,SAASE,OAAT,CAAiBE,CAAjB,EAAoBC,SAApB,EAA+BL,cAA/B,EAA+C;IAC7C,IAAIP,CAAC,GAAGW,CAAC,CAACR,MAAV;IACA,IAAIU,EAAE,GAAGxB,GAAG,CAACuB,SAAS,GAAGZ,CAAb,CAAZ;IACA,IAAIe,GAAJ;IACA,IAAIC,GAAJ;;IACA,IAAIT,cAAJ,EAAoB;MAClBS,GAAG,GAAG,IAAIC,KAAJ,CAAUjB,CAAV,CAAN,CADkB,CAElB;;MACA,KAAK,IAAIkB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGlB,CAApB,EAAuBkB,CAAC,EAAxB,EAA4B;QAC1BF,GAAG,CAACE,CAAD,CAAH,GAASD,KAAK,CAACjB,CAAD,CAAL,CAASmB,IAAT,CAAc,CAAd,CAAT;QACAH,GAAG,CAACE,CAAD,CAAH,CAAOA,CAAP,IAAY,GAAZ;MACD;IACF,CAZ4C,CAa7C;;;IACA,IAAIE,GAAG,GAAGU,SAAS,CAACnB,CAAD,CAAnB;;IACA,OAAOtB,GAAG,CAAC+B,GAAG,CAAC,CAAD,CAAJ,CAAH,IAAe/B,GAAG,CAACwB,EAAD,CAAzB,EAA+B;MAC7B,IAAIkB,GAAG,GAAGX,GAAG,CAAC,CAAD,CAAH,CAAO,CAAP,CAAV;MACA,IAAIG,CAAC,GAAGH,GAAG,CAAC,CAAD,CAAH,CAAO,CAAP,CAAR;MACAL,GAAG,GAAGiB,WAAW,CAACrB,CAAC,CAACoB,GAAD,CAAD,CAAOA,GAAP,CAAD,EAAcpB,CAAC,CAACY,CAAD,CAAD,CAAKA,CAAL,CAAd,EAAuBZ,CAAC,CAACoB,GAAD,CAAD,CAAOR,CAAP,CAAvB,CAAjB;MACAZ,CAAC,GAAGsB,KAAK,CAACtB,CAAD,EAAII,GAAJ,EAASgB,GAAT,EAAcR,CAAd,CAAT;MACA,IAAIhB,cAAJ,EAAoBS,GAAG,GAAGkB,OAAO,CAAClB,GAAD,EAAMD,GAAN,EAAWgB,GAAX,EAAgBR,CAAhB,CAAb;MACpBH,GAAG,GAAGU,SAAS,CAACnB,CAAD,CAAf;IACD;;IACD,IAAIgB,EAAE,GAAGV,KAAK,CAACjB,CAAD,CAAL,CAASmB,IAAT,CAAc,CAAd,CAAT,CAvB6C,CAuBlB;;IAC3B,KAAK,IAAIgB,GAAG,GAAG,CAAf,EAAkBA,GAAG,GAAGnC,CAAxB,EAA2BmC,GAAG,EAA9B,EAAkC;MAChCR,EAAE,CAACQ,GAAD,CAAF,GAAUxB,CAAC,CAACwB,GAAD,CAAD,CAAOA,GAAP,CAAV;IACD,CA1B4C,CA2B7C;;;IACA,OAAON,OAAO,CAAC9C,KAAK,CAAC4C,EAAD,CAAN,EAAYX,GAAZ,EAAiBT,cAAjB,CAAd;EACD,CA/FuC,CAiGxC;;;EACA,SAASiB,QAAT,CAAkBY,GAAlB,EAAuBC,GAAvB,EAA4BC,GAA5B,EAAiC;IAC/B,IAAIC,KAAK,GAAGF,GAAG,GAAGD,GAAlB;;IACA,IAAItB,IAAI,CAACzB,GAAL,CAASkD,KAAT,KAAmBrD,MAAM,CAACmB,MAA9B,EAAsC;MACpC,OAAOS,IAAI,CAAC0B,EAAL,GAAU,GAAjB;IACD,CAFD,MAEO;MACL,OAAO,MAAM1B,IAAI,CAACxB,IAAL,CAAU,MAAMgD,GAAN,IAAaD,GAAG,GAAGD,GAAnB,CAAV,CAAb;IACD;EACF,CAzGuC,CA2GxC;;;EACA,SAASJ,WAAT,CAAqBI,GAArB,EAA0BC,GAA1B,EAA+BC,GAA/B,EAAoC;IAClC,IAAIC,KAAK,GAAGnD,QAAQ,CAACiD,GAAD,EAAMD,GAAN,CAApB;;IACA,IAAI/C,GAAG,CAACkD,KAAD,CAAH,IAAcrD,MAAM,CAACmB,MAAzB,EAAiC;MAC/B,OAAOV,SAAS,CAAC,CAAC,CAAF,CAAT,CAAc8C,IAAd,GAAqBC,GAArB,CAAyB,CAAzB,CAAP;IACD,CAFD,MAEO;MACL,OAAOjD,cAAc,CAAC,GAAD,EAAMH,IAAI,CAACM,QAAQ,CAAC,GAAD,EAAM0C,GAAN,EAAW5C,GAAG,CAAC6C,KAAD,CAAd,CAAT,CAAV,CAArB;IACD;EACF,CAnHuC,CAqHxC;;;EACA,SAASb,IAAT,CAAcV,GAAd,EAAmB2B,KAAnB,EAA0BzB,CAA1B,EAA6BK,CAA7B,EAAgC;IAC9B,IAAIvB,CAAC,GAAGgB,GAAG,CAACb,MAAZ;IACA,IAAIyC,CAAC,GAAG9B,IAAI,CAACvB,GAAL,CAASoD,KAAT,CAAR;IACA,IAAIE,CAAC,GAAG/B,IAAI,CAACtB,GAAL,CAASmD,KAAT,CAAR;IACA,IAAIG,GAAG,GAAG7B,KAAK,CAACjB,CAAD,CAAL,CAASmB,IAAT,CAAc,CAAd,CAAV;IACA,IAAI4B,GAAG,GAAG9B,KAAK,CAACjB,CAAD,CAAL,CAASmB,IAAT,CAAc,CAAd,CAAV;;IACA,KAAK,IAAI6B,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGhD,CAApB,EAAuBgD,CAAC,EAAxB,EAA4B;MAC1BF,GAAG,CAACE,CAAD,CAAH,GAASJ,CAAC,GAAG5B,GAAG,CAACgC,CAAD,CAAH,CAAO9B,CAAP,CAAJ,GAAgB2B,CAAC,GAAG7B,GAAG,CAACgC,CAAD,CAAH,CAAOzB,CAAP,CAA7B;MACAwB,GAAG,CAACC,CAAD,CAAH,GAASH,CAAC,GAAG7B,GAAG,CAACgC,CAAD,CAAH,CAAO9B,CAAP,CAAJ,GAAgB0B,CAAC,GAAG5B,GAAG,CAACgC,CAAD,CAAH,CAAOzB,CAAP,CAA7B;IACD;;IACD,KAAK,IAAI0B,EAAE,GAAG,CAAd,EAAiBA,EAAE,GAAGjD,CAAtB,EAAyBiD,EAAE,EAA3B,EAA+B;MAC7BjC,GAAG,CAACiC,EAAD,CAAH,CAAQ/B,CAAR,IAAa4B,GAAG,CAACG,EAAD,CAAhB;MACAjC,GAAG,CAACiC,EAAD,CAAH,CAAQ1B,CAAR,IAAawB,GAAG,CAACE,EAAD,CAAhB;IACD;;IACD,OAAOjC,GAAP;EACD,CArIuC,CAsIxC;;;EACA,SAASkB,OAAT,CAAiBlB,GAAjB,EAAsB2B,KAAtB,EAA6BzB,CAA7B,EAAgCK,CAAhC,EAAmC;IACjC,IAAIvB,CAAC,GAAGgB,GAAG,CAACb,MAAZ;IACA,IAAIyC,CAAC,GAAGrD,GAAG,CAACoD,KAAD,CAAX;IACA,IAAIE,CAAC,GAAGrD,GAAG,CAACmD,KAAD,CAAX;IACA,IAAIG,GAAG,GAAG7B,KAAK,CAACjB,CAAD,CAAL,CAASmB,IAAT,CAAcxB,SAAS,CAAC,CAAD,CAAvB,CAAV;IACA,IAAIoD,GAAG,GAAG9B,KAAK,CAACjB,CAAD,CAAL,CAASmB,IAAT,CAAcxB,SAAS,CAAC,CAAD,CAAvB,CAAV;;IACA,KAAK,IAAIqD,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGhD,CAApB,EAAuBgD,CAAC,EAAxB,EAA4B;MAC1BF,GAAG,CAACE,CAAD,CAAH,GAAS5D,QAAQ,CAACK,cAAc,CAACmD,CAAD,EAAI5B,GAAG,CAACgC,CAAD,CAAH,CAAO9B,CAAP,CAAJ,CAAf,EAA+BzB,cAAc,CAACoD,CAAD,EAAI7B,GAAG,CAACgC,CAAD,CAAH,CAAOzB,CAAP,CAAJ,CAA7C,CAAjB;MACAwB,GAAG,CAACC,CAAD,CAAH,GAAS7D,SAAS,CAACM,cAAc,CAACoD,CAAD,EAAI7B,GAAG,CAACgC,CAAD,CAAH,CAAO9B,CAAP,CAAJ,CAAf,EAA+BzB,cAAc,CAACmD,CAAD,EAAI5B,GAAG,CAACgC,CAAD,CAAH,CAAOzB,CAAP,CAAJ,CAA7C,CAAlB;IACD;;IACD,KAAK,IAAI2B,GAAG,GAAG,CAAf,EAAkBA,GAAG,GAAGlD,CAAxB,EAA2BkD,GAAG,EAA9B,EAAkC;MAChClC,GAAG,CAACkC,GAAD,CAAH,CAAShC,CAAT,IAAc4B,GAAG,CAACI,GAAD,CAAjB;MACAlC,GAAG,CAACkC,GAAD,CAAH,CAAS3B,CAAT,IAAcwB,GAAG,CAACG,GAAD,CAAjB;IACD;;IACD,OAAOlC,GAAP;EACD,CAtJuC,CAwJxC;;;EACA,SAASiB,KAAT,CAAekB,GAAf,EAAoBR,KAApB,EAA2BzB,CAA3B,EAA8BK,CAA9B,EAAiC;IAC/B,IAAIvB,CAAC,GAAGmD,GAAG,CAAChD,MAAZ;IACA,IAAIyC,CAAC,GAAGjD,SAAS,CAACJ,GAAG,CAACoD,KAAD,CAAJ,CAAjB;IACA,IAAIE,CAAC,GAAGlD,SAAS,CAACH,GAAG,CAACmD,KAAD,CAAJ,CAAjB;IACA,IAAIS,EAAE,GAAG3D,cAAc,CAACmD,CAAD,EAAIA,CAAJ,CAAvB;IACA,IAAIS,EAAE,GAAG5D,cAAc,CAACoD,CAAD,EAAIA,CAAJ,CAAvB;IACA,IAAIS,GAAG,GAAGrC,KAAK,CAACjB,CAAD,CAAL,CAASmB,IAAT,CAAcxB,SAAS,CAAC,CAAD,CAAvB,CAAV;IACA,IAAI4D,GAAG,GAAGtC,KAAK,CAACjB,CAAD,CAAL,CAASmB,IAAT,CAAcxB,SAAS,CAAC,CAAD,CAAvB,CAAV,CAP+B,CAQ/B;;IACA,IAAI6D,KAAK,GAAG5D,QAAQ,CAACD,SAAS,CAAC,CAAD,CAAV,EAAeiD,CAAf,EAAkBC,CAAlB,EAAqBM,GAAG,CAACjC,CAAD,CAAH,CAAOK,CAAP,CAArB,CAApB,CAT+B,CAU/B;;IACA,IAAIkC,GAAG,GAAGtE,SAAS,CAACC,QAAQ,CAACK,cAAc,CAAC2D,EAAD,EAAKD,GAAG,CAACjC,CAAD,CAAH,CAAOA,CAAP,CAAL,CAAf,EAAgCsC,KAAhC,CAAT,EAAiD/D,cAAc,CAAC4D,EAAD,EAAKF,GAAG,CAAC5B,CAAD,CAAH,CAAOA,CAAP,CAAL,CAA/D,CAAnB;IACA,IAAImC,GAAG,GAAG7D,GAAG,CAACJ,cAAc,CAAC4D,EAAD,EAAKF,GAAG,CAACjC,CAAD,CAAH,CAAOA,CAAP,CAAL,CAAf,EAAgCsC,KAAhC,EAAuC/D,cAAc,CAAC2D,EAAD,EAAKD,GAAG,CAAC5B,CAAD,CAAH,CAAOA,CAAP,CAAL,CAArD,CAAb,CAZ+B,CAa/B;;IACA,KAAK,IAAIyB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGhD,CAApB,EAAuBgD,CAAC,EAAxB,EAA4B;MAC1BM,GAAG,CAACN,CAAD,CAAH,GAAS5D,QAAQ,CAACK,cAAc,CAACmD,CAAD,EAAIO,GAAG,CAACjC,CAAD,CAAH,CAAO8B,CAAP,CAAJ,CAAf,EAA+BvD,cAAc,CAACoD,CAAD,EAAIM,GAAG,CAAC5B,CAAD,CAAH,CAAOyB,CAAP,CAAJ,CAA7C,CAAjB;MACAO,GAAG,CAACP,CAAD,CAAH,GAAS7D,SAAS,CAACM,cAAc,CAACoD,CAAD,EAAIM,GAAG,CAACjC,CAAD,CAAH,CAAO8B,CAAP,CAAJ,CAAf,EAA+BvD,cAAc,CAACmD,CAAD,EAAIO,GAAG,CAAC5B,CAAD,CAAH,CAAOyB,CAAP,CAAJ,CAA7C,CAAlB;IACD,CAjB8B,CAkB/B;;;IACAG,GAAG,CAACjC,CAAD,CAAH,CAAOA,CAAP,IAAYuC,GAAZ;IACAN,GAAG,CAAC5B,CAAD,CAAH,CAAOA,CAAP,IAAYmC,GAAZ;IACAP,GAAG,CAACjC,CAAD,CAAH,CAAOK,CAAP,IAAY5B,SAAS,CAAC,CAAD,CAArB;IACAwD,GAAG,CAAC5B,CAAD,CAAH,CAAOL,CAAP,IAAYvB,SAAS,CAAC,CAAD,CAArB,CAtB+B,CAuB/B;;IACA,KAAK,IAAIgE,GAAG,GAAG,CAAf,EAAkBA,GAAG,GAAG3D,CAAxB,EAA2B2D,GAAG,EAA9B,EAAkC;MAChC,IAAIA,GAAG,KAAKzC,CAAR,IAAayC,GAAG,KAAKpC,CAAzB,EAA4B;QAC1B4B,GAAG,CAACjC,CAAD,CAAH,CAAOyC,GAAP,IAAcL,GAAG,CAACK,GAAD,CAAjB;QACAR,GAAG,CAACQ,GAAD,CAAH,CAASzC,CAAT,IAAcoC,GAAG,CAACK,GAAD,CAAjB;QACAR,GAAG,CAAC5B,CAAD,CAAH,CAAOoC,GAAP,IAAcJ,GAAG,CAACI,GAAD,CAAjB;QACAR,GAAG,CAACQ,GAAD,CAAH,CAASpC,CAAT,IAAcgC,GAAG,CAACI,GAAD,CAAjB;MACD;IACF;;IACD,OAAOR,GAAP;EACD,CA1LuC,CA4LxC;;;EACA,SAAS1B,EAAT,CAAY0B,GAAZ,EAAiBR,KAAjB,EAAwBzB,CAAxB,EAA2BK,CAA3B,EAA8B;IAC5B,IAAIvB,CAAC,GAAGmD,GAAG,CAAChD,MAAZ;IACA,IAAIyC,CAAC,GAAG9B,IAAI,CAACvB,GAAL,CAASoD,KAAT,CAAR;IACA,IAAIE,CAAC,GAAG/B,IAAI,CAACtB,GAAL,CAASmD,KAAT,CAAR;IACA,IAAIS,EAAE,GAAGR,CAAC,GAAGA,CAAb;IACA,IAAIS,EAAE,GAAGR,CAAC,GAAGA,CAAb;IACA,IAAIS,GAAG,GAAGrC,KAAK,CAACjB,CAAD,CAAL,CAASmB,IAAT,CAAc,CAAd,CAAV;IACA,IAAIoC,GAAG,GAAGtC,KAAK,CAACjB,CAAD,CAAL,CAASmB,IAAT,CAAc,CAAd,CAAV,CAP4B,CAQ5B;;IACA,IAAIsC,GAAG,GAAGL,EAAE,GAAGD,GAAG,CAACjC,CAAD,CAAH,CAAOA,CAAP,CAAL,GAAiB,IAAI0B,CAAJ,GAAQC,CAAR,GAAYM,GAAG,CAACjC,CAAD,CAAH,CAAOK,CAAP,CAA7B,GAAyC8B,EAAE,GAAGF,GAAG,CAAC5B,CAAD,CAAH,CAAOA,CAAP,CAAxD;IACA,IAAImC,GAAG,GAAGL,EAAE,GAAGF,GAAG,CAACjC,CAAD,CAAH,CAAOA,CAAP,CAAL,GAAiB,IAAI0B,CAAJ,GAAQC,CAAR,GAAYM,GAAG,CAACjC,CAAD,CAAH,CAAOK,CAAP,CAA7B,GAAyC6B,EAAE,GAAGD,GAAG,CAAC5B,CAAD,CAAH,CAAOA,CAAP,CAAxD,CAV4B,CAW5B;;IACA,KAAK,IAAIyB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGhD,CAApB,EAAuBgD,CAAC,EAAxB,EAA4B;MAC1BM,GAAG,CAACN,CAAD,CAAH,GAASJ,CAAC,GAAGO,GAAG,CAACjC,CAAD,CAAH,CAAO8B,CAAP,CAAJ,GAAgBH,CAAC,GAAGM,GAAG,CAAC5B,CAAD,CAAH,CAAOyB,CAAP,CAA7B;MACAO,GAAG,CAACP,CAAD,CAAH,GAASH,CAAC,GAAGM,GAAG,CAACjC,CAAD,CAAH,CAAO8B,CAAP,CAAJ,GAAgBJ,CAAC,GAAGO,GAAG,CAAC5B,CAAD,CAAH,CAAOyB,CAAP,CAA7B;IACD,CAf2B,CAgB5B;;;IACAG,GAAG,CAACjC,CAAD,CAAH,CAAOA,CAAP,IAAYuC,GAAZ;IACAN,GAAG,CAAC5B,CAAD,CAAH,CAAOA,CAAP,IAAYmC,GAAZ;IACAP,GAAG,CAACjC,CAAD,CAAH,CAAOK,CAAP,IAAY,CAAZ;IACA4B,GAAG,CAAC5B,CAAD,CAAH,CAAOL,CAAP,IAAY,CAAZ,CApB4B,CAqB5B;;IACA,KAAK,IAAI0C,GAAG,GAAG,CAAf,EAAkBA,GAAG,GAAG5D,CAAxB,EAA2B4D,GAAG,EAA9B,EAAkC;MAChC,IAAIA,GAAG,KAAK1C,CAAR,IAAa0C,GAAG,KAAKrC,CAAzB,EAA4B;QAC1B4B,GAAG,CAACjC,CAAD,CAAH,CAAO0C,GAAP,IAAcN,GAAG,CAACM,GAAD,CAAjB;QACAT,GAAG,CAACS,GAAD,CAAH,CAAS1C,CAAT,IAAcoC,GAAG,CAACM,GAAD,CAAjB;QACAT,GAAG,CAAC5B,CAAD,CAAH,CAAOqC,GAAP,IAAcL,GAAG,CAACK,GAAD,CAAjB;QACAT,GAAG,CAACS,GAAD,CAAH,CAASrC,CAAT,IAAcgC,GAAG,CAACK,GAAD,CAAjB;MACD;IACF;;IACD,OAAOT,GAAP;EACD,CA5NuC,CA8NxC;;;EACA,SAAS9B,MAAT,CAAgBwC,GAAhB,EAAqB;IACnB,IAAI7D,CAAC,GAAG6D,GAAG,CAAC1D,MAAZ;IACA,IAAI2D,MAAM,GAAG,CAAb;IACA,IAAIC,KAAK,GAAG,CAAC,CAAD,EAAI,CAAJ,CAAZ;;IACA,KAAK,IAAI7C,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGlB,CAApB,EAAuBkB,CAAC,EAAxB,EAA4B;MAC1B,KAAK,IAAIK,CAAC,GAAGL,CAAC,GAAG,CAAjB,EAAoBK,CAAC,GAAGvB,CAAxB,EAA2BuB,CAAC,EAA5B,EAAgC;QAC9B,IAAIT,IAAI,CAACzB,GAAL,CAASyE,MAAT,IAAmBhD,IAAI,CAACzB,GAAL,CAASwE,GAAG,CAAC3C,CAAD,CAAH,CAAOK,CAAP,CAAT,CAAvB,EAA4C;UAC1CuC,MAAM,GAAGhD,IAAI,CAACzB,GAAL,CAASwE,GAAG,CAAC3C,CAAD,CAAH,CAAOK,CAAP,CAAT,CAAT;UACAwC,KAAK,GAAG,CAAC7C,CAAD,EAAIK,CAAJ,CAAR;QACD;MACF;IACF;;IACD,OAAO,CAACwC,KAAD,EAAQD,MAAR,CAAP;EACD,CA5OuC,CA8OxC;;;EACA,SAAShC,SAAT,CAAmB+B,GAAnB,EAAwB;IACtB,IAAI7D,CAAC,GAAG6D,GAAG,CAAC1D,MAAZ;IACA,IAAI2D,MAAM,GAAG,CAAb;IACA,IAAIC,KAAK,GAAG,CAAC,CAAD,EAAI,CAAJ,CAAZ;;IACA,KAAK,IAAI7C,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGlB,CAApB,EAAuBkB,CAAC,EAAxB,EAA4B;MAC1B,KAAK,IAAIK,CAAC,GAAGL,CAAC,GAAG,CAAjB,EAAoBK,CAAC,GAAGvB,CAAxB,EAA2BuB,CAAC,EAA5B,EAAgC;QAC9B,IAAIlC,GAAG,CAACyE,MAAD,CAAH,GAAczE,GAAG,CAACwE,GAAG,CAAC3C,CAAD,CAAH,CAAOK,CAAP,CAAD,CAArB,EAAkC;UAChCuC,MAAM,GAAGzE,GAAG,CAACwE,GAAG,CAAC3C,CAAD,CAAH,CAAOK,CAAP,CAAD,CAAZ;UACAwC,KAAK,GAAG,CAAC7C,CAAD,EAAIK,CAAJ,CAAR;QACD;MACF;IACF;;IACD,OAAO,CAACwC,KAAD,EAAQD,MAAR,CAAP;EACD,CA5PuC,CA8PxC;;;EACA,SAASjC,OAAT,CAAiBmC,CAAjB,EAAoBC,CAApB,EAAuB1D,cAAvB,EAAuC;IACrC,IAAIP,CAAC,GAAGgE,CAAC,CAAC7D,MAAV;IACA,IAAI+D,MAAM,GAAGjD,KAAK,CAACjB,CAAD,CAAlB;IACA,IAAImE,IAAJ;;IACA,IAAI5D,cAAJ,EAAoB;MAClB4D,IAAI,GAAGlD,KAAK,CAACjB,CAAD,CAAZ;;MACA,KAAK,IAAIgD,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGhD,CAApB,EAAuBgD,CAAC,EAAxB,EAA4B;QAC1BmB,IAAI,CAACnB,CAAD,CAAJ,GAAU/B,KAAK,CAACjB,CAAD,CAAf;MACD;IACF;;IACD,KAAK,IAAIkB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGlB,CAApB,EAAuBkB,CAAC,EAAxB,EAA4B;MAC1B,IAAIkD,KAAK,GAAG,CAAZ;MACA,IAAIC,IAAI,GAAGL,CAAC,CAAC,CAAD,CAAZ;;MACA,KAAK,IAAIzC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGyC,CAAC,CAAC7D,MAAtB,EAA8BoB,CAAC,EAA/B,EAAmC;QACjC,IAAIlC,GAAG,CAAC2E,CAAC,CAACzC,CAAD,CAAF,CAAH,GAAYlC,GAAG,CAACgF,IAAD,CAAnB,EAA2B;UACzBD,KAAK,GAAG7C,CAAR;UACA8C,IAAI,GAAGL,CAAC,CAACI,KAAD,CAAR;QACD;MACF;;MACDF,MAAM,CAAChD,CAAD,CAAN,GAAY8C,CAAC,CAACM,MAAF,CAASF,KAAT,EAAgB,CAAhB,EAAmB,CAAnB,CAAZ;;MACA,IAAI7D,cAAJ,EAAoB;QAClB,KAAK,IAAIgE,GAAG,GAAG,CAAf,EAAkBA,GAAG,GAAGvE,CAAxB,EAA2BuE,GAAG,EAA9B,EAAkC;UAChCJ,IAAI,CAACjD,CAAD,CAAJ,CAAQqD,GAAR,IAAeN,CAAC,CAACM,GAAD,CAAD,CAAOH,KAAP,CAAf;;UACAH,CAAC,CAACM,GAAD,CAAD,CAAOD,MAAP,CAAcF,KAAd,EAAqB,CAArB;QACD;MACF;IACF;;IACD,IAAI,CAAC7D,cAAL,EAAqB,OAAO;MAC1B2D;IAD0B,CAAP;IAGrB,IAAIM,YAAY,GAAGL,IAAI,CAACM,GAAL,CAAS,CAACC,MAAD,EAASxD,CAAT,MAAgB;MAC1CyD,KAAK,EAAET,MAAM,CAAChD,CAAD,CAD6B;MAE1CwD;IAF0C,CAAhB,CAAT,CAAnB;IAIA,OAAO;MACLR,MADK;MAELM;IAFK,CAAP;EAID;;EACD,OAAO1E,IAAP;AACD"}, "metadata": {}, "sourceType": "module"}