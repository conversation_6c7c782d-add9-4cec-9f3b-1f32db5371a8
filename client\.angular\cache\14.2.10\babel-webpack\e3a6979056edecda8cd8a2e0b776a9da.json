{"ast": null, "code": "import { factory } from '../../../utils/factory.js';\nimport { DimensionError } from '../../../error/DimensionError.js';\nvar name = 'matAlgo07xSSf';\nvar dependencies = ['typed', 'SparseMatrix'];\nexport var createMatAlgo07xSSf = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed,\n    SparseMatrix\n  } = _ref;\n  /**\n  * Iterates over SparseMatrix A and SparseMatrix B items (zero and nonzero) and invokes the callback function f(Aij, Bij).\n  * Callback function invoked MxN times.\n  *\n  * C(i,j) = f(Aij, Bij)\n  *\n  * @param {Matrix}   a                 The SparseMatrix instance (A)\n  * @param {Matrix}   b                 The SparseMatrix instance (B)\n  * @param {Function} callback          The f(Aij,Bij) operation to invoke\n  *\n  * @return {Matrix}                    SparseMatrix (C)\n  *\n  * see https://github.com/josdejong/mathjs/pull/346#issuecomment-97620294\n  */\n\n  return function matAlgo07xSSf(a, b, callback) {\n    // sparse matrix arrays\n    var asize = a._size;\n    var adt = a._datatype || a._data === undefined ? a._datatype : a.getDataType();\n    var bsize = b._size;\n    var bdt = b._datatype || b._data === undefined ? b._datatype : b.getDataType(); // validate dimensions\n\n    if (asize.length !== bsize.length) {\n      throw new DimensionError(asize.length, bsize.length);\n    }\n\n    if (asize[0] !== bsize[0] || asize[1] !== bsize[1]) {\n      throw new RangeError('Dimension mismatch. Matrix A (' + asize + ') must match Matrix B (' + bsize + ')');\n    } // rows & columns\n\n\n    var rows = asize[0];\n    var columns = asize[1]; // datatype\n\n    var dt;\n    var zero = 0;\n    var cf = callback; // process data types\n\n    if (typeof adt === 'string' && adt === bdt && adt !== 'mixed') {\n      dt = adt;\n      zero = typed.convert(0, dt);\n      cf = typed.find(callback, [dt, dt]);\n    } // result arrays for sparse format\n\n\n    var cvalues = [];\n    var cindex = [];\n    var cptr = new Array(columns + 1).fill(0); // Start with column pointer array\n    // workspaces\n\n    var xa = [];\n    var xb = [];\n    var wa = [];\n    var wb = []; // loop columns\n\n    for (var j = 0; j < columns; j++) {\n      var mark = j + 1;\n      var nonZeroCount = 0;\n\n      _scatter(a, j, wa, xa, mark);\n\n      _scatter(b, j, wb, xb, mark); // loop rows\n\n\n      for (var i = 0; i < rows; i++) {\n        var va = wa[i] === mark ? xa[i] : zero;\n        var vb = wb[i] === mark ? xb[i] : zero; // invoke callback\n\n        var cij = cf(va, vb); // Store all non zero and true values\n\n        if (cij !== 0 && cij !== false) {\n          cindex.push(i); // row index\n\n          cvalues.push(cij); // computed value\n\n          nonZeroCount++;\n        }\n      } // Update column pointer with cumulative count of non-zero values\n\n\n      cptr[j + 1] = cptr[j] + nonZeroCount;\n    } // Return the result as a sparse matrix\n\n\n    return new SparseMatrix({\n      values: cvalues,\n      index: cindex,\n      ptr: cptr,\n      size: [rows, columns],\n      datatype: adt === a._datatype && bdt === b._datatype ? dt : undefined\n    });\n  };\n\n  function _scatter(m, j, w, x, mark) {\n    // a arrays\n    var values = m._values;\n    var index = m._index;\n    var ptr = m._ptr; // loop values in column j\n\n    for (var k = ptr[j], k1 = ptr[j + 1]; k < k1; k++) {\n      // row\n      var i = index[k]; // update workspace\n\n      w[i] = mark;\n      x[i] = values[k];\n    }\n  }\n});", "map": {"version": 3, "names": ["factory", "DimensionError", "name", "dependencies", "createMatAlgo07xSSf", "_ref", "typed", "SparseMatrix", "matAlgo07xSSf", "a", "b", "callback", "asize", "_size", "adt", "_datatype", "_data", "undefined", "getDataType", "bsize", "bdt", "length", "RangeError", "rows", "columns", "dt", "zero", "cf", "convert", "find", "cvalues", "cindex", "cptr", "Array", "fill", "xa", "xb", "wa", "wb", "j", "mark", "nonZeroCount", "_scatter", "i", "va", "vb", "cij", "push", "values", "index", "ptr", "size", "datatype", "m", "w", "x", "_values", "_index", "_ptr", "k", "k1"], "sources": ["D:/work/joyserver/client/node_modules/mathjs/lib/esm/type/matrix/utils/matAlgo07xSSf.js"], "sourcesContent": ["import { factory } from '../../../utils/factory.js';\nimport { DimensionError } from '../../../error/DimensionError.js';\nvar name = 'matAlgo07xSSf';\nvar dependencies = ['typed', 'SparseMatrix'];\nexport var createMatAlgo07xSSf = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed,\n    SparseMatrix\n  } = _ref;\n  /**\n  * Iterates over SparseMatrix A and SparseMatrix B items (zero and nonzero) and invokes the callback function f(Aij, Bij).\n  * Callback function invoked MxN times.\n  *\n  * C(i,j) = f(Aij, Bij)\n  *\n  * @param {Matrix}   a                 The SparseMatrix instance (A)\n  * @param {Matrix}   b                 The SparseMatrix instance (B)\n  * @param {Function} callback          The f(Aij,Bij) operation to invoke\n  *\n  * @return {Matrix}                    SparseMatrix (C)\n  *\n  * see https://github.com/josdejong/mathjs/pull/346#issuecomment-97620294\n  */\n  return function matAlgo07xSSf(a, b, callback) {\n    // sparse matrix arrays\n    var asize = a._size;\n    var adt = a._datatype || a._data === undefined ? a._datatype : a.getDataType();\n    var bsize = b._size;\n    var bdt = b._datatype || b._data === undefined ? b._datatype : b.getDataType();\n\n    // validate dimensions\n    if (asize.length !== bsize.length) {\n      throw new DimensionError(asize.length, bsize.length);\n    }\n    if (asize[0] !== bsize[0] || asize[1] !== bsize[1]) {\n      throw new RangeError('Dimension mismatch. Matrix A (' + asize + ') must match Matrix B (' + bsize + ')');\n    }\n\n    // rows & columns\n    var rows = asize[0];\n    var columns = asize[1];\n\n    // datatype\n    var dt;\n    var zero = 0;\n    var cf = callback;\n\n    // process data types\n    if (typeof adt === 'string' && adt === bdt && adt !== 'mixed') {\n      dt = adt;\n      zero = typed.convert(0, dt);\n      cf = typed.find(callback, [dt, dt]);\n    }\n\n    // result arrays for sparse format\n    var cvalues = [];\n    var cindex = [];\n    var cptr = new Array(columns + 1).fill(0); // Start with column pointer array\n\n    // workspaces\n    var xa = [];\n    var xb = [];\n    var wa = [];\n    var wb = [];\n\n    // loop columns\n    for (var j = 0; j < columns; j++) {\n      var mark = j + 1;\n      var nonZeroCount = 0;\n      _scatter(a, j, wa, xa, mark);\n      _scatter(b, j, wb, xb, mark);\n\n      // loop rows\n      for (var i = 0; i < rows; i++) {\n        var va = wa[i] === mark ? xa[i] : zero;\n        var vb = wb[i] === mark ? xb[i] : zero;\n\n        // invoke callback\n        var cij = cf(va, vb);\n        // Store all non zero and true values\n        if (cij !== 0 && cij !== false) {\n          cindex.push(i); // row index\n          cvalues.push(cij); // computed value\n          nonZeroCount++;\n        }\n      }\n\n      // Update column pointer with cumulative count of non-zero values\n      cptr[j + 1] = cptr[j] + nonZeroCount;\n    }\n\n    // Return the result as a sparse matrix\n    return new SparseMatrix({\n      values: cvalues,\n      index: cindex,\n      ptr: cptr,\n      size: [rows, columns],\n      datatype: adt === a._datatype && bdt === b._datatype ? dt : undefined\n    });\n  };\n  function _scatter(m, j, w, x, mark) {\n    // a arrays\n    var values = m._values;\n    var index = m._index;\n    var ptr = m._ptr;\n    // loop values in column j\n    for (var k = ptr[j], k1 = ptr[j + 1]; k < k1; k++) {\n      // row\n      var i = index[k];\n      // update workspace\n      w[i] = mark;\n      x[i] = values[k];\n    }\n  }\n});"], "mappings": "AAAA,SAASA,OAAT,QAAwB,2BAAxB;AACA,SAASC,cAAT,QAA+B,kCAA/B;AACA,IAAIC,IAAI,GAAG,eAAX;AACA,IAAIC,YAAY,GAAG,CAAC,OAAD,EAAU,cAAV,CAAnB;AACA,OAAO,IAAIC,mBAAmB,GAAG,eAAeJ,OAAO,CAACE,IAAD,EAAOC,YAAP,EAAqBE,IAAI,IAAI;EAClF,IAAI;IACFC,KADE;IAEFC;EAFE,IAGAF,IAHJ;EAIA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EACE,OAAO,SAASG,aAAT,CAAuBC,CAAvB,EAA0BC,CAA1B,EAA6BC,QAA7B,EAAuC;IAC5C;IACA,IAAIC,KAAK,GAAGH,CAAC,CAACI,KAAd;IACA,IAAIC,GAAG,GAAGL,CAAC,CAACM,SAAF,IAAeN,CAAC,CAACO,KAAF,KAAYC,SAA3B,GAAuCR,CAAC,CAACM,SAAzC,GAAqDN,CAAC,CAACS,WAAF,EAA/D;IACA,IAAIC,KAAK,GAAGT,CAAC,CAACG,KAAd;IACA,IAAIO,GAAG,GAAGV,CAAC,CAACK,SAAF,IAAeL,CAAC,CAACM,KAAF,KAAYC,SAA3B,GAAuCP,CAAC,CAACK,SAAzC,GAAqDL,CAAC,CAACQ,WAAF,EAA/D,CAL4C,CAO5C;;IACA,IAAIN,KAAK,CAACS,MAAN,KAAiBF,KAAK,CAACE,MAA3B,EAAmC;MACjC,MAAM,IAAIpB,cAAJ,CAAmBW,KAAK,CAACS,MAAzB,EAAiCF,KAAK,CAACE,MAAvC,CAAN;IACD;;IACD,IAAIT,KAAK,CAAC,CAAD,CAAL,KAAaO,KAAK,CAAC,CAAD,CAAlB,IAAyBP,KAAK,CAAC,CAAD,CAAL,KAAaO,KAAK,CAAC,CAAD,CAA/C,EAAoD;MAClD,MAAM,IAAIG,UAAJ,CAAe,mCAAmCV,KAAnC,GAA2C,yBAA3C,GAAuEO,KAAvE,GAA+E,GAA9F,CAAN;IACD,CAb2C,CAe5C;;;IACA,IAAII,IAAI,GAAGX,KAAK,CAAC,CAAD,CAAhB;IACA,IAAIY,OAAO,GAAGZ,KAAK,CAAC,CAAD,CAAnB,CAjB4C,CAmB5C;;IACA,IAAIa,EAAJ;IACA,IAAIC,IAAI,GAAG,CAAX;IACA,IAAIC,EAAE,GAAGhB,QAAT,CAtB4C,CAwB5C;;IACA,IAAI,OAAOG,GAAP,KAAe,QAAf,IAA2BA,GAAG,KAAKM,GAAnC,IAA0CN,GAAG,KAAK,OAAtD,EAA+D;MAC7DW,EAAE,GAAGX,GAAL;MACAY,IAAI,GAAGpB,KAAK,CAACsB,OAAN,CAAc,CAAd,EAAiBH,EAAjB,CAAP;MACAE,EAAE,GAAGrB,KAAK,CAACuB,IAAN,CAAWlB,QAAX,EAAqB,CAACc,EAAD,EAAKA,EAAL,CAArB,CAAL;IACD,CA7B2C,CA+B5C;;;IACA,IAAIK,OAAO,GAAG,EAAd;IACA,IAAIC,MAAM,GAAG,EAAb;IACA,IAAIC,IAAI,GAAG,IAAIC,KAAJ,CAAUT,OAAO,GAAG,CAApB,EAAuBU,IAAvB,CAA4B,CAA5B,CAAX,CAlC4C,CAkCD;IAE3C;;IACA,IAAIC,EAAE,GAAG,EAAT;IACA,IAAIC,EAAE,GAAG,EAAT;IACA,IAAIC,EAAE,GAAG,EAAT;IACA,IAAIC,EAAE,GAAG,EAAT,CAxC4C,CA0C5C;;IACA,KAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGf,OAApB,EAA6Be,CAAC,EAA9B,EAAkC;MAChC,IAAIC,IAAI,GAAGD,CAAC,GAAG,CAAf;MACA,IAAIE,YAAY,GAAG,CAAnB;;MACAC,QAAQ,CAACjC,CAAD,EAAI8B,CAAJ,EAAOF,EAAP,EAAWF,EAAX,EAAeK,IAAf,CAAR;;MACAE,QAAQ,CAAChC,CAAD,EAAI6B,CAAJ,EAAOD,EAAP,EAAWF,EAAX,EAAeI,IAAf,CAAR,CAJgC,CAMhC;;;MACA,KAAK,IAAIG,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGpB,IAApB,EAA0BoB,CAAC,EAA3B,EAA+B;QAC7B,IAAIC,EAAE,GAAGP,EAAE,CAACM,CAAD,CAAF,KAAUH,IAAV,GAAiBL,EAAE,CAACQ,CAAD,CAAnB,GAAyBjB,IAAlC;QACA,IAAImB,EAAE,GAAGP,EAAE,CAACK,CAAD,CAAF,KAAUH,IAAV,GAAiBJ,EAAE,CAACO,CAAD,CAAnB,GAAyBjB,IAAlC,CAF6B,CAI7B;;QACA,IAAIoB,GAAG,GAAGnB,EAAE,CAACiB,EAAD,EAAKC,EAAL,CAAZ,CAL6B,CAM7B;;QACA,IAAIC,GAAG,KAAK,CAAR,IAAaA,GAAG,KAAK,KAAzB,EAAgC;UAC9Bf,MAAM,CAACgB,IAAP,CAAYJ,CAAZ,EAD8B,CACd;;UAChBb,OAAO,CAACiB,IAAR,CAAaD,GAAb,EAF8B,CAEX;;UACnBL,YAAY;QACb;MACF,CAnB+B,CAqBhC;;;MACAT,IAAI,CAACO,CAAC,GAAG,CAAL,CAAJ,GAAcP,IAAI,CAACO,CAAD,CAAJ,GAAUE,YAAxB;IACD,CAlE2C,CAoE5C;;;IACA,OAAO,IAAIlC,YAAJ,CAAiB;MACtByC,MAAM,EAAElB,OADc;MAEtBmB,KAAK,EAAElB,MAFe;MAGtBmB,GAAG,EAAElB,IAHiB;MAItBmB,IAAI,EAAE,CAAC5B,IAAD,EAAOC,OAAP,CAJgB;MAKtB4B,QAAQ,EAAEtC,GAAG,KAAKL,CAAC,CAACM,SAAV,IAAuBK,GAAG,KAAKV,CAAC,CAACK,SAAjC,GAA6CU,EAA7C,GAAkDR;IALtC,CAAjB,CAAP;EAOD,CA5ED;;EA6EA,SAASyB,QAAT,CAAkBW,CAAlB,EAAqBd,CAArB,EAAwBe,CAAxB,EAA2BC,CAA3B,EAA8Bf,IAA9B,EAAoC;IAClC;IACA,IAAIQ,MAAM,GAAGK,CAAC,CAACG,OAAf;IACA,IAAIP,KAAK,GAAGI,CAAC,CAACI,MAAd;IACA,IAAIP,GAAG,GAAGG,CAAC,CAACK,IAAZ,CAJkC,CAKlC;;IACA,KAAK,IAAIC,CAAC,GAAGT,GAAG,CAACX,CAAD,CAAX,EAAgBqB,EAAE,GAAGV,GAAG,CAACX,CAAC,GAAG,CAAL,CAA7B,EAAsCoB,CAAC,GAAGC,EAA1C,EAA8CD,CAAC,EAA/C,EAAmD;MACjD;MACA,IAAIhB,CAAC,GAAGM,KAAK,CAACU,CAAD,CAAb,CAFiD,CAGjD;;MACAL,CAAC,CAACX,CAAD,CAAD,GAAOH,IAAP;MACAe,CAAC,CAACZ,CAAD,CAAD,GAAOK,MAAM,CAACW,CAAD,CAAb;IACD;EACF;AACF,CA9GsD,CAAhD"}, "metadata": {}, "sourceType": "module"}