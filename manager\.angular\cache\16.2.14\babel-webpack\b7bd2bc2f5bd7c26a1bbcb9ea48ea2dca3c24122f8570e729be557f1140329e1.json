{"ast": null, "code": "import { BehaviorSubject } from \"rxjs\";\nimport { map, shareReplay, distinctUntilChanged } from \"rxjs/operators\";\nimport * as M from \"@app/core/io\";\nimport { RestoreCardDataAdapter } from \"./restore-card-data.adapter\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@app/core/http/setting.service\";\nimport * as i2 from \"@app/core/services/socket.service\";\nexport class RestoreCardCheckService {\n  constructor(settingService, socketService) {\n    this.settingService = settingService;\n    this.socketService = socketService;\n    this.serviceState = new BehaviorSubject({\n      sessionId: \"\",\n      clientStates: new Map(),\n      counts: {\n        success: 0,\n        failed: 0,\n        total: 0\n      },\n      isLoading: false,\n      lastRefresh: 0\n    });\n    this.clientCheckModal = new BehaviorSubject(false);\n    this.saveCheckResultTrigger = new BehaviorSubject({\n      trigger: false\n    });\n    this.singleClientLoadingStates = new Map();\n    this.AUTO_SAVE_INTERVAL = 5000; // 5秒间隔\n    this.MIN_SAVE_INTERVAL = 3000; // 最小保存间隔3秒，防止频繁保存\n    // 超时重试相关属性\n    this.timeoutRetryTimers = new Map(); // 存储每个客户端的重试定时器\n    this.timeoutRetryAttempts = new Map(); // 存储每个客户端的重试次数\n    this.RETRY_INTERVAL = 45000; // 45秒重试间隔\n    this.MAX_RETRY_ATTEMPTS = 10; // 最大重试次数\n    this.MAX_RETRY_DURATION = 600000; // 最大重试时长10分钟\n    this.lastSaveTime = 0;\n    this.serviceState$ = this.serviceState.asObservable();\n    this.clientCheckModal$ = this.clientCheckModal.asObservable();\n    this.saveCheckResultTrigger$ = this.saveCheckResultTrigger.asObservable();\n    this.clientList$ = this.serviceState$.pipe(map(state => this.transformAllClientStates(state)), distinctUntilChanged(), shareReplay(1));\n    this.loading$ = this.serviceState$.pipe(map(state => state.isLoading), distinctUntilChanged(), shareReplay(1));\n    this.counts$ = this.serviceState$.pipe(map(state => state.counts), distinctUntilChanged(), shareReplay(1));\n    this.startAutoSave();\n  }\n  ngOnDestroy() {\n    console.log(\"RestoreCardCheckService: Destroying service, cleaning up resources\");\n    this.stopAutoSave();\n    this.stopAllTimeoutRetries();\n    this.serviceState.complete();\n    this.clientCheckModal.complete();\n    this.saveCheckResultTrigger.complete();\n    this.singleClientLoadingStates.forEach(subject => subject.complete());\n    this.singleClientLoadingStates.clear();\n  }\n  isRestoreCardBatchResponse(response) {\n    return response && response.info && Array.isArray(response.info.results);\n  }\n  isVerifyResponse(response) {\n    return response && response.info && Array.isArray(response.info.clients);\n  }\n  // 模态框控制方法\n  openClientCheckModal() {\n    this.clientCheckModal.next(true);\n  }\n  closeClientCheckModal() {\n    this.clientCheckModal.next(false);\n  }\n  // 数据更新方法\n  setSessionId(sessionId) {\n    this.updateServiceState({\n      sessionId\n    });\n    // 重启自动保存以确保新会话的数据安全\n    this.startAutoSave();\n  }\n  updateClientList(clientStates) {\n    try {\n      // 直接处理ClientState数组，无需格式转换\n      const newClientStatesMap = new Map();\n      clientStates.forEach(clientState => {\n        if (RestoreCardDataAdapter.validateClientState(clientState)) {\n          newClientStatesMap.set(clientState.clientInfo.seat_number, clientState);\n        }\n      });\n      // 更新客户端状态映射\n      this.updateServiceState({\n        clientStates: newClientStatesMap,\n        lastRefresh: Date.now()\n      });\n      // 使用全量重新计算（因为是批量更新）\n      this.updateCountsFullRecalculation();\n      this.checkAutoSave();\n    } catch (error) {\n      console.error(\"RestoreCardCheckService: Error updating client list:\", error);\n    }\n  }\n  // 保留全量重新计算方法，用于批量更新场景\n  updateCountsFullRecalculation() {\n    const currentState = this.serviceState.getValue();\n    const clientStates = Array.from(currentState.clientStates.values());\n    const success = clientStates.filter(state => state.checkInfo.status === \"Success\").length;\n    const failed = clientStates.filter(state => state.checkInfo.status === \"Failed\").length;\n    const timeout = clientStates.filter(state => state.checkInfo.status === \"Timeout\").length;\n    const total = clientStates.length;\n    this.updateServiceState({\n      counts: {\n        success,\n        failed: failed + timeout,\n        total\n      }\n    });\n    console.log(`RestoreCardCheckService: Full count recalculation - Success: ${success}, Failed: ${failed + timeout}, Total: ${total}`);\n  }\n  // 批量状态更新方法 - 性能优化核心\n  updateMultipleClientStatuses(updates) {\n    if (updates.length === 0) return;\n    const currentState = this.serviceState.getValue();\n    const newClientStates = new Map(currentState.clientStates);\n    let hasChanges = false;\n    // 批量处理所有更新\n    updates.forEach(({\n      seatNumber,\n      status,\n      statusText\n    }) => {\n      const existing = newClientStates.get(seatNumber);\n      if (existing) {\n        const updatedState = RestoreCardDataAdapter.mergeClientState(existing, {\n          checkInfo: {\n            ...existing.checkInfo,\n            status,\n            statusText: statusText || this.getStatusText(status)\n          },\n          lastUpdated: Date.now()\n        });\n        newClientStates.set(seatNumber, updatedState);\n        hasChanges = true;\n      }\n    });\n    if (hasChanges) {\n      // 一次性更新状态\n      this.updateServiceState({\n        clientStates: newClientStates,\n        lastRefresh: Date.now()\n      });\n      // 批量更新后重新计算计数\n      this.updateCountsFullRecalculation();\n      this.checkAutoSave();\n      console.log(`RestoreCardCheckService: Batch updated ${updates.length} client statuses`);\n    }\n  }\n  checkClient() {\n    console.log(\"CheckRestoreCard: Check Client restore card\");\n    const currentSessionId = this.serviceState.getValue().sessionId;\n    this.updateServiceState({\n      isLoading: true\n    });\n    this.settingService.restoreCardCheck(\"client\", currentSessionId, [\"write\", \"reboot\", \"verify\"]).subscribe(res => {\n      console.log(\"CheckRestoreCard: Client restore card response:\", res);\n      this.updateServiceState({\n        isLoading: false\n      });\n    });\n  }\n  checkClientWithRefresh() {\n    console.log(\"CheckRestoreCard: Check Client with refresh - Starting refresh and check process\");\n    const currentSessionId = this.serviceState.getValue().sessionId;\n    this.updateServiceState({\n      isLoading: true\n    });\n    console.log(\"CheckRestoreCard: Set loading state to true, starting refresh...\");\n    // 首先刷新最新状态\n    this.settingService.restoreCardCheck(\"client\", currentSessionId, \"verify\").subscribe(refreshRes => {\n      console.log(\"CheckRestoreCard: Client verify response:\", refreshRes);\n      if (this.isVerifyResponse(refreshRes)) {\n        this.updateClientListFromServerData(refreshRes.info.clients);\n      }\n      // 刷新完成后执行检查\n      const currentState = this.serviceState.getValue();\n      const notCheckedSeats = this.getNotCheckedClientSeats();\n      // 如果没有客户端状态数据（首次检查），直接执行原有逻辑\n      if (currentState.clientStates.size === 0) {\n        console.log(\"CheckRestoreCard: No client state data, performing initial check\");\n      } else if (notCheckedSeats.length === 0) {\n        console.log(\"CheckRestoreCard: All clients already checked\");\n        this.updateServiceState({\n          isLoading: false\n        });\n        return;\n      } else {\n        console.log(`CheckRestoreCard: Starting check for ${notCheckedSeats.length} not checked clients`);\n      }\n      this.settingService.restoreCardCheck(\"client\", currentSessionId, [\"write\", \"reboot\", \"verify\"]).subscribe(checkRes => {\n        console.log(\"CheckRestoreCard: Client restore card response:\", checkRes);\n        this.updateServiceState({\n          isLoading: false\n        });\n      });\n    }, error => {\n      console.error(\"CheckRestoreCard: Failed to refresh client status:\", error);\n      this.updateServiceState({\n        isLoading: false\n      });\n    });\n  }\n  checkClientVerify() {\n    console.log(\"CheckRestoreCard: Check Client Verify\");\n    const currentSessionId = this.serviceState.getValue().sessionId;\n    this.updateServiceState({\n      isLoading: true\n    });\n    this.settingService.restoreCardCheck(\"client\", currentSessionId, \"verify\").subscribe(res => {\n      console.log(\"CheckRestoreCard: Client verify response:\", res);\n      this.updateServiceState({\n        isLoading: false\n      });\n      if (this.isVerifyResponse(res)) {\n        this.updateClientListFromServerData(res.info.clients);\n      }\n    });\n  }\n  // 新增：批量操作方法\n  checkClientBatch(actions) {\n    console.log(\"CheckRestoreCard: Check Client Batch\", actions);\n    const currentSessionId = this.serviceState.getValue().sessionId;\n    this.updateServiceState({\n      isLoading: true\n    });\n    return this.settingService.restoreCardCheck(\"client\", currentSessionId, actions);\n  }\n  // 新增：reboot专用方法\n  checkClientReboot() {\n    console.log(\"CheckRestoreCard: Check Client Reboot\");\n    const currentSessionId = this.serviceState.getValue().sessionId;\n    this.updateServiceState({\n      isLoading: true\n    });\n    this.settingService.restoreCardCheck(\"client\", currentSessionId, \"reboot\").subscribe(res => {\n      console.log(\"CheckRestoreCard: Client reboot response:\", res);\n      this.updateServiceState({\n        isLoading: false\n      });\n      if (res.status === \"success\") {\n        console.log(\"CheckRestoreCard: Client reboot success\");\n      } else {\n        console.error(\"CheckRestoreCard: Client reboot failed:\", res);\n      }\n    });\n  }\n  checkManagerReboot() {\n    console.log(\"CheckRestoreCard: Check Manager Reboot\");\n    const currentSessionId = this.serviceState.getValue().sessionId;\n    this.settingService.restoreCardCheck(\"manager\", currentSessionId, \"reboot\").subscribe(res => {\n      console.log(\"CheckRestoreCard: Manager reboot response:\", res);\n      // Manager reboot不支持，会返回错误\n      if (res.status === \"failed\") {\n        console.warn(\"CheckRestoreCard: Manager reboot not supported\");\n      }\n    });\n  }\n  // 新增：写入后重启流程\n  checkClientWriteAndReboot() {\n    console.log(\"CheckRestoreCard: Check Client Write and Reboot\");\n    const currentSessionId = this.serviceState.getValue().sessionId;\n    this.updateServiceState({\n      isLoading: true\n    });\n    this.settingService.restoreCardCheck(\"client\", currentSessionId, [\"write\", \"reboot\"]).subscribe(res => {\n      console.log(\"CheckRestoreCard: Client write and reboot response:\", res);\n      this.updateServiceState({\n        isLoading: false\n      });\n      if (res.status === \"success\") {\n        if (this.isRestoreCardBatchResponse(res)) {\n          // 处理批量操作结果\n          const results = res.info.results;\n          const writeResult = results.find(r => r.action === \"write\");\n          const rebootResult = results.find(r => r.action === \"reboot\");\n          if (writeResult && writeResult.status === \"success\") {\n            console.log(\"CheckRestoreCard: Client write success\");\n          }\n          if (rebootResult && rebootResult.status === \"success\") {\n            console.log(\"CheckRestoreCard: Client reboot success\");\n          }\n        } else {\n          console.log(\"CheckRestoreCard: Operation completed successfully\");\n        }\n      } else {\n        // 处理失败情况\n        console.error(\"CheckRestoreCard: Client write and reboot failed:\", res);\n      }\n    });\n  }\n  // 单个客户端检查方法\n  checkSingleClient(seatNumber, actions) {\n    // 检查客户端是否在线\n    if (!this.canPerformCheck(seatNumber)) {\n      console.warn(`RestoreCardCheckService: Cannot check offline client ${seatNumber}`);\n      return;\n    }\n    console.log(`CheckRestoreCard: Check single client ${seatNumber}`, actions);\n    const currentSessionId = this.serviceState.getValue().sessionId;\n    this.setSingleClientLoading(seatNumber, true);\n    this.settingService.restoreCardCheck(\"client\", currentSessionId, actions, [seatNumber]).subscribe(res => {\n      console.log(`CheckRestoreCard: Single client ${seatNumber} response:`, res);\n      setTimeout(() => {\n        this.setSingleClientLoading(seatNumber, false);\n      }, 1000);\n    }, error => {\n      console.error(`CheckRestoreCard: Single client ${seatNumber} failed:`, error);\n      setTimeout(() => {\n        this.setSingleClientLoading(seatNumber, false);\n      }, 1000);\n    });\n  }\n  // 新增：获取单个客户端加载状态\n  isSingleClientLoading(seatNumber) {\n    if (!this.singleClientLoadingStates.has(seatNumber)) {\n      this.singleClientLoadingStates.set(seatNumber, new BehaviorSubject(false));\n    }\n    return this.singleClientLoadingStates.get(seatNumber).asObservable();\n  }\n  // 新增：设置单个客户端加载状态\n  setSingleClientLoading(seatNumber, loading) {\n    if (!this.singleClientLoadingStates.has(seatNumber)) {\n      this.singleClientLoadingStates.set(seatNumber, new BehaviorSubject(false));\n    }\n    this.singleClientLoadingStates.get(seatNumber).next(loading);\n  }\n  updateClientStatus(seatNumber, status) {\n    this.updateClientCheckStatus(seatNumber, {\n      status,\n      statusText: this.getStatusText(status)\n    });\n    // 检查是否需要自动保存\n    this.checkAutoSave();\n  }\n  getClientStatus(seatNumber) {\n    const clientState = this.getClientState(seatNumber);\n    return clientState?.checkInfo.status || \"NotChecked\";\n  }\n  resetAllStatuses() {\n    const currentState = this.serviceState.getValue();\n    const newClientStates = new Map();\n    currentState.clientStates.forEach((state, seatNumber) => {\n      const resetState = RestoreCardDataAdapter.mergeClientState(state, {\n        checkInfo: {\n          status: \"NotChecked\",\n          statusText: \"未检查\"\n        },\n        lastUpdated: Date.now()\n      });\n      newClientStates.set(seatNumber, resetState);\n    });\n    this.updateServiceState({\n      clientStates: newClientStates,\n      lastRefresh: Date.now()\n    });\n    this.updateCountsFullRecalculation();\n    // 重置保存时间，允许立即触发智能保存\n    this.lastSaveTime = 0;\n  }\n  // WebSocket 消息处理\n  handleRestoreCardUpdate(data) {\n    console.log(\"RestoreCardCheckService: Received status update\", data);\n    if (data.session_id === this.serviceState.getValue().sessionId) {\n      // 使用数据适配器处理服务器更新\n      this.updateClientListFromServerData(data.clients);\n    }\n  }\n  updateClientListFromServerData(clients) {\n    try {\n      const currentState = this.serviceState.getValue();\n      const newClientStates = new Map(currentState.clientStates);\n      const serverDataMap = new Map();\n      clients.forEach((client, index) => {\n        if (RestoreCardDataAdapter.validateServerData(client)) {\n          const seatNumber = client.seatNumber;\n          serverDataMap.set(seatNumber, client);\n        } else {\n          console.error(`RestoreCardCheckService: Invalid server data at index ${index}:`, {\n            client,\n            expectedFields: [\"seatNumber\", \"status\", \"ip\"]\n          });\n        }\n      });\n      // 合并现有数据和服务器数据\n      serverDataMap.forEach((serverData, seatNumber) => {\n        const existingState = newClientStates.get(seatNumber);\n        if (existingState) {\n          try {\n            const serverUpdate = RestoreCardDataAdapter.fromServerUpdate(serverData);\n            const updatedState = RestoreCardDataAdapter.mergeClientState(existingState, serverUpdate);\n            newClientStates.set(seatNumber, updatedState);\n          } catch (error) {\n            console.error(`RestoreCardCheckService: Error updating client ${seatNumber}:`, error);\n          }\n        }\n      });\n      this.updateServiceState({\n        clientStates: newClientStates,\n        lastRefresh: Date.now()\n      });\n      this.updateCountsFullRecalculation();\n    } catch (error) {\n      console.error(\"RestoreCardCheckService: Error updating client list from server data:\", error);\n    }\n  }\n  mapStatusToDisplay(status) {\n    const statusMap = {\n      NotChecked: \"NotChecked\",\n      Writing: \"Writing\",\n      Restarting: \"Restarting\",\n      Verifying: \"Verifying\",\n      Success: \"Success\",\n      Failed: \"Failed\",\n      Timeout: \"Timeout\"\n    };\n    return statusMap[status];\n  }\n  getStatusText(status) {\n    const textMap = {\n      NotChecked: \"未检查\",\n      Writing: \"检查中\",\n      Restarting: \"重启中\",\n      Verifying: \"验证中\",\n      Success: \"检查成功\",\n      Failed: \"检查失败\",\n      Timeout: \"超时\"\n    };\n    return textMap[status] || \"未知状态\";\n  }\n  // 新增：保存检查结果通知方法\n  triggerSaveCheckResult(reason = \"manual\") {\n    console.log(`RestoreCardCheckService: Triggering save check result, reason: ${reason}`);\n    this.saveCheckResultTrigger.next({\n      trigger: true,\n      reason\n    });\n  }\n  resetSaveCheckResultTrigger() {\n    this.saveCheckResultTrigger.next({\n      trigger: false\n    });\n  }\n  // 自动保存相关方法\n  startAutoSave() {\n    this.stopAutoSave(); // 防止重复启动\n    this.autoSaveTimer = setInterval(() => {\n      const currentState = this.serviceState.getValue();\n      // 只在有客户端数据且不在加载状态时保存\n      if (currentState.clientStates.size > 0 && !currentState.isLoading) {\n        console.log(\"RestoreCardCheckService: Auto-save triggered (periodic)\");\n        this.triggerSaveCheckResult(\"auto_periodic_save\");\n      }\n    }, this.AUTO_SAVE_INTERVAL);\n    console.log(\"RestoreCardCheckService: Auto-save started with interval:\", this.AUTO_SAVE_INTERVAL);\n  }\n  stopAutoSave() {\n    if (this.autoSaveTimer) {\n      clearInterval(this.autoSaveTimer);\n      this.autoSaveTimer = null;\n      console.log(\"RestoreCardCheckService: Auto-save stopped\");\n    }\n  }\n  checkSmartSave() {\n    const now = Date.now();\n    if (now - this.lastSaveTime > this.MIN_SAVE_INTERVAL) {\n      this.lastSaveTime = now;\n      console.log(\"RestoreCardCheckService: Smart save triggered\");\n      this.triggerSaveCheckResult(\"smart_trigger_save\");\n    }\n  }\n  // 超时重试相关方法\n  startTimeoutRetry(seatNumber) {\n    // 如果已经有重试定时器在运行，先停止\n    this.stopTimeoutRetry(seatNumber);\n    // 初始化重试次数\n    this.timeoutRetryAttempts.set(seatNumber, 0);\n    console.log(`RestoreCardCheckService: Starting timeout retry for client ${seatNumber}`);\n    const retryTimer = setInterval(() => {\n      this.performTimeoutRetry(seatNumber);\n    }, this.RETRY_INTERVAL);\n    this.timeoutRetryTimers.set(seatNumber, retryTimer);\n  }\n  performTimeoutRetry(seatNumber) {\n    const currentAttempts = this.timeoutRetryAttempts.get(seatNumber) || 0;\n    const newAttempts = currentAttempts + 1;\n    console.log(`RestoreCardCheckService: Performing retry attempt ${newAttempts} for client ${seatNumber}`);\n    // 检查是否超过最大重试次数\n    if (newAttempts > this.MAX_RETRY_ATTEMPTS) {\n      console.log(`RestoreCardCheckService: Max retry attempts reached for client ${seatNumber}, stopping retry`);\n      this.stopTimeoutRetry(seatNumber);\n      return;\n    }\n    // 更新重试次数\n    this.timeoutRetryAttempts.set(seatNumber, newAttempts);\n    // 发送启动考试机命令\n    this.sendStartClientCommand(seatNumber);\n  }\n  sendStartClientCommand(seatNumber) {\n    console.log(`RestoreCardCheckService: Sending start command to client ${seatNumber}`);\n    this.socketService.clientIO.sendMsg(M.MSG_START_CLIENT, {\n      seats: [seatNumber]\n    }, res => {\n      if (res) {\n        console.log(`RestoreCardCheckService: Start command sent to client ${seatNumber}:`, res);\n      } else {\n        console.error(`RestoreCardCheckService: Failed to send start command to client ${seatNumber}`);\n      }\n    });\n  }\n  stopTimeoutRetry(seatNumber) {\n    const timer = this.timeoutRetryTimers.get(seatNumber);\n    if (timer) {\n      clearInterval(timer);\n      this.timeoutRetryTimers.delete(seatNumber);\n      this.timeoutRetryAttempts.delete(seatNumber);\n      console.log(`RestoreCardCheckService: Stopped timeout retry for client ${seatNumber}`);\n    }\n  }\n  stopAllTimeoutRetries() {\n    console.log(\"RestoreCardCheckService: Stopping all timeout retries\");\n    this.timeoutRetryTimers.forEach((timer, seatNumber) => {\n      clearInterval(timer);\n      console.log(`RestoreCardCheckService: Stopped retry timer for client ${seatNumber}`);\n    });\n    this.timeoutRetryTimers.clear();\n    this.timeoutRetryAttempts.clear();\n  }\n  checkAutoSave() {\n    const currentState = this.serviceState.getValue();\n    const clientStates = Array.from(currentState.clientStates.values());\n    // 检查是否所有客户端都有了最终状态（成功或失败）\n    const allClientsHaveFinalStatus = clientStates.length > 0 && clientStates.every(clientState => {\n      const status = clientState.checkInfo.status;\n      return status === \"Success\" || status === \"Failed\" || status === \"Timeout\";\n    });\n    if (allClientsHaveFinalStatus) {\n      console.log(\"RestoreCardCheckService: All clients have final status, triggering auto-save\");\n      this.triggerSaveCheckResult(\"auto_all_clients_completed\");\n    } else {\n      // 新增：检查是否有新的成功状态，智能触发保存\n      const hasSuccessClients = clientStates.some(state => state.checkInfo.status === \"Success\");\n      if (hasSuccessClients) {\n        this.checkSmartSave();\n      }\n    }\n  }\n  // 状态重置\n  resetClientStatus(seatNumber) {\n    console.log(`重置座位 ${seatNumber} 的状态`);\n    this.updateClientStatus(seatNumber, \"NotChecked\");\n  }\n  resetAllClientStatus() {\n    console.log(\"重置所有客户端状态\");\n    this.resetAllStatuses();\n  }\n  getClientState(seatNumber) {\n    return this.serviceState.getValue().clientStates.get(seatNumber);\n  }\n  getNotCheckedClientSeats() {\n    const currentState = this.serviceState.getValue();\n    const notCheckedSeats = [];\n    currentState.clientStates.forEach((clientState, seatNumber) => {\n      if (clientState.checkInfo.status === \"NotChecked\") {\n        notCheckedSeats.push(seatNumber);\n      }\n    });\n    console.log(`RestoreCardCheckService: Found ${notCheckedSeats.length} not checked clients:`, notCheckedSeats);\n    return notCheckedSeats;\n  }\n  updateClientInfo(seatNumber, info) {\n    const currentState = this.serviceState.getValue();\n    const newClientStates = new Map(currentState.clientStates);\n    const existing = newClientStates.get(seatNumber);\n    if (existing) {\n      const updatedState = RestoreCardDataAdapter.mergeClientState(existing, {\n        clientInfo: {\n          ...existing.clientInfo,\n          ...info\n        },\n        lastUpdated: Date.now()\n      });\n      newClientStates.set(seatNumber, updatedState);\n    } else {\n      // 创建新的客户端状态\n      const clientInfo = {\n        seat_number: seatNumber,\n        ip: info.ip || \"\",\n        host: info.host || info.ip || \"\",\n        host_name: info.host_name || \"\",\n        is_online: info.is_online || false,\n        ...info\n      };\n      newClientStates.set(seatNumber, RestoreCardDataAdapter.createDefaultClientState(clientInfo));\n      // 新增客户端，重新计算总数\n      this.updateCountsFullRecalculation();\n    }\n    this.updateServiceState({\n      clientStates: newClientStates,\n      lastRefresh: Date.now()\n    });\n  }\n  updateClientCheckStatus(seatNumber, checkInfo) {\n    const currentState = this.serviceState.getValue();\n    const newClientStates = new Map(currentState.clientStates);\n    const existing = newClientStates.get(seatNumber);\n    if (existing) {\n      const oldStatus = existing.checkInfo.status;\n      const newStatus = checkInfo.status || oldStatus;\n      const updatedState = RestoreCardDataAdapter.mergeClientState(existing, {\n        checkInfo: {\n          ...existing.checkInfo,\n          ...checkInfo\n        },\n        lastUpdated: Date.now()\n      });\n      newClientStates.set(seatNumber, updatedState);\n      this.updateServiceState({\n        clientStates: newClientStates,\n        lastRefresh: Date.now()\n      });\n      // 重新计算计数\n      if (oldStatus !== newStatus) {\n        this.updateCountsFullRecalculation();\n      }\n    }\n  }\n  updateServiceState(update) {\n    const currentState = this.serviceState.getValue();\n    this.serviceState.next({\n      ...currentState,\n      ...update\n    });\n  }\n  transformClientStateToDisplay(clientState) {\n    return {\n      seat_number: clientState.clientInfo.seat_number,\n      ip: clientState.clientInfo.ip,\n      host: clientState.clientInfo.host,\n      host_name: clientState.clientInfo.host_name,\n      is_online: clientState.clientInfo.is_online,\n      version: clientState.clientInfo.version,\n      status: clientState.clientInfo.status,\n      permit_id: clientState.clientInfo.permit_id,\n      entry_id: clientState.clientInfo.entry_id,\n      mac_addr: clientState.clientInfo.mac_addr,\n      check: {\n        status: this.mapStatusToDisplay(clientState.checkInfo.status),\n        statusText: clientState.checkInfo.statusText\n      },\n      // 各检查阶段的具体时间戳\n      startTime: clientState.checkInfo.startTime,\n      writeTime: clientState.checkInfo.writeTime,\n      restartTime: clientState.checkInfo.restartTime,\n      verifyTime: clientState.checkInfo.verifyTime,\n      // 服务器端时间信息\n      timestamp: clientState.checkInfo.timestamp,\n      timeoutAt: clientState.checkInfo.timeoutAt\n    };\n  }\n  // 批量状态转换方法\n  transformAllClientStates(state) {\n    return Array.from(state.clientStates.values()).map(clientState => this.transformClientStateToDisplay(clientState));\n  }\n  /**\n   * 更新客户端连接状态\n   */\n  updateClientConnectionStatus(seatNumber, isOnline, additionalInfo) {\n    const currentState = this.serviceState.getValue();\n    const newClientStates = new Map(currentState.clientStates);\n    const existing = newClientStates.get(seatNumber);\n    if (existing) {\n      // 更新现有客户端的连接状态\n      const updatedClientInfo = {\n        ...existing.clientInfo,\n        is_online: isOnline,\n        ...(additionalInfo?.host && {\n          host: additionalInfo.host\n        }),\n        ...(additionalInfo?.version && {\n          version: additionalInfo.version\n        })\n      };\n      const updatedState = RestoreCardDataAdapter.mergeClientState(existing, {\n        clientInfo: updatedClientInfo,\n        lastUpdated: Date.now()\n      });\n      newClientStates.set(seatNumber, updatedState);\n      this.updateServiceState({\n        clientStates: newClientStates,\n        lastRefresh: Date.now()\n      });\n    } else {\n      console.warn(`RestoreCardCheck: Client ${seatNumber} not found in current state`);\n    }\n  }\n  /**\n   * 检查客户端是否可以执行操作\n   */\n  canPerformCheck(seatNumber) {\n    const clientState = this.serviceState.getValue().clientStates.get(seatNumber);\n    return clientState?.clientInfo?.is_online ?? false;\n  }\n  static #_ = this.ɵfac = function RestoreCardCheckService_Factory(t) {\n    return new (t || RestoreCardCheckService)(i0.ɵɵinject(i1.SettingService), i0.ɵɵinject(i2.SocketService));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: RestoreCardCheckService,\n    factory: RestoreCardCheckService.ɵfac\n  });\n}", "map": {"version": 3, "names": ["BehaviorSubject", "map", "shareReplay", "distinctUntilChanged", "M", "RestoreCardDataAdapter", "RestoreCardCheckService", "constructor", "settingService", "socketService", "serviceState", "sessionId", "clientStates", "Map", "counts", "success", "failed", "total", "isLoading", "lastRefresh", "clientCheckModal", "saveCheckResultTrigger", "trigger", "singleClientLoadingStates", "AUTO_SAVE_INTERVAL", "MIN_SAVE_INTERVAL", "timeoutRetryTimers", "timeoutRetryAttempts", "RETRY_INTERVAL", "MAX_RETRY_ATTEMPTS", "MAX_RETRY_DURATION", "lastSaveTime", "serviceState$", "asObservable", "clientCheckModal$", "saveCheckResultTrigger$", "clientList$", "pipe", "state", "transformAllClientStates", "loading$", "counts$", "startAutoSave", "ngOnDestroy", "console", "log", "stopAutoSave", "stopAllTimeoutRetries", "complete", "for<PERSON>ach", "subject", "clear", "isRestoreCardBatchResponse", "response", "info", "Array", "isArray", "results", "isVerifyResponse", "clients", "openClientCheckModal", "next", "closeClientCheckModal", "setSessionId", "updateServiceState", "updateClientList", "newClientStatesMap", "clientState", "validateClientState", "set", "clientInfo", "seat_number", "Date", "now", "updateCountsFullRecalculation", "checkAutoSave", "error", "currentState", "getValue", "from", "values", "filter", "checkInfo", "status", "length", "timeout", "updateMultipleClientStatuses", "updates", "newClientStates", "has<PERSON><PERSON><PERSON>", "seatNumber", "statusText", "existing", "get", "updatedState", "mergeClientState", "getStatusText", "lastUpdated", "checkClient", "currentSessionId", "restoreCardCheck", "subscribe", "res", "checkClientWithRefresh", "refreshRes", "updateClientListFromServerData", "notCheckedSeats", "getNotCheckedClientSeats", "size", "checkRes", "checkClientVerify", "checkClientBatch", "actions", "checkClientReboot", "checkManagerReboot", "warn", "checkClientWriteAndReboot", "writeResult", "find", "r", "action", "rebootResult", "checkSingleClient", "canPerformCheck", "setSingleClientLoading", "setTimeout", "isSingleClientLoading", "has", "loading", "updateClientStatus", "updateClientCheckStatus", "getClientStatus", "getClientState", "resetAllStatuses", "resetState", "handleRestoreCardUpdate", "data", "session_id", "serverDataMap", "client", "index", "validateServerData", "expectedFields", "serverData", "existingState", "serverUpdate", "fromServerUpdate", "mapStatusToDisplay", "statusMap", "NotChecked", "Writing", "Restarting", "Verifying", "Success", "Failed", "Timeout", "textMap", "triggerSaveCheckResult", "reason", "resetSaveCheckResultTrigger", "autoSaveTimer", "setInterval", "clearInterval", "checkSmartSave", "startTimeoutRetry", "stopTimeoutRetry", "retryTimer", "performTimeoutRetry", "currentAttempts", "newAttempts", "sendStartClientCommand", "clientIO", "sendMsg", "MSG_START_CLIENT", "seats", "timer", "delete", "allClientsHaveFinalStatus", "every", "hasSuccessClients", "some", "resetClientStatus", "resetAllClientStatus", "push", "updateClientInfo", "ip", "host", "host_name", "is_online", "createDefaultClientState", "oldStatus", "newStatus", "update", "transformClientStateToDisplay", "version", "permit_id", "entry_id", "mac_addr", "check", "startTime", "writeTime", "restartTime", "verifyTime", "timestamp", "timeoutAt", "updateClientConnectionStatus", "isOnline", "additionalInfo", "updatedClientInfo", "_", "i0", "ɵɵinject", "i1", "SettingService", "i2", "SocketService", "_2", "factory", "ɵfac"], "sources": ["D:\\work\\joyserver\\manager\\src\\app\\dashboard\\session-prepare\\prepare-detail\\check-restore-card\\restore-card-check.service.ts"], "sourcesContent": ["import { Injectable, <PERSON><PERSON><PERSON>roy } from \"@angular/core\";\nimport { BehaviorSubject, Observable } from \"rxjs\";\nimport { map, shareReplay, distinctUntilChanged } from \"rxjs/operators\";\nimport { SettingService } from \"@app/core/http/setting.service\";\nimport { SocketService } from \"@app/core/services/socket.service\";\nimport { RestoreCardAction, RestoreCardActions, RestoreCardCheckStatus } from \"@app/core/io/io.message.consts\";\nimport * as M from \"@app/core/io\";\nimport {\n  RestoreCardServiceState,\n  ClientState,\n  SaveCheckResultTrigger,\n  RestoreCardClientInfo,\n  RestoreCardCheckInfo,\n  RestoreCardClientDisplay,\n  RestoreCardWebSocketMessage,\n  ClientCheckResult,\n  RestoreCardBatchResult,\n} from \"./restore-card-check.types\";\nimport { RestoreCardDataAdapter } from \"./restore-card-data.adapter\";\n\n@Injectable()\nexport class RestoreCardCheckService implements OnD<PERSON>roy {\n  private serviceState = new BehaviorSubject<RestoreCardServiceState>({\n    sessionId: \"\",\n    clientStates: new Map<number, ClientState>(),\n    counts: {\n      success: 0,\n      failed: 0,\n      total: 0,\n    },\n    isLoading: false,\n    lastRefresh: 0,\n  });\n\n  private clientCheckModal = new BehaviorSubject<boolean>(false);\n  private saveCheckResultTrigger = new BehaviorSubject<SaveCheckResultTrigger>({ trigger: false });\n  private singleClientLoadingStates = new Map<number, BehaviorSubject<boolean>>();\n\n  // 自动保存相关属性\n  private autoSaveTimer: any;\n  private readonly AUTO_SAVE_INTERVAL = 5000; // 5秒间隔\n  private readonly MIN_SAVE_INTERVAL = 3000; // 最小保存间隔3秒，防止频繁保存\n\n  // 超时重试相关属性\n  private timeoutRetryTimers = new Map<number, any>(); // 存储每个客户端的重试定时器\n  private timeoutRetryAttempts = new Map<number, number>(); // 存储每个客户端的重试次数\n  private readonly RETRY_INTERVAL = 45000; // 45秒重试间隔\n  private readonly MAX_RETRY_ATTEMPTS = 10; // 最大重试次数\n  private readonly MAX_RETRY_DURATION = 600000; // 最大重试时长10分钟\n  private lastSaveTime = 0;\n\n  public serviceState$ = this.serviceState.asObservable();\n  public clientCheckModal$ = this.clientCheckModal.asObservable();\n  public saveCheckResultTrigger$ = this.saveCheckResultTrigger.asObservable();\n\n  public clientList$: Observable<RestoreCardClientDisplay[]> = this.serviceState$.pipe(\n    map((state) => this.transformAllClientStates(state)),\n    distinctUntilChanged(),\n    shareReplay(1)\n  );\n\n  public loading$ = this.serviceState$.pipe(\n    map((state) => state.isLoading),\n    distinctUntilChanged(),\n    shareReplay(1)\n  );\n\n  public counts$ = this.serviceState$.pipe(\n    map((state) => state.counts),\n    distinctUntilChanged(),\n    shareReplay(1)\n  );\n\n  constructor(private settingService: SettingService, private socketService: SocketService) {\n    this.startAutoSave();\n  }\n\n  ngOnDestroy(): void {\n    console.log(\"RestoreCardCheckService: Destroying service, cleaning up resources\");\n\n    this.stopAutoSave();\n    this.stopAllTimeoutRetries();\n    this.serviceState.complete();\n    this.clientCheckModal.complete();\n    this.saveCheckResultTrigger.complete();\n    this.singleClientLoadingStates.forEach((subject) => subject.complete());\n    this.singleClientLoadingStates.clear();\n  }\n\n  private isRestoreCardBatchResponse(response: any): response is { info: RestoreCardBatchResult } {\n    return response && response.info && Array.isArray(response.info.results);\n  }\n\n  private isVerifyResponse(response: any): response is { info: { clients: ClientCheckResult[] } } {\n    return response && response.info && Array.isArray(response.info.clients);\n  }\n\n  // 模态框控制方法\n  openClientCheckModal(): void {\n    this.clientCheckModal.next(true);\n  }\n\n  closeClientCheckModal(): void {\n    this.clientCheckModal.next(false);\n  }\n\n  // 数据更新方法\n  setSessionId(sessionId: string): void {\n    this.updateServiceState({ sessionId });\n    // 重启自动保存以确保新会话的数据安全\n    this.startAutoSave();\n  }\n\n  updateClientList(clientStates: ClientState[]): void {\n    try {\n      // 直接处理ClientState数组，无需格式转换\n      const newClientStatesMap = new Map<number, ClientState>();\n\n      clientStates.forEach((clientState) => {\n        if (RestoreCardDataAdapter.validateClientState(clientState)) {\n          newClientStatesMap.set(clientState.clientInfo.seat_number, clientState);\n        }\n      });\n\n      // 更新客户端状态映射\n\n      this.updateServiceState({\n        clientStates: newClientStatesMap,\n        lastRefresh: Date.now(),\n      });\n\n      // 使用全量重新计算（因为是批量更新）\n      this.updateCountsFullRecalculation();\n      this.checkAutoSave();\n    } catch (error) {\n      console.error(\"RestoreCardCheckService: Error updating client list:\", error);\n    }\n  }\n\n  // 保留全量重新计算方法，用于批量更新场景\n  private updateCountsFullRecalculation(): void {\n    const currentState = this.serviceState.getValue();\n    const clientStates = Array.from(currentState.clientStates.values());\n\n    const success = clientStates.filter((state) => state.checkInfo.status === \"Success\").length;\n    const failed = clientStates.filter((state) => state.checkInfo.status === \"Failed\").length;\n    const timeout = clientStates.filter((state) => state.checkInfo.status === \"Timeout\").length;\n    const total = clientStates.length;\n\n    this.updateServiceState({\n      counts: {\n        success,\n        failed: failed + timeout, // 将timeout也计入failed\n        total,\n      },\n    });\n\n    console.log(\n      `RestoreCardCheckService: Full count recalculation - Success: ${success}, Failed: ${\n        failed + timeout\n      }, Total: ${total}`\n    );\n  }\n\n  // 批量状态更新方法 - 性能优化核心\n  public updateMultipleClientStatuses(\n    updates: Array<{\n      seatNumber: number;\n      status: RestoreCardCheckStatus;\n      statusText?: string;\n    }>\n  ): void {\n    if (updates.length === 0) return;\n\n    const currentState = this.serviceState.getValue();\n    const newClientStates = new Map(currentState.clientStates);\n    let hasChanges = false;\n\n    // 批量处理所有更新\n    updates.forEach(({ seatNumber, status, statusText }) => {\n      const existing = newClientStates.get(seatNumber);\n      if (existing) {\n        const updatedState = RestoreCardDataAdapter.mergeClientState(existing, {\n          checkInfo: {\n            ...existing.checkInfo,\n            status,\n            statusText: statusText || this.getStatusText(status),\n          },\n          lastUpdated: Date.now(),\n        });\n        newClientStates.set(seatNumber, updatedState);\n        hasChanges = true;\n      }\n    });\n\n    if (hasChanges) {\n      // 一次性更新状态\n      this.updateServiceState({\n        clientStates: newClientStates,\n        lastRefresh: Date.now(),\n      });\n\n      // 批量更新后重新计算计数\n      this.updateCountsFullRecalculation();\n      this.checkAutoSave();\n\n      console.log(`RestoreCardCheckService: Batch updated ${updates.length} client statuses`);\n    }\n  }\n\n  checkClient(): void {\n    console.log(\"CheckRestoreCard: Check Client restore card\");\n    const currentSessionId = this.serviceState.getValue().sessionId;\n\n    this.updateServiceState({ isLoading: true });\n    this.settingService.restoreCardCheck(\"client\", currentSessionId, [\"write\", \"reboot\", \"verify\"]).subscribe((res) => {\n      console.log(\"CheckRestoreCard: Client restore card response:\", res);\n      this.updateServiceState({ isLoading: false });\n    });\n  }\n\n  checkClientWithRefresh(): void {\n    console.log(\"CheckRestoreCard: Check Client with refresh - Starting refresh and check process\");\n    const currentSessionId = this.serviceState.getValue().sessionId;\n\n    this.updateServiceState({ isLoading: true });\n    console.log(\"CheckRestoreCard: Set loading state to true, starting refresh...\");\n\n    // 首先刷新最新状态\n    this.settingService.restoreCardCheck(\"client\", currentSessionId, \"verify\").subscribe(\n      (refreshRes) => {\n        console.log(\"CheckRestoreCard: Client verify response:\", refreshRes);\n\n        if (this.isVerifyResponse(refreshRes)) {\n          this.updateClientListFromServerData(refreshRes.info.clients);\n        }\n\n        // 刷新完成后执行检查\n        const currentState = this.serviceState.getValue();\n        const notCheckedSeats = this.getNotCheckedClientSeats();\n\n        // 如果没有客户端状态数据（首次检查），直接执行原有逻辑\n        if (currentState.clientStates.size === 0) {\n          console.log(\"CheckRestoreCard: No client state data, performing initial check\");\n        } else if (notCheckedSeats.length === 0) {\n          console.log(\"CheckRestoreCard: All clients already checked\");\n          this.updateServiceState({ isLoading: false });\n          return;\n        } else {\n          console.log(`CheckRestoreCard: Starting check for ${notCheckedSeats.length} not checked clients`);\n        }\n        this.settingService\n          .restoreCardCheck(\"client\", currentSessionId, [\"write\", \"reboot\", \"verify\"])\n          .subscribe((checkRes) => {\n            console.log(\"CheckRestoreCard: Client restore card response:\", checkRes);\n            this.updateServiceState({ isLoading: false });\n          });\n      },\n      (error) => {\n        console.error(\"CheckRestoreCard: Failed to refresh client status:\", error);\n        this.updateServiceState({ isLoading: false });\n      }\n    );\n  }\n\n  checkClientVerify(): void {\n    console.log(\"CheckRestoreCard: Check Client Verify\");\n    const currentSessionId = this.serviceState.getValue().sessionId;\n\n    this.updateServiceState({ isLoading: true });\n    this.settingService.restoreCardCheck(\"client\", currentSessionId, \"verify\").subscribe((res) => {\n      console.log(\"CheckRestoreCard: Client verify response:\", res);\n      this.updateServiceState({ isLoading: false });\n\n      if (this.isVerifyResponse(res)) {\n        this.updateClientListFromServerData(res.info.clients);\n      }\n    });\n  }\n\n  // 新增：批量操作方法\n  checkClientBatch(actions: RestoreCardAction[]): Observable<any> {\n    console.log(\"CheckRestoreCard: Check Client Batch\", actions);\n    const currentSessionId = this.serviceState.getValue().sessionId;\n\n    this.updateServiceState({ isLoading: true });\n    return this.settingService.restoreCardCheck(\"client\", currentSessionId, actions);\n  }\n\n  // 新增：reboot专用方法\n  checkClientReboot(): void {\n    console.log(\"CheckRestoreCard: Check Client Reboot\");\n    const currentSessionId = this.serviceState.getValue().sessionId;\n\n    this.updateServiceState({ isLoading: true });\n    this.settingService.restoreCardCheck(\"client\", currentSessionId, \"reboot\").subscribe((res) => {\n      console.log(\"CheckRestoreCard: Client reboot response:\", res);\n      this.updateServiceState({ isLoading: false });\n      if (res.status === \"success\") {\n        console.log(\"CheckRestoreCard: Client reboot success\");\n      } else {\n        console.error(\"CheckRestoreCard: Client reboot failed:\", res);\n      }\n    });\n  }\n\n  checkManagerReboot(): void {\n    console.log(\"CheckRestoreCard: Check Manager Reboot\");\n    const currentSessionId = this.serviceState.getValue().sessionId;\n\n    this.settingService.restoreCardCheck(\"manager\", currentSessionId, \"reboot\").subscribe((res) => {\n      console.log(\"CheckRestoreCard: Manager reboot response:\", res);\n      // Manager reboot不支持，会返回错误\n      if (res.status === \"failed\") {\n        console.warn(\"CheckRestoreCard: Manager reboot not supported\");\n      }\n    });\n  }\n\n  // 新增：写入后重启流程\n  checkClientWriteAndReboot(): void {\n    console.log(\"CheckRestoreCard: Check Client Write and Reboot\");\n    const currentSessionId = this.serviceState.getValue().sessionId;\n\n    this.updateServiceState({ isLoading: true });\n    this.settingService.restoreCardCheck(\"client\", currentSessionId, [\"write\", \"reboot\"]).subscribe((res) => {\n      console.log(\"CheckRestoreCard: Client write and reboot response:\", res);\n      this.updateServiceState({ isLoading: false });\n\n      if (res.status === \"success\") {\n        if (this.isRestoreCardBatchResponse(res)) {\n          // 处理批量操作结果\n          const results = res.info.results;\n          const writeResult = results.find((r) => r.action === \"write\");\n          const rebootResult = results.find((r) => r.action === \"reboot\");\n\n          if (writeResult && writeResult.status === \"success\") {\n            console.log(\"CheckRestoreCard: Client write success\");\n          }\n\n          if (rebootResult && rebootResult.status === \"success\") {\n            console.log(\"CheckRestoreCard: Client reboot success\");\n          }\n        } else {\n          console.log(\"CheckRestoreCard: Operation completed successfully\");\n        }\n      } else {\n        // 处理失败情况\n        console.error(\"CheckRestoreCard: Client write and reboot failed:\", res);\n      }\n    });\n  }\n\n  // 单个客户端检查方法\n  checkSingleClient(seatNumber: number, actions: RestoreCardActions): void {\n    // 检查客户端是否在线\n    if (!this.canPerformCheck(seatNumber)) {\n      console.warn(`RestoreCardCheckService: Cannot check offline client ${seatNumber}`);\n      return;\n    }\n\n    console.log(`CheckRestoreCard: Check single client ${seatNumber}`, actions);\n    const currentSessionId = this.serviceState.getValue().sessionId;\n\n    this.setSingleClientLoading(seatNumber, true);\n    this.settingService.restoreCardCheck(\"client\", currentSessionId, actions, [seatNumber]).subscribe(\n      (res) => {\n        console.log(`CheckRestoreCard: Single client ${seatNumber} response:`, res);\n        setTimeout(() => {\n          this.setSingleClientLoading(seatNumber, false);\n        }, 1000);\n      },\n      (error) => {\n        console.error(`CheckRestoreCard: Single client ${seatNumber} failed:`, error);\n        setTimeout(() => {\n          this.setSingleClientLoading(seatNumber, false);\n        }, 1000);\n      }\n    );\n  }\n\n  // 新增：获取单个客户端加载状态\n  isSingleClientLoading(seatNumber: number): Observable<boolean> {\n    if (!this.singleClientLoadingStates.has(seatNumber)) {\n      this.singleClientLoadingStates.set(seatNumber, new BehaviorSubject<boolean>(false));\n    }\n    return this.singleClientLoadingStates.get(seatNumber)!.asObservable();\n  }\n\n  // 新增：设置单个客户端加载状态\n  private setSingleClientLoading(seatNumber: number, loading: boolean): void {\n    if (!this.singleClientLoadingStates.has(seatNumber)) {\n      this.singleClientLoadingStates.set(seatNumber, new BehaviorSubject<boolean>(false));\n    }\n    this.singleClientLoadingStates.get(seatNumber)!.next(loading);\n  }\n\n  public updateClientStatus(seatNumber: number, status: RestoreCardCheckStatus): void {\n    this.updateClientCheckStatus(seatNumber, {\n      status,\n      statusText: this.getStatusText(status),\n    });\n\n    // 检查是否需要自动保存\n    this.checkAutoSave();\n  }\n\n  public getClientStatus(seatNumber: number): RestoreCardCheckStatus {\n    const clientState = this.getClientState(seatNumber);\n    return clientState?.checkInfo.status || \"NotChecked\";\n  }\n\n  public resetAllStatuses(): void {\n    const currentState = this.serviceState.getValue();\n    const newClientStates = new Map<number, ClientState>();\n\n    currentState.clientStates.forEach((state, seatNumber) => {\n      const resetState = RestoreCardDataAdapter.mergeClientState(state, {\n        checkInfo: {\n          status: \"NotChecked\" as RestoreCardCheckStatus,\n          statusText: \"未检查\",\n        },\n        lastUpdated: Date.now(),\n      });\n      newClientStates.set(seatNumber, resetState);\n    });\n\n    this.updateServiceState({\n      clientStates: newClientStates,\n      lastRefresh: Date.now(),\n    });\n    this.updateCountsFullRecalculation();\n\n    // 重置保存时间，允许立即触发智能保存\n    this.lastSaveTime = 0;\n  }\n\n  // WebSocket 消息处理\n  public handleRestoreCardUpdate(data: RestoreCardWebSocketMessage): void {\n    console.log(\"RestoreCardCheckService: Received status update\", data);\n\n    if (data.session_id === this.serviceState.getValue().sessionId) {\n      // 使用数据适配器处理服务器更新\n      this.updateClientListFromServerData(data.clients);\n    }\n  }\n\n  private updateClientListFromServerData(clients: ClientCheckResult[]): void {\n    try {\n      const currentState = this.serviceState.getValue();\n      const newClientStates = new Map(currentState.clientStates);\n\n      const serverDataMap = new Map<number, ClientCheckResult>();\n      clients.forEach((client, index) => {\n        if (RestoreCardDataAdapter.validateServerData(client)) {\n          const seatNumber = client.seatNumber;\n          serverDataMap.set(seatNumber, client);\n        } else {\n          console.error(`RestoreCardCheckService: Invalid server data at index ${index}:`, {\n            client,\n            expectedFields: [\"seatNumber\", \"status\", \"ip\"],\n          });\n        }\n      });\n\n      // 合并现有数据和服务器数据\n      serverDataMap.forEach((serverData, seatNumber) => {\n        const existingState = newClientStates.get(seatNumber);\n        if (existingState) {\n          try {\n            const serverUpdate = RestoreCardDataAdapter.fromServerUpdate(serverData);\n            const updatedState = RestoreCardDataAdapter.mergeClientState(existingState, serverUpdate);\n            newClientStates.set(seatNumber, updatedState);\n          } catch (error) {\n            console.error(`RestoreCardCheckService: Error updating client ${seatNumber}:`, error);\n          }\n        }\n      });\n\n      this.updateServiceState({\n        clientStates: newClientStates,\n        lastRefresh: Date.now(),\n      });\n\n      this.updateCountsFullRecalculation();\n    } catch (error) {\n      console.error(\"RestoreCardCheckService: Error updating client list from server data:\", error);\n    }\n  }\n\n  private mapStatusToDisplay(status: RestoreCardCheckStatus): string {\n    const statusMap: { [key in RestoreCardCheckStatus]: string } = {\n      NotChecked: \"NotChecked\",\n      Writing: \"Writing\",\n      Restarting: \"Restarting\",\n      Verifying: \"Verifying\",\n      Success: \"Success\",\n      Failed: \"Failed\",\n      Timeout: \"Timeout\",\n    };\n    return statusMap[status];\n  }\n\n  private getStatusText(status: RestoreCardCheckStatus): string {\n    const textMap: { [key in RestoreCardCheckStatus]: string } = {\n      NotChecked: \"未检查\",\n      Writing: \"检查中\",\n      Restarting: \"重启中\",\n      Verifying: \"验证中\",\n      Success: \"检查成功\",\n      Failed: \"检查失败\",\n      Timeout: \"超时\",\n    };\n    return textMap[status] || \"未知状态\";\n  }\n\n  // 新增：保存检查结果通知方法\n  public triggerSaveCheckResult(reason: string = \"manual\"): void {\n    console.log(`RestoreCardCheckService: Triggering save check result, reason: ${reason}`);\n    this.saveCheckResultTrigger.next({ trigger: true, reason });\n  }\n\n  public resetSaveCheckResultTrigger(): void {\n    this.saveCheckResultTrigger.next({ trigger: false });\n  }\n\n  // 自动保存相关方法\n  private startAutoSave(): void {\n    this.stopAutoSave(); // 防止重复启动\n    this.autoSaveTimer = setInterval(() => {\n      const currentState = this.serviceState.getValue();\n      // 只在有客户端数据且不在加载状态时保存\n      if (currentState.clientStates.size > 0 && !currentState.isLoading) {\n        console.log(\"RestoreCardCheckService: Auto-save triggered (periodic)\");\n        this.triggerSaveCheckResult(\"auto_periodic_save\");\n      }\n    }, this.AUTO_SAVE_INTERVAL);\n    console.log(\"RestoreCardCheckService: Auto-save started with interval:\", this.AUTO_SAVE_INTERVAL);\n  }\n\n  private stopAutoSave(): void {\n    if (this.autoSaveTimer) {\n      clearInterval(this.autoSaveTimer);\n      this.autoSaveTimer = null;\n      console.log(\"RestoreCardCheckService: Auto-save stopped\");\n    }\n  }\n\n  private checkSmartSave(): void {\n    const now = Date.now();\n    if (now - this.lastSaveTime > this.MIN_SAVE_INTERVAL) {\n      this.lastSaveTime = now;\n      console.log(\"RestoreCardCheckService: Smart save triggered\");\n      this.triggerSaveCheckResult(\"smart_trigger_save\");\n    }\n  }\n\n  // 超时重试相关方法\n  private startTimeoutRetry(seatNumber: number): void {\n    // 如果已经有重试定时器在运行，先停止\n    this.stopTimeoutRetry(seatNumber);\n\n    // 初始化重试次数\n    this.timeoutRetryAttempts.set(seatNumber, 0);\n\n    console.log(`RestoreCardCheckService: Starting timeout retry for client ${seatNumber}`);\n\n    const retryTimer = setInterval(() => {\n      this.performTimeoutRetry(seatNumber);\n    }, this.RETRY_INTERVAL);\n\n    this.timeoutRetryTimers.set(seatNumber, retryTimer);\n  }\n\n  private performTimeoutRetry(seatNumber: number): void {\n    const currentAttempts = this.timeoutRetryAttempts.get(seatNumber) || 0;\n    const newAttempts = currentAttempts + 1;\n\n    console.log(`RestoreCardCheckService: Performing retry attempt ${newAttempts} for client ${seatNumber}`);\n\n    // 检查是否超过最大重试次数\n    if (newAttempts > this.MAX_RETRY_ATTEMPTS) {\n      console.log(`RestoreCardCheckService: Max retry attempts reached for client ${seatNumber}, stopping retry`);\n      this.stopTimeoutRetry(seatNumber);\n      return;\n    }\n\n    // 更新重试次数\n    this.timeoutRetryAttempts.set(seatNumber, newAttempts);\n\n    // 发送启动考试机命令\n    this.sendStartClientCommand(seatNumber);\n  }\n\n  private sendStartClientCommand(seatNumber: number): void {\n    console.log(`RestoreCardCheckService: Sending start command to client ${seatNumber}`);\n\n    this.socketService.clientIO.sendMsg(M.MSG_START_CLIENT, { seats: [seatNumber] }, (res) => {\n      if (res) {\n        console.log(`RestoreCardCheckService: Start command sent to client ${seatNumber}:`, res);\n      } else {\n        console.error(`RestoreCardCheckService: Failed to send start command to client ${seatNumber}`);\n      }\n    });\n  }\n\n  private stopTimeoutRetry(seatNumber: number): void {\n    const timer = this.timeoutRetryTimers.get(seatNumber);\n    if (timer) {\n      clearInterval(timer);\n      this.timeoutRetryTimers.delete(seatNumber);\n      this.timeoutRetryAttempts.delete(seatNumber);\n      console.log(`RestoreCardCheckService: Stopped timeout retry for client ${seatNumber}`);\n    }\n  }\n\n  private stopAllTimeoutRetries(): void {\n    console.log(\"RestoreCardCheckService: Stopping all timeout retries\");\n    this.timeoutRetryTimers.forEach((timer, seatNumber) => {\n      clearInterval(timer);\n      console.log(`RestoreCardCheckService: Stopped retry timer for client ${seatNumber}`);\n    });\n    this.timeoutRetryTimers.clear();\n    this.timeoutRetryAttempts.clear();\n  }\n\n  private checkAutoSave(): void {\n    const currentState = this.serviceState.getValue();\n    const clientStates = Array.from(currentState.clientStates.values());\n\n    // 检查是否所有客户端都有了最终状态（成功或失败）\n    const allClientsHaveFinalStatus =\n      clientStates.length > 0 &&\n      clientStates.every((clientState) => {\n        const status = clientState.checkInfo.status;\n        return status === \"Success\" || status === \"Failed\" || status === \"Timeout\";\n      });\n\n    if (allClientsHaveFinalStatus) {\n      console.log(\"RestoreCardCheckService: All clients have final status, triggering auto-save\");\n      this.triggerSaveCheckResult(\"auto_all_clients_completed\");\n    } else {\n      // 新增：检查是否有新的成功状态，智能触发保存\n      const hasSuccessClients = clientStates.some((state) => state.checkInfo.status === \"Success\");\n      if (hasSuccessClients) {\n        this.checkSmartSave();\n      }\n    }\n  }\n\n  // 状态重置\n  public resetClientStatus(seatNumber: number): void {\n    console.log(`重置座位 ${seatNumber} 的状态`);\n    this.updateClientStatus(seatNumber, \"NotChecked\");\n  }\n\n  public resetAllClientStatus(): void {\n    console.log(\"重置所有客户端状态\");\n    this.resetAllStatuses();\n  }\n\n  public getClientState(seatNumber: number): ClientState | undefined {\n    return this.serviceState.getValue().clientStates.get(seatNumber);\n  }\n\n  public getNotCheckedClientSeats(): number[] {\n    const currentState = this.serviceState.getValue();\n    const notCheckedSeats: number[] = [];\n\n    currentState.clientStates.forEach((clientState, seatNumber) => {\n      if (clientState.checkInfo.status === \"NotChecked\") {\n        notCheckedSeats.push(seatNumber);\n      }\n    });\n\n    console.log(`RestoreCardCheckService: Found ${notCheckedSeats.length} not checked clients:`, notCheckedSeats);\n    return notCheckedSeats;\n  }\n\n  public updateClientInfo(seatNumber: number, info: Partial<RestoreCardClientInfo>): void {\n    const currentState = this.serviceState.getValue();\n    const newClientStates = new Map(currentState.clientStates);\n\n    const existing = newClientStates.get(seatNumber);\n    if (existing) {\n      const updatedState = RestoreCardDataAdapter.mergeClientState(existing, {\n        clientInfo: { ...existing.clientInfo, ...info },\n        lastUpdated: Date.now(),\n      });\n      newClientStates.set(seatNumber, updatedState);\n    } else {\n      // 创建新的客户端状态\n      const clientInfo: RestoreCardClientInfo = {\n        seat_number: seatNumber,\n        ip: info.ip || \"\",\n        host: info.host || info.ip || \"\",\n        host_name: info.host_name || \"\",\n        is_online: info.is_online || false,\n        ...info,\n      };\n      newClientStates.set(seatNumber, RestoreCardDataAdapter.createDefaultClientState(clientInfo));\n\n      // 新增客户端，重新计算总数\n      this.updateCountsFullRecalculation();\n    }\n\n    this.updateServiceState({\n      clientStates: newClientStates,\n      lastRefresh: Date.now(),\n    });\n  }\n\n  public updateClientCheckStatus(seatNumber: number, checkInfo: Partial<RestoreCardCheckInfo>): void {\n    const currentState = this.serviceState.getValue();\n    const newClientStates = new Map(currentState.clientStates);\n\n    const existing = newClientStates.get(seatNumber);\n    if (existing) {\n      const oldStatus = existing.checkInfo.status;\n      const newStatus = checkInfo.status || oldStatus;\n\n      const updatedState = RestoreCardDataAdapter.mergeClientState(existing, {\n        checkInfo: { ...existing.checkInfo, ...checkInfo },\n        lastUpdated: Date.now(),\n      });\n      newClientStates.set(seatNumber, updatedState);\n\n      this.updateServiceState({\n        clientStates: newClientStates,\n        lastRefresh: Date.now(),\n      });\n\n      // 重新计算计数\n      if (oldStatus !== newStatus) {\n        this.updateCountsFullRecalculation();\n      }\n    }\n  }\n\n  private updateServiceState(update: Partial<RestoreCardServiceState>): void {\n    const currentState = this.serviceState.getValue();\n    this.serviceState.next({\n      ...currentState,\n      ...update,\n    });\n  }\n\n  private transformClientStateToDisplay(clientState: ClientState): RestoreCardClientDisplay {\n    return {\n      seat_number: clientState.clientInfo.seat_number,\n      ip: clientState.clientInfo.ip,\n      host: clientState.clientInfo.host,\n      host_name: clientState.clientInfo.host_name,\n      is_online: clientState.clientInfo.is_online,\n      version: clientState.clientInfo.version,\n      status: clientState.clientInfo.status,\n      permit_id: clientState.clientInfo.permit_id,\n      entry_id: clientState.clientInfo.entry_id,\n      mac_addr: clientState.clientInfo.mac_addr,\n      check: {\n        status: this.mapStatusToDisplay(clientState.checkInfo.status),\n        statusText: clientState.checkInfo.statusText,\n      },\n      // 各检查阶段的具体时间戳\n      startTime: clientState.checkInfo.startTime,\n      writeTime: clientState.checkInfo.writeTime,\n      restartTime: clientState.checkInfo.restartTime,\n      verifyTime: clientState.checkInfo.verifyTime,\n      // 服务器端时间信息\n      timestamp: clientState.checkInfo.timestamp,\n      timeoutAt: clientState.checkInfo.timeoutAt,\n    };\n  }\n\n  // 批量状态转换方法\n  private transformAllClientStates(state: RestoreCardServiceState): RestoreCardClientDisplay[] {\n    return Array.from(state.clientStates.values()).map((clientState) =>\n      this.transformClientStateToDisplay(clientState)\n    );\n  }\n\n  /**\n   * 更新客户端连接状态\n   */\n  public updateClientConnectionStatus(\n    seatNumber: number,\n    isOnline: boolean,\n    additionalInfo?: { host?: string; version?: string }\n  ): void {\n    const currentState = this.serviceState.getValue();\n    const newClientStates = new Map(currentState.clientStates);\n\n    const existing = newClientStates.get(seatNumber);\n    if (existing) {\n      // 更新现有客户端的连接状态\n      const updatedClientInfo = {\n        ...existing.clientInfo,\n        is_online: isOnline,\n        ...(additionalInfo?.host && { host: additionalInfo.host }),\n        ...(additionalInfo?.version && { version: additionalInfo.version }),\n      };\n\n      const updatedState = RestoreCardDataAdapter.mergeClientState(existing, {\n        clientInfo: updatedClientInfo,\n        lastUpdated: Date.now(),\n      });\n\n      newClientStates.set(seatNumber, updatedState);\n\n      this.updateServiceState({\n        clientStates: newClientStates,\n        lastRefresh: Date.now(),\n      });\n    } else {\n      console.warn(`RestoreCardCheck: Client ${seatNumber} not found in current state`);\n    }\n  }\n\n  /**\n   * 检查客户端是否可以执行操作\n   */\n  public canPerformCheck(seatNumber: number): boolean {\n    const clientState = this.serviceState.getValue().clientStates.get(seatNumber);\n    return clientState?.clientInfo?.is_online ?? false;\n  }\n}\n"], "mappings": "AACA,SAASA,eAAe,QAAoB,MAAM;AAClD,SAASC,GAAG,EAAEC,WAAW,EAAEC,oBAAoB,QAAQ,gBAAgB;AAIvE,OAAO,KAAKC,CAAC,MAAM,cAAc;AAYjC,SAASC,sBAAsB,QAAQ,6BAA6B;;;;AAGpE,OAAM,MAAOC,uBAAuB;EAoDlCC,YAAoBC,cAA8B,EAAUC,aAA4B;IAApE,KAAAD,cAAc,GAAdA,cAAc;IAA0B,KAAAC,aAAa,GAAbA,aAAa;IAnDjE,KAAAC,YAAY,GAAG,IAAIV,eAAe,CAA0B;MAClEW,SAAS,EAAE,EAAE;MACbC,YAAY,EAAE,IAAIC,GAAG,EAAuB;MAC5CC,MAAM,EAAE;QACNC,OAAO,EAAE,CAAC;QACVC,MAAM,EAAE,CAAC;QACTC,KAAK,EAAE;OACR;MACDC,SAAS,EAAE,KAAK;MAChBC,WAAW,EAAE;KACd,CAAC;IAEM,KAAAC,gBAAgB,GAAG,IAAIpB,eAAe,CAAU,KAAK,CAAC;IACtD,KAAAqB,sBAAsB,GAAG,IAAIrB,eAAe,CAAyB;MAAEsB,OAAO,EAAE;IAAK,CAAE,CAAC;IACxF,KAAAC,yBAAyB,GAAG,IAAIV,GAAG,EAAoC;IAI9D,KAAAW,kBAAkB,GAAG,IAAI,CAAC,CAAC;IAC3B,KAAAC,iBAAiB,GAAG,IAAI,CAAC,CAAC;IAE3C;IACQ,KAAAC,kBAAkB,GAAG,IAAIb,GAAG,EAAe,CAAC,CAAC;IAC7C,KAAAc,oBAAoB,GAAG,IAAId,GAAG,EAAkB,CAAC,CAAC;IACzC,KAAAe,cAAc,GAAG,KAAK,CAAC,CAAC;IACxB,KAAAC,kBAAkB,GAAG,EAAE,CAAC,CAAC;IACzB,KAAAC,kBAAkB,GAAG,MAAM,CAAC,CAAC;IACtC,KAAAC,YAAY,GAAG,CAAC;IAEjB,KAAAC,aAAa,GAAG,IAAI,CAACtB,YAAY,CAACuB,YAAY,EAAE;IAChD,KAAAC,iBAAiB,GAAG,IAAI,CAACd,gBAAgB,CAACa,YAAY,EAAE;IACxD,KAAAE,uBAAuB,GAAG,IAAI,CAACd,sBAAsB,CAACY,YAAY,EAAE;IAEpE,KAAAG,WAAW,GAA2C,IAAI,CAACJ,aAAa,CAACK,IAAI,CAClFpC,GAAG,CAAEqC,KAAK,IAAK,IAAI,CAACC,wBAAwB,CAACD,KAAK,CAAC,CAAC,EACpDnC,oBAAoB,EAAE,EACtBD,WAAW,CAAC,CAAC,CAAC,CACf;IAEM,KAAAsC,QAAQ,GAAG,IAAI,CAACR,aAAa,CAACK,IAAI,CACvCpC,GAAG,CAAEqC,KAAK,IAAKA,KAAK,CAACpB,SAAS,CAAC,EAC/Bf,oBAAoB,EAAE,EACtBD,WAAW,CAAC,CAAC,CAAC,CACf;IAEM,KAAAuC,OAAO,GAAG,IAAI,CAACT,aAAa,CAACK,IAAI,CACtCpC,GAAG,CAAEqC,KAAK,IAAKA,KAAK,CAACxB,MAAM,CAAC,EAC5BX,oBAAoB,EAAE,EACtBD,WAAW,CAAC,CAAC,CAAC,CACf;IAGC,IAAI,CAACwC,aAAa,EAAE;EACtB;EAEAC,WAAWA,CAAA;IACTC,OAAO,CAACC,GAAG,CAAC,oEAAoE,CAAC;IAEjF,IAAI,CAACC,YAAY,EAAE;IACnB,IAAI,CAACC,qBAAqB,EAAE;IAC5B,IAAI,CAACrC,YAAY,CAACsC,QAAQ,EAAE;IAC5B,IAAI,CAAC5B,gBAAgB,CAAC4B,QAAQ,EAAE;IAChC,IAAI,CAAC3B,sBAAsB,CAAC2B,QAAQ,EAAE;IACtC,IAAI,CAACzB,yBAAyB,CAAC0B,OAAO,CAAEC,OAAO,IAAKA,OAAO,CAACF,QAAQ,EAAE,CAAC;IACvE,IAAI,CAACzB,yBAAyB,CAAC4B,KAAK,EAAE;EACxC;EAEQC,0BAA0BA,CAACC,QAAa;IAC9C,OAAOA,QAAQ,IAAIA,QAAQ,CAACC,IAAI,IAAIC,KAAK,CAACC,OAAO,CAACH,QAAQ,CAACC,IAAI,CAACG,OAAO,CAAC;EAC1E;EAEQC,gBAAgBA,CAACL,QAAa;IACpC,OAAOA,QAAQ,IAAIA,QAAQ,CAACC,IAAI,IAAIC,KAAK,CAACC,OAAO,CAACH,QAAQ,CAACC,IAAI,CAACK,OAAO,CAAC;EAC1E;EAEA;EACAC,oBAAoBA,CAAA;IAClB,IAAI,CAACxC,gBAAgB,CAACyC,IAAI,CAAC,IAAI,CAAC;EAClC;EAEAC,qBAAqBA,CAAA;IACnB,IAAI,CAAC1C,gBAAgB,CAACyC,IAAI,CAAC,KAAK,CAAC;EACnC;EAEA;EACAE,YAAYA,CAACpD,SAAiB;IAC5B,IAAI,CAACqD,kBAAkB,CAAC;MAAErD;IAAS,CAAE,CAAC;IACtC;IACA,IAAI,CAAC+B,aAAa,EAAE;EACtB;EAEAuB,gBAAgBA,CAACrD,YAA2B;IAC1C,IAAI;MACF;MACA,MAAMsD,kBAAkB,GAAG,IAAIrD,GAAG,EAAuB;MAEzDD,YAAY,CAACqC,OAAO,CAAEkB,WAAW,IAAI;QACnC,IAAI9D,sBAAsB,CAAC+D,mBAAmB,CAACD,WAAW,CAAC,EAAE;UAC3DD,kBAAkB,CAACG,GAAG,CAACF,WAAW,CAACG,UAAU,CAACC,WAAW,EAAEJ,WAAW,CAAC;;MAE3E,CAAC,CAAC;MAEF;MAEA,IAAI,CAACH,kBAAkB,CAAC;QACtBpD,YAAY,EAAEsD,kBAAkB;QAChC/C,WAAW,EAAEqD,IAAI,CAACC,GAAG;OACtB,CAAC;MAEF;MACA,IAAI,CAACC,6BAA6B,EAAE;MACpC,IAAI,CAACC,aAAa,EAAE;KACrB,CAAC,OAAOC,KAAK,EAAE;MACdhC,OAAO,CAACgC,KAAK,CAAC,sDAAsD,EAAEA,KAAK,CAAC;;EAEhF;EAEA;EACQF,6BAA6BA,CAAA;IACnC,MAAMG,YAAY,GAAG,IAAI,CAACnE,YAAY,CAACoE,QAAQ,EAAE;IACjD,MAAMlE,YAAY,GAAG2C,KAAK,CAACwB,IAAI,CAACF,YAAY,CAACjE,YAAY,CAACoE,MAAM,EAAE,CAAC;IAEnE,MAAMjE,OAAO,GAAGH,YAAY,CAACqE,MAAM,CAAE3C,KAAK,IAAKA,KAAK,CAAC4C,SAAS,CAACC,MAAM,KAAK,SAAS,CAAC,CAACC,MAAM;IAC3F,MAAMpE,MAAM,GAAGJ,YAAY,CAACqE,MAAM,CAAE3C,KAAK,IAAKA,KAAK,CAAC4C,SAAS,CAACC,MAAM,KAAK,QAAQ,CAAC,CAACC,MAAM;IACzF,MAAMC,OAAO,GAAGzE,YAAY,CAACqE,MAAM,CAAE3C,KAAK,IAAKA,KAAK,CAAC4C,SAAS,CAACC,MAAM,KAAK,SAAS,CAAC,CAACC,MAAM;IAC3F,MAAMnE,KAAK,GAAGL,YAAY,CAACwE,MAAM;IAEjC,IAAI,CAACpB,kBAAkB,CAAC;MACtBlD,MAAM,EAAE;QACNC,OAAO;QACPC,MAAM,EAAEA,MAAM,GAAGqE,OAAO;QACxBpE;;KAEH,CAAC;IAEF2B,OAAO,CAACC,GAAG,CACT,gEAAgE9B,OAAO,aACrEC,MAAM,GAAGqE,OACX,YAAYpE,KAAK,EAAE,CACpB;EACH;EAEA;EACOqE,4BAA4BA,CACjCC,OAIE;IAEF,IAAIA,OAAO,CAACH,MAAM,KAAK,CAAC,EAAE;IAE1B,MAAMP,YAAY,GAAG,IAAI,CAACnE,YAAY,CAACoE,QAAQ,EAAE;IACjD,MAAMU,eAAe,GAAG,IAAI3E,GAAG,CAACgE,YAAY,CAACjE,YAAY,CAAC;IAC1D,IAAI6E,UAAU,GAAG,KAAK;IAEtB;IACAF,OAAO,CAACtC,OAAO,CAAC,CAAC;MAAEyC,UAAU;MAAEP,MAAM;MAAEQ;IAAU,CAAE,KAAI;MACrD,MAAMC,QAAQ,GAAGJ,eAAe,CAACK,GAAG,CAACH,UAAU,CAAC;MAChD,IAAIE,QAAQ,EAAE;QACZ,MAAME,YAAY,GAAGzF,sBAAsB,CAAC0F,gBAAgB,CAACH,QAAQ,EAAE;UACrEV,SAAS,EAAE;YACT,GAAGU,QAAQ,CAACV,SAAS;YACrBC,MAAM;YACNQ,UAAU,EAAEA,UAAU,IAAI,IAAI,CAACK,aAAa,CAACb,MAAM;WACpD;UACDc,WAAW,EAAEzB,IAAI,CAACC,GAAG;SACtB,CAAC;QACFe,eAAe,CAACnB,GAAG,CAACqB,UAAU,EAAEI,YAAY,CAAC;QAC7CL,UAAU,GAAG,IAAI;;IAErB,CAAC,CAAC;IAEF,IAAIA,UAAU,EAAE;MACd;MACA,IAAI,CAACzB,kBAAkB,CAAC;QACtBpD,YAAY,EAAE4E,eAAe;QAC7BrE,WAAW,EAAEqD,IAAI,CAACC,GAAG;OACtB,CAAC;MAEF;MACA,IAAI,CAACC,6BAA6B,EAAE;MACpC,IAAI,CAACC,aAAa,EAAE;MAEpB/B,OAAO,CAACC,GAAG,CAAC,0CAA0C0C,OAAO,CAACH,MAAM,kBAAkB,CAAC;;EAE3F;EAEAc,WAAWA,CAAA;IACTtD,OAAO,CAACC,GAAG,CAAC,6CAA6C,CAAC;IAC1D,MAAMsD,gBAAgB,GAAG,IAAI,CAACzF,YAAY,CAACoE,QAAQ,EAAE,CAACnE,SAAS;IAE/D,IAAI,CAACqD,kBAAkB,CAAC;MAAE9C,SAAS,EAAE;IAAI,CAAE,CAAC;IAC5C,IAAI,CAACV,cAAc,CAAC4F,gBAAgB,CAAC,QAAQ,EAAED,gBAAgB,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC,CAACE,SAAS,CAAEC,GAAG,IAAI;MAChH1D,OAAO,CAACC,GAAG,CAAC,iDAAiD,EAAEyD,GAAG,CAAC;MACnE,IAAI,CAACtC,kBAAkB,CAAC;QAAE9C,SAAS,EAAE;MAAK,CAAE,CAAC;IAC/C,CAAC,CAAC;EACJ;EAEAqF,sBAAsBA,CAAA;IACpB3D,OAAO,CAACC,GAAG,CAAC,kFAAkF,CAAC;IAC/F,MAAMsD,gBAAgB,GAAG,IAAI,CAACzF,YAAY,CAACoE,QAAQ,EAAE,CAACnE,SAAS;IAE/D,IAAI,CAACqD,kBAAkB,CAAC;MAAE9C,SAAS,EAAE;IAAI,CAAE,CAAC;IAC5C0B,OAAO,CAACC,GAAG,CAAC,kEAAkE,CAAC;IAE/E;IACA,IAAI,CAACrC,cAAc,CAAC4F,gBAAgB,CAAC,QAAQ,EAAED,gBAAgB,EAAE,QAAQ,CAAC,CAACE,SAAS,CACjFG,UAAU,IAAI;MACb5D,OAAO,CAACC,GAAG,CAAC,2CAA2C,EAAE2D,UAAU,CAAC;MAEpE,IAAI,IAAI,CAAC9C,gBAAgB,CAAC8C,UAAU,CAAC,EAAE;QACrC,IAAI,CAACC,8BAA8B,CAACD,UAAU,CAAClD,IAAI,CAACK,OAAO,CAAC;;MAG9D;MACA,MAAMkB,YAAY,GAAG,IAAI,CAACnE,YAAY,CAACoE,QAAQ,EAAE;MACjD,MAAM4B,eAAe,GAAG,IAAI,CAACC,wBAAwB,EAAE;MAEvD;MACA,IAAI9B,YAAY,CAACjE,YAAY,CAACgG,IAAI,KAAK,CAAC,EAAE;QACxChE,OAAO,CAACC,GAAG,CAAC,kEAAkE,CAAC;OAChF,MAAM,IAAI6D,eAAe,CAACtB,MAAM,KAAK,CAAC,EAAE;QACvCxC,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;QAC5D,IAAI,CAACmB,kBAAkB,CAAC;UAAE9C,SAAS,EAAE;QAAK,CAAE,CAAC;QAC7C;OACD,MAAM;QACL0B,OAAO,CAACC,GAAG,CAAC,wCAAwC6D,eAAe,CAACtB,MAAM,sBAAsB,CAAC;;MAEnG,IAAI,CAAC5E,cAAc,CAChB4F,gBAAgB,CAAC,QAAQ,EAAED,gBAAgB,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC,CAC3EE,SAAS,CAAEQ,QAAQ,IAAI;QACtBjE,OAAO,CAACC,GAAG,CAAC,iDAAiD,EAAEgE,QAAQ,CAAC;QACxE,IAAI,CAAC7C,kBAAkB,CAAC;UAAE9C,SAAS,EAAE;QAAK,CAAE,CAAC;MAC/C,CAAC,CAAC;IACN,CAAC,EACA0D,KAAK,IAAI;MACRhC,OAAO,CAACgC,KAAK,CAAC,oDAAoD,EAAEA,KAAK,CAAC;MAC1E,IAAI,CAACZ,kBAAkB,CAAC;QAAE9C,SAAS,EAAE;MAAK,CAAE,CAAC;IAC/C,CAAC,CACF;EACH;EAEA4F,iBAAiBA,CAAA;IACflE,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;IACpD,MAAMsD,gBAAgB,GAAG,IAAI,CAACzF,YAAY,CAACoE,QAAQ,EAAE,CAACnE,SAAS;IAE/D,IAAI,CAACqD,kBAAkB,CAAC;MAAE9C,SAAS,EAAE;IAAI,CAAE,CAAC;IAC5C,IAAI,CAACV,cAAc,CAAC4F,gBAAgB,CAAC,QAAQ,EAAED,gBAAgB,EAAE,QAAQ,CAAC,CAACE,SAAS,CAAEC,GAAG,IAAI;MAC3F1D,OAAO,CAACC,GAAG,CAAC,2CAA2C,EAAEyD,GAAG,CAAC;MAC7D,IAAI,CAACtC,kBAAkB,CAAC;QAAE9C,SAAS,EAAE;MAAK,CAAE,CAAC;MAE7C,IAAI,IAAI,CAACwC,gBAAgB,CAAC4C,GAAG,CAAC,EAAE;QAC9B,IAAI,CAACG,8BAA8B,CAACH,GAAG,CAAChD,IAAI,CAACK,OAAO,CAAC;;IAEzD,CAAC,CAAC;EACJ;EAEA;EACAoD,gBAAgBA,CAACC,OAA4B;IAC3CpE,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAEmE,OAAO,CAAC;IAC5D,MAAMb,gBAAgB,GAAG,IAAI,CAACzF,YAAY,CAACoE,QAAQ,EAAE,CAACnE,SAAS;IAE/D,IAAI,CAACqD,kBAAkB,CAAC;MAAE9C,SAAS,EAAE;IAAI,CAAE,CAAC;IAC5C,OAAO,IAAI,CAACV,cAAc,CAAC4F,gBAAgB,CAAC,QAAQ,EAAED,gBAAgB,EAAEa,OAAO,CAAC;EAClF;EAEA;EACAC,iBAAiBA,CAAA;IACfrE,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;IACpD,MAAMsD,gBAAgB,GAAG,IAAI,CAACzF,YAAY,CAACoE,QAAQ,EAAE,CAACnE,SAAS;IAE/D,IAAI,CAACqD,kBAAkB,CAAC;MAAE9C,SAAS,EAAE;IAAI,CAAE,CAAC;IAC5C,IAAI,CAACV,cAAc,CAAC4F,gBAAgB,CAAC,QAAQ,EAAED,gBAAgB,EAAE,QAAQ,CAAC,CAACE,SAAS,CAAEC,GAAG,IAAI;MAC3F1D,OAAO,CAACC,GAAG,CAAC,2CAA2C,EAAEyD,GAAG,CAAC;MAC7D,IAAI,CAACtC,kBAAkB,CAAC;QAAE9C,SAAS,EAAE;MAAK,CAAE,CAAC;MAC7C,IAAIoF,GAAG,CAACnB,MAAM,KAAK,SAAS,EAAE;QAC5BvC,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;OACvD,MAAM;QACLD,OAAO,CAACgC,KAAK,CAAC,yCAAyC,EAAE0B,GAAG,CAAC;;IAEjE,CAAC,CAAC;EACJ;EAEAY,kBAAkBA,CAAA;IAChBtE,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;IACrD,MAAMsD,gBAAgB,GAAG,IAAI,CAACzF,YAAY,CAACoE,QAAQ,EAAE,CAACnE,SAAS;IAE/D,IAAI,CAACH,cAAc,CAAC4F,gBAAgB,CAAC,SAAS,EAAED,gBAAgB,EAAE,QAAQ,CAAC,CAACE,SAAS,CAAEC,GAAG,IAAI;MAC5F1D,OAAO,CAACC,GAAG,CAAC,4CAA4C,EAAEyD,GAAG,CAAC;MAC9D;MACA,IAAIA,GAAG,CAACnB,MAAM,KAAK,QAAQ,EAAE;QAC3BvC,OAAO,CAACuE,IAAI,CAAC,gDAAgD,CAAC;;IAElE,CAAC,CAAC;EACJ;EAEA;EACAC,yBAAyBA,CAAA;IACvBxE,OAAO,CAACC,GAAG,CAAC,iDAAiD,CAAC;IAC9D,MAAMsD,gBAAgB,GAAG,IAAI,CAACzF,YAAY,CAACoE,QAAQ,EAAE,CAACnE,SAAS;IAE/D,IAAI,CAACqD,kBAAkB,CAAC;MAAE9C,SAAS,EAAE;IAAI,CAAE,CAAC;IAC5C,IAAI,CAACV,cAAc,CAAC4F,gBAAgB,CAAC,QAAQ,EAAED,gBAAgB,EAAE,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC,CAACE,SAAS,CAAEC,GAAG,IAAI;MACtG1D,OAAO,CAACC,GAAG,CAAC,qDAAqD,EAAEyD,GAAG,CAAC;MACvE,IAAI,CAACtC,kBAAkB,CAAC;QAAE9C,SAAS,EAAE;MAAK,CAAE,CAAC;MAE7C,IAAIoF,GAAG,CAACnB,MAAM,KAAK,SAAS,EAAE;QAC5B,IAAI,IAAI,CAAC/B,0BAA0B,CAACkD,GAAG,CAAC,EAAE;UACxC;UACA,MAAM7C,OAAO,GAAG6C,GAAG,CAAChD,IAAI,CAACG,OAAO;UAChC,MAAM4D,WAAW,GAAG5D,OAAO,CAAC6D,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACC,MAAM,KAAK,OAAO,CAAC;UAC7D,MAAMC,YAAY,GAAGhE,OAAO,CAAC6D,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACC,MAAM,KAAK,QAAQ,CAAC;UAE/D,IAAIH,WAAW,IAAIA,WAAW,CAAClC,MAAM,KAAK,SAAS,EAAE;YACnDvC,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;;UAGvD,IAAI4E,YAAY,IAAIA,YAAY,CAACtC,MAAM,KAAK,SAAS,EAAE;YACrDvC,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;;SAEzD,MAAM;UACLD,OAAO,CAACC,GAAG,CAAC,oDAAoD,CAAC;;OAEpE,MAAM;QACL;QACAD,OAAO,CAACgC,KAAK,CAAC,mDAAmD,EAAE0B,GAAG,CAAC;;IAE3E,CAAC,CAAC;EACJ;EAEA;EACAoB,iBAAiBA,CAAChC,UAAkB,EAAEsB,OAA2B;IAC/D;IACA,IAAI,CAAC,IAAI,CAACW,eAAe,CAACjC,UAAU,CAAC,EAAE;MACrC9C,OAAO,CAACuE,IAAI,CAAC,wDAAwDzB,UAAU,EAAE,CAAC;MAClF;;IAGF9C,OAAO,CAACC,GAAG,CAAC,yCAAyC6C,UAAU,EAAE,EAAEsB,OAAO,CAAC;IAC3E,MAAMb,gBAAgB,GAAG,IAAI,CAACzF,YAAY,CAACoE,QAAQ,EAAE,CAACnE,SAAS;IAE/D,IAAI,CAACiH,sBAAsB,CAAClC,UAAU,EAAE,IAAI,CAAC;IAC7C,IAAI,CAAClF,cAAc,CAAC4F,gBAAgB,CAAC,QAAQ,EAAED,gBAAgB,EAAEa,OAAO,EAAE,CAACtB,UAAU,CAAC,CAAC,CAACW,SAAS,CAC9FC,GAAG,IAAI;MACN1D,OAAO,CAACC,GAAG,CAAC,mCAAmC6C,UAAU,YAAY,EAAEY,GAAG,CAAC;MAC3EuB,UAAU,CAAC,MAAK;QACd,IAAI,CAACD,sBAAsB,CAAClC,UAAU,EAAE,KAAK,CAAC;MAChD,CAAC,EAAE,IAAI,CAAC;IACV,CAAC,EACAd,KAAK,IAAI;MACRhC,OAAO,CAACgC,KAAK,CAAC,mCAAmCc,UAAU,UAAU,EAAEd,KAAK,CAAC;MAC7EiD,UAAU,CAAC,MAAK;QACd,IAAI,CAACD,sBAAsB,CAAClC,UAAU,EAAE,KAAK,CAAC;MAChD,CAAC,EAAE,IAAI,CAAC;IACV,CAAC,CACF;EACH;EAEA;EACAoC,qBAAqBA,CAACpC,UAAkB;IACtC,IAAI,CAAC,IAAI,CAACnE,yBAAyB,CAACwG,GAAG,CAACrC,UAAU,CAAC,EAAE;MACnD,IAAI,CAACnE,yBAAyB,CAAC8C,GAAG,CAACqB,UAAU,EAAE,IAAI1F,eAAe,CAAU,KAAK,CAAC,CAAC;;IAErF,OAAO,IAAI,CAACuB,yBAAyB,CAACsE,GAAG,CAACH,UAAU,CAAE,CAACzD,YAAY,EAAE;EACvE;EAEA;EACQ2F,sBAAsBA,CAAClC,UAAkB,EAAEsC,OAAgB;IACjE,IAAI,CAAC,IAAI,CAACzG,yBAAyB,CAACwG,GAAG,CAACrC,UAAU,CAAC,EAAE;MACnD,IAAI,CAACnE,yBAAyB,CAAC8C,GAAG,CAACqB,UAAU,EAAE,IAAI1F,eAAe,CAAU,KAAK,CAAC,CAAC;;IAErF,IAAI,CAACuB,yBAAyB,CAACsE,GAAG,CAACH,UAAU,CAAE,CAAC7B,IAAI,CAACmE,OAAO,CAAC;EAC/D;EAEOC,kBAAkBA,CAACvC,UAAkB,EAAEP,MAA8B;IAC1E,IAAI,CAAC+C,uBAAuB,CAACxC,UAAU,EAAE;MACvCP,MAAM;MACNQ,UAAU,EAAE,IAAI,CAACK,aAAa,CAACb,MAAM;KACtC,CAAC;IAEF;IACA,IAAI,CAACR,aAAa,EAAE;EACtB;EAEOwD,eAAeA,CAACzC,UAAkB;IACvC,MAAMvB,WAAW,GAAG,IAAI,CAACiE,cAAc,CAAC1C,UAAU,CAAC;IACnD,OAAOvB,WAAW,EAAEe,SAAS,CAACC,MAAM,IAAI,YAAY;EACtD;EAEOkD,gBAAgBA,CAAA;IACrB,MAAMxD,YAAY,GAAG,IAAI,CAACnE,YAAY,CAACoE,QAAQ,EAAE;IACjD,MAAMU,eAAe,GAAG,IAAI3E,GAAG,EAAuB;IAEtDgE,YAAY,CAACjE,YAAY,CAACqC,OAAO,CAAC,CAACX,KAAK,EAAEoD,UAAU,KAAI;MACtD,MAAM4C,UAAU,GAAGjI,sBAAsB,CAAC0F,gBAAgB,CAACzD,KAAK,EAAE;QAChE4C,SAAS,EAAE;UACTC,MAAM,EAAE,YAAsC;UAC9CQ,UAAU,EAAE;SACb;QACDM,WAAW,EAAEzB,IAAI,CAACC,GAAG;OACtB,CAAC;MACFe,eAAe,CAACnB,GAAG,CAACqB,UAAU,EAAE4C,UAAU,CAAC;IAC7C,CAAC,CAAC;IAEF,IAAI,CAACtE,kBAAkB,CAAC;MACtBpD,YAAY,EAAE4E,eAAe;MAC7BrE,WAAW,EAAEqD,IAAI,CAACC,GAAG;KACtB,CAAC;IACF,IAAI,CAACC,6BAA6B,EAAE;IAEpC;IACA,IAAI,CAAC3C,YAAY,GAAG,CAAC;EACvB;EAEA;EACOwG,uBAAuBA,CAACC,IAAiC;IAC9D5F,OAAO,CAACC,GAAG,CAAC,iDAAiD,EAAE2F,IAAI,CAAC;IAEpE,IAAIA,IAAI,CAACC,UAAU,KAAK,IAAI,CAAC/H,YAAY,CAACoE,QAAQ,EAAE,CAACnE,SAAS,EAAE;MAC9D;MACA,IAAI,CAAC8F,8BAA8B,CAAC+B,IAAI,CAAC7E,OAAO,CAAC;;EAErD;EAEQ8C,8BAA8BA,CAAC9C,OAA4B;IACjE,IAAI;MACF,MAAMkB,YAAY,GAAG,IAAI,CAACnE,YAAY,CAACoE,QAAQ,EAAE;MACjD,MAAMU,eAAe,GAAG,IAAI3E,GAAG,CAACgE,YAAY,CAACjE,YAAY,CAAC;MAE1D,MAAM8H,aAAa,GAAG,IAAI7H,GAAG,EAA6B;MAC1D8C,OAAO,CAACV,OAAO,CAAC,CAAC0F,MAAM,EAAEC,KAAK,KAAI;QAChC,IAAIvI,sBAAsB,CAACwI,kBAAkB,CAACF,MAAM,CAAC,EAAE;UACrD,MAAMjD,UAAU,GAAGiD,MAAM,CAACjD,UAAU;UACpCgD,aAAa,CAACrE,GAAG,CAACqB,UAAU,EAAEiD,MAAM,CAAC;SACtC,MAAM;UACL/F,OAAO,CAACgC,KAAK,CAAC,yDAAyDgE,KAAK,GAAG,EAAE;YAC/ED,MAAM;YACNG,cAAc,EAAE,CAAC,YAAY,EAAE,QAAQ,EAAE,IAAI;WAC9C,CAAC;;MAEN,CAAC,CAAC;MAEF;MACAJ,aAAa,CAACzF,OAAO,CAAC,CAAC8F,UAAU,EAAErD,UAAU,KAAI;QAC/C,MAAMsD,aAAa,GAAGxD,eAAe,CAACK,GAAG,CAACH,UAAU,CAAC;QACrD,IAAIsD,aAAa,EAAE;UACjB,IAAI;YACF,MAAMC,YAAY,GAAG5I,sBAAsB,CAAC6I,gBAAgB,CAACH,UAAU,CAAC;YACxE,MAAMjD,YAAY,GAAGzF,sBAAsB,CAAC0F,gBAAgB,CAACiD,aAAa,EAAEC,YAAY,CAAC;YACzFzD,eAAe,CAACnB,GAAG,CAACqB,UAAU,EAAEI,YAAY,CAAC;WAC9C,CAAC,OAAOlB,KAAK,EAAE;YACdhC,OAAO,CAACgC,KAAK,CAAC,kDAAkDc,UAAU,GAAG,EAAEd,KAAK,CAAC;;;MAG3F,CAAC,CAAC;MAEF,IAAI,CAACZ,kBAAkB,CAAC;QACtBpD,YAAY,EAAE4E,eAAe;QAC7BrE,WAAW,EAAEqD,IAAI,CAACC,GAAG;OACtB,CAAC;MAEF,IAAI,CAACC,6BAA6B,EAAE;KACrC,CAAC,OAAOE,KAAK,EAAE;MACdhC,OAAO,CAACgC,KAAK,CAAC,uEAAuE,EAAEA,KAAK,CAAC;;EAEjG;EAEQuE,kBAAkBA,CAAChE,MAA8B;IACvD,MAAMiE,SAAS,GAAgD;MAC7DC,UAAU,EAAE,YAAY;MACxBC,OAAO,EAAE,SAAS;MAClBC,UAAU,EAAE,YAAY;MACxBC,SAAS,EAAE,WAAW;MACtBC,OAAO,EAAE,SAAS;MAClBC,MAAM,EAAE,QAAQ;MAChBC,OAAO,EAAE;KACV;IACD,OAAOP,SAAS,CAACjE,MAAM,CAAC;EAC1B;EAEQa,aAAaA,CAACb,MAA8B;IAClD,MAAMyE,OAAO,GAAgD;MAC3DP,UAAU,EAAE,KAAK;MACjBC,OAAO,EAAE,KAAK;MACdC,UAAU,EAAE,KAAK;MACjBC,SAAS,EAAE,KAAK;MAChBC,OAAO,EAAE,MAAM;MACfC,MAAM,EAAE,MAAM;MACdC,OAAO,EAAE;KACV;IACD,OAAOC,OAAO,CAACzE,MAAM,CAAC,IAAI,MAAM;EAClC;EAEA;EACO0E,sBAAsBA,CAACC,MAAA,GAAiB,QAAQ;IACrDlH,OAAO,CAACC,GAAG,CAAC,kEAAkEiH,MAAM,EAAE,CAAC;IACvF,IAAI,CAACzI,sBAAsB,CAACwC,IAAI,CAAC;MAAEvC,OAAO,EAAE,IAAI;MAAEwI;IAAM,CAAE,CAAC;EAC7D;EAEOC,2BAA2BA,CAAA;IAChC,IAAI,CAAC1I,sBAAsB,CAACwC,IAAI,CAAC;MAAEvC,OAAO,EAAE;IAAK,CAAE,CAAC;EACtD;EAEA;EACQoB,aAAaA,CAAA;IACnB,IAAI,CAACI,YAAY,EAAE,CAAC,CAAC;IACrB,IAAI,CAACkH,aAAa,GAAGC,WAAW,CAAC,MAAK;MACpC,MAAMpF,YAAY,GAAG,IAAI,CAACnE,YAAY,CAACoE,QAAQ,EAAE;MACjD;MACA,IAAID,YAAY,CAACjE,YAAY,CAACgG,IAAI,GAAG,CAAC,IAAI,CAAC/B,YAAY,CAAC3D,SAAS,EAAE;QACjE0B,OAAO,CAACC,GAAG,CAAC,yDAAyD,CAAC;QACtE,IAAI,CAACgH,sBAAsB,CAAC,oBAAoB,CAAC;;IAErD,CAAC,EAAE,IAAI,CAACrI,kBAAkB,CAAC;IAC3BoB,OAAO,CAACC,GAAG,CAAC,2DAA2D,EAAE,IAAI,CAACrB,kBAAkB,CAAC;EACnG;EAEQsB,YAAYA,CAAA;IAClB,IAAI,IAAI,CAACkH,aAAa,EAAE;MACtBE,aAAa,CAAC,IAAI,CAACF,aAAa,CAAC;MACjC,IAAI,CAACA,aAAa,GAAG,IAAI;MACzBpH,OAAO,CAACC,GAAG,CAAC,4CAA4C,CAAC;;EAE7D;EAEQsH,cAAcA,CAAA;IACpB,MAAM1F,GAAG,GAAGD,IAAI,CAACC,GAAG,EAAE;IACtB,IAAIA,GAAG,GAAG,IAAI,CAAC1C,YAAY,GAAG,IAAI,CAACN,iBAAiB,EAAE;MACpD,IAAI,CAACM,YAAY,GAAG0C,GAAG;MACvB7B,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;MAC5D,IAAI,CAACgH,sBAAsB,CAAC,oBAAoB,CAAC;;EAErD;EAEA;EACQO,iBAAiBA,CAAC1E,UAAkB;IAC1C;IACA,IAAI,CAAC2E,gBAAgB,CAAC3E,UAAU,CAAC;IAEjC;IACA,IAAI,CAAC/D,oBAAoB,CAAC0C,GAAG,CAACqB,UAAU,EAAE,CAAC,CAAC;IAE5C9C,OAAO,CAACC,GAAG,CAAC,8DAA8D6C,UAAU,EAAE,CAAC;IAEvF,MAAM4E,UAAU,GAAGL,WAAW,CAAC,MAAK;MAClC,IAAI,CAACM,mBAAmB,CAAC7E,UAAU,CAAC;IACtC,CAAC,EAAE,IAAI,CAAC9D,cAAc,CAAC;IAEvB,IAAI,CAACF,kBAAkB,CAAC2C,GAAG,CAACqB,UAAU,EAAE4E,UAAU,CAAC;EACrD;EAEQC,mBAAmBA,CAAC7E,UAAkB;IAC5C,MAAM8E,eAAe,GAAG,IAAI,CAAC7I,oBAAoB,CAACkE,GAAG,CAACH,UAAU,CAAC,IAAI,CAAC;IACtE,MAAM+E,WAAW,GAAGD,eAAe,GAAG,CAAC;IAEvC5H,OAAO,CAACC,GAAG,CAAC,qDAAqD4H,WAAW,eAAe/E,UAAU,EAAE,CAAC;IAExG;IACA,IAAI+E,WAAW,GAAG,IAAI,CAAC5I,kBAAkB,EAAE;MACzCe,OAAO,CAACC,GAAG,CAAC,kEAAkE6C,UAAU,kBAAkB,CAAC;MAC3G,IAAI,CAAC2E,gBAAgB,CAAC3E,UAAU,CAAC;MACjC;;IAGF;IACA,IAAI,CAAC/D,oBAAoB,CAAC0C,GAAG,CAACqB,UAAU,EAAE+E,WAAW,CAAC;IAEtD;IACA,IAAI,CAACC,sBAAsB,CAAChF,UAAU,CAAC;EACzC;EAEQgF,sBAAsBA,CAAChF,UAAkB;IAC/C9C,OAAO,CAACC,GAAG,CAAC,4DAA4D6C,UAAU,EAAE,CAAC;IAErF,IAAI,CAACjF,aAAa,CAACkK,QAAQ,CAACC,OAAO,CAACxK,CAAC,CAACyK,gBAAgB,EAAE;MAAEC,KAAK,EAAE,CAACpF,UAAU;IAAC,CAAE,EAAGY,GAAG,IAAI;MACvF,IAAIA,GAAG,EAAE;QACP1D,OAAO,CAACC,GAAG,CAAC,yDAAyD6C,UAAU,GAAG,EAAEY,GAAG,CAAC;OACzF,MAAM;QACL1D,OAAO,CAACgC,KAAK,CAAC,mEAAmEc,UAAU,EAAE,CAAC;;IAElG,CAAC,CAAC;EACJ;EAEQ2E,gBAAgBA,CAAC3E,UAAkB;IACzC,MAAMqF,KAAK,GAAG,IAAI,CAACrJ,kBAAkB,CAACmE,GAAG,CAACH,UAAU,CAAC;IACrD,IAAIqF,KAAK,EAAE;MACTb,aAAa,CAACa,KAAK,CAAC;MACpB,IAAI,CAACrJ,kBAAkB,CAACsJ,MAAM,CAACtF,UAAU,CAAC;MAC1C,IAAI,CAAC/D,oBAAoB,CAACqJ,MAAM,CAACtF,UAAU,CAAC;MAC5C9C,OAAO,CAACC,GAAG,CAAC,6DAA6D6C,UAAU,EAAE,CAAC;;EAE1F;EAEQ3C,qBAAqBA,CAAA;IAC3BH,OAAO,CAACC,GAAG,CAAC,uDAAuD,CAAC;IACpE,IAAI,CAACnB,kBAAkB,CAACuB,OAAO,CAAC,CAAC8H,KAAK,EAAErF,UAAU,KAAI;MACpDwE,aAAa,CAACa,KAAK,CAAC;MACpBnI,OAAO,CAACC,GAAG,CAAC,2DAA2D6C,UAAU,EAAE,CAAC;IACtF,CAAC,CAAC;IACF,IAAI,CAAChE,kBAAkB,CAACyB,KAAK,EAAE;IAC/B,IAAI,CAACxB,oBAAoB,CAACwB,KAAK,EAAE;EACnC;EAEQwB,aAAaA,CAAA;IACnB,MAAME,YAAY,GAAG,IAAI,CAACnE,YAAY,CAACoE,QAAQ,EAAE;IACjD,MAAMlE,YAAY,GAAG2C,KAAK,CAACwB,IAAI,CAACF,YAAY,CAACjE,YAAY,CAACoE,MAAM,EAAE,CAAC;IAEnE;IACA,MAAMiG,yBAAyB,GAC7BrK,YAAY,CAACwE,MAAM,GAAG,CAAC,IACvBxE,YAAY,CAACsK,KAAK,CAAE/G,WAAW,IAAI;MACjC,MAAMgB,MAAM,GAAGhB,WAAW,CAACe,SAAS,CAACC,MAAM;MAC3C,OAAOA,MAAM,KAAK,SAAS,IAAIA,MAAM,KAAK,QAAQ,IAAIA,MAAM,KAAK,SAAS;IAC5E,CAAC,CAAC;IAEJ,IAAI8F,yBAAyB,EAAE;MAC7BrI,OAAO,CAACC,GAAG,CAAC,8EAA8E,CAAC;MAC3F,IAAI,CAACgH,sBAAsB,CAAC,4BAA4B,CAAC;KAC1D,MAAM;MACL;MACA,MAAMsB,iBAAiB,GAAGvK,YAAY,CAACwK,IAAI,CAAE9I,KAAK,IAAKA,KAAK,CAAC4C,SAAS,CAACC,MAAM,KAAK,SAAS,CAAC;MAC5F,IAAIgG,iBAAiB,EAAE;QACrB,IAAI,CAAChB,cAAc,EAAE;;;EAG3B;EAEA;EACOkB,iBAAiBA,CAAC3F,UAAkB;IACzC9C,OAAO,CAACC,GAAG,CAAC,QAAQ6C,UAAU,MAAM,CAAC;IACrC,IAAI,CAACuC,kBAAkB,CAACvC,UAAU,EAAE,YAAY,CAAC;EACnD;EAEO4F,oBAAoBA,CAAA;IACzB1I,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;IACxB,IAAI,CAACwF,gBAAgB,EAAE;EACzB;EAEOD,cAAcA,CAAC1C,UAAkB;IACtC,OAAO,IAAI,CAAChF,YAAY,CAACoE,QAAQ,EAAE,CAAClE,YAAY,CAACiF,GAAG,CAACH,UAAU,CAAC;EAClE;EAEOiB,wBAAwBA,CAAA;IAC7B,MAAM9B,YAAY,GAAG,IAAI,CAACnE,YAAY,CAACoE,QAAQ,EAAE;IACjD,MAAM4B,eAAe,GAAa,EAAE;IAEpC7B,YAAY,CAACjE,YAAY,CAACqC,OAAO,CAAC,CAACkB,WAAW,EAAEuB,UAAU,KAAI;MAC5D,IAAIvB,WAAW,CAACe,SAAS,CAACC,MAAM,KAAK,YAAY,EAAE;QACjDuB,eAAe,CAAC6E,IAAI,CAAC7F,UAAU,CAAC;;IAEpC,CAAC,CAAC;IAEF9C,OAAO,CAACC,GAAG,CAAC,kCAAkC6D,eAAe,CAACtB,MAAM,uBAAuB,EAAEsB,eAAe,CAAC;IAC7G,OAAOA,eAAe;EACxB;EAEO8E,gBAAgBA,CAAC9F,UAAkB,EAAEpC,IAAoC;IAC9E,MAAMuB,YAAY,GAAG,IAAI,CAACnE,YAAY,CAACoE,QAAQ,EAAE;IACjD,MAAMU,eAAe,GAAG,IAAI3E,GAAG,CAACgE,YAAY,CAACjE,YAAY,CAAC;IAE1D,MAAMgF,QAAQ,GAAGJ,eAAe,CAACK,GAAG,CAACH,UAAU,CAAC;IAChD,IAAIE,QAAQ,EAAE;MACZ,MAAME,YAAY,GAAGzF,sBAAsB,CAAC0F,gBAAgB,CAACH,QAAQ,EAAE;QACrEtB,UAAU,EAAE;UAAE,GAAGsB,QAAQ,CAACtB,UAAU;UAAE,GAAGhB;QAAI,CAAE;QAC/C2C,WAAW,EAAEzB,IAAI,CAACC,GAAG;OACtB,CAAC;MACFe,eAAe,CAACnB,GAAG,CAACqB,UAAU,EAAEI,YAAY,CAAC;KAC9C,MAAM;MACL;MACA,MAAMxB,UAAU,GAA0B;QACxCC,WAAW,EAAEmB,UAAU;QACvB+F,EAAE,EAAEnI,IAAI,CAACmI,EAAE,IAAI,EAAE;QACjBC,IAAI,EAAEpI,IAAI,CAACoI,IAAI,IAAIpI,IAAI,CAACmI,EAAE,IAAI,EAAE;QAChCE,SAAS,EAAErI,IAAI,CAACqI,SAAS,IAAI,EAAE;QAC/BC,SAAS,EAAEtI,IAAI,CAACsI,SAAS,IAAI,KAAK;QAClC,GAAGtI;OACJ;MACDkC,eAAe,CAACnB,GAAG,CAACqB,UAAU,EAAErF,sBAAsB,CAACwL,wBAAwB,CAACvH,UAAU,CAAC,CAAC;MAE5F;MACA,IAAI,CAACI,6BAA6B,EAAE;;IAGtC,IAAI,CAACV,kBAAkB,CAAC;MACtBpD,YAAY,EAAE4E,eAAe;MAC7BrE,WAAW,EAAEqD,IAAI,CAACC,GAAG;KACtB,CAAC;EACJ;EAEOyD,uBAAuBA,CAACxC,UAAkB,EAAER,SAAwC;IACzF,MAAML,YAAY,GAAG,IAAI,CAACnE,YAAY,CAACoE,QAAQ,EAAE;IACjD,MAAMU,eAAe,GAAG,IAAI3E,GAAG,CAACgE,YAAY,CAACjE,YAAY,CAAC;IAE1D,MAAMgF,QAAQ,GAAGJ,eAAe,CAACK,GAAG,CAACH,UAAU,CAAC;IAChD,IAAIE,QAAQ,EAAE;MACZ,MAAMkG,SAAS,GAAGlG,QAAQ,CAACV,SAAS,CAACC,MAAM;MAC3C,MAAM4G,SAAS,GAAG7G,SAAS,CAACC,MAAM,IAAI2G,SAAS;MAE/C,MAAMhG,YAAY,GAAGzF,sBAAsB,CAAC0F,gBAAgB,CAACH,QAAQ,EAAE;QACrEV,SAAS,EAAE;UAAE,GAAGU,QAAQ,CAACV,SAAS;UAAE,GAAGA;QAAS,CAAE;QAClDe,WAAW,EAAEzB,IAAI,CAACC,GAAG;OACtB,CAAC;MACFe,eAAe,CAACnB,GAAG,CAACqB,UAAU,EAAEI,YAAY,CAAC;MAE7C,IAAI,CAAC9B,kBAAkB,CAAC;QACtBpD,YAAY,EAAE4E,eAAe;QAC7BrE,WAAW,EAAEqD,IAAI,CAACC,GAAG;OACtB,CAAC;MAEF;MACA,IAAIqH,SAAS,KAAKC,SAAS,EAAE;QAC3B,IAAI,CAACrH,6BAA6B,EAAE;;;EAG1C;EAEQV,kBAAkBA,CAACgI,MAAwC;IACjE,MAAMnH,YAAY,GAAG,IAAI,CAACnE,YAAY,CAACoE,QAAQ,EAAE;IACjD,IAAI,CAACpE,YAAY,CAACmD,IAAI,CAAC;MACrB,GAAGgB,YAAY;MACf,GAAGmH;KACJ,CAAC;EACJ;EAEQC,6BAA6BA,CAAC9H,WAAwB;IAC5D,OAAO;MACLI,WAAW,EAAEJ,WAAW,CAACG,UAAU,CAACC,WAAW;MAC/CkH,EAAE,EAAEtH,WAAW,CAACG,UAAU,CAACmH,EAAE;MAC7BC,IAAI,EAAEvH,WAAW,CAACG,UAAU,CAACoH,IAAI;MACjCC,SAAS,EAAExH,WAAW,CAACG,UAAU,CAACqH,SAAS;MAC3CC,SAAS,EAAEzH,WAAW,CAACG,UAAU,CAACsH,SAAS;MAC3CM,OAAO,EAAE/H,WAAW,CAACG,UAAU,CAAC4H,OAAO;MACvC/G,MAAM,EAAEhB,WAAW,CAACG,UAAU,CAACa,MAAM;MACrCgH,SAAS,EAAEhI,WAAW,CAACG,UAAU,CAAC6H,SAAS;MAC3CC,QAAQ,EAAEjI,WAAW,CAACG,UAAU,CAAC8H,QAAQ;MACzCC,QAAQ,EAAElI,WAAW,CAACG,UAAU,CAAC+H,QAAQ;MACzCC,KAAK,EAAE;QACLnH,MAAM,EAAE,IAAI,CAACgE,kBAAkB,CAAChF,WAAW,CAACe,SAAS,CAACC,MAAM,CAAC;QAC7DQ,UAAU,EAAExB,WAAW,CAACe,SAAS,CAACS;OACnC;MACD;MACA4G,SAAS,EAAEpI,WAAW,CAACe,SAAS,CAACqH,SAAS;MAC1CC,SAAS,EAAErI,WAAW,CAACe,SAAS,CAACsH,SAAS;MAC1CC,WAAW,EAAEtI,WAAW,CAACe,SAAS,CAACuH,WAAW;MAC9CC,UAAU,EAAEvI,WAAW,CAACe,SAAS,CAACwH,UAAU;MAC5C;MACAC,SAAS,EAAExI,WAAW,CAACe,SAAS,CAACyH,SAAS;MAC1CC,SAAS,EAAEzI,WAAW,CAACe,SAAS,CAAC0H;KAClC;EACH;EAEA;EACQrK,wBAAwBA,CAACD,KAA8B;IAC7D,OAAOiB,KAAK,CAACwB,IAAI,CAACzC,KAAK,CAAC1B,YAAY,CAACoE,MAAM,EAAE,CAAC,CAAC/E,GAAG,CAAEkE,WAAW,IAC7D,IAAI,CAAC8H,6BAA6B,CAAC9H,WAAW,CAAC,CAChD;EACH;EAEA;;;EAGO0I,4BAA4BA,CACjCnH,UAAkB,EAClBoH,QAAiB,EACjBC,cAAoD;IAEpD,MAAMlI,YAAY,GAAG,IAAI,CAACnE,YAAY,CAACoE,QAAQ,EAAE;IACjD,MAAMU,eAAe,GAAG,IAAI3E,GAAG,CAACgE,YAAY,CAACjE,YAAY,CAAC;IAE1D,MAAMgF,QAAQ,GAAGJ,eAAe,CAACK,GAAG,CAACH,UAAU,CAAC;IAChD,IAAIE,QAAQ,EAAE;MACZ;MACA,MAAMoH,iBAAiB,GAAG;QACxB,GAAGpH,QAAQ,CAACtB,UAAU;QACtBsH,SAAS,EAAEkB,QAAQ;QACnB,IAAIC,cAAc,EAAErB,IAAI,IAAI;UAAEA,IAAI,EAAEqB,cAAc,CAACrB;QAAI,CAAE,CAAC;QAC1D,IAAIqB,cAAc,EAAEb,OAAO,IAAI;UAAEA,OAAO,EAAEa,cAAc,CAACb;QAAO,CAAE;OACnE;MAED,MAAMpG,YAAY,GAAGzF,sBAAsB,CAAC0F,gBAAgB,CAACH,QAAQ,EAAE;QACrEtB,UAAU,EAAE0I,iBAAiB;QAC7B/G,WAAW,EAAEzB,IAAI,CAACC,GAAG;OACtB,CAAC;MAEFe,eAAe,CAACnB,GAAG,CAACqB,UAAU,EAAEI,YAAY,CAAC;MAE7C,IAAI,CAAC9B,kBAAkB,CAAC;QACtBpD,YAAY,EAAE4E,eAAe;QAC7BrE,WAAW,EAAEqD,IAAI,CAACC,GAAG;OACtB,CAAC;KACH,MAAM;MACL7B,OAAO,CAACuE,IAAI,CAAC,4BAA4BzB,UAAU,6BAA6B,CAAC;;EAErF;EAEA;;;EAGOiC,eAAeA,CAACjC,UAAkB;IACvC,MAAMvB,WAAW,GAAG,IAAI,CAACzD,YAAY,CAACoE,QAAQ,EAAE,CAAClE,YAAY,CAACiF,GAAG,CAACH,UAAU,CAAC;IAC7E,OAAOvB,WAAW,EAAEG,UAAU,EAAEsH,SAAS,IAAI,KAAK;EACpD;EAAC,QAAAqB,CAAA,G;qBAnyBU3M,uBAAuB,EAAA4M,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,cAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,aAAA;EAAA;EAAA,QAAAC,EAAA,G;WAAvBlN,uBAAuB;IAAAmN,OAAA,EAAvBnN,uBAAuB,CAAAoN;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}