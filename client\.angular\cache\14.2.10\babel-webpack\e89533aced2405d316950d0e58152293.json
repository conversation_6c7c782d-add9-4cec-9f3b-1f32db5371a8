{"ast": null, "code": "var Buffer = require('../utils/buffer');\n\nvar Polynomial = require('./polynomial');\n\nfunction ReedSolomonEncoder(degree) {\n  this.genPoly = undefined;\n  this.degree = degree;\n  if (this.degree) this.initialize(this.degree);\n}\n/**\n * Initialize the encoder.\n * The input param should correspond to the number of error correction codewords.\n *\n * @param  {Number} degree\n */\n\n\nReedSolomonEncoder.prototype.initialize = function initialize(degree) {\n  // create an irreducible generator polynomial\n  this.degree = degree;\n  this.genPoly = Polynomial.generateECPolynomial(this.degree);\n};\n/**\n * Encodes a chunk of data\n *\n * @param  {Buffer} data Buffer containing input data\n * @return {Buffer}      Buffer containing encoded data\n */\n\n\nReedSolomonEncoder.prototype.encode = function encode(data) {\n  if (!this.genPoly) {\n    throw new Error('Encoder not initialized');\n  } // Calculate EC for this data block\n  // extends data size to data+genPoly size\n\n\n  var pad = new Buffer(this.degree);\n  pad.fill(0);\n  var paddedData = Buffer.concat([data, pad], data.length + this.degree); // The error correction codewords are the remainder after dividing the data codewords\n  // by a generator polynomial\n\n  var remainder = Polynomial.mod(paddedData, this.genPoly); // return EC data blocks (last n byte, where n is the degree of genPoly)\n  // If coefficients number in remainder are less than genPoly degree,\n  // pad with 0s to the left to reach the needed number of coefficients\n\n  var start = this.degree - remainder.length;\n\n  if (start > 0) {\n    var buff = new Buffer(this.degree);\n    buff.fill(0);\n    remainder.copy(buff, start);\n    return buff;\n  }\n\n  return remainder;\n};\n\nmodule.exports = ReedSolomonEncoder;", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON>", "require", "Polynomial", "ReedSolomonEncoder", "degree", "genPoly", "undefined", "initialize", "prototype", "generateECPolynomial", "encode", "data", "Error", "pad", "fill", "paddedData", "concat", "length", "remainder", "mod", "start", "buff", "copy", "module", "exports"], "sources": ["D:/work/joyserver/client/node_modules/qrcode/lib/core/reed-solomon-encoder.js"], "sourcesContent": ["var Buffer = require('../utils/buffer')\nvar Polynomial = require('./polynomial')\n\nfunction ReedSolomonEncoder (degree) {\n  this.genPoly = undefined\n  this.degree = degree\n\n  if (this.degree) this.initialize(this.degree)\n}\n\n/**\n * Initialize the encoder.\n * The input param should correspond to the number of error correction codewords.\n *\n * @param  {Number} degree\n */\nReedSolomonEncoder.prototype.initialize = function initialize (degree) {\n  // create an irreducible generator polynomial\n  this.degree = degree\n  this.genPoly = Polynomial.generateECPolynomial(this.degree)\n}\n\n/**\n * Encodes a chunk of data\n *\n * @param  {Buffer} data Buffer containing input data\n * @return {Buffer}      Buffer containing encoded data\n */\nReedSolomonEncoder.prototype.encode = function encode (data) {\n  if (!this.genPoly) {\n    throw new Error('Encoder not initialized')\n  }\n\n  // Calculate EC for this data block\n  // extends data size to data+genPoly size\n  var pad = new Buffer(this.degree)\n  pad.fill(0)\n  var paddedData = Buffer.concat([data, pad], data.length + this.degree)\n\n  // The error correction codewords are the remainder after dividing the data codewords\n  // by a generator polynomial\n  var remainder = Polynomial.mod(paddedData, this.genPoly)\n\n  // return EC data blocks (last n byte, where n is the degree of genPoly)\n  // If coefficients number in remainder are less than genPoly degree,\n  // pad with 0s to the left to reach the needed number of coefficients\n  var start = this.degree - remainder.length\n  if (start > 0) {\n    var buff = new Buffer(this.degree)\n    buff.fill(0)\n    remainder.copy(buff, start)\n\n    return buff\n  }\n\n  return remainder\n}\n\nmodule.exports = ReedSolomonEncoder\n"], "mappings": "AAAA,IAAIA,MAAM,GAAGC,OAAO,CAAC,iBAAD,CAApB;;AACA,IAAIC,UAAU,GAAGD,OAAO,CAAC,cAAD,CAAxB;;AAEA,SAASE,kBAAT,CAA6BC,MAA7B,EAAqC;EACnC,KAAKC,OAAL,GAAeC,SAAf;EACA,KAAKF,MAAL,GAAcA,MAAd;EAEA,IAAI,KAAKA,MAAT,EAAiB,KAAKG,UAAL,CAAgB,KAAKH,MAArB;AAClB;AAED;AACA;AACA;AACA;AACA;AACA;;;AACAD,kBAAkB,CAACK,SAAnB,CAA6BD,UAA7B,GAA0C,SAASA,UAAT,CAAqBH,MAArB,EAA6B;EACrE;EACA,KAAKA,MAAL,GAAcA,MAAd;EACA,KAAKC,OAAL,GAAeH,UAAU,CAACO,oBAAX,CAAgC,KAAKL,MAArC,CAAf;AACD,CAJD;AAMA;AACA;AACA;AACA;AACA;AACA;;;AACAD,kBAAkB,CAACK,SAAnB,CAA6BE,MAA7B,GAAsC,SAASA,MAAT,CAAiBC,IAAjB,EAAuB;EAC3D,IAAI,CAAC,KAAKN,OAAV,EAAmB;IACjB,MAAM,IAAIO,KAAJ,CAAU,yBAAV,CAAN;EACD,CAH0D,CAK3D;EACA;;;EACA,IAAIC,GAAG,GAAG,IAAIb,MAAJ,CAAW,KAAKI,MAAhB,CAAV;EACAS,GAAG,CAACC,IAAJ,CAAS,CAAT;EACA,IAAIC,UAAU,GAAGf,MAAM,CAACgB,MAAP,CAAc,CAACL,IAAD,EAAOE,GAAP,CAAd,EAA2BF,IAAI,CAACM,MAAL,GAAc,KAAKb,MAA9C,CAAjB,CAT2D,CAW3D;EACA;;EACA,IAAIc,SAAS,GAAGhB,UAAU,CAACiB,GAAX,CAAeJ,UAAf,EAA2B,KAAKV,OAAhC,CAAhB,CAb2D,CAe3D;EACA;EACA;;EACA,IAAIe,KAAK,GAAG,KAAKhB,MAAL,GAAcc,SAAS,CAACD,MAApC;;EACA,IAAIG,KAAK,GAAG,CAAZ,EAAe;IACb,IAAIC,IAAI,GAAG,IAAIrB,MAAJ,CAAW,KAAKI,MAAhB,CAAX;IACAiB,IAAI,CAACP,IAAL,CAAU,CAAV;IACAI,SAAS,CAACI,IAAV,CAAeD,IAAf,EAAqBD,KAArB;IAEA,OAAOC,IAAP;EACD;;EAED,OAAOH,SAAP;AACD,CA5BD;;AA8BAK,MAAM,CAACC,OAAP,GAAiBrB,kBAAjB"}, "metadata": {}, "sourceType": "script"}