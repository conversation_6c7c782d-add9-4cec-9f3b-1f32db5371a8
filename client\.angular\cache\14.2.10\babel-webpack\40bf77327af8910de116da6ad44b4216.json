{"ast": null, "code": "var Buffer = require('../utils/buffer');\n/**\n * Helper class to handle QR Code symbol modules\n *\n * @param {Number} size Symbol size\n */\n\n\nfunction BitMatrix(size) {\n  if (!size || size < 1) {\n    throw new Error('BitMatrix size must be defined and greater than 0');\n  }\n\n  this.size = size;\n  this.data = new Buffer(size * size);\n  this.data.fill(0);\n  this.reservedBit = new Buffer(size * size);\n  this.reservedBit.fill(0);\n}\n/**\n * Set bit value at specified location\n * If reserved flag is set, this bit will be ignored during masking process\n *\n * @param {Number}  row\n * @param {Number}  col\n * @param {Boolean} value\n * @param {Boolean} reserved\n */\n\n\nBitMatrix.prototype.set = function (row, col, value, reserved) {\n  var index = row * this.size + col;\n  this.data[index] = value;\n  if (reserved) this.reservedBit[index] = true;\n};\n/**\n * Returns bit value at specified location\n *\n * @param  {Number}  row\n * @param  {Number}  col\n * @return {Boolean}\n */\n\n\nBitMatrix.prototype.get = function (row, col) {\n  return this.data[row * this.size + col];\n};\n/**\n * Applies xor operator at specified location\n * (used during masking process)\n *\n * @param {Number}  row\n * @param {Number}  col\n * @param {Boolean} value\n */\n\n\nBitMatrix.prototype.xor = function (row, col, value) {\n  this.data[row * this.size + col] ^= value;\n};\n/**\n * Check if bit at specified location is reserved\n *\n * @param {Number}   row\n * @param {Number}   col\n * @return {Boolean}\n */\n\n\nBitMatrix.prototype.isReserved = function (row, col) {\n  return this.reservedBit[row * this.size + col];\n};\n\nmodule.exports = BitMatrix;", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON>", "require", "BitMatrix", "size", "Error", "data", "fill", "reservedBit", "prototype", "set", "row", "col", "value", "reserved", "index", "get", "xor", "isReserved", "module", "exports"], "sources": ["D:/work/joyserver/client/node_modules/qrcode/lib/core/bit-matrix.js"], "sourcesContent": ["var Buffer = require('../utils/buffer')\n\n/**\n * Helper class to handle QR Code symbol modules\n *\n * @param {Number} size Symbol size\n */\nfunction BitMatrix (size) {\n  if (!size || size < 1) {\n    throw new Error('BitMatrix size must be defined and greater than 0')\n  }\n\n  this.size = size\n  this.data = new Buffer(size * size)\n  this.data.fill(0)\n  this.reservedBit = new Buffer(size * size)\n  this.reservedBit.fill(0)\n}\n\n/**\n * Set bit value at specified location\n * If reserved flag is set, this bit will be ignored during masking process\n *\n * @param {Number}  row\n * @param {Number}  col\n * @param {Boolean} value\n * @param {Boolean} reserved\n */\nBitMatrix.prototype.set = function (row, col, value, reserved) {\n  var index = row * this.size + col\n  this.data[index] = value\n  if (reserved) this.reservedBit[index] = true\n}\n\n/**\n * Returns bit value at specified location\n *\n * @param  {Number}  row\n * @param  {Number}  col\n * @return {Boolean}\n */\nBitMatrix.prototype.get = function (row, col) {\n  return this.data[row * this.size + col]\n}\n\n/**\n * Applies xor operator at specified location\n * (used during masking process)\n *\n * @param {Number}  row\n * @param {Number}  col\n * @param {Boolean} value\n */\nBitMatrix.prototype.xor = function (row, col, value) {\n  this.data[row * this.size + col] ^= value\n}\n\n/**\n * Check if bit at specified location is reserved\n *\n * @param {Number}   row\n * @param {Number}   col\n * @return {Boolean}\n */\nBitMatrix.prototype.isReserved = function (row, col) {\n  return this.reservedBit[row * this.size + col]\n}\n\nmodule.exports = BitMatrix\n"], "mappings": "AAAA,IAAIA,MAAM,GAAGC,OAAO,CAAC,iBAAD,CAApB;AAEA;AACA;AACA;AACA;AACA;;;AACA,SAASC,SAAT,CAAoBC,IAApB,EAA0B;EACxB,IAAI,CAACA,IAAD,IAASA,IAAI,GAAG,CAApB,EAAuB;IACrB,MAAM,IAAIC,KAAJ,CAAU,mDAAV,CAAN;EACD;;EAED,KAAKD,IAAL,GAAYA,IAAZ;EACA,KAAKE,IAAL,GAAY,IAAIL,MAAJ,CAAWG,IAAI,GAAGA,IAAlB,CAAZ;EACA,KAAKE,IAAL,CAAUC,IAAV,CAAe,CAAf;EACA,KAAKC,WAAL,GAAmB,IAAIP,MAAJ,CAAWG,IAAI,GAAGA,IAAlB,CAAnB;EACA,KAAKI,WAAL,CAAiBD,IAAjB,CAAsB,CAAtB;AACD;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACAJ,SAAS,CAACM,SAAV,CAAoBC,GAApB,GAA0B,UAAUC,GAAV,EAAeC,GAAf,EAAoBC,KAApB,EAA2BC,QAA3B,EAAqC;EAC7D,IAAIC,KAAK,GAAGJ,GAAG,GAAG,KAAKP,IAAX,GAAkBQ,GAA9B;EACA,KAAKN,IAAL,CAAUS,KAAV,IAAmBF,KAAnB;EACA,IAAIC,QAAJ,EAAc,KAAKN,WAAL,CAAiBO,KAAjB,IAA0B,IAA1B;AACf,CAJD;AAMA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACAZ,SAAS,CAACM,SAAV,CAAoBO,GAApB,GAA0B,UAAUL,GAAV,EAAeC,GAAf,EAAoB;EAC5C,OAAO,KAAKN,IAAL,CAAUK,GAAG,GAAG,KAAKP,IAAX,GAAkBQ,GAA5B,CAAP;AACD,CAFD;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACAT,SAAS,CAACM,SAAV,CAAoBQ,GAApB,GAA0B,UAAUN,GAAV,EAAeC,GAAf,EAAoBC,KAApB,EAA2B;EACnD,KAAKP,IAAL,CAAUK,GAAG,GAAG,KAAKP,IAAX,GAAkBQ,GAA5B,KAAoCC,KAApC;AACD,CAFD;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACAV,SAAS,CAACM,SAAV,CAAoBS,UAApB,GAAiC,UAAUP,GAAV,EAAeC,GAAf,EAAoB;EACnD,OAAO,KAAKJ,WAAL,CAAiBG,GAAG,GAAG,KAAKP,IAAX,GAAkBQ,GAAnC,CAAP;AACD,CAFD;;AAIAO,MAAM,CAACC,OAAP,GAAiBjB,SAAjB"}, "metadata": {}, "sourceType": "script"}