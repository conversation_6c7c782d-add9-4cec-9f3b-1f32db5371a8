{"ast": null, "code": "export var lsolveDocs = {\n  name: 'lsolve',\n  category: 'Algebra',\n  syntax: ['x=lsolve(L, b)'],\n  description: 'Finds one solution of the linear system L * x = b where L is an [n x n] lower triangular matrix and b is a [n] column vector.',\n  examples: ['a = [-2, 3; 2, 1]', 'b = [11, 9]', 'x = lsolve(a, b)'],\n  seealso: ['lsolveAll', 'lup', 'lusolve', 'usolve', 'matrix', 'sparse']\n};", "map": {"version": 3, "names": ["lsolveDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["D:/work/joyserver/client/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/algebra/lsolve.js"], "sourcesContent": ["export var lsolveDocs = {\n  name: 'lsolve',\n  category: 'Algebra',\n  syntax: ['x=lsolve(L, b)'],\n  description: 'Finds one solution of the linear system L * x = b where L is an [n x n] lower triangular matrix and b is a [n] column vector.',\n  examples: ['a = [-2, 3; 2, 1]', 'b = [11, 9]', 'x = lsolve(a, b)'],\n  seealso: ['lsolveAll', 'lup', 'lusolve', 'usolve', 'matrix', 'sparse']\n};"], "mappings": "AAAA,OAAO,IAAIA,UAAU,GAAG;EACtBC,IAAI,EAAE,QADgB;EAEtBC,QAAQ,EAAE,SAFY;EAGtBC,MAAM,EAAE,CAAC,gBAAD,CAHc;EAItBC,WAAW,EAAE,+HAJS;EAKtBC,QAAQ,EAAE,CAAC,mBAAD,EAAsB,aAAtB,EAAqC,kBAArC,CALY;EAMtBC,OAAO,EAAE,CAAC,WAAD,EAAc,KAAd,EAAqB,SAArB,EAAgC,QAAhC,EAA0C,QAA1C,EAAoD,QAApD;AANa,CAAjB"}, "metadata": {}, "sourceType": "module"}