{"ast": null, "code": "import { factory } from '../../utils/factory.js';\nvar name = 'Matrix';\nvar dependencies = [];\nexport var createMatrixClass = /* #__PURE__ */factory(name, dependencies, () => {\n  /**\n   * @constructor Matrix\n   *\n   * A Matrix is a wrapper around an Array. A matrix can hold a multi dimensional\n   * array. A matrix can be constructed as:\n   *\n   *     let matrix = math.matrix(data)\n   *\n   * Matrix contains the functions to resize, get and set values, get the size,\n   * clone the matrix and to convert the matrix to a vector, array, or scalar.\n   * Furthermore, one can iterate over the matrix using map and forEach.\n   * The internal Array of the Matrix can be accessed using the function valueOf.\n   *\n   * Example usage:\n   *\n   *     let matrix = math.matrix([[1, 2], [3, 4]])\n   *     matix.size()              // [2, 2]\n   *     matrix.resize([3, 2], 5)\n   *     matrix.valueOf()          // [[1, 2], [3, 4], [5, 5]]\n   *     matrix.subset([1,2])       // 3 (indexes are zero-based)\n   *\n   */\n  function Matrix() {\n    if (!(this instanceof Matrix)) {\n      throw new SyntaxError('Constructor must be called with the new operator');\n    }\n  }\n  /**\n   * Attach type information\n   */\n\n\n  Matrix.prototype.type = 'Matrix';\n  Matrix.prototype.isMatrix = true;\n  /**\n   * Get the storage format used by the matrix.\n   *\n   * Usage:\n   *     const format = matrix.storage()   // retrieve storage format\n   *\n   * @return {string}           The storage format.\n   */\n\n  Matrix.prototype.storage = function () {\n    // must be implemented by each of the Matrix implementations\n    throw new Error('Cannot invoke storage on a Matrix interface');\n  };\n  /**\n   * Get the datatype of the data stored in the matrix.\n   *\n   * Usage:\n   *     const format = matrix.datatype()    // retrieve matrix datatype\n   *\n   * @return {string}           The datatype.\n   */\n\n\n  Matrix.prototype.datatype = function () {\n    // must be implemented by each of the Matrix implementations\n    throw new Error('Cannot invoke datatype on a Matrix interface');\n  };\n  /**\n   * Create a new Matrix With the type of the current matrix instance\n   * @param {Array | Object} data\n   * @param {string} [datatype]\n   */\n\n\n  Matrix.prototype.create = function (data, datatype) {\n    throw new Error('Cannot invoke create on a Matrix interface');\n  };\n  /**\n   * Get a subset of the matrix, or replace a subset of the matrix.\n   *\n   * Usage:\n   *     const subset = matrix.subset(index)               // retrieve subset\n   *     const value = matrix.subset(index, replacement)   // replace subset\n   *\n   * @param {Index} index\n   * @param {Array | Matrix | *} [replacement]\n   * @param {*} [defaultValue=0]      Default value, filled in on new entries when\n   *                                  the matrix is resized. If not provided,\n   *                                  new matrix elements will be filled with zeros.\n   */\n\n\n  Matrix.prototype.subset = function (index, replacement, defaultValue) {\n    // must be implemented by each of the Matrix implementations\n    throw new Error('Cannot invoke subset on a Matrix interface');\n  };\n  /**\n   * Get a single element from the matrix.\n   * @param {number[]} index   Zero-based index\n   * @return {*} value\n   */\n\n\n  Matrix.prototype.get = function (index) {\n    // must be implemented by each of the Matrix implementations\n    throw new Error('Cannot invoke get on a Matrix interface');\n  };\n  /**\n   * Replace a single element in the matrix.\n   * @param {number[]} index   Zero-based index\n   * @param {*} value\n   * @param {*} [defaultValue]        Default value, filled in on new entries when\n   *                                  the matrix is resized. If not provided,\n   *                                  new matrix elements will be left undefined.\n   * @return {Matrix} self\n   */\n\n\n  Matrix.prototype.set = function (index, value, defaultValue) {\n    // must be implemented by each of the Matrix implementations\n    throw new Error('Cannot invoke set on a Matrix interface');\n  };\n  /**\n   * Resize the matrix to the given size. Returns a copy of the matrix when\n   * `copy=true`, otherwise return the matrix itself (resize in place).\n   *\n   * @param {number[]} size           The new size the matrix should have.\n   * @param {*} [defaultValue=0]      Default value, filled in on new entries.\n   *                                  If not provided, the matrix elements will\n   *                                  be filled with zeros.\n   * @param {boolean} [copy]          Return a resized copy of the matrix\n   *\n   * @return {Matrix}                 The resized matrix\n   */\n\n\n  Matrix.prototype.resize = function (size, defaultValue) {\n    // must be implemented by each of the Matrix implementations\n    throw new Error('Cannot invoke resize on a Matrix interface');\n  };\n  /**\n   * Reshape the matrix to the given size. Returns a copy of the matrix when\n   * `copy=true`, otherwise return the matrix itself (reshape in place).\n   *\n   * @param {number[]} size           The new size the matrix should have.\n   * @param {boolean} [copy]          Return a reshaped copy of the matrix\n   *\n   * @return {Matrix}                 The reshaped matrix\n   */\n\n\n  Matrix.prototype.reshape = function (size, defaultValue) {\n    // must be implemented by each of the Matrix implementations\n    throw new Error('Cannot invoke reshape on a Matrix interface');\n  };\n  /**\n   * Create a clone of the matrix\n   * @return {Matrix} clone\n   */\n\n\n  Matrix.prototype.clone = function () {\n    // must be implemented by each of the Matrix implementations\n    throw new Error('Cannot invoke clone on a Matrix interface');\n  };\n  /**\n   * Retrieve the size of the matrix.\n   * @returns {number[]} size\n   */\n\n\n  Matrix.prototype.size = function () {\n    // must be implemented by each of the Matrix implementations\n    throw new Error('Cannot invoke size on a Matrix interface');\n  };\n  /**\n   * Create a new matrix with the results of the callback function executed on\n   * each entry of the matrix.\n   * @param {Function} callback   The callback function is invoked with three\n   *                              parameters: the value of the element, the index\n   *                              of the element, and the Matrix being traversed.\n   * @param {boolean} [skipZeros] Invoke callback function for non-zero values only.\n   *\n   * @return {Matrix} matrix\n   */\n\n\n  Matrix.prototype.map = function (callback, skipZeros) {\n    // must be implemented by each of the Matrix implementations\n    throw new Error('Cannot invoke map on a Matrix interface');\n  };\n  /**\n   * Execute a callback function on each entry of the matrix.\n   * @param {Function} callback   The callback function is invoked with three\n   *                              parameters: the value of the element, the index\n   *                              of the element, and the Matrix being traversed.\n   */\n\n\n  Matrix.prototype.forEach = function (callback) {\n    // must be implemented by each of the Matrix implementations\n    throw new Error('Cannot invoke forEach on a Matrix interface');\n  };\n  /**\n   * Iterate over the matrix elements\n   * @return {Iterable<{ value, index: number[] }>}\n   */\n\n\n  Matrix.prototype[Symbol.iterator] = function () {\n    // must be implemented by each of the Matrix implementations\n    throw new Error('Cannot iterate a Matrix interface');\n  };\n  /**\n   * Create an Array with a copy of the data of the Matrix\n   * @returns {Array} array\n   */\n\n\n  Matrix.prototype.toArray = function () {\n    // must be implemented by each of the Matrix implementations\n    throw new Error('Cannot invoke toArray on a Matrix interface');\n  };\n  /**\n   * Get the primitive value of the Matrix: a multidimensional array\n   * @returns {Array} array\n   */\n\n\n  Matrix.prototype.valueOf = function () {\n    // must be implemented by each of the Matrix implementations\n    throw new Error('Cannot invoke valueOf on a Matrix interface');\n  };\n  /**\n   * Get a string representation of the matrix, with optional formatting options.\n   * @param {Object | number | Function} [options]  Formatting options. See\n   *                                                lib/utils/number:format for a\n   *                                                description of the available\n   *                                                options.\n   * @returns {string} str\n   */\n\n\n  Matrix.prototype.format = function (options) {\n    // must be implemented by each of the Matrix implementations\n    throw new Error('Cannot invoke format on a Matrix interface');\n  };\n  /**\n   * Get a string representation of the matrix\n   * @returns {string} str\n   */\n\n\n  Matrix.prototype.toString = function () {\n    // must be implemented by each of the Matrix implementations\n    throw new Error('Cannot invoke toString on a Matrix interface');\n  };\n\n  return Matrix;\n}, {\n  isClass: true\n});", "map": {"version": 3, "names": ["factory", "name", "dependencies", "createMatrixClass", "Matrix", "SyntaxError", "prototype", "type", "isMatrix", "storage", "Error", "datatype", "create", "data", "subset", "index", "replacement", "defaultValue", "get", "set", "value", "resize", "size", "reshape", "clone", "map", "callback", "skipZ<PERSON><PERSON>", "for<PERSON>ach", "Symbol", "iterator", "toArray", "valueOf", "format", "options", "toString", "isClass"], "sources": ["D:/work/joyserver/client/node_modules/mathjs/lib/esm/type/matrix/Matrix.js"], "sourcesContent": ["import { factory } from '../../utils/factory.js';\nvar name = 'Matrix';\nvar dependencies = [];\nexport var createMatrixClass = /* #__PURE__ */factory(name, dependencies, () => {\n  /**\n   * @constructor Matrix\n   *\n   * A Matrix is a wrapper around an Array. A matrix can hold a multi dimensional\n   * array. A matrix can be constructed as:\n   *\n   *     let matrix = math.matrix(data)\n   *\n   * Matrix contains the functions to resize, get and set values, get the size,\n   * clone the matrix and to convert the matrix to a vector, array, or scalar.\n   * Furthermore, one can iterate over the matrix using map and forEach.\n   * The internal Array of the Matrix can be accessed using the function valueOf.\n   *\n   * Example usage:\n   *\n   *     let matrix = math.matrix([[1, 2], [3, 4]])\n   *     matix.size()              // [2, 2]\n   *     matrix.resize([3, 2], 5)\n   *     matrix.valueOf()          // [[1, 2], [3, 4], [5, 5]]\n   *     matrix.subset([1,2])       // 3 (indexes are zero-based)\n   *\n   */\n  function Matrix() {\n    if (!(this instanceof Matrix)) {\n      throw new SyntaxError('Constructor must be called with the new operator');\n    }\n  }\n\n  /**\n   * Attach type information\n   */\n  Matrix.prototype.type = 'Matrix';\n  Matrix.prototype.isMatrix = true;\n\n  /**\n   * Get the storage format used by the matrix.\n   *\n   * Usage:\n   *     const format = matrix.storage()   // retrieve storage format\n   *\n   * @return {string}           The storage format.\n   */\n  Matrix.prototype.storage = function () {\n    // must be implemented by each of the Matrix implementations\n    throw new Error('Cannot invoke storage on a Matrix interface');\n  };\n\n  /**\n   * Get the datatype of the data stored in the matrix.\n   *\n   * Usage:\n   *     const format = matrix.datatype()    // retrieve matrix datatype\n   *\n   * @return {string}           The datatype.\n   */\n  Matrix.prototype.datatype = function () {\n    // must be implemented by each of the Matrix implementations\n    throw new Error('Cannot invoke datatype on a Matrix interface');\n  };\n\n  /**\n   * Create a new Matrix With the type of the current matrix instance\n   * @param {Array | Object} data\n   * @param {string} [datatype]\n   */\n  Matrix.prototype.create = function (data, datatype) {\n    throw new Error('Cannot invoke create on a Matrix interface');\n  };\n\n  /**\n   * Get a subset of the matrix, or replace a subset of the matrix.\n   *\n   * Usage:\n   *     const subset = matrix.subset(index)               // retrieve subset\n   *     const value = matrix.subset(index, replacement)   // replace subset\n   *\n   * @param {Index} index\n   * @param {Array | Matrix | *} [replacement]\n   * @param {*} [defaultValue=0]      Default value, filled in on new entries when\n   *                                  the matrix is resized. If not provided,\n   *                                  new matrix elements will be filled with zeros.\n   */\n  Matrix.prototype.subset = function (index, replacement, defaultValue) {\n    // must be implemented by each of the Matrix implementations\n    throw new Error('Cannot invoke subset on a Matrix interface');\n  };\n\n  /**\n   * Get a single element from the matrix.\n   * @param {number[]} index   Zero-based index\n   * @return {*} value\n   */\n  Matrix.prototype.get = function (index) {\n    // must be implemented by each of the Matrix implementations\n    throw new Error('Cannot invoke get on a Matrix interface');\n  };\n\n  /**\n   * Replace a single element in the matrix.\n   * @param {number[]} index   Zero-based index\n   * @param {*} value\n   * @param {*} [defaultValue]        Default value, filled in on new entries when\n   *                                  the matrix is resized. If not provided,\n   *                                  new matrix elements will be left undefined.\n   * @return {Matrix} self\n   */\n  Matrix.prototype.set = function (index, value, defaultValue) {\n    // must be implemented by each of the Matrix implementations\n    throw new Error('Cannot invoke set on a Matrix interface');\n  };\n\n  /**\n   * Resize the matrix to the given size. Returns a copy of the matrix when\n   * `copy=true`, otherwise return the matrix itself (resize in place).\n   *\n   * @param {number[]} size           The new size the matrix should have.\n   * @param {*} [defaultValue=0]      Default value, filled in on new entries.\n   *                                  If not provided, the matrix elements will\n   *                                  be filled with zeros.\n   * @param {boolean} [copy]          Return a resized copy of the matrix\n   *\n   * @return {Matrix}                 The resized matrix\n   */\n  Matrix.prototype.resize = function (size, defaultValue) {\n    // must be implemented by each of the Matrix implementations\n    throw new Error('Cannot invoke resize on a Matrix interface');\n  };\n\n  /**\n   * Reshape the matrix to the given size. Returns a copy of the matrix when\n   * `copy=true`, otherwise return the matrix itself (reshape in place).\n   *\n   * @param {number[]} size           The new size the matrix should have.\n   * @param {boolean} [copy]          Return a reshaped copy of the matrix\n   *\n   * @return {Matrix}                 The reshaped matrix\n   */\n  Matrix.prototype.reshape = function (size, defaultValue) {\n    // must be implemented by each of the Matrix implementations\n    throw new Error('Cannot invoke reshape on a Matrix interface');\n  };\n\n  /**\n   * Create a clone of the matrix\n   * @return {Matrix} clone\n   */\n  Matrix.prototype.clone = function () {\n    // must be implemented by each of the Matrix implementations\n    throw new Error('Cannot invoke clone on a Matrix interface');\n  };\n\n  /**\n   * Retrieve the size of the matrix.\n   * @returns {number[]} size\n   */\n  Matrix.prototype.size = function () {\n    // must be implemented by each of the Matrix implementations\n    throw new Error('Cannot invoke size on a Matrix interface');\n  };\n\n  /**\n   * Create a new matrix with the results of the callback function executed on\n   * each entry of the matrix.\n   * @param {Function} callback   The callback function is invoked with three\n   *                              parameters: the value of the element, the index\n   *                              of the element, and the Matrix being traversed.\n   * @param {boolean} [skipZeros] Invoke callback function for non-zero values only.\n   *\n   * @return {Matrix} matrix\n   */\n  Matrix.prototype.map = function (callback, skipZeros) {\n    // must be implemented by each of the Matrix implementations\n    throw new Error('Cannot invoke map on a Matrix interface');\n  };\n\n  /**\n   * Execute a callback function on each entry of the matrix.\n   * @param {Function} callback   The callback function is invoked with three\n   *                              parameters: the value of the element, the index\n   *                              of the element, and the Matrix being traversed.\n   */\n  Matrix.prototype.forEach = function (callback) {\n    // must be implemented by each of the Matrix implementations\n    throw new Error('Cannot invoke forEach on a Matrix interface');\n  };\n\n  /**\n   * Iterate over the matrix elements\n   * @return {Iterable<{ value, index: number[] }>}\n   */\n  Matrix.prototype[Symbol.iterator] = function () {\n    // must be implemented by each of the Matrix implementations\n    throw new Error('Cannot iterate a Matrix interface');\n  };\n\n  /**\n   * Create an Array with a copy of the data of the Matrix\n   * @returns {Array} array\n   */\n  Matrix.prototype.toArray = function () {\n    // must be implemented by each of the Matrix implementations\n    throw new Error('Cannot invoke toArray on a Matrix interface');\n  };\n\n  /**\n   * Get the primitive value of the Matrix: a multidimensional array\n   * @returns {Array} array\n   */\n  Matrix.prototype.valueOf = function () {\n    // must be implemented by each of the Matrix implementations\n    throw new Error('Cannot invoke valueOf on a Matrix interface');\n  };\n\n  /**\n   * Get a string representation of the matrix, with optional formatting options.\n   * @param {Object | number | Function} [options]  Formatting options. See\n   *                                                lib/utils/number:format for a\n   *                                                description of the available\n   *                                                options.\n   * @returns {string} str\n   */\n  Matrix.prototype.format = function (options) {\n    // must be implemented by each of the Matrix implementations\n    throw new Error('Cannot invoke format on a Matrix interface');\n  };\n\n  /**\n   * Get a string representation of the matrix\n   * @returns {string} str\n   */\n  Matrix.prototype.toString = function () {\n    // must be implemented by each of the Matrix implementations\n    throw new Error('Cannot invoke toString on a Matrix interface');\n  };\n  return Matrix;\n}, {\n  isClass: true\n});"], "mappings": "AAAA,SAASA,OAAT,QAAwB,wBAAxB;AACA,IAAIC,IAAI,GAAG,QAAX;AACA,IAAIC,YAAY,GAAG,EAAnB;AACA,OAAO,IAAIC,iBAAiB,GAAG,eAAeH,OAAO,CAACC,IAAD,EAAOC,YAAP,EAAqB,MAAM;EAC9E;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASE,MAAT,GAAkB;IAChB,IAAI,EAAE,gBAAgBA,MAAlB,CAAJ,EAA+B;MAC7B,MAAM,IAAIC,WAAJ,CAAgB,kDAAhB,CAAN;IACD;EACF;EAED;AACF;AACA;;;EACED,MAAM,CAACE,SAAP,CAAiBC,IAAjB,GAAwB,QAAxB;EACAH,MAAM,CAACE,SAAP,CAAiBE,QAAjB,GAA4B,IAA5B;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;;EACEJ,MAAM,CAACE,SAAP,CAAiBG,OAAjB,GAA2B,YAAY;IACrC;IACA,MAAM,IAAIC,KAAJ,CAAU,6CAAV,CAAN;EACD,CAHD;EAKA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;;;EACEN,MAAM,CAACE,SAAP,CAAiBK,QAAjB,GAA4B,YAAY;IACtC;IACA,MAAM,IAAID,KAAJ,CAAU,8CAAV,CAAN;EACD,CAHD;EAKA;AACF;AACA;AACA;AACA;;;EACEN,MAAM,CAACE,SAAP,CAAiBM,MAAjB,GAA0B,UAAUC,IAAV,EAAgBF,QAAhB,EAA0B;IAClD,MAAM,IAAID,KAAJ,CAAU,4CAAV,CAAN;EACD,CAFD;EAIA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;EACEN,MAAM,CAACE,SAAP,CAAiBQ,MAAjB,GAA0B,UAAUC,KAAV,EAAiBC,WAAjB,EAA8BC,YAA9B,EAA4C;IACpE;IACA,MAAM,IAAIP,KAAJ,CAAU,4CAAV,CAAN;EACD,CAHD;EAKA;AACF;AACA;AACA;AACA;;;EACEN,MAAM,CAACE,SAAP,CAAiBY,GAAjB,GAAuB,UAAUH,KAAV,EAAiB;IACtC;IACA,MAAM,IAAIL,KAAJ,CAAU,yCAAV,CAAN;EACD,CAHD;EAKA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;EACEN,MAAM,CAACE,SAAP,CAAiBa,GAAjB,GAAuB,UAAUJ,KAAV,EAAiBK,KAAjB,EAAwBH,YAAxB,EAAsC;IAC3D;IACA,MAAM,IAAIP,KAAJ,CAAU,yCAAV,CAAN;EACD,CAHD;EAKA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;EACEN,MAAM,CAACE,SAAP,CAAiBe,MAAjB,GAA0B,UAAUC,IAAV,EAAgBL,YAAhB,EAA8B;IACtD;IACA,MAAM,IAAIP,KAAJ,CAAU,4CAAV,CAAN;EACD,CAHD;EAKA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;EACEN,MAAM,CAACE,SAAP,CAAiBiB,OAAjB,GAA2B,UAAUD,IAAV,EAAgBL,YAAhB,EAA8B;IACvD;IACA,MAAM,IAAIP,KAAJ,CAAU,6CAAV,CAAN;EACD,CAHD;EAKA;AACF;AACA;AACA;;;EACEN,MAAM,CAACE,SAAP,CAAiBkB,KAAjB,GAAyB,YAAY;IACnC;IACA,MAAM,IAAId,KAAJ,CAAU,2CAAV,CAAN;EACD,CAHD;EAKA;AACF;AACA;AACA;;;EACEN,MAAM,CAACE,SAAP,CAAiBgB,IAAjB,GAAwB,YAAY;IAClC;IACA,MAAM,IAAIZ,KAAJ,CAAU,0CAAV,CAAN;EACD,CAHD;EAKA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;EACEN,MAAM,CAACE,SAAP,CAAiBmB,GAAjB,GAAuB,UAAUC,QAAV,EAAoBC,SAApB,EAA+B;IACpD;IACA,MAAM,IAAIjB,KAAJ,CAAU,yCAAV,CAAN;EACD,CAHD;EAKA;AACF;AACA;AACA;AACA;AACA;;;EACEN,MAAM,CAACE,SAAP,CAAiBsB,OAAjB,GAA2B,UAAUF,QAAV,EAAoB;IAC7C;IACA,MAAM,IAAIhB,KAAJ,CAAU,6CAAV,CAAN;EACD,CAHD;EAKA;AACF;AACA;AACA;;;EACEN,MAAM,CAACE,SAAP,CAAiBuB,MAAM,CAACC,QAAxB,IAAoC,YAAY;IAC9C;IACA,MAAM,IAAIpB,KAAJ,CAAU,mCAAV,CAAN;EACD,CAHD;EAKA;AACF;AACA;AACA;;;EACEN,MAAM,CAACE,SAAP,CAAiByB,OAAjB,GAA2B,YAAY;IACrC;IACA,MAAM,IAAIrB,KAAJ,CAAU,6CAAV,CAAN;EACD,CAHD;EAKA;AACF;AACA;AACA;;;EACEN,MAAM,CAACE,SAAP,CAAiB0B,OAAjB,GAA2B,YAAY;IACrC;IACA,MAAM,IAAItB,KAAJ,CAAU,6CAAV,CAAN;EACD,CAHD;EAKA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;;;EACEN,MAAM,CAACE,SAAP,CAAiB2B,MAAjB,GAA0B,UAAUC,OAAV,EAAmB;IAC3C;IACA,MAAM,IAAIxB,KAAJ,CAAU,4CAAV,CAAN;EACD,CAHD;EAKA;AACF;AACA;AACA;;;EACEN,MAAM,CAACE,SAAP,CAAiB6B,QAAjB,GAA4B,YAAY;IACtC;IACA,MAAM,IAAIzB,KAAJ,CAAU,8CAAV,CAAN;EACD,CAHD;;EAIA,OAAON,MAAP;AACD,CA5OoD,EA4OlD;EACDgC,OAAO,EAAE;AADR,CA5OkD,CAA9C"}, "metadata": {}, "sourceType": "module"}