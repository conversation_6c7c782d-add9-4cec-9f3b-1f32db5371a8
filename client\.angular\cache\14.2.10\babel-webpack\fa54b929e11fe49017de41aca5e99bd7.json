{"ast": null, "code": "import { factory } from '../../utils/factory.js';\nimport { isInteger } from '../../utils/number.js';\nimport { isMatrix } from '../../utils/is.js';\nvar name = 'diff';\nvar dependencies = ['typed', 'matrix', 'subtract', 'number'];\nexport var createDiff = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed,\n    matrix,\n    subtract,\n    number\n  } = _ref;\n  /**\n   * Create a new matrix or array of the difference between elements of the given array\n   * The optional dim parameter lets you specify the dimension to evaluate the difference of\n   * If no dimension parameter is passed it is assumed as dimension 0\n   *\n   * Dimension is zero-based in javascript and one-based in the parser and can be a number or bignumber\n   * Arrays must be 'rectangular' meaning arrays like [1, 2]\n   * If something is passed as a matrix it will be returned as a matrix but other than that all matrices are converted to arrays\n   *\n   * Syntax:\n   *\n   *     math.diff(arr)\n   *     math.diff(arr, dim)\n   *\n   * Examples:\n   *\n   *     const arr = [1, 2, 4, 7, 0]\n   *     math.diff(arr) // returns [1, 2, 3, -7] (no dimension passed so 0 is assumed)\n   *     math.diff(math.matrix(arr)) // returns Matrix [1, 2, 3, -7]\n   *\n   *     const arr = [[1, 2, 3, 4, 5], [1, 2, 3, 4, 5], [9, 8, 7, 6, 4]]\n   *     math.diff(arr) // returns [[0, 0, 0, 0, 0], [8, 6, 4, 2, -1]]\n   *     math.diff(arr, 0) // returns [[0, 0, 0, 0, 0], [8, 6, 4, 2, -1]]\n   *     math.diff(arr, 1) // returns [[1, 1, 1, 1], [1, 1, 1, 1], [-1, -1, -1, -2]]\n   *     math.diff(arr, math.bignumber(1)) // returns [[1, 1, 1, 1], [1, 1, 1, 1], [-1, -1, -1, -2]]\n   *\n   *     math.diff(arr, 2) // throws RangeError as arr is 2 dimensional not 3\n   *     math.diff(arr, -1) // throws RangeError as negative dimensions are not allowed\n   *\n   *     // These will all produce the same result\n   *     math.diff([[1, 2], [3, 4]])\n   *     math.diff([math.matrix([1, 2]), math.matrix([3, 4])])\n   *     math.diff([[1, 2], math.matrix([3, 4])])\n   *     math.diff([math.matrix([1, 2]), [3, 4]])\n   *     // They do not produce the same result as  math.diff(math.matrix([[1, 2], [3, 4]])) as this returns a matrix\n   *\n   * See Also:\n   *\n   *      sum\n   *      subtract\n   *      partitionSelect\n   *\n   * @param {Array | Matrix} arr      An array or matrix\n   * @param {number | BigNumber} dim  Dimension\n   * @return {Array | Matrix}         Difference between array elements in given dimension\n   */\n\n  return typed(name, {\n    'Array | Matrix': function Array__Matrix(arr) {\n      // No dimension specified => assume dimension 0\n      if (isMatrix(arr)) {\n        return matrix(_diff(arr.toArray()));\n      } else {\n        return _diff(arr);\n      }\n    },\n    'Array | Matrix, number': function Array__Matrix_number(arr, dim) {\n      if (!isInteger(dim)) throw new RangeError('Dimension must be a whole number');\n\n      if (isMatrix(arr)) {\n        return matrix(_recursive(arr.toArray(), dim));\n      } else {\n        return _recursive(arr, dim);\n      }\n    },\n    'Array, BigNumber': typed.referTo('Array,number', selfAn => (arr, dim) => selfAn(arr, number(dim))),\n    'Matrix, BigNumber': typed.referTo('Matrix,number', selfMn => (arr, dim) => selfMn(arr, number(dim)))\n  });\n  /**\n   * Recursively find the correct dimension in the array/matrix\n   * Then Apply _diff to that dimension\n   *\n   * @param {Array} arr      The array\n   * @param {number} dim     Dimension\n   * @return {Array}         resulting array\n   */\n\n  function _recursive(arr, dim) {\n    if (isMatrix(arr)) {\n      arr = arr.toArray(); // Makes sure arrays like [ matrix([0, 1]), matrix([1, 0]) ] are processed properly\n    }\n\n    if (!Array.isArray(arr)) {\n      throw RangeError('Array/Matrix does not have that many dimensions');\n    }\n\n    if (dim > 0) {\n      var result = [];\n      arr.forEach(element => {\n        result.push(_recursive(element, dim - 1));\n      });\n      return result;\n    } else if (dim === 0) {\n      return _diff(arr);\n    } else {\n      throw RangeError('Cannot have negative dimension');\n    }\n  }\n  /**\n   * Difference between elements in the array\n   *\n   * @param {Array} arr      An array\n   * @return {Array}         resulting array\n   */\n\n\n  function _diff(arr) {\n    var result = [];\n    var size = arr.length;\n\n    for (var i = 1; i < size; i++) {\n      result.push(_ElementDiff(arr[i - 1], arr[i]));\n    }\n\n    return result;\n  }\n  /**\n   * Difference between 2 objects\n   *\n   * @param {Object} obj1    First object\n   * @param {Object} obj2    Second object\n   * @return {Array}         resulting array\n   */\n\n\n  function _ElementDiff(obj1, obj2) {\n    // Convert matrices to arrays\n    if (isMatrix(obj1)) obj1 = obj1.toArray();\n    if (isMatrix(obj2)) obj2 = obj2.toArray();\n    var obj1IsArray = Array.isArray(obj1);\n    var obj2IsArray = Array.isArray(obj2);\n\n    if (obj1IsArray && obj2IsArray) {\n      return _ArrayDiff(obj1, obj2);\n    }\n\n    if (!obj1IsArray && !obj2IsArray) {\n      return subtract(obj2, obj1); // Difference is (second - first) NOT (first - second)\n    }\n\n    throw TypeError('Cannot calculate difference between 1 array and 1 non-array');\n  }\n  /**\n   * Difference of elements in 2 arrays\n   *\n   * @param {Array} arr1     Array 1\n   * @param {Array} arr2     Array 2\n   * @return {Array}         resulting array\n   */\n\n\n  function _ArrayDiff(arr1, arr2) {\n    if (arr1.length !== arr2.length) {\n      throw RangeError('Not all sub-arrays have the same length');\n    }\n\n    var result = [];\n    var size = arr1.length;\n\n    for (var i = 0; i < size; i++) {\n      result.push(_ElementDiff(arr1[i], arr2[i]));\n    }\n\n    return result;\n  }\n});", "map": {"version": 3, "names": ["factory", "isInteger", "isMatrix", "name", "dependencies", "createDiff", "_ref", "typed", "matrix", "subtract", "number", "Array__Matrix", "arr", "_diff", "toArray", "Array__Matrix_number", "dim", "RangeError", "_recursive", "referTo", "selfAn", "selfMn", "Array", "isArray", "result", "for<PERSON>ach", "element", "push", "size", "length", "i", "_ElementDiff", "obj1", "obj2", "obj1IsArray", "obj2IsArray", "_ArrayDiff", "TypeError", "arr1", "arr2"], "sources": ["D:/work/joyserver/client/node_modules/mathjs/lib/esm/function/matrix/diff.js"], "sourcesContent": ["import { factory } from '../../utils/factory.js';\nimport { isInteger } from '../../utils/number.js';\nimport { isMatrix } from '../../utils/is.js';\nvar name = 'diff';\nvar dependencies = ['typed', 'matrix', 'subtract', 'number'];\nexport var createDiff = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed,\n    matrix,\n    subtract,\n    number\n  } = _ref;\n  /**\n   * Create a new matrix or array of the difference between elements of the given array\n   * The optional dim parameter lets you specify the dimension to evaluate the difference of\n   * If no dimension parameter is passed it is assumed as dimension 0\n   *\n   * Dimension is zero-based in javascript and one-based in the parser and can be a number or bignumber\n   * Arrays must be 'rectangular' meaning arrays like [1, 2]\n   * If something is passed as a matrix it will be returned as a matrix but other than that all matrices are converted to arrays\n   *\n   * Syntax:\n   *\n   *     math.diff(arr)\n   *     math.diff(arr, dim)\n   *\n   * Examples:\n   *\n   *     const arr = [1, 2, 4, 7, 0]\n   *     math.diff(arr) // returns [1, 2, 3, -7] (no dimension passed so 0 is assumed)\n   *     math.diff(math.matrix(arr)) // returns Matrix [1, 2, 3, -7]\n   *\n   *     const arr = [[1, 2, 3, 4, 5], [1, 2, 3, 4, 5], [9, 8, 7, 6, 4]]\n   *     math.diff(arr) // returns [[0, 0, 0, 0, 0], [8, 6, 4, 2, -1]]\n   *     math.diff(arr, 0) // returns [[0, 0, 0, 0, 0], [8, 6, 4, 2, -1]]\n   *     math.diff(arr, 1) // returns [[1, 1, 1, 1], [1, 1, 1, 1], [-1, -1, -1, -2]]\n   *     math.diff(arr, math.bignumber(1)) // returns [[1, 1, 1, 1], [1, 1, 1, 1], [-1, -1, -1, -2]]\n   *\n   *     math.diff(arr, 2) // throws RangeError as arr is 2 dimensional not 3\n   *     math.diff(arr, -1) // throws RangeError as negative dimensions are not allowed\n   *\n   *     // These will all produce the same result\n   *     math.diff([[1, 2], [3, 4]])\n   *     math.diff([math.matrix([1, 2]), math.matrix([3, 4])])\n   *     math.diff([[1, 2], math.matrix([3, 4])])\n   *     math.diff([math.matrix([1, 2]), [3, 4]])\n   *     // They do not produce the same result as  math.diff(math.matrix([[1, 2], [3, 4]])) as this returns a matrix\n   *\n   * See Also:\n   *\n   *      sum\n   *      subtract\n   *      partitionSelect\n   *\n   * @param {Array | Matrix} arr      An array or matrix\n   * @param {number | BigNumber} dim  Dimension\n   * @return {Array | Matrix}         Difference between array elements in given dimension\n   */\n  return typed(name, {\n    'Array | Matrix': function Array__Matrix(arr) {\n      // No dimension specified => assume dimension 0\n      if (isMatrix(arr)) {\n        return matrix(_diff(arr.toArray()));\n      } else {\n        return _diff(arr);\n      }\n    },\n    'Array | Matrix, number': function Array__Matrix_number(arr, dim) {\n      if (!isInteger(dim)) throw new RangeError('Dimension must be a whole number');\n      if (isMatrix(arr)) {\n        return matrix(_recursive(arr.toArray(), dim));\n      } else {\n        return _recursive(arr, dim);\n      }\n    },\n    'Array, BigNumber': typed.referTo('Array,number', selfAn => (arr, dim) => selfAn(arr, number(dim))),\n    'Matrix, BigNumber': typed.referTo('Matrix,number', selfMn => (arr, dim) => selfMn(arr, number(dim)))\n  });\n\n  /**\n   * Recursively find the correct dimension in the array/matrix\n   * Then Apply _diff to that dimension\n   *\n   * @param {Array} arr      The array\n   * @param {number} dim     Dimension\n   * @return {Array}         resulting array\n   */\n  function _recursive(arr, dim) {\n    if (isMatrix(arr)) {\n      arr = arr.toArray(); // Makes sure arrays like [ matrix([0, 1]), matrix([1, 0]) ] are processed properly\n    }\n    if (!Array.isArray(arr)) {\n      throw RangeError('Array/Matrix does not have that many dimensions');\n    }\n    if (dim > 0) {\n      var result = [];\n      arr.forEach(element => {\n        result.push(_recursive(element, dim - 1));\n      });\n      return result;\n    } else if (dim === 0) {\n      return _diff(arr);\n    } else {\n      throw RangeError('Cannot have negative dimension');\n    }\n  }\n\n  /**\n   * Difference between elements in the array\n   *\n   * @param {Array} arr      An array\n   * @return {Array}         resulting array\n   */\n  function _diff(arr) {\n    var result = [];\n    var size = arr.length;\n    for (var i = 1; i < size; i++) {\n      result.push(_ElementDiff(arr[i - 1], arr[i]));\n    }\n    return result;\n  }\n\n  /**\n   * Difference between 2 objects\n   *\n   * @param {Object} obj1    First object\n   * @param {Object} obj2    Second object\n   * @return {Array}         resulting array\n   */\n  function _ElementDiff(obj1, obj2) {\n    // Convert matrices to arrays\n    if (isMatrix(obj1)) obj1 = obj1.toArray();\n    if (isMatrix(obj2)) obj2 = obj2.toArray();\n    var obj1IsArray = Array.isArray(obj1);\n    var obj2IsArray = Array.isArray(obj2);\n    if (obj1IsArray && obj2IsArray) {\n      return _ArrayDiff(obj1, obj2);\n    }\n    if (!obj1IsArray && !obj2IsArray) {\n      return subtract(obj2, obj1); // Difference is (second - first) NOT (first - second)\n    }\n    throw TypeError('Cannot calculate difference between 1 array and 1 non-array');\n  }\n\n  /**\n   * Difference of elements in 2 arrays\n   *\n   * @param {Array} arr1     Array 1\n   * @param {Array} arr2     Array 2\n   * @return {Array}         resulting array\n   */\n  function _ArrayDiff(arr1, arr2) {\n    if (arr1.length !== arr2.length) {\n      throw RangeError('Not all sub-arrays have the same length');\n    }\n    var result = [];\n    var size = arr1.length;\n    for (var i = 0; i < size; i++) {\n      result.push(_ElementDiff(arr1[i], arr2[i]));\n    }\n    return result;\n  }\n});"], "mappings": "AAAA,SAASA,OAAT,QAAwB,wBAAxB;AACA,SAASC,SAAT,QAA0B,uBAA1B;AACA,SAASC,QAAT,QAAyB,mBAAzB;AACA,IAAIC,IAAI,GAAG,MAAX;AACA,IAAIC,YAAY,GAAG,CAAC,OAAD,EAAU,QAAV,EAAoB,UAApB,EAAgC,QAAhC,CAAnB;AACA,OAAO,IAAIC,UAAU,GAAG,eAAeL,OAAO,CAACG,IAAD,EAAOC,YAAP,EAAqBE,IAAI,IAAI;EACzE,IAAI;IACFC,KADE;IAEFC,MAFE;IAGFC,QAHE;IAIFC;EAJE,IAKAJ,IALJ;EAMA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EACE,OAAOC,KAAK,CAACJ,IAAD,EAAO;IACjB,kBAAkB,SAASQ,aAAT,CAAuBC,GAAvB,EAA4B;MAC5C;MACA,IAAIV,QAAQ,CAACU,GAAD,CAAZ,EAAmB;QACjB,OAAOJ,MAAM,CAACK,KAAK,CAACD,GAAG,CAACE,OAAJ,EAAD,CAAN,CAAb;MACD,CAFD,MAEO;QACL,OAAOD,KAAK,CAACD,GAAD,CAAZ;MACD;IACF,CARgB;IASjB,0BAA0B,SAASG,oBAAT,CAA8BH,GAA9B,EAAmCI,GAAnC,EAAwC;MAChE,IAAI,CAACf,SAAS,CAACe,GAAD,CAAd,EAAqB,MAAM,IAAIC,UAAJ,CAAe,kCAAf,CAAN;;MACrB,IAAIf,QAAQ,CAACU,GAAD,CAAZ,EAAmB;QACjB,OAAOJ,MAAM,CAACU,UAAU,CAACN,GAAG,CAACE,OAAJ,EAAD,EAAgBE,GAAhB,CAAX,CAAb;MACD,CAFD,MAEO;QACL,OAAOE,UAAU,CAACN,GAAD,EAAMI,GAAN,CAAjB;MACD;IACF,CAhBgB;IAiBjB,oBAAoBT,KAAK,CAACY,OAAN,CAAc,cAAd,EAA8BC,MAAM,IAAI,CAACR,GAAD,EAAMI,GAAN,KAAcI,MAAM,CAACR,GAAD,EAAMF,MAAM,CAACM,GAAD,CAAZ,CAA5D,CAjBH;IAkBjB,qBAAqBT,KAAK,CAACY,OAAN,CAAc,eAAd,EAA+BE,MAAM,IAAI,CAACT,GAAD,EAAMI,GAAN,KAAcK,MAAM,CAACT,GAAD,EAAMF,MAAM,CAACM,GAAD,CAAZ,CAA7D;EAlBJ,CAAP,CAAZ;EAqBA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;;EACE,SAASE,UAAT,CAAoBN,GAApB,EAAyBI,GAAzB,EAA8B;IAC5B,IAAId,QAAQ,CAACU,GAAD,CAAZ,EAAmB;MACjBA,GAAG,GAAGA,GAAG,CAACE,OAAJ,EAAN,CADiB,CACI;IACtB;;IACD,IAAI,CAACQ,KAAK,CAACC,OAAN,CAAcX,GAAd,CAAL,EAAyB;MACvB,MAAMK,UAAU,CAAC,iDAAD,CAAhB;IACD;;IACD,IAAID,GAAG,GAAG,CAAV,EAAa;MACX,IAAIQ,MAAM,GAAG,EAAb;MACAZ,GAAG,CAACa,OAAJ,CAAYC,OAAO,IAAI;QACrBF,MAAM,CAACG,IAAP,CAAYT,UAAU,CAACQ,OAAD,EAAUV,GAAG,GAAG,CAAhB,CAAtB;MACD,CAFD;MAGA,OAAOQ,MAAP;IACD,CAND,MAMO,IAAIR,GAAG,KAAK,CAAZ,EAAe;MACpB,OAAOH,KAAK,CAACD,GAAD,CAAZ;IACD,CAFM,MAEA;MACL,MAAMK,UAAU,CAAC,gCAAD,CAAhB;IACD;EACF;EAED;AACF;AACA;AACA;AACA;AACA;;;EACE,SAASJ,KAAT,CAAeD,GAAf,EAAoB;IAClB,IAAIY,MAAM,GAAG,EAAb;IACA,IAAII,IAAI,GAAGhB,GAAG,CAACiB,MAAf;;IACA,KAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGF,IAApB,EAA0BE,CAAC,EAA3B,EAA+B;MAC7BN,MAAM,CAACG,IAAP,CAAYI,YAAY,CAACnB,GAAG,CAACkB,CAAC,GAAG,CAAL,CAAJ,EAAalB,GAAG,CAACkB,CAAD,CAAhB,CAAxB;IACD;;IACD,OAAON,MAAP;EACD;EAED;AACF;AACA;AACA;AACA;AACA;AACA;;;EACE,SAASO,YAAT,CAAsBC,IAAtB,EAA4BC,IAA5B,EAAkC;IAChC;IACA,IAAI/B,QAAQ,CAAC8B,IAAD,CAAZ,EAAoBA,IAAI,GAAGA,IAAI,CAAClB,OAAL,EAAP;IACpB,IAAIZ,QAAQ,CAAC+B,IAAD,CAAZ,EAAoBA,IAAI,GAAGA,IAAI,CAACnB,OAAL,EAAP;IACpB,IAAIoB,WAAW,GAAGZ,KAAK,CAACC,OAAN,CAAcS,IAAd,CAAlB;IACA,IAAIG,WAAW,GAAGb,KAAK,CAACC,OAAN,CAAcU,IAAd,CAAlB;;IACA,IAAIC,WAAW,IAAIC,WAAnB,EAAgC;MAC9B,OAAOC,UAAU,CAACJ,IAAD,EAAOC,IAAP,CAAjB;IACD;;IACD,IAAI,CAACC,WAAD,IAAgB,CAACC,WAArB,EAAkC;MAChC,OAAO1B,QAAQ,CAACwB,IAAD,EAAOD,IAAP,CAAf,CADgC,CACH;IAC9B;;IACD,MAAMK,SAAS,CAAC,6DAAD,CAAf;EACD;EAED;AACF;AACA;AACA;AACA;AACA;AACA;;;EACE,SAASD,UAAT,CAAoBE,IAApB,EAA0BC,IAA1B,EAAgC;IAC9B,IAAID,IAAI,CAACT,MAAL,KAAgBU,IAAI,CAACV,MAAzB,EAAiC;MAC/B,MAAMZ,UAAU,CAAC,yCAAD,CAAhB;IACD;;IACD,IAAIO,MAAM,GAAG,EAAb;IACA,IAAII,IAAI,GAAGU,IAAI,CAACT,MAAhB;;IACA,KAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGF,IAApB,EAA0BE,CAAC,EAA3B,EAA+B;MAC7BN,MAAM,CAACG,IAAP,CAAYI,YAAY,CAACO,IAAI,CAACR,CAAD,CAAL,EAAUS,IAAI,CAACT,CAAD,CAAd,CAAxB;IACD;;IACD,OAAON,MAAP;EACD;AACF,CA7J6C,CAAvC"}, "metadata": {}, "sourceType": "module"}