{"ast": null, "code": "export var atan2Docs = {\n  name: 'atan2',\n  category: 'Trigonometry',\n  syntax: ['atan2(y, x)'],\n  description: 'Computes the principal value of the arc tangent of y/x in radians.',\n  examples: ['atan2(2, 2) / pi', 'angle = 60 deg in rad', 'x = cos(angle)', 'y = sin(angle)', 'atan2(y, x)'],\n  seealso: ['sin', 'cos', 'tan']\n};", "map": {"version": 3, "names": ["atan2Docs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["D:/work/joyserver/client/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/trigonometry/atan2.js"], "sourcesContent": ["export var atan2Docs = {\n  name: 'atan2',\n  category: 'Trigonometry',\n  syntax: ['atan2(y, x)'],\n  description: 'Computes the principal value of the arc tangent of y/x in radians.',\n  examples: ['atan2(2, 2) / pi', 'angle = 60 deg in rad', 'x = cos(angle)', 'y = sin(angle)', 'atan2(y, x)'],\n  seealso: ['sin', 'cos', 'tan']\n};"], "mappings": "AAAA,OAAO,IAAIA,SAAS,GAAG;EACrBC,IAAI,EAAE,OADe;EAErBC,QAAQ,EAAE,cAFW;EAGrBC,MAAM,EAAE,CAAC,aAAD,CAHa;EAIrBC,WAAW,EAAE,oEAJQ;EAKrBC,QAAQ,EAAE,CAAC,kBAAD,EAAqB,uBAArB,EAA8C,gBAA9C,EAAgE,gBAAhE,EAAkF,aAAlF,CALW;EAMrBC,OAAO,EAAE,CAAC,KAAD,EAAQ,KAAR,EAAe,KAAf;AANY,CAAhB"}, "metadata": {}, "sourceType": "module"}