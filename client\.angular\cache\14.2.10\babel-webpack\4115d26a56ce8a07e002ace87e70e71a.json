{"ast": null, "code": "ace.define(\"ace/theme/clouds\", [\"require\", \"exports\", \"module\", \"ace/lib/dom\"], function (acequire, exports, module) {\n  exports.isDark = false;\n  exports.cssClass = \"ace-clouds\";\n  exports.cssText = \".ace-clouds .ace_gutter {\\\nbackground: #ebebeb;\\\ncolor: #333\\\n}\\\n.ace-clouds .ace_print-margin {\\\nwidth: 1px;\\\nbackground: #e8e8e8\\\n}\\\n.ace-clouds {\\\nbackground-color: #FFFFFF;\\\ncolor: #000000\\\n}\\\n.ace-clouds .ace_cursor {\\\ncolor: #000000\\\n}\\\n.ace-clouds .ace_marker-layer .ace_selection {\\\nbackground: #BDD5FC\\\n}\\\n.ace-clouds.ace_multiselect .ace_selection.ace_start {\\\nbox-shadow: 0 0 3px 0px #FFFFFF;\\\n}\\\n.ace-clouds .ace_marker-layer .ace_step {\\\nbackground: rgb(255, 255, 0)\\\n}\\\n.ace-clouds .ace_marker-layer .ace_bracket {\\\nmargin: -1px 0 0 -1px;\\\nborder: 1px solid #BFBFBF\\\n}\\\n.ace-clouds .ace_marker-layer .ace_active-line {\\\nbackground: #FFFBD1\\\n}\\\n.ace-clouds .ace_gutter-active-line {\\\nbackground-color : #dcdcdc\\\n}\\\n.ace-clouds .ace_marker-layer .ace_selected-word {\\\nborder: 1px solid #BDD5FC\\\n}\\\n.ace-clouds .ace_invisible {\\\ncolor: #BFBFBF\\\n}\\\n.ace-clouds .ace_keyword,\\\n.ace-clouds .ace_meta,\\\n.ace-clouds .ace_support.ace_constant.ace_property-value {\\\ncolor: #AF956F\\\n}\\\n.ace-clouds .ace_keyword.ace_operator {\\\ncolor: #484848\\\n}\\\n.ace-clouds .ace_keyword.ace_other.ace_unit {\\\ncolor: #96DC5F\\\n}\\\n.ace-clouds .ace_constant.ace_language {\\\ncolor: #39946A\\\n}\\\n.ace-clouds .ace_constant.ace_numeric {\\\ncolor: #46A609\\\n}\\\n.ace-clouds .ace_constant.ace_character.ace_entity {\\\ncolor: #BF78CC\\\n}\\\n.ace-clouds .ace_invalid {\\\nbackground-color: #FF002A\\\n}\\\n.ace-clouds .ace_fold {\\\nbackground-color: #AF956F;\\\nborder-color: #000000\\\n}\\\n.ace-clouds .ace_storage,\\\n.ace-clouds .ace_support.ace_class,\\\n.ace-clouds .ace_support.ace_function,\\\n.ace-clouds .ace_support.ace_other,\\\n.ace-clouds .ace_support.ace_type {\\\ncolor: #C52727\\\n}\\\n.ace-clouds .ace_string {\\\ncolor: #5D90CD\\\n}\\\n.ace-clouds .ace_comment {\\\ncolor: #BCC8BA\\\n}\\\n.ace-clouds .ace_entity.ace_name.ace_tag,\\\n.ace-clouds .ace_entity.ace_other.ace_attribute-name {\\\ncolor: #606060\\\n}\\\n.ace-clouds .ace_indent-guide {\\\nbackground: url(\\\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAACCAYAAACZgbYnAAAAE0lEQVQImWP4////f4bLly//BwAmVgd1/w11/gAAAABJRU5ErkJggg==\\\") right repeat-y\\\n}\";\n  var dom = acequire(\"../lib/dom\");\n  dom.importCssString(exports.cssText, exports.cssClass);\n});", "map": {"version": 3, "names": ["ace", "define", "acequire", "exports", "module", "isDark", "cssClass", "cssText", "dom", "importCssString"], "sources": ["D:/work/joyserver/client/node_modules/brace/theme/clouds.js"], "sourcesContent": ["ace.define(\"ace/theme/clouds\",[\"require\",\"exports\",\"module\",\"ace/lib/dom\"], function(acequire, exports, module) {\n\nexports.isDark = false;\nexports.cssClass = \"ace-clouds\";\nexports.cssText = \".ace-clouds .ace_gutter {\\\nbackground: #ebebeb;\\\ncolor: #333\\\n}\\\n.ace-clouds .ace_print-margin {\\\nwidth: 1px;\\\nbackground: #e8e8e8\\\n}\\\n.ace-clouds {\\\nbackground-color: #FFFFFF;\\\ncolor: #000000\\\n}\\\n.ace-clouds .ace_cursor {\\\ncolor: #000000\\\n}\\\n.ace-clouds .ace_marker-layer .ace_selection {\\\nbackground: #BDD5FC\\\n}\\\n.ace-clouds.ace_multiselect .ace_selection.ace_start {\\\nbox-shadow: 0 0 3px 0px #FFFFFF;\\\n}\\\n.ace-clouds .ace_marker-layer .ace_step {\\\nbackground: rgb(255, 255, 0)\\\n}\\\n.ace-clouds .ace_marker-layer .ace_bracket {\\\nmargin: -1px 0 0 -1px;\\\nborder: 1px solid #BFBFBF\\\n}\\\n.ace-clouds .ace_marker-layer .ace_active-line {\\\nbackground: #FFFBD1\\\n}\\\n.ace-clouds .ace_gutter-active-line {\\\nbackground-color : #dcdcdc\\\n}\\\n.ace-clouds .ace_marker-layer .ace_selected-word {\\\nborder: 1px solid #BDD5FC\\\n}\\\n.ace-clouds .ace_invisible {\\\ncolor: #BFBFBF\\\n}\\\n.ace-clouds .ace_keyword,\\\n.ace-clouds .ace_meta,\\\n.ace-clouds .ace_support.ace_constant.ace_property-value {\\\ncolor: #AF956F\\\n}\\\n.ace-clouds .ace_keyword.ace_operator {\\\ncolor: #484848\\\n}\\\n.ace-clouds .ace_keyword.ace_other.ace_unit {\\\ncolor: #96DC5F\\\n}\\\n.ace-clouds .ace_constant.ace_language {\\\ncolor: #39946A\\\n}\\\n.ace-clouds .ace_constant.ace_numeric {\\\ncolor: #46A609\\\n}\\\n.ace-clouds .ace_constant.ace_character.ace_entity {\\\ncolor: #BF78CC\\\n}\\\n.ace-clouds .ace_invalid {\\\nbackground-color: #FF002A\\\n}\\\n.ace-clouds .ace_fold {\\\nbackground-color: #AF956F;\\\nborder-color: #000000\\\n}\\\n.ace-clouds .ace_storage,\\\n.ace-clouds .ace_support.ace_class,\\\n.ace-clouds .ace_support.ace_function,\\\n.ace-clouds .ace_support.ace_other,\\\n.ace-clouds .ace_support.ace_type {\\\ncolor: #C52727\\\n}\\\n.ace-clouds .ace_string {\\\ncolor: #5D90CD\\\n}\\\n.ace-clouds .ace_comment {\\\ncolor: #BCC8BA\\\n}\\\n.ace-clouds .ace_entity.ace_name.ace_tag,\\\n.ace-clouds .ace_entity.ace_other.ace_attribute-name {\\\ncolor: #606060\\\n}\\\n.ace-clouds .ace_indent-guide {\\\nbackground: url(\\\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAACCAYAAACZgbYnAAAAE0lEQVQImWP4////f4bLly//BwAmVgd1/w11/gAAAABJRU5ErkJggg==\\\") right repeat-y\\\n}\";\n\nvar dom = acequire(\"../lib/dom\");\ndom.importCssString(exports.cssText, exports.cssClass);\n});\n"], "mappings": "AAAAA,GAAG,CAACC,MAAJ,CAAW,kBAAX,EAA8B,CAAC,SAAD,EAAW,SAAX,EAAqB,QAArB,EAA8B,aAA9B,CAA9B,EAA4E,UAASC,QAAT,EAAmBC,OAAnB,EAA4BC,MAA5B,EAAoC;EAEhHD,OAAO,CAACE,MAAR,GAAiB,KAAjB;EACAF,OAAO,CAACG,QAAR,GAAmB,YAAnB;EACAH,OAAO,CAACI,OAAR,GAAkB;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAtFA;EAwFA,IAAIC,GAAG,GAAGN,QAAQ,CAAC,YAAD,CAAlB;EACAM,GAAG,CAACC,eAAJ,CAAoBN,OAAO,CAACI,OAA5B,EAAqCJ,OAAO,CAACG,QAA7C;AACC,CA9FD"}, "metadata": {}, "sourceType": "script"}