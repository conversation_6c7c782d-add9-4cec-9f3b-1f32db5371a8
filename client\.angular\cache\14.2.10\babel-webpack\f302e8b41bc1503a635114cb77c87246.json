{"ast": null, "code": "import { factory } from '../utils/factory.js';\nvar name = 'reviver';\nvar dependencies = ['classes'];\nexport var createReviver = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    classes\n  } = _ref;\n  /**\n   * Instantiate mathjs data types from their JSON representation\n   * @param {string} key\n   * @param {*} value\n   * @returns {*} Returns the revived object\n   */\n\n  return function reviver(key, value) {\n    var constructor = classes[value && value.mathjs];\n\n    if (constructor && typeof constructor.fromJSON === 'function') {\n      return constructor.fromJSON(value);\n    }\n\n    return value;\n  };\n});", "map": null, "metadata": {}, "sourceType": "module"}