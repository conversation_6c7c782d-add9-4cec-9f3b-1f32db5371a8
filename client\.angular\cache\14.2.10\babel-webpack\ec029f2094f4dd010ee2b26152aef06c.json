{"ast": null, "code": "import { factory } from '../../utils/factory.js';\nvar name = 'matrixFromColumns';\nvar dependencies = ['typed', 'matrix', 'flatten', 'size'];\nexport var createMatrixFromColumns = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed,\n    matrix,\n    flatten,\n    size\n  } = _ref;\n  /**\n   * Create a dense matrix from vectors as individual columns.\n   * If you pass row vectors, they will be transposed (but not conjugated!)\n   *\n   * Syntax:\n   *\n   *    math.matrixFromColumns(...arr)\n   *    math.matrixFromColumns(col1, col2)\n   *    math.matrixFromColumns(col1, col2, col3)\n   *\n   * Examples:\n   *\n   *    math.matrixFromColumns([1, 2, 3], [[4],[5],[6]])\n   *    math.matrixFromColumns(...vectors)\n   *\n   * See also:\n   *\n   *    matrix, matrixFromRows, matrixFromFunction, zeros\n   *\n   * @param {... Array | Matrix} cols Multiple columns\n   * @return { number[][] | Matrix } if at least one of the arguments is an array, an array will be returned\n   */\n\n  return typed(name, {\n    '...Array': function Array(arr) {\n      return _createArray(arr);\n    },\n    '...Matrix': function Matrix(arr) {\n      return matrix(_createArray(arr.map(m => m.toArray())));\n    } // TODO implement this properly for SparseMatrix\n\n  });\n\n  function _createArray(arr) {\n    if (arr.length === 0) throw new TypeError('At least one column is needed to construct a matrix.');\n    var N = checkVectorTypeAndReturnLength(arr[0]); // create an array with empty rows\n\n    var result = [];\n\n    for (var i = 0; i < N; i++) {\n      result[i] = [];\n    } // loop columns\n\n\n    for (var col of arr) {\n      var colLength = checkVectorTypeAndReturnLength(col);\n\n      if (colLength !== N) {\n        throw new TypeError('The vectors had different length: ' + (N | 0) + ' ≠ ' + (colLength | 0));\n      }\n\n      var f = flatten(col); // push a value to each row\n\n      for (var _i = 0; _i < N; _i++) {\n        result[_i].push(f[_i]);\n      }\n    }\n\n    return result;\n  }\n\n  function checkVectorTypeAndReturnLength(vec) {\n    var s = size(vec);\n\n    if (s.length === 1) {\n      // 1D vector\n      return s[0];\n    } else if (s.length === 2) {\n      // 2D vector\n      if (s[0] === 1) {\n        // row vector\n        return s[1];\n      } else if (s[1] === 1) {\n        // col vector\n        return s[0];\n      } else {\n        throw new TypeError('At least one of the arguments is not a vector.');\n      }\n    } else {\n      throw new TypeError('Only one- or two-dimensional vectors are supported.');\n    }\n  }\n});", "map": null, "metadata": {}, "sourceType": "module"}