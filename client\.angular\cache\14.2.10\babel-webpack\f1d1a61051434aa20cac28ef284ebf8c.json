{"ast": null, "code": "/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { BigNumberDependencies } from './dependenciesBigNumberClass.generated.js';\nimport { ComplexDependencies } from './dependenciesComplexClass.generated.js';\nimport { FractionDependencies } from './dependenciesFractionClass.generated.js';\nimport { absDependencies } from './dependenciesAbs.generated.js';\nimport { addScalarDependencies } from './dependenciesAddScalar.generated.js';\nimport { divideScalarDependencies } from './dependenciesDivideScalar.generated.js';\nimport { equalDependencies } from './dependenciesEqual.generated.js';\nimport { fixDependencies } from './dependenciesFix.generated.js';\nimport { formatDependencies } from './dependenciesFormat.generated.js';\nimport { isNumericDependencies } from './dependenciesIsNumeric.generated.js';\nimport { multiplyScalarDependencies } from './dependenciesMultiplyScalar.generated.js';\nimport { numberDependencies } from './dependenciesNumber.generated.js';\nimport { powDependencies } from './dependenciesPow.generated.js';\nimport { roundDependencies } from './dependenciesRound.generated.js';\nimport { subtractScalarDependencies } from './dependenciesSubtractScalar.generated.js';\nimport { createUnitClass } from '../../factoriesAny.js';\nexport var UnitDependencies = {\n  BigNumberDependencies,\n  ComplexDependencies,\n  FractionDependencies,\n  absDependencies,\n  addScalarDependencies,\n  divideScalarDependencies,\n  equalDependencies,\n  fixDependencies,\n  formatDependencies,\n  isNumericDependencies,\n  multiplyScalarDependencies,\n  numberDependencies,\n  powDependencies,\n  roundDependencies,\n  subtractScalarDependencies,\n  createUnitClass\n};", "map": {"version": 3, "names": ["BigNumberDependencies", "ComplexDependencies", "FractionDependencies", "absDependencies", "addScalarDependencies", "divideScalarDependencies", "equalDependencies", "fixDependencies", "formatDependencies", "isNumericDependencies", "multiplyScalarDependencies", "numberDependencies", "powDependencies", "roundDependencies", "subtractScalarDependencies", "createUnitClass", "UnitDependencies"], "sources": ["D:/work/joyserver/client/node_modules/mathjs/lib/esm/entry/dependenciesAny/dependenciesUnitClass.generated.js"], "sourcesContent": ["/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { BigNumberDependencies } from './dependenciesBigNumberClass.generated.js';\nimport { ComplexDependencies } from './dependenciesComplexClass.generated.js';\nimport { FractionDependencies } from './dependenciesFractionClass.generated.js';\nimport { absDependencies } from './dependenciesAbs.generated.js';\nimport { addScalarDependencies } from './dependenciesAddScalar.generated.js';\nimport { divideScalarDependencies } from './dependenciesDivideScalar.generated.js';\nimport { equalDependencies } from './dependenciesEqual.generated.js';\nimport { fixDependencies } from './dependenciesFix.generated.js';\nimport { formatDependencies } from './dependenciesFormat.generated.js';\nimport { isNumericDependencies } from './dependenciesIsNumeric.generated.js';\nimport { multiplyScalarDependencies } from './dependenciesMultiplyScalar.generated.js';\nimport { numberDependencies } from './dependenciesNumber.generated.js';\nimport { powDependencies } from './dependenciesPow.generated.js';\nimport { roundDependencies } from './dependenciesRound.generated.js';\nimport { subtractScalarDependencies } from './dependenciesSubtractScalar.generated.js';\nimport { createUnitClass } from '../../factoriesAny.js';\nexport var UnitDependencies = {\n  BigNumberDependencies,\n  ComplexDependencies,\n  FractionDependencies,\n  absDependencies,\n  addScalarDependencies,\n  divideScalarDependencies,\n  equalDependencies,\n  fixDependencies,\n  formatDependencies,\n  isNumericDependencies,\n  multiplyScalarDependencies,\n  numberDependencies,\n  powDependencies,\n  roundDependencies,\n  subtractScalarDependencies,\n  createUnitClass\n};"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,qBAAT,QAAsC,2CAAtC;AACA,SAASC,mBAAT,QAAoC,yCAApC;AACA,SAASC,oBAAT,QAAqC,0CAArC;AACA,SAASC,eAAT,QAAgC,gCAAhC;AACA,SAASC,qBAAT,QAAsC,sCAAtC;AACA,SAASC,wBAAT,QAAyC,yCAAzC;AACA,SAASC,iBAAT,QAAkC,kCAAlC;AACA,SAASC,eAAT,QAAgC,gCAAhC;AACA,SAASC,kBAAT,QAAmC,mCAAnC;AACA,SAASC,qBAAT,QAAsC,sCAAtC;AACA,SAASC,0BAAT,QAA2C,2CAA3C;AACA,SAASC,kBAAT,QAAmC,mCAAnC;AACA,SAASC,eAAT,QAAgC,gCAAhC;AACA,SAASC,iBAAT,QAAkC,kCAAlC;AACA,SAASC,0BAAT,QAA2C,2CAA3C;AACA,SAASC,eAAT,QAAgC,uBAAhC;AACA,OAAO,IAAIC,gBAAgB,GAAG;EAC5BhB,qBAD4B;EAE5BC,mBAF4B;EAG5BC,oBAH4B;EAI5BC,eAJ4B;EAK5BC,qBAL4B;EAM5BC,wBAN4B;EAO5BC,iBAP4B;EAQ5BC,eAR4B;EAS5BC,kBAT4B;EAU5BC,qBAV4B;EAW5BC,0BAX4B;EAY5BC,kBAZ4B;EAa5BC,eAb4B;EAc5BC,iBAd4B;EAe5BC,0BAf4B;EAgB5BC;AAhB4B,CAAvB"}, "metadata": {}, "sourceType": "module"}