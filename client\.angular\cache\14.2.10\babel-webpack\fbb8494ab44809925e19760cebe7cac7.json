{"ast": null, "code": "import { Observable } from '../Observable';\nimport { asap } from '../scheduler/asap';\nimport { isNumeric } from '../util/isNumeric';\nexport class SubscribeOnObservable extends Observable {\n  constructor(source, delayTime = 0, scheduler = asap) {\n    super();\n    this.source = source;\n    this.delayTime = delayTime;\n    this.scheduler = scheduler;\n\n    if (!isNumeric(delayTime) || delayTime < 0) {\n      this.delayTime = 0;\n    }\n\n    if (!scheduler || typeof scheduler.schedule !== 'function') {\n      this.scheduler = asap;\n    }\n  }\n\n  static create(source, delay = 0, scheduler = asap) {\n    return new SubscribeOnObservable(source, delay, scheduler);\n  }\n\n  static dispatch(arg) {\n    const {\n      source,\n      subscriber\n    } = arg;\n    return this.add(source.subscribe(subscriber));\n  }\n\n  _subscribe(subscriber) {\n    const delay = this.delayTime;\n    const source = this.source;\n    const scheduler = this.scheduler;\n    return scheduler.schedule(SubscribeOnObservable.dispatch, delay, {\n      source,\n      subscriber\n    });\n  }\n\n} //# sourceMappingURL=SubscribeOnObservable.js.map", "map": null, "metadata": {}, "sourceType": "module"}