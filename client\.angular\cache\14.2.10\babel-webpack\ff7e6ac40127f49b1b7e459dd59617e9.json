{"ast": null, "code": "import { factory } from '../../utils/factory.js';\nvar name = 'composition';\nvar dependencies = ['typed', 'addScalar', 'combinations', 'isNegative', 'isPositive', 'isInteger', 'larger'];\nexport var createComposition = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed,\n    addScalar,\n    combinations,\n    isPositive,\n    isNegative,\n    isInteger,\n    larger\n  } = _ref;\n  /**\n   * The composition counts of n into k parts.\n   *\n   * composition only takes integer arguments.\n   * The following condition must be enforced: k <= n.\n   *\n   * Syntax:\n   *\n   *   math.composition(n, k)\n   *\n   * Examples:\n   *\n   *    math.composition(5, 3) // returns 6\n   *\n   * See also:\n   *\n   *    combinations\n   *\n   * @param {Number | BigNumber} n    Total number of objects in the set\n   * @param {Number | BigNumber} k    Number of objects in the subset\n   * @return {Number | BigNumber}     Returns the composition counts of n into k parts.\n   */\n\n  return typed(name, {\n    'number | BigNumber, number | BigNumber': function number__BigNumber_number__BigNumber(n, k) {\n      if (!isInteger(n) || !isPositive(n) || !isInteger(k) || !isPositive(k)) {\n        throw new TypeError('Positive integer value expected in function composition');\n      } else if (larger(k, n)) {\n        throw new TypeError('k must be less than or equal to n in function composition');\n      }\n\n      return combinations(addScalar(n, -1), addScalar(k, -1));\n    }\n  });\n});", "map": {"version": 3, "names": ["factory", "name", "dependencies", "createComposition", "_ref", "typed", "addScalar", "combinations", "isPositive", "isNegative", "isInteger", "larger", "number__BigNumber_number__BigNumber", "n", "k", "TypeError"], "sources": ["D:/work/joyserver/client/node_modules/mathjs/lib/esm/function/combinatorics/composition.js"], "sourcesContent": ["import { factory } from '../../utils/factory.js';\nvar name = 'composition';\nvar dependencies = ['typed', 'addScalar', 'combinations', 'isNegative', 'isPositive', 'isInteger', 'larger'];\nexport var createComposition = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed,\n    addScalar,\n    combinations,\n    isPositive,\n    isNegative,\n    isInteger,\n    larger\n  } = _ref;\n  /**\n   * The composition counts of n into k parts.\n   *\n   * composition only takes integer arguments.\n   * The following condition must be enforced: k <= n.\n   *\n   * Syntax:\n   *\n   *   math.composition(n, k)\n   *\n   * Examples:\n   *\n   *    math.composition(5, 3) // returns 6\n   *\n   * See also:\n   *\n   *    combinations\n   *\n   * @param {Number | BigNumber} n    Total number of objects in the set\n   * @param {Number | BigNumber} k    Number of objects in the subset\n   * @return {Number | BigNumber}     Returns the composition counts of n into k parts.\n   */\n  return typed(name, {\n    'number | BigNumber, number | BigNumber': function number__BigNumber_number__BigNumber(n, k) {\n      if (!isInteger(n) || !isPositive(n) || !isInteger(k) || !isPositive(k)) {\n        throw new TypeError('Positive integer value expected in function composition');\n      } else if (larger(k, n)) {\n        throw new TypeError('k must be less than or equal to n in function composition');\n      }\n      return combinations(addScalar(n, -1), addScalar(k, -1));\n    }\n  });\n});"], "mappings": "AAAA,SAASA,OAAT,QAAwB,wBAAxB;AACA,IAAIC,IAAI,GAAG,aAAX;AACA,IAAIC,YAAY,GAAG,CAAC,OAAD,EAAU,WAAV,EAAuB,cAAvB,EAAuC,YAAvC,EAAqD,YAArD,EAAmE,WAAnE,EAAgF,QAAhF,CAAnB;AACA,OAAO,IAAIC,iBAAiB,GAAG,eAAeH,OAAO,CAACC,IAAD,EAAOC,YAAP,EAAqBE,IAAI,IAAI;EAChF,IAAI;IACFC,KADE;IAEFC,SAFE;IAGFC,YAHE;IAIFC,UAJE;IAKFC,UALE;IAMFC,SANE;IAOFC;EAPE,IAQAP,IARJ;EASA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EACE,OAAOC,KAAK,CAACJ,IAAD,EAAO;IACjB,0CAA0C,SAASW,mCAAT,CAA6CC,CAA7C,EAAgDC,CAAhD,EAAmD;MAC3F,IAAI,CAACJ,SAAS,CAACG,CAAD,CAAV,IAAiB,CAACL,UAAU,CAACK,CAAD,CAA5B,IAAmC,CAACH,SAAS,CAACI,CAAD,CAA7C,IAAoD,CAACN,UAAU,CAACM,CAAD,CAAnE,EAAwE;QACtE,MAAM,IAAIC,SAAJ,CAAc,yDAAd,CAAN;MACD,CAFD,MAEO,IAAIJ,MAAM,CAACG,CAAD,EAAID,CAAJ,CAAV,EAAkB;QACvB,MAAM,IAAIE,SAAJ,CAAc,2DAAd,CAAN;MACD;;MACD,OAAOR,YAAY,CAACD,SAAS,CAACO,CAAD,EAAI,CAAC,CAAL,CAAV,EAAmBP,SAAS,CAACQ,CAAD,EAAI,CAAC,CAAL,CAA5B,CAAnB;IACD;EARgB,CAAP,CAAZ;AAUD,CA1CoD,CAA9C"}, "metadata": {}, "sourceType": "module"}