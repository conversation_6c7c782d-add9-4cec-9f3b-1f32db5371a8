{"ast": null, "code": "import { factory } from '../../utils/factory.js';\nimport { isMatrix } from '../../utils/is.js';\nvar name = 'dot';\nvar dependencies = ['typed', 'addScalar', 'multiplyScalar', 'conj', 'size'];\nexport var createDot = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed,\n    addScalar,\n    multiplyScalar,\n    conj,\n    size\n  } = _ref;\n  /**\n   * Calculate the dot product of two vectors. The dot product of\n   * `A = [a1, a2, ..., an]` and `B = [b1, b2, ..., bn]` is defined as:\n   *\n   *    dot(A, B) = conj(a1) * b1 + conj(a2) * b2 + ... + conj(an) * bn\n   *\n   * Syntax:\n   *\n   *    math.dot(x, y)\n   *\n   * Examples:\n   *\n   *    math.dot([2, 4, 1], [2, 2, 3])       // returns number 15\n   *    math.multiply([2, 4, 1], [2, 2, 3])  // returns number 15\n   *\n   * See also:\n   *\n   *    multiply, cross\n   *\n   * @param  {Array | Matrix} x     First vector\n   * @param  {Array | Matrix} y     Second vector\n   * @return {number}               Returns the dot product of `x` and `y`\n   */\n\n  return typed(name, {\n    'Array | DenseMatrix, Array | DenseMatrix': _denseDot,\n    'SparseMatrix, SparseMatrix': _sparseDot\n  });\n\n  function _validateDim(x, y) {\n    var xSize = _size(x);\n\n    var ySize = _size(y);\n\n    var xLen, yLen;\n\n    if (xSize.length === 1) {\n      xLen = xSize[0];\n    } else if (xSize.length === 2 && xSize[1] === 1) {\n      xLen = xSize[0];\n    } else {\n      throw new RangeError('Expected a column vector, instead got a matrix of size (' + xSize.join(', ') + ')');\n    }\n\n    if (ySize.length === 1) {\n      yLen = ySize[0];\n    } else if (ySize.length === 2 && ySize[1] === 1) {\n      yLen = ySize[0];\n    } else {\n      throw new RangeError('Expected a column vector, instead got a matrix of size (' + ySize.join(', ') + ')');\n    }\n\n    if (xLen !== yLen) throw new RangeError('Vectors must have equal length (' + xLen + ' != ' + yLen + ')');\n    if (xLen === 0) throw new RangeError('Cannot calculate the dot product of empty vectors');\n    return xLen;\n  }\n\n  function _denseDot(a, b) {\n    var N = _validateDim(a, b);\n\n    var adata = isMatrix(a) ? a._data : a;\n    var adt = isMatrix(a) ? a._datatype || a.getDataType() : undefined;\n    var bdata = isMatrix(b) ? b._data : b;\n    var bdt = isMatrix(b) ? b._datatype || b.getDataType() : undefined; // are these 2-dimensional column vectors? (as opposed to 1-dimensional vectors)\n\n    var aIsColumn = _size(a).length === 2;\n    var bIsColumn = _size(b).length === 2;\n    var add = addScalar;\n    var mul = multiplyScalar; // process data types\n\n    if (adt && bdt && adt === bdt && typeof adt === 'string' && adt !== 'mixed') {\n      var dt = adt; // find signatures that matches (dt, dt)\n\n      add = typed.find(addScalar, [dt, dt]);\n      mul = typed.find(multiplyScalar, [dt, dt]);\n    } // both vectors 1-dimensional\n\n\n    if (!aIsColumn && !bIsColumn) {\n      var c = mul(conj(adata[0]), bdata[0]);\n\n      for (var i = 1; i < N; i++) {\n        c = add(c, mul(conj(adata[i]), bdata[i]));\n      }\n\n      return c;\n    } // a is 1-dim, b is column\n\n\n    if (!aIsColumn && bIsColumn) {\n      var _c = mul(conj(adata[0]), bdata[0][0]);\n\n      for (var _i = 1; _i < N; _i++) {\n        _c = add(_c, mul(conj(adata[_i]), bdata[_i][0]));\n      }\n\n      return _c;\n    } // a is column, b is 1-dim\n\n\n    if (aIsColumn && !bIsColumn) {\n      var _c2 = mul(conj(adata[0][0]), bdata[0]);\n\n      for (var _i2 = 1; _i2 < N; _i2++) {\n        _c2 = add(_c2, mul(conj(adata[_i2][0]), bdata[_i2]));\n      }\n\n      return _c2;\n    } // both vectors are column\n\n\n    if (aIsColumn && bIsColumn) {\n      var _c3 = mul(conj(adata[0][0]), bdata[0][0]);\n\n      for (var _i3 = 1; _i3 < N; _i3++) {\n        _c3 = add(_c3, mul(conj(adata[_i3][0]), bdata[_i3][0]));\n      }\n\n      return _c3;\n    }\n  }\n\n  function _sparseDot(x, y) {\n    _validateDim(x, y);\n\n    var xindex = x._index;\n    var xvalues = x._values;\n    var yindex = y._index;\n    var yvalues = y._values; // TODO optimize add & mul using datatype\n\n    var c = 0;\n    var add = addScalar;\n    var mul = multiplyScalar;\n    var i = 0;\n    var j = 0;\n\n    while (i < xindex.length && j < yindex.length) {\n      var I = xindex[i];\n      var J = yindex[j];\n\n      if (I < J) {\n        i++;\n        continue;\n      }\n\n      if (I > J) {\n        j++;\n        continue;\n      }\n\n      if (I === J) {\n        c = add(c, mul(xvalues[i], yvalues[j]));\n        i++;\n        j++;\n      }\n    }\n\n    return c;\n  } // TODO remove this once #1771 is fixed\n\n\n  function _size(x) {\n    return isMatrix(x) ? x.size() : size(x);\n  }\n});", "map": null, "metadata": {}, "sourceType": "module"}