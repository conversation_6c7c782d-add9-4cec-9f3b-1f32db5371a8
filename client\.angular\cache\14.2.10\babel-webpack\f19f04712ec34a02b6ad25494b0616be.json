{"ast": null, "code": "export var filterDocs = {\n  name: 'filter',\n  category: 'Matrix',\n  syntax: ['filter(x, test)'],\n  description: 'Filter items in a matrix.',\n  examples: ['isPositive(x) = x > 0', 'filter([6, -2, -1, 4, 3], isPositive)', 'filter([6, -2, 0, 1, 0], x != 0)'],\n  seealso: ['sort', 'map', 'forEach']\n};", "map": {"version": 3, "names": ["filterDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["D:/work/joyserver/client/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/matrix/filter.js"], "sourcesContent": ["export var filterDocs = {\n  name: 'filter',\n  category: 'Matrix',\n  syntax: ['filter(x, test)'],\n  description: 'Filter items in a matrix.',\n  examples: ['isPositive(x) = x > 0', 'filter([6, -2, -1, 4, 3], isPositive)', 'filter([6, -2, 0, 1, 0], x != 0)'],\n  seealso: ['sort', 'map', 'forEach']\n};"], "mappings": "AAAA,OAAO,IAAIA,UAAU,GAAG;EACtBC,IAAI,EAAE,QADgB;EAEtBC,QAAQ,EAAE,QAFY;EAGtBC,MAAM,EAAE,CAAC,iBAAD,CAHc;EAItBC,WAAW,EAAE,2BAJS;EAKtBC,QAAQ,EAAE,CAAC,uBAAD,EAA0B,uCAA1B,EAAmE,kCAAnE,CALY;EAMtBC,OAAO,EAAE,CAAC,MAAD,EAAS,KAAT,EAAgB,SAAhB;AANa,CAAjB"}, "metadata": {}, "sourceType": "module"}