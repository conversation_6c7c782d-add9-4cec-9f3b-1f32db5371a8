{"ast": null, "code": "'use strict';\n/**\n *\n * This class offers the possibility to calculate fractions.\n * You can pass a fraction in different formats. Either as array, as double, as string or as an integer.\n *\n * Array/Object form\n * [ 0 => <numerator>, 1 => <denominator> ]\n * { n => <numerator>, d => <denominator> }\n *\n * Integer form\n * - Single integer value as BigInt or Number\n *\n * Double form\n * - Single double value as Number\n *\n * String form\n * 123.456 - a simple double\n * 123/456 - a string fraction\n * 123.'456' - a double with repeating decimal places\n * 123.(456) - synonym\n * 123.45'6' - a double with repeating last place\n * 123.45(6) - synonym\n *\n * Example:\n * let f = new Fraction(\"9.4'31'\");\n * f.mul([-4, 3]).div(4.9);\n *\n */\n// Set Identity function to downgrade BigInt to Number if needed\n\nif (typeof BigInt === 'undefined') BigInt = function (n) {\n  if (isNaN(n)) throw new Error(\"\");\n  return n;\n};\nconst C_ZERO = BigInt(0);\nconst C_ONE = BigInt(1);\nconst C_TWO = BigInt(2);\nconst C_FIVE = BigInt(5);\nconst C_TEN = BigInt(10); // Maximum search depth for cyclic rational numbers. 2000 should be more than enough.\n// Example: 1/7 = 0.(142857) has 6 repeating decimal places.\n// If MAX_CYCLE_LEN gets reduced, long cycles will not be detected and toString() only gets the first 10 digits\n\nconst MAX_CYCLE_LEN = 2000; // Parsed data to avoid calling \"new\" all the time\n\nconst P = {\n  \"s\": C_ONE,\n  \"n\": C_ZERO,\n  \"d\": C_ONE\n};\n\nfunction assign(n, s) {\n  try {\n    n = BigInt(n);\n  } catch (e) {\n    throw InvalidParameter();\n  }\n\n  return n * s;\n}\n\nfunction trunc(x) {\n  return typeof x === 'bigint' ? x : Math.floor(x);\n} // Creates a new Fraction internally without the need of the bulky constructor\n\n\nfunction newFraction(n, d) {\n  if (d === C_ZERO) {\n    throw DivisionByZero();\n  }\n\n  const f = Object.create(Fraction.prototype);\n  f[\"s\"] = n < C_ZERO ? -C_ONE : C_ONE;\n  n = n < C_ZERO ? -n : n;\n  const a = gcd(n, d);\n  f[\"n\"] = n / a;\n  f[\"d\"] = d / a;\n  return f;\n}\n\nfunction factorize(num) {\n  const factors = {};\n  let n = num;\n  let i = C_TWO;\n  let s = C_FIVE - C_ONE;\n\n  while (s <= n) {\n    while (n % i === C_ZERO) {\n      n /= i;\n      factors[i] = (factors[i] || C_ZERO) + C_ONE;\n    }\n\n    s += C_ONE + C_TWO * i++;\n  }\n\n  if (n !== num) {\n    if (n > 1) factors[n] = (factors[n] || C_ZERO) + C_ONE;\n  } else {\n    factors[num] = (factors[num] || C_ZERO) + C_ONE;\n  }\n\n  return factors;\n}\n\nconst parse = function (p1, p2) {\n  let n = C_ZERO,\n      d = C_ONE,\n      s = C_ONE;\n\n  if (p1 === undefined || p1 === null) {// No argument\n\n    /* void */\n  } else if (p2 !== undefined) {\n    // Two arguments\n    if (typeof p1 === \"bigint\") {\n      n = p1;\n    } else if (isNaN(p1)) {\n      throw InvalidParameter();\n    } else if (p1 % 1 !== 0) {\n      throw NonIntegerParameter();\n    } else {\n      n = BigInt(p1);\n    }\n\n    if (typeof p2 === \"bigint\") {\n      d = p2;\n    } else if (isNaN(p2)) {\n      throw InvalidParameter();\n    } else if (p2 % 1 !== 0) {\n      throw NonIntegerParameter();\n    } else {\n      d = BigInt(p2);\n    }\n\n    s = n * d;\n  } else if (typeof p1 === \"object\") {\n    if (\"d\" in p1 && \"n\" in p1) {\n      n = BigInt(p1[\"n\"]);\n      d = BigInt(p1[\"d\"]);\n      if (\"s\" in p1) n *= BigInt(p1[\"s\"]);\n    } else if (0 in p1) {\n      n = BigInt(p1[0]);\n      if (1 in p1) d = BigInt(p1[1]);\n    } else if (typeof p1 === \"bigint\") {\n      n = p1;\n    } else {\n      throw InvalidParameter();\n    }\n\n    s = n * d;\n  } else if (typeof p1 === \"number\") {\n    if (isNaN(p1)) {\n      throw InvalidParameter();\n    }\n\n    if (p1 < 0) {\n      s = -C_ONE;\n      p1 = -p1;\n    }\n\n    if (p1 % 1 === 0) {\n      n = BigInt(p1);\n    } else if (p1 > 0) {\n      // check for != 0, scale would become NaN (log(0)), which converges really slow\n      let z = 1;\n      let A = 0,\n          B = 1;\n      let C = 1,\n          D = 1;\n      let N = 10000000;\n\n      if (p1 >= 1) {\n        z = 10 ** Math.floor(1 + Math.log10(p1));\n        p1 /= z;\n      } // Using Farey Sequences\n\n\n      while (B <= N && D <= N) {\n        let M = (A + C) / (B + D);\n\n        if (p1 === M) {\n          if (B + D <= N) {\n            n = A + C;\n            d = B + D;\n          } else if (D > B) {\n            n = C;\n            d = D;\n          } else {\n            n = A;\n            d = B;\n          }\n\n          break;\n        } else {\n          if (p1 > M) {\n            A += C;\n            B += D;\n          } else {\n            C += A;\n            D += B;\n          }\n\n          if (B > N) {\n            n = C;\n            d = D;\n          } else {\n            n = A;\n            d = B;\n          }\n        }\n      }\n\n      n = BigInt(n) * BigInt(z);\n      d = BigInt(d);\n    }\n  } else if (typeof p1 === \"string\") {\n    let ndx = 0;\n    let v = C_ZERO,\n        w = C_ZERO,\n        x = C_ZERO,\n        y = C_ONE,\n        z = C_ONE;\n    let match = p1.replace(/_/g, '').match(/\\d+|./g);\n    if (match === null) throw InvalidParameter();\n\n    if (match[ndx] === '-') {\n      // Check for minus sign at the beginning\n      s = -C_ONE;\n      ndx++;\n    } else if (match[ndx] === '+') {\n      // Check for plus sign at the beginning\n      ndx++;\n    }\n\n    if (match.length === ndx + 1) {\n      // Check if it's just a simple number \"1234\"\n      w = assign(match[ndx++], s);\n    } else if (match[ndx + 1] === '.' || match[ndx] === '.') {\n      // Check if it's a decimal number\n      if (match[ndx] !== '.') {\n        // Handle 0.5 and .5\n        v = assign(match[ndx++], s);\n      }\n\n      ndx++; // Check for decimal places\n\n      if (ndx + 1 === match.length || match[ndx + 1] === '(' && match[ndx + 3] === ')' || match[ndx + 1] === \"'\" && match[ndx + 3] === \"'\") {\n        w = assign(match[ndx], s);\n        y = C_TEN ** BigInt(match[ndx].length);\n        ndx++;\n      } // Check for repeating places\n\n\n      if (match[ndx] === '(' && match[ndx + 2] === ')' || match[ndx] === \"'\" && match[ndx + 2] === \"'\") {\n        x = assign(match[ndx + 1], s);\n        z = C_TEN ** BigInt(match[ndx + 1].length) - C_ONE;\n        ndx += 3;\n      }\n    } else if (match[ndx + 1] === '/' || match[ndx + 1] === ':') {\n      // Check for a simple fraction \"123/456\" or \"123:456\"\n      w = assign(match[ndx], s);\n      y = assign(match[ndx + 2], C_ONE);\n      ndx += 3;\n    } else if (match[ndx + 3] === '/' && match[ndx + 1] === ' ') {\n      // Check for a complex fraction \"123 1/2\"\n      v = assign(match[ndx], s);\n      w = assign(match[ndx + 2], s);\n      y = assign(match[ndx + 4], C_ONE);\n      ndx += 5;\n    }\n\n    if (match.length <= ndx) {\n      // Check for more tokens on the stack\n      d = y * z;\n      s =\n      /* void */\n      n = x + d * v + z * w;\n    } else {\n      throw InvalidParameter();\n    }\n  } else if (typeof p1 === \"bigint\") {\n    n = p1;\n    s = p1;\n    d = C_ONE;\n  } else {\n    throw InvalidParameter();\n  }\n\n  if (d === C_ZERO) {\n    throw DivisionByZero();\n  }\n\n  P[\"s\"] = s < C_ZERO ? -C_ONE : C_ONE;\n  P[\"n\"] = n < C_ZERO ? -n : n;\n  P[\"d\"] = d < C_ZERO ? -d : d;\n};\n\nfunction modpow(b, e, m) {\n  let r = C_ONE;\n\n  for (; e > C_ZERO; b = b * b % m, e >>= C_ONE) {\n    if (e & C_ONE) {\n      r = r * b % m;\n    }\n  }\n\n  return r;\n}\n\nfunction cycleLen(n, d) {\n  for (; d % C_TWO === C_ZERO; d /= C_TWO) {}\n\n  for (; d % C_FIVE === C_ZERO; d /= C_FIVE) {}\n\n  if (d === C_ONE) // Catch non-cyclic numbers\n    return C_ZERO; // If we would like to compute really large numbers quicker, we could make use of Fermat's little theorem:\n  // 10^(d-1) % d == 1\n  // However, we don't need such large numbers and MAX_CYCLE_LEN should be the capstone,\n  // as we want to translate the numbers to strings.\n\n  let rem = C_TEN % d;\n  let t = 1;\n\n  for (; rem !== C_ONE; t++) {\n    rem = rem * C_TEN % d;\n    if (t > MAX_CYCLE_LEN) return C_ZERO; // Returning 0 here means that we don't print it as a cyclic number. It's likely that the answer is `d-1`\n  }\n\n  return BigInt(t);\n}\n\nfunction cycleStart(n, d, len) {\n  let rem1 = C_ONE;\n  let rem2 = modpow(C_TEN, len, d);\n\n  for (let t = 0; t < 300; t++) {\n    // s < ~log10(Number.MAX_VALUE)\n    // Solve 10^s == 10^(s+t) (mod d)\n    if (rem1 === rem2) return BigInt(t);\n    rem1 = rem1 * C_TEN % d;\n    rem2 = rem2 * C_TEN % d;\n  }\n\n  return 0;\n}\n\nfunction gcd(a, b) {\n  if (!a) return b;\n  if (!b) return a;\n\n  while (1) {\n    a %= b;\n    if (!a) return b;\n    b %= a;\n    if (!b) return a;\n  }\n}\n/**\n * Module constructor\n *\n * @constructor\n * @param {number|Fraction=} a\n * @param {number=} b\n */\n\n\nfunction Fraction(a, b) {\n  parse(a, b);\n\n  if (this instanceof Fraction) {\n    a = gcd(P[\"d\"], P[\"n\"]); // Abuse a\n\n    this[\"s\"] = P[\"s\"];\n    this[\"n\"] = P[\"n\"] / a;\n    this[\"d\"] = P[\"d\"] / a;\n  } else {\n    return newFraction(P['s'] * P['n'], P['d']);\n  }\n}\n\nvar DivisionByZero = function () {\n  return new Error(\"Division by Zero\");\n};\n\nvar InvalidParameter = function () {\n  return new Error(\"Invalid argument\");\n};\n\nvar NonIntegerParameter = function () {\n  return new Error(\"Parameters must be integer\");\n};\n\nFraction.prototype = {\n  \"s\": C_ONE,\n  \"n\": C_ZERO,\n  \"d\": C_ONE,\n\n  /**\n   * Calculates the absolute value\n   *\n   * Ex: new Fraction(-4).abs() => 4\n   **/\n  \"abs\": function () {\n    return newFraction(this[\"n\"], this[\"d\"]);\n  },\n\n  /**\n   * Inverts the sign of the current fraction\n   *\n   * Ex: new Fraction(-4).neg() => 4\n   **/\n  \"neg\": function () {\n    return newFraction(-this[\"s\"] * this[\"n\"], this[\"d\"]);\n  },\n\n  /**\n   * Adds two rational numbers\n   *\n   * Ex: new Fraction({n: 2, d: 3}).add(\"14.9\") => 467 / 30\n   **/\n  \"add\": function (a, b) {\n    parse(a, b);\n    return newFraction(this[\"s\"] * this[\"n\"] * P[\"d\"] + P[\"s\"] * this[\"d\"] * P[\"n\"], this[\"d\"] * P[\"d\"]);\n  },\n\n  /**\n   * Subtracts two rational numbers\n   *\n   * Ex: new Fraction({n: 2, d: 3}).add(\"14.9\") => -427 / 30\n   **/\n  \"sub\": function (a, b) {\n    parse(a, b);\n    return newFraction(this[\"s\"] * this[\"n\"] * P[\"d\"] - P[\"s\"] * this[\"d\"] * P[\"n\"], this[\"d\"] * P[\"d\"]);\n  },\n\n  /**\n   * Multiplies two rational numbers\n   *\n   * Ex: new Fraction(\"-17.(345)\").mul(3) => 5776 / 111\n   **/\n  \"mul\": function (a, b) {\n    parse(a, b);\n    return newFraction(this[\"s\"] * P[\"s\"] * this[\"n\"] * P[\"n\"], this[\"d\"] * P[\"d\"]);\n  },\n\n  /**\n   * Divides two rational numbers\n   *\n   * Ex: new Fraction(\"-17.(345)\").inverse().div(3)\n   **/\n  \"div\": function (a, b) {\n    parse(a, b);\n    return newFraction(this[\"s\"] * P[\"s\"] * this[\"n\"] * P[\"d\"], this[\"d\"] * P[\"n\"]);\n  },\n\n  /**\n   * Clones the actual object\n   *\n   * Ex: new Fraction(\"-17.(345)\").clone()\n   **/\n  \"clone\": function () {\n    return newFraction(this['s'] * this['n'], this['d']);\n  },\n\n  /**\n   * Calculates the modulo of two rational numbers - a more precise fmod\n   *\n   * Ex: new Fraction('4.(3)').mod([7, 8]) => (13/3) % (7/8) = (5/6)\n   * Ex: new Fraction(20, 10).mod().equals(0) ? \"is Integer\"\n   **/\n  \"mod\": function (a, b) {\n    if (a === undefined) {\n      return newFraction(this[\"s\"] * this[\"n\"] % this[\"d\"], C_ONE);\n    }\n\n    parse(a, b);\n\n    if (C_ZERO === P[\"n\"] * this[\"d\"]) {\n      throw DivisionByZero();\n    }\n    /*\n     * First silly attempt, kinda slow\n     *\n     return that[\"sub\"]({\n     \"n\": num[\"n\"] * Math.floor((this.n / this.d) / (num.n / num.d)),\n     \"d\": num[\"d\"],\n     \"s\": this[\"s\"]\n     });*/\n\n    /*\n     * New attempt: a1 / b1 = a2 / b2 * q + r\n     * => b2 * a1 = a2 * b1 * q + b1 * b2 * r\n     * => (b2 * a1 % a2 * b1) / (b1 * b2)\n     */\n\n\n    return newFraction(this[\"s\"] * (P[\"d\"] * this[\"n\"]) % (P[\"n\"] * this[\"d\"]), P[\"d\"] * this[\"d\"]);\n  },\n\n  /**\n   * Calculates the fractional gcd of two rational numbers\n   *\n   * Ex: new Fraction(5,8).gcd(3,7) => 1/56\n   */\n  \"gcd\": function (a, b) {\n    parse(a, b); // gcd(a / b, c / d) = gcd(a, c) / lcm(b, d)\n\n    return newFraction(gcd(P[\"n\"], this[\"n\"]) * gcd(P[\"d\"], this[\"d\"]), P[\"d\"] * this[\"d\"]);\n  },\n\n  /**\n   * Calculates the fractional lcm of two rational numbers\n   *\n   * Ex: new Fraction(5,8).lcm(3,7) => 15\n   */\n  \"lcm\": function (a, b) {\n    parse(a, b); // lcm(a / b, c / d) = lcm(a, c) / gcd(b, d)\n\n    if (P[\"n\"] === C_ZERO && this[\"n\"] === C_ZERO) {\n      return newFraction(C_ZERO, C_ONE);\n    }\n\n    return newFraction(P[\"n\"] * this[\"n\"], gcd(P[\"n\"], this[\"n\"]) * gcd(P[\"d\"], this[\"d\"]));\n  },\n\n  /**\n   * Gets the inverse of the fraction, means numerator and denominator are exchanged\n   *\n   * Ex: new Fraction([-3, 4]).inverse() => -4 / 3\n   **/\n  \"inverse\": function () {\n    return newFraction(this[\"s\"] * this[\"d\"], this[\"n\"]);\n  },\n\n  /**\n   * Calculates the fraction to some integer exponent\n   *\n   * Ex: new Fraction(-1,2).pow(-3) => -8\n   */\n  \"pow\": function (a, b) {\n    parse(a, b); // Trivial case when exp is an integer\n\n    if (P['d'] === C_ONE) {\n      if (P['s'] < C_ZERO) {\n        return newFraction((this['s'] * this[\"d\"]) ** P['n'], this[\"n\"] ** P['n']);\n      } else {\n        return newFraction((this['s'] * this[\"n\"]) ** P['n'], this[\"d\"] ** P['n']);\n      }\n    } // Negative roots become complex\n    //     (-a/b)^(c/d) = x\n    // ⇔ (-1)^(c/d) * (a/b)^(c/d) = x\n    // ⇔ (cos(pi) + i*sin(pi))^(c/d) * (a/b)^(c/d) = x\n    // ⇔ (cos(c*pi/d) + i*sin(c*pi/d)) * (a/b)^(c/d) = x       # DeMoivre's formula\n    // From which follows that only for c=0 the root is non-complex\n\n\n    if (this['s'] < C_ZERO) return null; // Now prime factor n and d\n\n    let N = factorize(this['n']);\n    let D = factorize(this['d']); // Exponentiate and take root for n and d individually\n\n    let n = C_ONE;\n    let d = C_ONE;\n\n    for (let k in N) {\n      if (k === '1') continue;\n\n      if (k === '0') {\n        n = C_ZERO;\n        break;\n      }\n\n      N[k] *= P['n'];\n\n      if (N[k] % P['d'] === C_ZERO) {\n        N[k] /= P['d'];\n      } else return null;\n\n      n *= BigInt(k) ** N[k];\n    }\n\n    for (let k in D) {\n      if (k === '1') continue;\n      D[k] *= P['n'];\n\n      if (D[k] % P['d'] === C_ZERO) {\n        D[k] /= P['d'];\n      } else return null;\n\n      d *= BigInt(k) ** D[k];\n    }\n\n    if (P['s'] < C_ZERO) {\n      return newFraction(d, n);\n    }\n\n    return newFraction(n, d);\n  },\n\n  /**\n   * Calculates the logarithm of a fraction to a given rational base\n   *\n   * Ex: new Fraction(27, 8).log(9, 4) => 3/2\n   */\n  \"log\": function (a, b) {\n    parse(a, b);\n    if (this['s'] <= C_ZERO || P['s'] <= C_ZERO) return null;\n    const allPrimes = {};\n    const baseFactors = factorize(P['n']);\n    const T1 = factorize(P['d']);\n    const numberFactors = factorize(this['n']);\n    const T2 = factorize(this['d']);\n\n    for (const prime in T1) {\n      baseFactors[prime] = (baseFactors[prime] || C_ZERO) - T1[prime];\n    }\n\n    for (const prime in T2) {\n      numberFactors[prime] = (numberFactors[prime] || C_ZERO) - T2[prime];\n    }\n\n    for (const prime in baseFactors) {\n      if (prime === '1') continue;\n      allPrimes[prime] = true;\n    }\n\n    for (const prime in numberFactors) {\n      if (prime === '1') continue;\n      allPrimes[prime] = true;\n    }\n\n    let retN = null;\n    let retD = null; // Iterate over all unique primes to determine if a consistent ratio exists\n\n    for (const prime in allPrimes) {\n      const baseExponent = baseFactors[prime] || C_ZERO;\n      const numberExponent = numberFactors[prime] || C_ZERO;\n\n      if (baseExponent === C_ZERO) {\n        if (numberExponent !== C_ZERO) {\n          return null; // Logarithm cannot be expressed as a rational number\n        }\n\n        continue; // Skip this prime since both exponents are zero\n      } // Calculate the ratio of exponents for this prime\n\n\n      let curN = numberExponent;\n      let curD = baseExponent; // Simplify the current ratio\n\n      const gcdValue = gcd(curN, curD);\n      curN /= gcdValue;\n      curD /= gcdValue; // Check if this is the first ratio; otherwise, ensure ratios are consistent\n\n      if (retN === null && retD === null) {\n        retN = curN;\n        retD = curD;\n      } else if (curN * retD !== retN * curD) {\n        return null; // Ratios do not match, logarithm cannot be rational\n      }\n    }\n\n    return retN !== null && retD !== null ? newFraction(retN, retD) : null;\n  },\n\n  /**\n   * Check if two rational numbers are the same\n   *\n   * Ex: new Fraction(19.6).equals([98, 5]);\n   **/\n  \"equals\": function (a, b) {\n    parse(a, b);\n    return this[\"s\"] * this[\"n\"] * P[\"d\"] === P[\"s\"] * P[\"n\"] * this[\"d\"];\n  },\n\n  /**\n   * Check if this rational number is less than another\n   *\n   * Ex: new Fraction(19.6).lt([98, 5]);\n   **/\n  \"lt\": function (a, b) {\n    parse(a, b);\n    return this[\"s\"] * this[\"n\"] * P[\"d\"] < P[\"s\"] * P[\"n\"] * this[\"d\"];\n  },\n\n  /**\n   * Check if this rational number is less than or equal another\n   *\n   * Ex: new Fraction(19.6).lt([98, 5]);\n   **/\n  \"lte\": function (a, b) {\n    parse(a, b);\n    return this[\"s\"] * this[\"n\"] * P[\"d\"] <= P[\"s\"] * P[\"n\"] * this[\"d\"];\n  },\n\n  /**\n   * Check if this rational number is greater than another\n   *\n   * Ex: new Fraction(19.6).lt([98, 5]);\n   **/\n  \"gt\": function (a, b) {\n    parse(a, b);\n    return this[\"s\"] * this[\"n\"] * P[\"d\"] > P[\"s\"] * P[\"n\"] * this[\"d\"];\n  },\n\n  /**\n   * Check if this rational number is greater than or equal another\n   *\n   * Ex: new Fraction(19.6).lt([98, 5]);\n   **/\n  \"gte\": function (a, b) {\n    parse(a, b);\n    return this[\"s\"] * this[\"n\"] * P[\"d\"] >= P[\"s\"] * P[\"n\"] * this[\"d\"];\n  },\n\n  /**\n   * Compare two rational numbers\n   * < 0 iff this < that\n   * > 0 iff this > that\n   * = 0 iff this = that\n   *\n   * Ex: new Fraction(19.6).compare([98, 5]);\n   **/\n  \"compare\": function (a, b) {\n    parse(a, b);\n    let t = this[\"s\"] * this[\"n\"] * P[\"d\"] - P[\"s\"] * P[\"n\"] * this[\"d\"];\n    return (C_ZERO < t) - (t < C_ZERO);\n  },\n\n  /**\n   * Calculates the ceil of a rational number\n   *\n   * Ex: new Fraction('4.(3)').ceil() => (5 / 1)\n   **/\n  \"ceil\": function (places) {\n    places = C_TEN ** BigInt(places || 0);\n    return newFraction(trunc(this[\"s\"] * places * this[\"n\"] / this[\"d\"]) + (places * this[\"n\"] % this[\"d\"] > C_ZERO && this[\"s\"] >= C_ZERO ? C_ONE : C_ZERO), places);\n  },\n\n  /**\n   * Calculates the floor of a rational number\n   *\n   * Ex: new Fraction('4.(3)').floor() => (4 / 1)\n   **/\n  \"floor\": function (places) {\n    places = C_TEN ** BigInt(places || 0);\n    return newFraction(trunc(this[\"s\"] * places * this[\"n\"] / this[\"d\"]) - (places * this[\"n\"] % this[\"d\"] > C_ZERO && this[\"s\"] < C_ZERO ? C_ONE : C_ZERO), places);\n  },\n\n  /**\n   * Rounds a rational numbers\n   *\n   * Ex: new Fraction('4.(3)').round() => (4 / 1)\n   **/\n  \"round\": function (places) {\n    places = C_TEN ** BigInt(places || 0);\n    /* Derivation:\n     s >= 0:\n      round(n / d) = trunc(n / d) + (n % d) / d >= 0.5 ? 1 : 0\n                   = trunc(n / d) + 2(n % d) >= d ? 1 : 0\n    s < 0:\n      round(n / d) =-trunc(n / d) - (n % d) / d > 0.5 ? 1 : 0\n                   =-trunc(n / d) - 2(n % d) > d ? 1 : 0\n     =>:\n     round(s * n / d) = s * trunc(n / d) + s * (C + 2(n % d) > d ? 1 : 0)\n        where C = s >= 0 ? 1 : 0, to fix the >= for the positve case.\n    */\n\n    return newFraction(trunc(this[\"s\"] * places * this[\"n\"] / this[\"d\"]) + this[\"s\"] * ((this[\"s\"] >= C_ZERO ? C_ONE : C_ZERO) + C_TWO * (places * this[\"n\"] % this[\"d\"]) > this[\"d\"] ? C_ONE : C_ZERO), places);\n  },\n\n  /**\n    * Rounds a rational number to a multiple of another rational number\n    *\n    * Ex: new Fraction('0.9').roundTo(\"1/8\") => 7 / 8\n    **/\n  \"roundTo\": function (a, b) {\n    /*\n    k * x/y ≤ a/b < (k+1) * x/y\n    ⇔ k ≤ a/b / (x/y) < (k+1)\n    ⇔ k = floor(a/b * y/x)\n    ⇔ k = floor((a * y) / (b * x))\n    */\n    parse(a, b);\n    const n = this['n'] * P['d'];\n    const d = this['d'] * P['n'];\n    const r = n % d; // round(n / d) = trunc(n / d) + 2(n % d) >= d ? 1 : 0\n\n    let k = trunc(n / d);\n\n    if (r + r >= d) {\n      k++;\n    }\n\n    return newFraction(this['s'] * k * P['n'], P['d']);\n  },\n\n  /**\n   * Check if two rational numbers are divisible\n   *\n   * Ex: new Fraction(19.6).divisible(1.5);\n   */\n  \"divisible\": function (a, b) {\n    parse(a, b);\n    return !(!(P[\"n\"] * this[\"d\"]) || this[\"n\"] * P[\"d\"] % (P[\"n\"] * this[\"d\"]));\n  },\n\n  /**\n   * Returns a decimal representation of the fraction\n   *\n   * Ex: new Fraction(\"100.'91823'\").valueOf() => 100.91823918239183\n   **/\n  'valueOf': function () {\n    // Best we can do so far\n    return Number(this[\"s\"] * this[\"n\"]) / Number(this[\"d\"]);\n  },\n\n  /**\n   * Creates a string representation of a fraction with all digits\n   *\n   * Ex: new Fraction(\"100.'91823'\").toString() => \"100.(91823)\"\n   **/\n  'toString': function (dec) {\n    let N = this[\"n\"];\n    let D = this[\"d\"];\n    dec = dec || 15; // 15 = decimal places when no repetition\n\n    let cycLen = cycleLen(N, D); // Cycle length\n\n    let cycOff = cycleStart(N, D, cycLen); // Cycle start\n\n    let str = this['s'] < C_ZERO ? \"-\" : \"\"; // Append integer part\n\n    str += trunc(N / D);\n    N %= D;\n    N *= C_TEN;\n    if (N) str += \".\";\n\n    if (cycLen) {\n      for (let i = cycOff; i--;) {\n        str += trunc(N / D);\n        N %= D;\n        N *= C_TEN;\n      }\n\n      str += \"(\";\n\n      for (let i = cycLen; i--;) {\n        str += trunc(N / D);\n        N %= D;\n        N *= C_TEN;\n      }\n\n      str += \")\";\n    } else {\n      for (let i = dec; N && i--;) {\n        str += trunc(N / D);\n        N %= D;\n        N *= C_TEN;\n      }\n    }\n\n    return str;\n  },\n\n  /**\n   * Returns a string-fraction representation of a Fraction object\n   *\n   * Ex: new Fraction(\"1.'3'\").toFraction() => \"4 1/3\"\n   **/\n  'toFraction': function (showMixed) {\n    let n = this[\"n\"];\n    let d = this[\"d\"];\n    let str = this['s'] < C_ZERO ? \"-\" : \"\";\n\n    if (d === C_ONE) {\n      str += n;\n    } else {\n      let whole = trunc(n / d);\n\n      if (showMixed && whole > C_ZERO) {\n        str += whole;\n        str += \" \";\n        n %= d;\n      }\n\n      str += n;\n      str += '/';\n      str += d;\n    }\n\n    return str;\n  },\n\n  /**\n   * Returns a latex representation of a Fraction object\n   *\n   * Ex: new Fraction(\"1.'3'\").toLatex() => \"\\frac{4}{3}\"\n   **/\n  'toLatex': function (showMixed) {\n    let n = this[\"n\"];\n    let d = this[\"d\"];\n    let str = this['s'] < C_ZERO ? \"-\" : \"\";\n\n    if (d === C_ONE) {\n      str += n;\n    } else {\n      let whole = trunc(n / d);\n\n      if (showMixed && whole > C_ZERO) {\n        str += whole;\n        n %= d;\n      }\n\n      str += \"\\\\frac{\";\n      str += n;\n      str += '}{';\n      str += d;\n      str += '}';\n    }\n\n    return str;\n  },\n\n  /**\n   * Returns an array of continued fraction elements\n   *\n   * Ex: new Fraction(\"7/8\").toContinued() => [0,1,7]\n   */\n  'toContinued': function () {\n    let a = this['n'];\n    let b = this['d'];\n    let res = [];\n\n    do {\n      res.push(trunc(a / b));\n      let t = a % b;\n      a = b;\n      b = t;\n    } while (a !== C_ONE);\n\n    return res;\n  },\n  \"simplify\": function (eps) {\n    const ieps = BigInt(1 / (eps || 0.001) | 0);\n    const thisABS = this['abs']();\n    const cont = thisABS['toContinued']();\n\n    for (let i = 1; i < cont.length; i++) {\n      let s = newFraction(cont[i - 1], C_ONE);\n\n      for (let k = i - 2; k >= 0; k--) {\n        s = s['inverse']()['add'](cont[k]);\n      }\n\n      let t = s['sub'](thisABS);\n\n      if (t['n'] * ieps < t['d']) {\n        // More robust than Math.abs(t.valueOf()) < eps\n        return s['mul'](this['s']);\n      }\n    }\n\n    return this;\n  }\n};\nexport { Fraction as default, Fraction };", "map": null, "metadata": {}, "sourceType": "module"}