{"ast": null, "code": "export var invmodDocs = {\n  name: 'invmod',\n  category: 'Arithmetic',\n  syntax: ['invmod(a, b)'],\n  description: 'Calculate the (modular) multiplicative inverse of a modulo b. Solution to the equation ax ≣ 1 (mod b)',\n  examples: ['invmod(8, 12)', 'invmod(7, 13)', 'invmod(15151, 15122)'],\n  seealso: ['gcd', 'xgcd']\n};", "map": null, "metadata": {}, "sourceType": "module"}