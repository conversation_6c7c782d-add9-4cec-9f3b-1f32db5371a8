{"ast": null, "code": "/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { BigNumberDependencies } from './dependenciesBigNumberClass.generated.js';\nimport { createLN10 } from '../../factoriesAny.js';\nexport var LN10Dependencies = {\n  BigNumberDependencies,\n  createLN10\n};", "map": {"version": 3, "names": ["BigNumberDependencies", "createLN10", "LN10Dependencies"], "sources": ["D:/work/joyserver/client/node_modules/mathjs/lib/esm/entry/dependenciesAny/dependenciesLN10.generated.js"], "sourcesContent": ["/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { BigNumberDependencies } from './dependenciesBigNumberClass.generated.js';\nimport { createLN10 } from '../../factoriesAny.js';\nexport var LN10Dependencies = {\n  BigNumberDependencies,\n  createLN10\n};"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,qBAAT,QAAsC,2CAAtC;AACA,SAASC,UAAT,QAA2B,uBAA3B;AACA,OAAO,IAAIC,gBAAgB,GAAG;EAC5BF,qBAD4B;EAE5BC;AAF4B,CAAvB"}, "metadata": {}, "sourceType": "module"}