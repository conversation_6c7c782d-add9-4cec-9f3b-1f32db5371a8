{"ast": null, "code": "import { factory } from '../../utils/factory.js';\nvar name = 'ResultSet';\nvar dependencies = [];\nexport var createResultSet = /* #__PURE__ */factory(name, dependencies, () => {\n  /**\n   * A ResultSet contains a list or results\n   * @class ResultSet\n   * @param {Array} entries\n   * @constructor ResultSet\n   */\n  function ResultSet(entries) {\n    if (!(this instanceof ResultSet)) {\n      throw new SyntaxError('Constructor must be called with the new operator');\n    }\n\n    this.entries = entries || [];\n  }\n  /**\n   * Attach type information\n   */\n\n\n  ResultSet.prototype.type = 'ResultSet';\n  ResultSet.prototype.isResultSet = true;\n  /**\n   * Returns the array with results hold by this ResultSet\n   * @memberof ResultSet\n   * @returns {Array} entries\n   */\n\n  ResultSet.prototype.valueOf = function () {\n    return this.entries;\n  };\n  /**\n   * Returns the stringified results of the ResultSet\n   * @memberof ResultSet\n   * @returns {string} string\n   */\n\n\n  ResultSet.prototype.toString = function () {\n    return '[' + this.entries.map(String).join(', ') + ']';\n  };\n  /**\n   * Get a JSON representation of the ResultSet\n   * @memberof ResultSet\n   * @returns {Object} Returns a JSON object structured as:\n   *                   `{\"mathjs\": \"ResultSet\", \"entries\": [...]}`\n   */\n\n\n  ResultSet.prototype.toJSON = function () {\n    return {\n      mathjs: 'ResultSet',\n      entries: this.entries\n    };\n  };\n  /**\n   * Instantiate a ResultSet from a JSON object\n   * @memberof ResultSet\n   * @param {Object} json  A JSON object structured as:\n   *                       `{\"mathjs\": \"ResultSet\", \"entries\": [...]}`\n   * @return {ResultSet}\n   */\n\n\n  ResultSet.fromJSON = function (json) {\n    return new ResultSet(json.entries);\n  };\n\n  return ResultSet;\n}, {\n  isClass: true\n});", "map": {"version": 3, "names": ["factory", "name", "dependencies", "createResultSet", "ResultSet", "entries", "SyntaxError", "prototype", "type", "isResultSet", "valueOf", "toString", "map", "String", "join", "toJSON", "mathjs", "fromJSON", "json", "isClass"], "sources": ["D:/work/joyserver/client/node_modules/mathjs/lib/esm/type/resultset/ResultSet.js"], "sourcesContent": ["import { factory } from '../../utils/factory.js';\nvar name = 'ResultSet';\nvar dependencies = [];\nexport var createResultSet = /* #__PURE__ */factory(name, dependencies, () => {\n  /**\n   * A ResultSet contains a list or results\n   * @class ResultSet\n   * @param {Array} entries\n   * @constructor ResultSet\n   */\n  function ResultSet(entries) {\n    if (!(this instanceof ResultSet)) {\n      throw new SyntaxError('Constructor must be called with the new operator');\n    }\n    this.entries = entries || [];\n  }\n\n  /**\n   * Attach type information\n   */\n  ResultSet.prototype.type = 'ResultSet';\n  ResultSet.prototype.isResultSet = true;\n\n  /**\n   * Returns the array with results hold by this ResultSet\n   * @memberof ResultSet\n   * @returns {Array} entries\n   */\n  ResultSet.prototype.valueOf = function () {\n    return this.entries;\n  };\n\n  /**\n   * Returns the stringified results of the ResultSet\n   * @memberof ResultSet\n   * @returns {string} string\n   */\n  ResultSet.prototype.toString = function () {\n    return '[' + this.entries.map(String).join(', ') + ']';\n  };\n\n  /**\n   * Get a JSON representation of the ResultSet\n   * @memberof ResultSet\n   * @returns {Object} Returns a JSON object structured as:\n   *                   `{\"mathjs\": \"ResultSet\", \"entries\": [...]}`\n   */\n  ResultSet.prototype.toJSON = function () {\n    return {\n      mathjs: 'ResultSet',\n      entries: this.entries\n    };\n  };\n\n  /**\n   * Instantiate a ResultSet from a JSON object\n   * @memberof ResultSet\n   * @param {Object} json  A JSON object structured as:\n   *                       `{\"mathjs\": \"ResultSet\", \"entries\": [...]}`\n   * @return {ResultSet}\n   */\n  ResultSet.fromJSON = function (json) {\n    return new ResultSet(json.entries);\n  };\n  return ResultSet;\n}, {\n  isClass: true\n});"], "mappings": "AAAA,SAASA,OAAT,QAAwB,wBAAxB;AACA,IAAIC,IAAI,GAAG,WAAX;AACA,IAAIC,YAAY,GAAG,EAAnB;AACA,OAAO,IAAIC,eAAe,GAAG,eAAeH,OAAO,CAACC,IAAD,EAAOC,YAAP,EAAqB,MAAM;EAC5E;AACF;AACA;AACA;AACA;AACA;EACE,SAASE,SAAT,CAAmBC,OAAnB,EAA4B;IAC1B,IAAI,EAAE,gBAAgBD,SAAlB,CAAJ,EAAkC;MAChC,MAAM,IAAIE,WAAJ,CAAgB,kDAAhB,CAAN;IACD;;IACD,KAAKD,OAAL,GAAeA,OAAO,IAAI,EAA1B;EACD;EAED;AACF;AACA;;;EACED,SAAS,CAACG,SAAV,CAAoBC,IAApB,GAA2B,WAA3B;EACAJ,SAAS,CAACG,SAAV,CAAoBE,WAApB,GAAkC,IAAlC;EAEA;AACF;AACA;AACA;AACA;;EACEL,SAAS,CAACG,SAAV,CAAoBG,OAApB,GAA8B,YAAY;IACxC,OAAO,KAAKL,OAAZ;EACD,CAFD;EAIA;AACF;AACA;AACA;AACA;;;EACED,SAAS,CAACG,SAAV,CAAoBI,QAApB,GAA+B,YAAY;IACzC,OAAO,MAAM,KAAKN,OAAL,CAAaO,GAAb,CAAiBC,MAAjB,EAAyBC,IAAzB,CAA8B,IAA9B,CAAN,GAA4C,GAAnD;EACD,CAFD;EAIA;AACF;AACA;AACA;AACA;AACA;;;EACEV,SAAS,CAACG,SAAV,CAAoBQ,MAApB,GAA6B,YAAY;IACvC,OAAO;MACLC,MAAM,EAAE,WADH;MAELX,OAAO,EAAE,KAAKA;IAFT,CAAP;EAID,CALD;EAOA;AACF;AACA;AACA;AACA;AACA;AACA;;;EACED,SAAS,CAACa,QAAV,GAAqB,UAAUC,IAAV,EAAgB;IACnC,OAAO,IAAId,SAAJ,CAAcc,IAAI,CAACb,OAAnB,CAAP;EACD,CAFD;;EAGA,OAAOD,SAAP;AACD,CA9DkD,EA8DhD;EACDe,OAAO,EAAE;AADR,CA9DgD,CAA5C"}, "metadata": {}, "sourceType": "module"}