{"ast": null, "code": "/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { BigNumberDependencies } from './dependenciesBigNumberClass.generated.js';\nimport { UnitDependencies } from './dependenciesUnitClass.generated.js';\nimport { createMolarVolume } from '../../factoriesAny.js';\nexport var molarVolumeDependencies = {\n  BigNumberDependencies,\n  UnitDependencies,\n  createMolarVolume\n};", "map": {"version": 3, "names": ["BigNumberDependencies", "UnitDependencies", "createMolarVolume", "molarVolumeDependencies"], "sources": ["D:/work/joyserver/client/node_modules/mathjs/lib/esm/entry/dependenciesAny/dependenciesMolarVolume.generated.js"], "sourcesContent": ["/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { BigNumberDependencies } from './dependenciesBigNumberClass.generated.js';\nimport { UnitDependencies } from './dependenciesUnitClass.generated.js';\nimport { createMolarVolume } from '../../factoriesAny.js';\nexport var molarVolumeDependencies = {\n  BigNumberDependencies,\n  UnitDependencies,\n  createMolarVolume\n};"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,qBAAT,QAAsC,2CAAtC;AACA,SAASC,gBAAT,QAAiC,sCAAjC;AACA,SAASC,iBAAT,QAAkC,uBAAlC;AACA,OAAO,IAAIC,uBAAuB,GAAG;EACnCH,qBADmC;EAEnCC,gBAFmC;EAGnCC;AAHmC,CAA9B"}, "metadata": {}, "sourceType": "module"}