{"ast": null, "code": "import _asyncToGenerator from \"D:/work/joyserver/client/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { LOCATION_INITIALIZED } from \"@angular/common\";\nimport { getAssetLang } from \"./utils/util.service\";\nimport { I18N_VERSION } from \"i18n-version\";\nimport { lastValueFrom } from \"rxjs\";\nexport function appInitializerFactory(translate, injector) {\n  return /*#__PURE__*/_asyncToGenerator(function* () {\n    const locationInitialized = injector.get(LOCATION_INITIALIZED, Promise.resolve(null));\n    return locationInitialized.then( /*#__PURE__*/_asyncToGenerator(function* () {\n      // 获取浏览器默认语言\n      let lang = translate.getBrowserLang(); // 注：该方法只会获取前两位，如zh-HK，只会返回zh\n\n      const browserLang = navigator.language;\n      console.log(\"browser lang:\", browserLang, lang);\n      lang = browserLang || lang;\n\n      if (joyshell) {\n        // 获取客户端配置\n        yield joyshell.WhenReady();\n        lang = joyshell.Settings.LANG || lang;\n        console.log(\"shell lang:\", lang);\n      }\n\n      translate.addLangs(Object.keys(I18N_VERSION));\n      translate.setDefaultLang(\"en\");\n      const langToSet = getAssetLang(lang);\n      console.log(\"app lang:\", langToSet);\n      return lastValueFrom(translate.use(langToSet)).catch(err => {\n        console.error(`load language '${langToSet}' failed.'`, err.message);\n      });\n    }));\n  });\n}", "map": {"version": 3, "mappings": ";AAEA,SAASA,oBAAT,QAAqC,iBAArC;AACA,SAASC,YAAT,QAA6B,sBAA7B;AACA,SAASC,YAAT,QAA6B,cAA7B;AACA,SAASC,aAAT,QAA8B,MAA9B;AAEA,OAAM,SAAUC,qBAAV,CAAgCC,SAAhC,EAA6DC,QAA7D,EAA+E;EACnF,sCAAO,aAAW;IAChB,MAAMC,mBAAmB,GAAGD,QAAQ,CAACE,GAAT,CAAaR,oBAAb,EAAmCS,OAAO,CAACC,OAAR,CAAgB,IAAhB,CAAnC,CAA5B;IACA,OAAOH,mBAAmB,CAACI,IAApB,iCAAyB,aAAW;MACzC;MACA,IAAIC,IAAI,GAAGP,SAAS,CAACQ,cAAV,EAAX,CAFyC,CAEF;;MACvC,MAAMC,WAAW,GAAGC,SAAS,CAACC,QAA9B;MACAC,OAAO,CAACC,GAAR,CAAY,eAAZ,EAA6BJ,WAA7B,EAA0CF,IAA1C;MACAA,IAAI,GAAGE,WAAW,IAAIF,IAAtB;;MAEA,IAAIO,QAAJ,EAAc;QACZ;QACA,MAAMA,QAAQ,CAACC,SAAT,EAAN;QACAR,IAAI,GAAGO,QAAQ,CAACE,QAAT,CAAkBC,IAAlB,IAA0BV,IAAjC;QACAK,OAAO,CAACC,GAAR,CAAY,aAAZ,EAA2BN,IAA3B;MACD;;MAEDP,SAAS,CAACkB,QAAV,CAAmBC,MAAM,CAACC,IAAP,CAAYvB,YAAZ,CAAnB;MACAG,SAAS,CAACqB,cAAV,CAAyB,IAAzB;MACA,MAAMC,SAAS,GAAG1B,YAAY,CAACW,IAAD,CAA9B;MACAK,OAAO,CAACC,GAAR,CAAY,WAAZ,EAAyBS,SAAzB;MACA,OAAOxB,aAAa,CAACE,SAAS,CAACuB,GAAV,CAAcD,SAAd,CAAD,CAAb,CAAwCE,KAAxC,CAA+CC,GAAD,IAAQ;QAC3Db,OAAO,CAACc,KAAR,CAAc,kBAAkBJ,SAAS,YAAzC,EAAuDG,GAAG,CAACE,OAA3D;MACD,CAFM,CAAP;IAGD,CArBM,EAAP;EAsBD,CAxBD;AAyBD", "names": ["LOCATION_INITIALIZED", "getAssetLang", "I18N_VERSION", "lastValueFrom", "appInitializerFactory", "translate", "injector", "locationInitialized", "get", "Promise", "resolve", "then", "lang", "getBrowserLang", "browserLang", "navigator", "language", "console", "log", "joyshell", "WhenReady", "Settings", "LANG", "addLangs", "Object", "keys", "setDefaultLang", "langToSet", "use", "catch", "err", "error", "message"], "sourceRoot": "", "sources": ["D:\\work\\joyserver\\client\\src\\app\\core\\service\\appInitializerFactory.ts"], "sourcesContent": ["import { Injector } from \"@angular/core\";\nimport { TranslateService } from \"@ngx-translate/core\";\nimport { LOCATION_INITIALIZED } from \"@angular/common\";\nimport { getAssetLang } from \"./utils/util.service\";\nimport { I18N_VERSION } from \"i18n-version\";\nimport { lastValueFrom } from \"rxjs\";\n\nexport function appInitializerFactory(translate: TranslateService, injector: Injector) {\n  return async () => {\n    const locationInitialized = injector.get(LOCATION_INITIALIZED, Promise.resolve(null));\n    return locationInitialized.then(async () => {\n      // 获取浏览器默认语言\n      let lang = translate.getBrowserLang(); // 注：该方法只会获取前两位，如zh-HK，只会返回zh\n      const browserLang = navigator.language;\n      console.log(\"browser lang:\", browserLang, lang);\n      lang = browserLang || lang;\n\n      if (joyshell) {\n        // 获取客户端配置\n        await joyshell.WhenReady();\n        lang = joyshell.Settings.LANG || lang;\n        console.log(\"shell lang:\", lang);\n      }\n\n      translate.addLangs(Object.keys(I18N_VERSION));\n      translate.setDefaultLang(\"en\");\n      const langToSet = getAssetLang(lang);\n      console.log(\"app lang:\", langToSet);\n      return lastValueFrom(translate.use(langToSet)).catch((err) => {\n        console.error(`load language '${langToSet}' failed.'`, err.message);\n      });\n    });\n  };\n}\n"]}, "metadata": {}, "sourceType": "module"}