{"ast": null, "code": "import { Observable } from '../Observable';\nimport { async } from '../scheduler/async';\nimport { isNumeric } from '../util/isNumeric';\nexport function interval(period = 0, scheduler = async) {\n  if (!isNumeric(period) || period < 0) {\n    period = 0;\n  }\n\n  if (!scheduler || typeof scheduler.schedule !== 'function') {\n    scheduler = async;\n  }\n\n  return new Observable(subscriber => {\n    subscriber.add(scheduler.schedule(dispatch, period, {\n      subscriber,\n      counter: 0,\n      period\n    }));\n    return subscriber;\n  });\n}\n\nfunction dispatch(state) {\n  const {\n    subscriber,\n    counter,\n    period\n  } = state;\n  subscriber.next(counter);\n  this.schedule({\n    subscriber,\n    counter: counter + 1,\n    period\n  }, period);\n} //# sourceMappingURL=interval.js.map", "map": null, "metadata": {}, "sourceType": "module"}