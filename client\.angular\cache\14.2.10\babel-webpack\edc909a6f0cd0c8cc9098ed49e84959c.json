{"ast": null, "code": "/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createSinh } from '../../factoriesAny.js';\nexport var sinhDependencies = {\n  typedDependencies,\n  createSinh\n};", "map": {"version": 3, "names": ["typedDependencies", "createSinh", "sinhDependencies"], "sources": ["D:/work/joyserver/client/node_modules/mathjs/lib/esm/entry/dependenciesAny/dependenciesSinh.generated.js"], "sourcesContent": ["/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createSinh } from '../../factoriesAny.js';\nexport var sinhDependencies = {\n  typedDependencies,\n  createSinh\n};"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,iBAAT,QAAkC,kCAAlC;AACA,SAASC,UAAT,QAA2B,uBAA3B;AACA,OAAO,IAAIC,gBAAgB,GAAG;EAC5BF,iBAD4B;EAE5BC;AAF4B,CAAvB"}, "metadata": {}, "sourceType": "module"}