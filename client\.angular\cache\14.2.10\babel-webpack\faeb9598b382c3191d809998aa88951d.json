{"ast": null, "code": "import _asyncToGenerator from \"D:/work/joyserver/client/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Ng<PERSON><PERSON> } from \"@angular/core\";\nimport { Subject } from \"rxjs\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./localStorage.service\";\nimport * as i2 from \"@core/modal/custom-alert/custom-alert.service\";\nimport * as i3 from \"@ngx-translate/core\";\nimport * as i4 from \"./api.service\";\nimport * as i5 from \"./utils/util.service\";\nimport * as i6 from \"./response.service\";\nimport * as i7 from \"@core/modal/custom-loading/custom-loading.service\";\nimport * as i8 from \"./form.service\";\nexport class MsoService {\n  constructor(localStorageService, _alertSvc, translate, apiSer, utilSer, responseSer, _loading, localStorSer, formSer, zone) {\n    this.localStorageService = localStorageService;\n    this._alertSvc = _alertSvc;\n    this.translate = translate;\n    this.apiSer = apiSer;\n    this.utilSer = utilSer;\n    this.responseSer = responseSer;\n    this._loading = _loading;\n    this.localStorSer = localStorSer;\n    this.formSer = formSer;\n    this.zone = zone;\n    this.showAutoCollectTip = false;\n    this.showAutoCollectFailTip = false;\n    this.events = new Subject();\n  }\n\n  executentNewAdjustments(data) {\n    if (data) {\n      const {\n        session_id,\n        newAdjustments\n      } = data;\n      this.examSer.doNewAdjustmenets(session_id, newAdjustments);\n    }\n  }\n\n  checkTimeLeft(res, secRes, item) {\n    if (!joyshell || item.type !== \"mso\") {\n      return;\n    }\n\n    let timeLeft;\n\n    if (res.time_left !== -1 && secRes.time_left !== -1) {\n      timeLeft = Math.min(res.time_left, secRes.time_left);\n    } else {\n      timeLeft = Math.max(res.time_left, secRes.time_left);\n    }\n\n    if (timeLeft === 30 && this.isInStemWin()) {\n      this.showAutoCollectTip = true;\n    }\n  }\n\n  isAppRunning(item) {\n    var _this = this;\n\n    return _asyncToGenerator(function* () {\n      if (joyshell) {\n        const apps = _this.getAppName(item);\n\n        const isRunList = yield joyshell.IsAppRunning(apps);\n\n        if (isRunList?.length) {\n          return isRunList.find(isRun => isRun);\n        }\n\n        return false;\n      }\n\n      return false;\n    })();\n  }\n\n  getAppName(item) {\n    return item.content.office.applications.map(application => {\n      const n = application.toLocaleLowerCase();\n\n      if (n === \"ppt\") {\n        application = \"powerpoint\";\n      }\n\n      return application;\n    });\n  }\n\n  isInStemWin() {\n    const stemWin = this.localStorageService.getObject(\"stemWin\");\n\n    if (stemWin) {\n      return true;\n    }\n\n    return false;\n  }\n\n  logFailItemId(items) {\n    if (items?.length) {\n      console.log(`** Collect failed item [MSO], item_id: ${JSON.stringify(items)}`);\n    }\n  }\n\n  mso_request_submit(itemId) {\n    var _this2 = this;\n\n    return _asyncToGenerator(function* () {\n      return new Promise( /*#__PURE__*/function () {\n        var _ref = _asyncToGenerator(function* (resolve, reject) {\n          _this2.autoCollectReject = reject;\n          _this2.autoCollectResovel = resolve;\n\n          _this2.closeDesk().then( /*#__PURE__*/_asyncToGenerator(function* () {\n            yield _this2.autoCollect(itemId);\n          })).catch(e => {\n            throw e;\n          });\n        });\n\n        return function (_x, _x2) {\n          return _ref.apply(this, arguments);\n        };\n      }());\n    })();\n  }\n\n  autoCollect(itemId) {\n    var _this3 = this;\n\n    return _asyncToGenerator(function* () {\n      const item = _this3.formSer.getItemById(_this3.examSer.currentSection(), itemId);\n\n      try {\n        const packageInfo = yield _this3.startPackFiles(item, \"force\"); // 打包成功，上传\n\n        packageInfo[\"packTimestamp\"] = Date.now();\n\n        _this3.packFilesSuccess(item, packageInfo).subscribe(resData => {\n          if (resData) {\n            // console.log(JSON.stringify(data));\n            console.log(`** MSO files upload: success`);\n\n            _this3.updateItemResponse(item, packageInfo.filename, resData, packageInfo, \"force\");\n\n            _this3.updateFilesStatus(item.id, true);\n\n            _this3.autoCollectResovel();\n\n            _this3._loading.setValue(false);\n          }\n        }, err => {\n          console.log(`** MSO files upload: failed`);\n\n          _this3.collectReject(\"MSO files upload: failed\");\n\n          _this3._loading.setValue(false);\n        });\n      } catch (e) {\n        throw e;\n      }\n    })();\n  } // 提示：文件收集失败，请重新登录；本地记录失败试题（重新登录时自动收集，交卷成功后删除）\n\n\n  autoCollectMsoFilesFail(isInStemWin, err) {\n    this.zone.run(() => {\n      this.showAutoCollectFailTip = true;\n    }); // 如果在小窗时，保存当前题\n\n    if (isInStemWin) {\n      const itemId = this.examSer.currentItem().id;\n      this.setItemStatus(itemId, \"uncompleted\");\n      console.log(`* Mso request submit: failed (${err}), cur_item_id: ${itemId}`);\n      const itemData = {\n        id: itemId,\n        workDir: this.workDir\n      };\n      this.localStorSer.saveMsoItemId(this.examSer.session.id, this.examSer.entryInfo.permit, itemData);\n    } else {\n      console.log(`* Mso request submit: failed (${err})`);\n    }\n  } // 小窗窗口提交成功后，清掉本地存储中的记录\n\n\n  removeLocalstorMsoItemId(itemId) {\n    const localItems = this.getItemsIdCollectFail();\n\n    if (localItems.find(item => item.id === itemId)) {\n      const localItemsFilter = localItems.filter(localItem => {\n        return localItem.id != itemId;\n      });\n      console.log(`** Localstorage remove mso item id: ${itemId} `);\n      this.localStorSer.saveMsoItemsId(this.examSer.session.id, this.examSer.entryInfo.permit, localItemsFilter);\n    }\n  } // 关闭桌面\n\n\n  closeDesk() {\n    var _this4 = this;\n\n    return _asyncToGenerator(function* () {\n      try {\n        console.log(`**** Close Desk, ID: ${JSON.stringify(_this4.deskID)}   - Start`);\n        yield window.joyshell.CloseDesk(_this4.deskID);\n        console.log(`**** Close Desk, ID: ${JSON.stringify(_this4.deskID)}   - Done`);\n        _this4.deskID = null;\n        return true;\n      } catch (e) {\n        console.log(\"**** Close desk : failed \");\n\n        _this4.collectReject(\"Close desk failed\");\n\n        _this4._loading.setValue(false);\n\n        throw {\n          type: \"CloseDesk\",\n          errMsg: \"Close desk failed\"\n        };\n      }\n    })();\n  }\n\n  downloadAnswerAttachments(resCurItem) {\n    var _this5 = this;\n\n    return _asyncToGenerator(function* () {\n      const mArr = resCurItem.answer.attachments;\n\n      if (mArr?.length) {\n        for (let i = 0, len = mArr.length; i < len; i++) {\n          const m = mArr[i];\n          console.log(`**** Download answer: ${m.filename}; workDir: ${_this5.workDir}     - Start`);\n          yield _this5.downloadFiles({\n            url: `/seat/rfile/${m.filename}`,\n            filename: m.filename,\n            unpack: true,\n            cwd: _this5.workDir\n          });\n        }\n      }\n    })();\n  }\n\n  downloadMaterial(item) {\n    var _this6 = this;\n\n    return _asyncToGenerator(function* () {\n      // 如果有素材直接下载素材（会自动创建文件夹）；否则创建文件夹\n      if (_this6.hasMaterials(item)) {\n        const materials = item.content.materials;\n\n        for (let i = 0, len = materials.length; i < len; i++) {\n          const m = materials[i];\n          const nameSaved = joyshell.ResolvePath(_this6.workDir, m.path);\n          yield _this6.downloadFiles({\n            url: `${m.url}`,\n            filename: nameSaved\n          });\n        }\n      } else {\n        yield joyshell.MkDirs(_this6.workDir);\n      }\n    })();\n  } // ** 下载素材文件 **\n  // url: string; - 资源路径\n  // filename?: string; - 保存的文件名\n  // unpack?: boolean; - 是否要解压zip文件\n  // cwd?: string; - 工作目录, 也是解压后存放的路径\n\n\n  downloadFiles(data) {\n    return new Promise((resolve, reject) => {\n      joyshell.DownloadFile(data).then(res => {\n        if (res.status !== 200) {\n          console.log(`* Download: failed, url: ${data.url}, res.status: ${res.status}`);\n\n          this._alertSvc.setValue({\n            status: true,\n            info: {\n              bodyText: this.translate.instant(\"items.mso.downloadFail\")\n            }\n          });\n        }\n\n        resolve();\n      }).catch(e => {\n        reject(e);\n        console.log(\"**** Download materials: end \" + e);\n      });\n    });\n  }\n\n  hasMaterials(item) {\n    return !!item.content.materials?.length;\n  }\n\n  ifAnswerHasAttachments(resCurItem) {\n    if (resCurItem.answer.attachments?.length) {\n      return true;\n    }\n\n    return false;\n  } // type: force 为结束单元时的自动收集\n\n\n  startPackFiles(item, type) {\n    var _this7 = this;\n\n    return new Promise((resolve, reject) => {\n      this._loading.setValue(true, this.translate.instant(\"items.mso.uploadingTip\"));\n\n      let packTimes = 1; // 延时打包\n\n      setTimeout( /*#__PURE__*/_asyncToGenerator(function* () {\n        // 打包文件\n        const {\n          filename,\n          workDir,\n          glob\n        } = _this7.getPackInfo(item, packTimes);\n\n        yield _this7.packFiles({\n          filename,\n          workDir,\n          glob,\n          type,\n          reject,\n          resolve,\n          packTimes\n        });\n      }), 1000);\n    });\n  }\n\n  packFiles(data) {\n    var _this8 = this;\n\n    return _asyncToGenerator(function* () {\n      const {\n        filename,\n        workDir,\n        glob,\n        resolve\n      } = data;\n\n      try {\n        const packageInfo = yield joyshell.PackFiles({\n          filename: filename,\n          workdir: workDir,\n          globs: glob\n        });\n\n        if (packageInfo?.fullpath) {\n          console.log(`Pack file fullpath: ${packageInfo.fullpath}, package size: ${packageInfo.size}`);\n          resolve(packageInfo);\n        } else {\n          yield _this8.packfailHandler(data);\n        }\n      } catch (e) {\n        yield _this8.packfailHandler(data);\n      }\n    })();\n  }\n\n  packfailHandler(data) {\n    var _this9 = this;\n\n    return _asyncToGenerator(function* () {\n      const {\n        type,\n        reject,\n        packTimes\n      } = data;\n      console.log(`**** Pack files: failed [packTimes: ${packTimes}]`); // 第一次失败后，重试一次\n\n      if (packTimes === 1) {\n        data.packTimes++;\n        yield _this9.packFiles(data);\n        return;\n      }\n\n      if (type === \"force\") {\n        _this9.collectReject(\"Pack failed\");\n\n        return;\n      }\n\n      reject({\n        type: \"PackFiles\",\n        errMsg: \"Pack files failed\"\n      });\n\n      _this9._loading.setValue(false);\n    })();\n  }\n\n  getPackInfo(item, packTimes) {\n    const itemRes = this.responseSer.getItemResById(item.id);\n    const filename = `${this.examSer.entryInfo.permit}-${item.index}-${itemRes.retry_count}.zip`;\n    const workDir = this.getWorkDir(item);\n    const glob = item.content.collect.glob;\n    console.log(`-- Pack file, item_id: ${item.id}, filename: ${filename}, workDir: ${workDir}, globs: ${glob}       - Start [packTimes: ${packTimes}]`);\n    return {\n      filename,\n      workDir,\n      glob\n    };\n  } // 获取workDir\n\n\n  getWorkDir(item) {\n    if (item.id === this.examSer.response.current_item_id) {\n      return this.workDir;\n    }\n\n    const localItems = this.localStorSer.getMsoItems(this.examSer.session.id, this.examSer.entryInfo.permit);\n    const localItem = localItems.find(localItem => localItem.id === item.id);\n\n    if (localItem) {\n      return localItem.workDir;\n    }\n\n    return \"\";\n  }\n\n  collectReject(errMsg) {\n    this.autoCollectReject(errMsg);\n  }\n\n  packFilesSuccess(item, packageInfo, callback, type) {\n    // 上传文件\n    return this.postFile(item, packageInfo, callback, type);\n  }\n\n  packageOverMaxSize(item, pSize) {\n    const maxSize = item.content.collect.max_size;\n    const sizeLimit = maxSize ? maxSize : \"10mb\";\n    const size = sizeLimit.indexOf(\"mb\") ? parseFloat(sizeLimit) * 1024 * 1024 : parseFloat(sizeLimit) * 1024;\n    console.log(`**** Package size (${pSize}B) - max_size (${maxSize})`);\n\n    if (pSize > size) {\n      this.examSer.alertTip(\"items.mso.overMaxSize\");\n      return true;\n    }\n\n    return false;\n  }\n\n  postFile(item, packageInfo, callback, type) {\n    const {\n      filename,\n      fullpath\n    } = packageInfo;\n    const formData = this.getFormData(filename, fullpath);\n    console.log(`MSO files (item_id: ${item.id}, filename: ${filename}, fullpath: ${fullpath}) : Start upload`);\n    return this.apiSer.uploadAttachment(formData);\n  }\n\n  updateItemResponse(item, filename, resData, packageInfo, type) {\n    const attachment = {\n      id: filename.substring(0, filename.lastIndexOf(\".\")),\n      filename: filename,\n      size: resData.size,\n      time: packageInfo.packTimestamp // 文件打包时间\n\n    };\n\n    if (type === \"force\") {\n      this.itemStatus = \"completed\";\n    }\n\n    this.setItemStatus(item.id, this.itemStatus);\n    this.examSer.updateItemResponse({\n      item: item,\n      attachment: attachment\n    });\n  } // 试题状态由考生标记决定（有文件不一定完成，没文件一定未完成）\n\n\n  setItemStatus(itemId, completed) {\n    const itemRes = this.responseSer.getItemResById(itemId);\n    itemRes.completed = completed === \"completed\" ? true : false;\n  }\n\n  updateFilesStatus(itemId, isUploaded) {\n    let filesStatus = this.localStorSer.getMsoFilesStatus(this.examSer.session.id, this.examSer.entryInfo.permit);\n\n    if (filesStatus.find(fileS => fileS.id === itemId)) {\n      filesStatus = filesStatus.map(fileS => {\n        if (fileS.id === itemId) {\n          fileS.uploaded = isUploaded;\n        }\n\n        return fileS;\n      });\n    } else {\n      filesStatus.push({\n        id: itemId,\n        uploaded: isUploaded\n      });\n    }\n\n    this.localStorageService.saveMsoFilesStatus(this.examSer.session.id, this.examSer.entryInfo.permit, filesStatus);\n  }\n\n  removeMsoItemId(session, entryInfo) {\n    console.log(\"** Remove Mso item id [in localstorage]\");\n    this.localStorageService.removeMsoItemId(session.id, entryInfo.permit);\n  }\n\n  getItemsIdCollectFail() {\n    const items = this.localStorSer.getMsoItems(this.examSer.session.id, this.examSer.entryInfo.permit);\n    return items || [];\n  }\n\n  getFormData(filename, fullpath) {\n    const formData = new FormData();\n    formData.append(\"filename\", filename);\n    formData.append(\"local\", fullpath);\n    return formData;\n  }\n  /* 清空试题工作目录；\r\n   ** 注：如果进入过小窗但没上传成功，不删除！（包含了自动收集失败的试题）\r\n   */\n\n\n  ifDeleteWorkDir(itemId) {\n    return this.getFileStatus(itemId);\n  }\n\n  getFileStatus(itemId) {\n    const allStatus = this.localStorSer.getMsoFilesStatus(this.examSer.session.id, this.examSer.entryInfo.permit);\n    const status = allStatus.find(s => {\n      return s.id === itemId;\n    });\n\n    if (!status) {\n      return true;\n    }\n\n    return status.uploaded;\n  }\n\n  removeMsoStatus() {\n    this.localStorSer.removeMsoFilesStatus(this.examSer.session.id, this.examSer.entryInfo.permit);\n    this.localStorSer.remove(\"MSO_last_permit\");\n  }\n\n  resetWin() {\n    if (window.innerHeight < 300 || window.innerWidth < 500) {\n      this.toSmallWin();\n    }\n  }\n\n  toSmallWin() {\n    const winPos = this.getWindowPos();\n    const winW = window.innerWidth;\n    window.joyshell.SetWindowMode(\"dialog\", {\n      position: {\n        x: winPos.x - (580 - winW),\n        y: winPos.y\n      },\n      size: {\n        width: 580,\n        height: 500\n      },\n      unlock: false\n    });\n    this.localStorSer.set(\"stemWin\", {\n      type: \"stem\"\n    });\n  }\n\n  getWindowPos() {\n    const x = window.screenX || window.screenLeft;\n    const y = window.screenY || window.screenTop;\n    return {\n      x,\n      y\n    };\n  }\n\n}\n\nMsoService.ɵfac = function MsoService_Factory(t) {\n  return new (t || MsoService)(i0.ɵɵinject(i1.LocalStorageService), i0.ɵɵinject(i2.CustomAlertService), i0.ɵɵinject(i3.TranslateService), i0.ɵɵinject(i4.APIService), i0.ɵɵinject(i5.UtilService), i0.ɵɵinject(i6.ResponseService), i0.ɵɵinject(i7.CustomLoadingService), i0.ɵɵinject(i1.LocalStorageService), i0.ɵɵinject(i8.FormService), i0.ɵɵinject(i0.NgZone));\n};\n\nMsoService.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n  token: MsoService,\n  factory: MsoService.ɵfac,\n  providedIn: \"root\"\n});", "map": null, "metadata": {}, "sourceType": "module"}