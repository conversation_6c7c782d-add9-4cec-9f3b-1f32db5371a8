{"ast": null, "code": "/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { NodeDependencies } from './dependenciesNode.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createFunctionAssignmentNode } from '../../factoriesAny.js';\nexport var FunctionAssignmentNodeDependencies = {\n  NodeDependencies,\n  typedDependencies,\n  createFunctionAssignmentNode\n};", "map": {"version": 3, "names": ["NodeDependencies", "typedDependencies", "createFunctionAssignmentNode", "FunctionAssignmentNodeDependencies"], "sources": ["D:/work/joyserver/client/node_modules/mathjs/lib/esm/entry/dependenciesAny/dependenciesFunctionAssignmentNode.generated.js"], "sourcesContent": ["/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { NodeDependencies } from './dependenciesNode.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createFunctionAssignmentNode } from '../../factoriesAny.js';\nexport var FunctionAssignmentNodeDependencies = {\n  NodeDependencies,\n  typedDependencies,\n  createFunctionAssignmentNode\n};"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,gBAAT,QAAiC,iCAAjC;AACA,SAASC,iBAAT,QAAkC,kCAAlC;AACA,SAASC,4BAAT,QAA6C,uBAA7C;AACA,OAAO,IAAIC,kCAAkC,GAAG;EAC9CH,gBAD8C;EAE9CC,iBAF8C;EAG9CC;AAH8C,CAAzC"}, "metadata": {}, "sourceType": "module"}