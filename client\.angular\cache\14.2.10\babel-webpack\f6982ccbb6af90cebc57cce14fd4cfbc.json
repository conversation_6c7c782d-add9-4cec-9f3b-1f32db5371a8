{"ast": null, "code": "import getVariation from \"./getVariation.js\";\nimport { variationPlacements, basePlacements, placements as allPlacements } from \"../enums.js\";\nimport detectOverflow from \"./detectOverflow.js\";\nimport getBasePlacement from \"./getBasePlacement.js\";\nexport default function computeAutoPlacement(state, options) {\n  if (options === void 0) {\n    options = {};\n  }\n\n  var _options = options,\n      placement = _options.placement,\n      boundary = _options.boundary,\n      rootBoundary = _options.rootBoundary,\n      padding = _options.padding,\n      flipVariations = _options.flipVariations,\n      _options$allowedAutoP = _options.allowedAutoPlacements,\n      allowedAutoPlacements = _options$allowedAutoP === void 0 ? allPlacements : _options$allowedAutoP;\n  var variation = getVariation(placement);\n  var placements = variation ? flipVariations ? variationPlacements : variationPlacements.filter(function (placement) {\n    return getVariation(placement) === variation;\n  }) : basePlacements;\n  var allowedPlacements = placements.filter(function (placement) {\n    return allowedAutoPlacements.indexOf(placement) >= 0;\n  });\n\n  if (allowedPlacements.length === 0) {\n    allowedPlacements = placements;\n\n    if (process.env.NODE_ENV !== \"production\") {\n      console.error(['Popper: The `allowedAutoPlacements` option did not allow any', 'placements. Ensure the `placement` option matches the variation', 'of the allowed placements.', 'For example, \"auto\" cannot be used to allow \"bottom-start\".', 'Use \"auto-start\" instead.'].join(' '));\n    }\n  } // $FlowFixMe[incompatible-type]: Flow seems to have problems with two array unions...\n\n\n  var overflows = allowedPlacements.reduce(function (acc, placement) {\n    acc[placement] = detectOverflow(state, {\n      placement: placement,\n      boundary: boundary,\n      rootBoundary: rootBoundary,\n      padding: padding\n    })[getBasePlacement(placement)];\n    return acc;\n  }, {});\n  return Object.keys(overflows).sort(function (a, b) {\n    return overflows[a] - overflows[b];\n  });\n}", "map": {"version": 3, "names": ["getVariation", "variationPlacements", "basePlacements", "placements", "allPlacements", "detectOverflow", "getBasePlacement", "computeAutoPlacement", "state", "options", "_options", "placement", "boundary", "rootBoundary", "padding", "flipVariations", "_options$allowedAutoP", "allowedAutoPlacements", "variation", "filter", "allowedPlacements", "indexOf", "length", "process", "env", "NODE_ENV", "console", "error", "join", "overflows", "reduce", "acc", "Object", "keys", "sort", "a", "b"], "sources": ["D:/work/joyserver/client/node_modules/@popperjs/core/lib/utils/computeAutoPlacement.js"], "sourcesContent": ["import getVariation from \"./getVariation.js\";\nimport { variationPlacements, basePlacements, placements as allPlacements } from \"../enums.js\";\nimport detectOverflow from \"./detectOverflow.js\";\nimport getBasePlacement from \"./getBasePlacement.js\";\nexport default function computeAutoPlacement(state, options) {\n  if (options === void 0) {\n    options = {};\n  }\n\n  var _options = options,\n      placement = _options.placement,\n      boundary = _options.boundary,\n      rootBoundary = _options.rootBoundary,\n      padding = _options.padding,\n      flipVariations = _options.flipVariations,\n      _options$allowedAutoP = _options.allowedAutoPlacements,\n      allowedAutoPlacements = _options$allowedAutoP === void 0 ? allPlacements : _options$allowedAutoP;\n  var variation = getVariation(placement);\n  var placements = variation ? flipVariations ? variationPlacements : variationPlacements.filter(function (placement) {\n    return getVariation(placement) === variation;\n  }) : basePlacements;\n  var allowedPlacements = placements.filter(function (placement) {\n    return allowedAutoPlacements.indexOf(placement) >= 0;\n  });\n\n  if (allowedPlacements.length === 0) {\n    allowedPlacements = placements;\n\n    if (process.env.NODE_ENV !== \"production\") {\n      console.error(['Popper: The `allowedAutoPlacements` option did not allow any', 'placements. Ensure the `placement` option matches the variation', 'of the allowed placements.', 'For example, \"auto\" cannot be used to allow \"bottom-start\".', 'Use \"auto-start\" instead.'].join(' '));\n    }\n  } // $FlowFixMe[incompatible-type]: Flow seems to have problems with two array unions...\n\n\n  var overflows = allowedPlacements.reduce(function (acc, placement) {\n    acc[placement] = detectOverflow(state, {\n      placement: placement,\n      boundary: boundary,\n      rootBoundary: rootBoundary,\n      padding: padding\n    })[getBasePlacement(placement)];\n    return acc;\n  }, {});\n  return Object.keys(overflows).sort(function (a, b) {\n    return overflows[a] - overflows[b];\n  });\n}"], "mappings": "AAAA,OAAOA,YAAP,MAAyB,mBAAzB;AACA,SAASC,mBAAT,EAA8BC,cAA9B,EAA8CC,UAAU,IAAIC,aAA5D,QAAiF,aAAjF;AACA,OAAOC,cAAP,MAA2B,qBAA3B;AACA,OAAOC,gBAAP,MAA6B,uBAA7B;AACA,eAAe,SAASC,oBAAT,CAA8BC,KAA9B,EAAqCC,OAArC,EAA8C;EAC3D,IAAIA,OAAO,KAAK,KAAK,CAArB,EAAwB;IACtBA,OAAO,GAAG,EAAV;EACD;;EAED,IAAIC,QAAQ,GAAGD,OAAf;EAAA,IACIE,SAAS,GAAGD,QAAQ,CAACC,SADzB;EAAA,IAEIC,QAAQ,GAAGF,QAAQ,CAACE,QAFxB;EAAA,IAGIC,YAAY,GAAGH,QAAQ,CAACG,YAH5B;EAAA,IAIIC,OAAO,GAAGJ,QAAQ,CAACI,OAJvB;EAAA,IAKIC,cAAc,GAAGL,QAAQ,CAACK,cAL9B;EAAA,IAMIC,qBAAqB,GAAGN,QAAQ,CAACO,qBANrC;EAAA,IAOIA,qBAAqB,GAAGD,qBAAqB,KAAK,KAAK,CAA/B,GAAmCZ,aAAnC,GAAmDY,qBAP/E;EAQA,IAAIE,SAAS,GAAGlB,YAAY,CAACW,SAAD,CAA5B;EACA,IAAIR,UAAU,GAAGe,SAAS,GAAGH,cAAc,GAAGd,mBAAH,GAAyBA,mBAAmB,CAACkB,MAApB,CAA2B,UAAUR,SAAV,EAAqB;IAClH,OAAOX,YAAY,CAACW,SAAD,CAAZ,KAA4BO,SAAnC;EACD,CAFmE,CAA1C,GAErBhB,cAFL;EAGA,IAAIkB,iBAAiB,GAAGjB,UAAU,CAACgB,MAAX,CAAkB,UAAUR,SAAV,EAAqB;IAC7D,OAAOM,qBAAqB,CAACI,OAAtB,CAA8BV,SAA9B,KAA4C,CAAnD;EACD,CAFuB,CAAxB;;EAIA,IAAIS,iBAAiB,CAACE,MAAlB,KAA6B,CAAjC,EAAoC;IAClCF,iBAAiB,GAAGjB,UAApB;;IAEA,IAAIoB,OAAO,CAACC,GAAR,CAAYC,QAAZ,KAAyB,YAA7B,EAA2C;MACzCC,OAAO,CAACC,KAAR,CAAc,CAAC,8DAAD,EAAiE,iEAAjE,EAAoI,4BAApI,EAAkK,6DAAlK,EAAiO,2BAAjO,EAA8PC,IAA9P,CAAmQ,GAAnQ,CAAd;IACD;EACF,CA3B0D,CA2BzD;;;EAGF,IAAIC,SAAS,GAAGT,iBAAiB,CAACU,MAAlB,CAAyB,UAAUC,GAAV,EAAepB,SAAf,EAA0B;IACjEoB,GAAG,CAACpB,SAAD,CAAH,GAAiBN,cAAc,CAACG,KAAD,EAAQ;MACrCG,SAAS,EAAEA,SAD0B;MAErCC,QAAQ,EAAEA,QAF2B;MAGrCC,YAAY,EAAEA,YAHuB;MAIrCC,OAAO,EAAEA;IAJ4B,CAAR,CAAd,CAKdR,gBAAgB,CAACK,SAAD,CALF,CAAjB;IAMA,OAAOoB,GAAP;EACD,CARe,EAQb,EARa,CAAhB;EASA,OAAOC,MAAM,CAACC,IAAP,CAAYJ,SAAZ,EAAuBK,IAAvB,CAA4B,UAAUC,CAAV,EAAaC,CAAb,EAAgB;IACjD,OAAOP,SAAS,CAACM,CAAD,CAAT,GAAeN,SAAS,CAACO,CAAD,CAA/B;EACD,CAFM,CAAP;AAGD"}, "metadata": {}, "sourceType": "module"}