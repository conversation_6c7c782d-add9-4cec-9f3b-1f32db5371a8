{"ast": null, "code": "/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { BigNumberDependencies } from './dependenciesBigNumberClass.generated.js';\nimport { UnitDependencies } from './dependenciesUnitClass.generated.js';\nimport { createMolarMassC12 } from '../../factoriesAny.js';\nexport var molarMassC12Dependencies = {\n  BigNumberDependencies,\n  UnitDependencies,\n  createMolarMassC12\n};", "map": {"version": 3, "names": ["BigNumberDependencies", "UnitDependencies", "createMolarMassC12", "molarMassC12Dependencies"], "sources": ["D:/work/joyserver/client/node_modules/mathjs/lib/esm/entry/dependenciesAny/dependenciesMolarMassC12.generated.js"], "sourcesContent": ["/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { BigNumberDependencies } from './dependenciesBigNumberClass.generated.js';\nimport { UnitDependencies } from './dependenciesUnitClass.generated.js';\nimport { createMolarMassC12 } from '../../factoriesAny.js';\nexport var molarMassC12Dependencies = {\n  BigNumberDependencies,\n  UnitDependencies,\n  createMolarMassC12\n};"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,qBAAT,QAAsC,2CAAtC;AACA,SAASC,gBAAT,QAAiC,sCAAjC;AACA,SAASC,kBAAT,QAAmC,uBAAnC;AACA,OAAO,IAAIC,wBAAwB,GAAG;EACpCH,qBADoC;EAEpCC,gBAFoC;EAGpCC;AAHoC,CAA/B"}, "metadata": {}, "sourceType": "module"}