{"ast": null, "code": "import { flatten } from '../../utils/array.js';\nimport { factory } from '../../utils/factory.js';\nvar name = 'setSymDifference';\nvar dependencies = ['typed', 'size', 'concat', 'subset', 'setDifference', 'Index'];\nexport var createSetSymDifference = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed,\n    size,\n    concat,\n    subset,\n    setDifference,\n    Index\n  } = _ref;\n  /**\n   * Create the symmetric difference of two (multi)sets.\n   * Multi-dimension arrays will be converted to single-dimension arrays before the operation.\n   *\n   * Syntax:\n   *\n   *    math.setSymDifference(set1, set2)\n   *\n   * Examples:\n   *\n   *    math.setSymDifference([1, 2, 3, 4], [3, 4, 5, 6])            // returns [1, 2, 5, 6]\n   *    math.setSymDifference([[1, 2], [3, 4]], [[3, 4], [5, 6]])    // returns [1, 2, 5, 6]\n   *\n   * See also:\n   *\n   *    setUnion, setIntersect, setDifference\n   *\n   * @param {Array | Matrix}    a1  A (multi)set\n   * @param {Array | Matrix}    a2  A (multi)set\n   * @return {Array | Matrix}    The symmetric difference of two (multi)sets\n   */\n\n  return typed(name, {\n    'Array | Matrix, Array | Matrix': function Array__Matrix_Array__Matrix(a1, a2) {\n      if (subset(size(a1), new Index(0)) === 0) {\n        // if any of them is empty, return the other one\n        return flatten(a2);\n      } else if (subset(size(a2), new Index(0)) === 0) {\n        return flatten(a1);\n      }\n\n      var b1 = flatten(a1);\n      var b2 = flatten(a2);\n      return concat(setDifference(b1, b2), setDifference(b2, b1));\n    }\n  });\n});", "map": {"version": 3, "names": ["flatten", "factory", "name", "dependencies", "createSetSymDifference", "_ref", "typed", "size", "concat", "subset", "setDifference", "Index", "Array__Matrix_Array__Matrix", "a1", "a2", "b1", "b2"], "sources": ["D:/work/joyserver/client/node_modules/mathjs/lib/esm/function/set/setSymDifference.js"], "sourcesContent": ["import { flatten } from '../../utils/array.js';\nimport { factory } from '../../utils/factory.js';\nvar name = 'setSymDifference';\nvar dependencies = ['typed', 'size', 'concat', 'subset', 'setDifference', 'Index'];\nexport var createSetSymDifference = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed,\n    size,\n    concat,\n    subset,\n    setDifference,\n    Index\n  } = _ref;\n  /**\n   * Create the symmetric difference of two (multi)sets.\n   * Multi-dimension arrays will be converted to single-dimension arrays before the operation.\n   *\n   * Syntax:\n   *\n   *    math.setSymDifference(set1, set2)\n   *\n   * Examples:\n   *\n   *    math.setSymDifference([1, 2, 3, 4], [3, 4, 5, 6])            // returns [1, 2, 5, 6]\n   *    math.setSymDifference([[1, 2], [3, 4]], [[3, 4], [5, 6]])    // returns [1, 2, 5, 6]\n   *\n   * See also:\n   *\n   *    setUnion, setIntersect, setDifference\n   *\n   * @param {Array | Matrix}    a1  A (multi)set\n   * @param {Array | Matrix}    a2  A (multi)set\n   * @return {Array | Matrix}    The symmetric difference of two (multi)sets\n   */\n  return typed(name, {\n    'Array | Matrix, Array | Matrix': function Array__Matrix_Array__Matrix(a1, a2) {\n      if (subset(size(a1), new Index(0)) === 0) {\n        // if any of them is empty, return the other one\n        return flatten(a2);\n      } else if (subset(size(a2), new Index(0)) === 0) {\n        return flatten(a1);\n      }\n      var b1 = flatten(a1);\n      var b2 = flatten(a2);\n      return concat(setDifference(b1, b2), setDifference(b2, b1));\n    }\n  });\n});"], "mappings": "AAAA,SAASA,OAAT,QAAwB,sBAAxB;AACA,SAASC,OAAT,QAAwB,wBAAxB;AACA,IAAIC,IAAI,GAAG,kBAAX;AACA,IAAIC,YAAY,GAAG,CAAC,OAAD,EAAU,MAAV,EAAkB,QAAlB,EAA4B,QAA5B,EAAsC,eAAtC,EAAuD,OAAvD,CAAnB;AACA,OAAO,IAAIC,sBAAsB,GAAG,eAAeH,OAAO,CAACC,IAAD,EAAOC,YAAP,EAAqBE,IAAI,IAAI;EACrF,IAAI;IACFC,KADE;IAEFC,IAFE;IAGFC,MAHE;IAIFC,MAJE;IAKFC,aALE;IAMFC;EANE,IAOAN,IAPJ;EAQA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EACE,OAAOC,KAAK,CAACJ,IAAD,EAAO;IACjB,kCAAkC,SAASU,2BAAT,CAAqCC,EAArC,EAAyCC,EAAzC,EAA6C;MAC7E,IAAIL,MAAM,CAACF,IAAI,CAACM,EAAD,CAAL,EAAW,IAAIF,KAAJ,CAAU,CAAV,CAAX,CAAN,KAAmC,CAAvC,EAA0C;QACxC;QACA,OAAOX,OAAO,CAACc,EAAD,CAAd;MACD,CAHD,MAGO,IAAIL,MAAM,CAACF,IAAI,CAACO,EAAD,CAAL,EAAW,IAAIH,KAAJ,CAAU,CAAV,CAAX,CAAN,KAAmC,CAAvC,EAA0C;QAC/C,OAAOX,OAAO,CAACa,EAAD,CAAd;MACD;;MACD,IAAIE,EAAE,GAAGf,OAAO,CAACa,EAAD,CAAhB;MACA,IAAIG,EAAE,GAAGhB,OAAO,CAACc,EAAD,CAAhB;MACA,OAAON,MAAM,CAACE,aAAa,CAACK,EAAD,EAAKC,EAAL,CAAd,EAAwBN,aAAa,CAACM,EAAD,EAAKD,EAAL,CAArC,CAAb;IACD;EAXgB,CAAP,CAAZ;AAaD,CA3CyD,CAAnD"}, "metadata": {}, "sourceType": "module"}