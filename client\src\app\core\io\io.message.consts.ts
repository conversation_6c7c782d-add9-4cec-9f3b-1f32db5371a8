import { SessionConfig, RegInfoState, SystemSetting } from "@core-types/config.types";
import { Manager } from "@core-types/manager.types";

export const MSG = "message";
export const REPLY = "reply";

/*
 * Client需要处理消息
 * */
// 允许考试机注册 params : { enabled: true/false }
export const MSG_ENABLE_REGISTER = "msg.enable_register";
interface MsgEnableRegister {
  enabled: boolean;
}
// 注销考试机
export const MSG_UNREGISTER = "msg.unregister";
// 转移考试: params: { permit_id: "", allow: true/false}
export const MSG_TRANSFER = "msg.transfer";
interface MsgTransfer {
  permit_id: string;
  allow: boolean;
}
// 考试机退出到登录页面: params: {session_id: 'xxxx', reason: ""}
export const MSG_RESET_CLIENT = "msg.reset";
interface MsgResetClient {
  session_id: string;
  reason: string;
  code?: string;
}
// 修改机号 params: {new_seat_number: 11, code: "xxxx"}
export const MSG_CHANGE_SEAT_NUMBER = "msg.change_seat_number";
interface MsgChangeSeatNumber {
  new_seat_number: number;
  code: string;
}
// 入场 params: { session_id: "xxxx", [force: true/false] }
export const MSG_SESSION_ENTER = "session.enter";
interface MsgSessionEnter {
  session_id: string;
  force: boolean;
}
// 开始考试 params: { session_id: "xxxx" }
export const MSG_SESSION_START = "session.start";
// 暂停考试 params: { session_id: "xxxx" }
export const MSG_SESSION_PAUSE = "session.pause";
// 继续考试 params: { session_id: "xxxx" }
export const MSG_SESSION_RESUME = "session.resume";
// 结束考试 params: { session_id: "xxxx" }
export const MSG_SESSION_END = "session.end";
// 试卷操作事件 （延时 撤销交卷/单元）
export const MSG_RESPONSE_EVENT = "msg.response.event";
interface MsgResponseEvent {
  session_id: string;
  permit: string;
  adjustments: Manager.ResponseEvent[];
}
// 延时考试 params: { session_id: "xxxx", seconds: 300 }
export const MSG_SESSION_DELAY = "session.delay";
interface MsgSessionDelay {
  session_id: string;
  seconds: number;
}
// 撤销收卷 params: { session_id: "xxxx", extend_mins?: 10}
export const MSG_SESSION_UNDO = "session.undo";
interface MsgSessionUndo {
  session_id: string;
  extend_mins?: number;
}
// 照片变更 params: { session_id: "xxxx" }
export const MSG_PHOTO_UPDATED = "msg.photo.updated";
// 获取当前考试机状态 params: { [ session_id: "" ] }
export const MSG_GET_STATUS = "msg.getstatus";
// 关闭考试机
export const MSG_CLOSE = "msg.close";
// 切换考场 params: {room: '' room_code: '',center_name: '', center_address: ''}
export const MSG_ROOM_CHANGE = "msg.roomchange";
interface MsgRoomChange {
  room: string;
  room_code: string;
  center_name: string;
  center_address: string;
}
// 通知考试机自动登录 params: {permit: 'xxxxxxxxxxx',identity_id: "xxxxx"}
export const MSG_AUTO_LOGIN = "msg.autologin";
// skin 数据变化
export const MSG_SKIN_INFO = "msg.skininfo";
// 配置信息变更 params: {reginfo}
export const MSG_RELOAD = "msg.reload";
// 配置信息变更 params: {permit: 'xxxx'}
export const MSG_RESUBMIT = "msg.resubmit";
// 通知自动试考 params: {permit: 'xxxx'}
export const MSG_AUTO_TEST = "msg.autotest";
// 通知授权事件处理 params {type: 'xxxx'}
export const MSG_AUTH_EVENT = "msg.auth.event";
// 重新登陆
export const MSG_RELOGIN = "msg.relogin";
// 更新配置 { "KEY": value }
export const MSG_SETTING_UPDATE = "msg.setting.update";
/*
 * Manager处理的消息
 * */

// 注册考试机 {seat: 1, force: true/false}
// 返回 { errcode: 0, code: "xxxxx" }
//  { errcode: ERR_CONFLICT, errmsg: "机号冲突"}
export const MSG_CLIENT_REGISTER = "client.register";
interface MsgClientRegister {
  seat: number;
  force?: boolean;
}
// 注销考试机
export const MSG_CLIENT_UNREGISTER = "client.unregister";
// 考试机登陆 params: { seat: 11, code: "", client?: true/false, version?:"", room?:"room code" }
export const MSG_CLIENT_LOGIN = "msg.login";
interface MsgClientLogin {
  seat: number;
  code: string;
  mac_addr: string;
  client?: boolean;
  version?: string;
  room?: string;
  permit?: string;
}
// 考试机登出
export const MSG_CLIENT_LOGOUT = "msg.logout";

// 传递消息 考试机与管理机前端
export const MSG_SENDTO = "msg.sendto";
interface MsgSendTo {
  target: string | number;
  info: { seat?: number; type: string; data: any };
}

// 给考试机发送消息
export const MSG_CALLSEAT = "msg.call.seat";
interface MsgCallSeat {
  seat: string | number;
  msg: string;
  params: any;
}

// 给所有考试机发送信息
export const MSG_SURVEY = "msg.survey";

// 获取进程列表
export const MSG_GET_PROCESSES = "msg.getprocesses";

// 杀死进程
export const MSG_KILL_PROCESS = "msg.killprocess";
interface MsgKillProcess {
  name: string;
}

// 获取考试机状态
export const MSG_GET_CLIENT_STATUS = "msg.getclientstatus";
// 获取当前考生信息
export const MSG_GET_ENTRY = "msg.getentry";
// 管理机同步状态
export const MSG_SYNC_STAT = "msg.sync.stat";

// 考试机执行脚本
export const MSG_EVAL_JS = "msg.evaljs";

// 还原卡检查相关消息
export const MSG_RESTORE_CARD_CHECK = "msg.restore_card.check";

export type RestoreCardAction = "write" | "reboot" | "verify";
export type RestoreCardActions = RestoreCardAction | RestoreCardAction[];

interface MsgRestoreCardCheck {
  session_id: string;
  actions: RestoreCardAction[];
  timeout?: { [key: string]: number };
}

export const MSG_RESTORE_CARD_REPORT = "msg.restore_card.report";
interface MsgRestoreCardReport {
  session_id: string;
  seat_number: number;
  status: RestoreCardCheckStatus;
  timestamp: number;
  result?: {
    isValid: boolean;
    payload?: { session_id: string; timestamp: number };
    wasRestartedSinceCreation?: boolean;
    status:
      | "SUCCESS"
      | "FILE_NOT_FOUND"
      | "INVALID_FILE_FORMAT"
      | "HMAC_MISMATCH"
      | "READ_ERROR"
      | "UNKNOWN_ERROR"
      | "FAILED";
  };
}

// 还原卡检查状态
export type RestoreCardCheckStatus =
  | "NotChecked"
  | "Writing"
  | "Restarting"
  | "Verifying"
  | "Success"
  | "Failed"
  | "Timeout";

// 还原卡检查状态更新通知
export const NOTIFY_RESTORE_CARD_UPDATE = "notify.restore_card.update";
interface NotifyRestoreCardUpdate {
  session_id: string;
  clients: {
    seatNumber: number;
    ip: string;
    status: RestoreCardCheckStatus;
    result?: any;
    timestamp?: number;
    startTime?: number;
    writeTime?: number;
    restartTime?: number;
    verifyTime?: number;
    timeoutAt?: number;
  }[];
}

// 扫描考试: params: {room_code, ip, from, to}
export const MSG_SCAN_CLIENT = "msg.scan.client";
interface MsgScanClient {
  room_code?: string;
  ip: string;
  from: number;
  to: number;
}
// 扫描到一个考试机 params: {id 机位号, host, code?, server, room}
export const MSG_FOUND_CLIENT = "msg.found.client";
interface MsgFoundClient {
  id: number;
  host: string;
  ip: string;
  host_name: string;
  code?: number;
  room: string;
  server: string;
  mac_addr: string;
  state?: RegInfoState;
  online?: boolean;
}
// 扫描考试机结束 params: { clients: [] }
export const MSG_SCAN_CLIENT_END = "msg.scan.client.end";

// 启动考试机: params: { }
export const MSG_START_CLIENT = "msg.start.client";
interface MsgStartClient {
  seats?: number[];
}
// 添加考试机: params: {id?, host}
export const MSG_ADD_CLIENT = "msg.add.client";
interface MsgAddClient {
  id: string | number;
  host: string;
  ip: string;
  host_name: string;
  mac_addr: string;
}
// 关闭考试机{seat?: 1}
export const MSG_CLOSE_CLIENT = "msg.close.client";
interface MsgCloseClient {
  seats?: number[];
  force?: number;
}
// 考试机执行程序命令
export const MSG_EXECUTE_COMMAND = "msg.execute.command";
interface MsgExecuteCommand {
  ip: string;
  name: string;
  dir: string;
  args: string[];
}
// 从考试机下载文件
export const MSG_DOWN_FILE = "msg.down.file";
interface MsgDownFile {
  ip: string;
  seat_number: number | string;
  file: string;
  target: string;
  abort: boolean;
}
// 发送文件至考试机
export const MSG_SEND_FILE = "msg.send.file";
interface MsgSendFile {
  ip: string;
  files: { name: string; path: string; target?: string }[];
  abort: boolean;
}
// 文件网络传输进度
export const MSG_FILE_PROGRESS = "msg.file.progress";
export interface MsgFileProgress {
  progress: { percent: number; transferred: number; total?: number };
  info: { session_id: string; file_id: string; type?: string };
}
// 发送文件进度 {ip, progress: 0}
export const MSG_SEND_PROGRESS = "msg.send.progress";
interface MsgSendProgress {
  ip: string;
  progress?: number;
  error?: any;
}
// 事件处理进度
export const MSG_EVENT_PROGRESS = "msg.event.progress";
export interface MsgEventProgerss {
  id: string;
  name: string;
  cur?: number;
  total?: number;
  percent: number;
  error?: any;
  errors?: any[];
}
// 考试机检查项目
export const MSG_CHECK = "msg.check";
interface MsgCheck {
  name: string;
  info: any;
}
// 新注册考试机: params: { seat: 1, host: "ip"}
export const MSG_CLIENT_REGISTERED = "client.registered";
interface MsgClientRegisterd {
  seat: number;
  host: string;
  ip: string;
  host_name: string;
  version: string;
}
// 考试机注销: params: { seat: 1, host: "ip"}
export const MSG_CLIENT_UNREGISTERED = "client.unregistered";
interface MsgClientUnregisterd {
  seat: number;
  host: string;
}
// 考试机上线: params: { seat: 1,  host: "ip"}
export const MSG_CLIENT_CONNECTED = "client.connected";
interface MsgClientConnected {
  seat: number;
  host: string;
  version: string;
}

// 考试机信息更新: params: {seat, host, version}
export const MSG_CLIENT_UPDATED = "client.update";
interface MsgClientUpdate {
  seat: number;
  host: string;
  version: string;
}

// 考试机下线: params: { seat: 1,  host: "ip"}
export const MSG_CLIENT_DISCONNECTED = "client.disconnected";
interface MsgClientDisconnected {
  seat: number;
  host?: string;
}
// 考试机状态变化: params: { seat: 1, ...}
export const MSG_CLIENT_STATUS = "client.status";
interface MsgClientStatus {
  seat: number;
  status: any;
}
// 转移考试请求: params: [{ permit_id: "", seat: 1, new_seat: 2, host: "ip"}, ...]
export const MSG_TRANSFER_REQ = "msg.transfer.req";
export interface MsgTransferReq {
  permit_id: string;
  seat: number;
  new_seat: number;
  host: string;
  full_name: string;
  occurred_at: number;
}
// 注册信息不正确
export const MSG_REGISTER_INVALID = "msg.register.invalid";

// 同步状态通知: params: {message: "", status: 'failed/success/etc.', statusCode?: 403 ,time?: 1213123, detail?: any}
export const NOTIFY_SYNC_STATUS = "notify.sync.status";
type NotifySyncStatus = CloudSyncStatus | CenterSyncStatus;
export interface CloudSyncStatus {
  syncType: "cloud";
  message: string;
  status?: string;
  statusCode?: number;
  time?: number;
  detail?: any;
  error?: Manager.CloudError;
}
export interface CenterSyncStatus {
  syncType: "center";
  message: string;
  status?: string;
  statusCode?: number;
  time?: number;
  detail?: any;
  error?: Manager.CenterError;
}
// 上传状态通知: params: {status: failed/success}
export const NOTIFY_UPLOAD_STATUS = "notify.upload.status";
interface NotifyUploadStatus {
  status: number;
  session_id: string;
  center?: boolean;
  err?: string;
}
// 密码同步状态: params: {status: failed/success, session_id: 'dsfasdf'}
export const NOTIFY_PASSWORD_STATUS = "notify.password.status";
interface NotifyPasswordStatus {
  status: "failed" | "success";
  session_id: string;
}
// 试卷答案同步状态
export const NOTIFY_FORMKEY_STATUS = "notify.formkey.status";
interface NotifyFormkeyStatus {
  status: "failed" | "success";
  session_id: string;
  err?: any;
}
// 试卷同步状态: params: {status: failed/success, session_id: 'dsfasdf'}
export const NOTIFY_FORM_STATUS = "notify.form.status";
interface NotifyFormStatus {
  status: "failed" | "success";
  session_id: string;
}
// 试卷准备就绪情况
export const NOTIFY_FORM_READY = "notify.form.ready";
interface NotifyFormReady {
  status: "failed" | "success";
  session_id: string;
  err?: any;
}
// 场次列表更新
export const NOTIFY_SESSION_STATUS = "notify.session.status";
// 场次信息更新: params: {session_id: 'dsfasdf', session: {config:{...}}
export const NOTIFY_SESSION_INFO = "notify.session.info";
interface NotifySessionInfo {
  session_id: string;
  session: { config: SessionConfig };
}
// 系统设置更新
export const NOTIFY_SETTING = "notify.system.setting";
type NotifySetting = Partial<SystemSetting>;
// 场次删除状态通知: params: {session_id: 'dfasdfasd'}
export const NOTIFY_SESSION_DELETE = "notify.session.delete";
interface NotifySessionDelete {
  session_id: string;
}
// 场次异常记录通知
export const NOTIFY_SESSION_ACTION = "notify.session.action";
interface NotifySessionAction {
  session_id: string;
  entry_id: string;
  entry_status: number;
  full_name: string;
  seat_number: number;
  action: {
    content: string;
    event_time: number;
  };
}
// 读取身份证信息通知
export const MSG_ID_CARD = "msg.id.card";
interface MsgIdCard {
  name: string;
  gender: "0" | "1";
  dateOfBirth: string;
  authority: string;
  id: string;
  beginOfValidity: string;
  endOfValidity: string;
  residentialAddress: string;
  currentResidentialAddress: string;
  ethnicity: number;
  photo: string;
}

// 监控客户端状态消息
export const MSG_MONITOR_STATUS = "msg.monitor.status";
export interface MsgMonitorStatus {
  deviceCode: string;
  position: string;
  lastUpdateTime?: number; // 最后更新时间 second
  statusInfo: {
    scheduleId: string;
    position: string;
    recording: 0 | 1; // 是否录制中 1：录制中 0：未录制
    pushing: 0 | 1; // 是否推流中 1：推流中 0：未推流
    recordResolution: string; // 实际录制分辨率
    clarity: "SD" | "HD" | "UHD"; // 清晰度(从配置获取) SD: 标清, HD: 高清, UHD: 超清
    errorCode: string; // DISK_WARNING DISK_FULL OK
    errorMessage?: string;
  };
}

// 更新考试机配置 { "KEY": value }
export const NOTIFY_CLIENT_SETTING_UPDATE = "msg.client.setting.update";

/**
 * 其他消息
 */
// 时间广播 params: { time: 111111  }, 单位秒
export const MSG_TIME = "msg.time";

// 断开连接 params: { reason: '踢出原因' }
export const MSG_KICKDOWN = "msg.kickdown";

/* *
 * 错误码
 * */

/*常见错误代码如下*/
/*
| 代码 | 说明                     |
|------|--------------------------|
| 0    | 执行成功                 |
| 1    | 不支持该请求             |
| 2    | 参数不正确               |
| 3    | 考试机注册信息失效       |
| 4    | 未登录                   |
| 5    | 未知错误                 |
| 6    | 取消                     |
| 7    | 未处理完成, 等待后续消息 |
| 8    | 机号冲突                 |
*/
export const ERR_OK = 0;
export const ERR_NOT_SUPPORT = 1;
export const ERR_INVALID_ARGS = 2;
export const ERR_INVALID_REGISTER = 3;
export const ERR_LOGIN_REQUIRED = 4;
export const ERR_UNKNOWN = 5;
export const ERR_ABORT = 6;
export const ERR_PENDING = 7; // 待处理
export const ERR_CONFLICT = 8; // 机号冲突
export const ERR_EXCEPT = 9; // 异常
export const ERR_NET_ERR = 10; // 网络错误
export const ERR_TARGET_NOT_FOUND = 11; // 目标不存在
export const ERR_TIMEOUT = 12; // timeout
export const ERR_UUID_MISMATCH = 13; // 考试机UUID冲突

export interface MsgType {
  [MSG_ENABLE_REGISTER]: MsgEnableRegister;
  [MSG_TRANSFER]: MsgTransfer;
  [MSG_RESET_CLIENT]: MsgResetClient;
  [MSG_CHANGE_SEAT_NUMBER]: MsgChangeSeatNumber;
  [MSG_SESSION_ENTER]: MsgSessionEnter;

  [MSG_SESSION_DELAY]: MsgSessionDelay;
  [MSG_SESSION_UNDO]: MsgSessionUndo;
  [MSG_PHOTO_UPDATED]: { session_id: string };
  [MSG_ROOM_CHANGE]: MsgRoomChange;

  [MSG_CLIENT_REGISTER]: MsgClientRegister;
  [MSG_SEND_PROGRESS]: MsgSendProgress;
  [MSG_EVENT_PROGRESS]: MsgEventProgerss;
  [MSG_DOWN_FILE]: MsgDownFile;
  [MSG_EXECUTE_COMMAND]: MsgExecuteCommand;
  [MSG_SEND_FILE]: MsgSendFile;
  [MSG_CLIENT_LOGIN]: MsgClientLogin;
  [MSG_SCAN_CLIENT]: MsgScanClient;
  [MSG_FOUND_CLIENT]: MsgFoundClient;
  [MSG_ADD_CLIENT]: MsgAddClient;
  [MSG_START_CLIENT]: MsgStartClient;
  [MSG_CLOSE_CLIENT]: MsgCloseClient;
  [MSG_CLIENT_REGISTERED]: MsgClientRegisterd;
  [MSG_CLIENT_UNREGISTERED]: MsgClientUnregisterd;
  [MSG_CLIENT_CONNECTED]: MsgClientConnected;
  [MSG_CLIENT_UPDATED]: MsgClientUpdate;
  [MSG_CLIENT_DISCONNECTED]: MsgClientDisconnected;
  [MSG_CLIENT_STATUS]: MsgClientStatus;
  [MSG_TRANSFER_REQ]: MsgTransferReq[];
  [MSG_ID_CARD]: MsgIdCard;
  [NOTIFY_SYNC_STATUS]: NotifySyncStatus;
  [NOTIFY_PASSWORD_STATUS]: NotifyPasswordStatus;
  [NOTIFY_FORMKEY_STATUS]: NotifyFormkeyStatus;
  [NOTIFY_FORM_STATUS]: NotifyFormStatus;
  [NOTIFY_FORM_READY]: NotifyFormReady;
  [NOTIFY_SESSION_INFO]: NotifySessionInfo;
  [NOTIFY_SESSION_DELETE]: NotifySessionDelete;
  [NOTIFY_UPLOAD_STATUS]: NotifyUploadStatus;
  [MSG_CHECK]: MsgCheck;
  [NOTIFY_SESSION_ACTION]: NotifySessionAction;
  [MSG_FILE_PROGRESS]: MsgFileProgress;
  [MSG_SENDTO]: MsgSendTo;
  [MSG_CALLSEAT]: MsgCallSeat;
  [NOTIFY_SETTING]: NotifySetting;
  [MSG_RESPONSE_EVENT]: MsgResponseEvent;
  [MSG_MONITOR_STATUS]: MsgMonitorStatus;
  [MSG_KILL_PROCESS]: MsgKillProcess;
  [MSG_RESTORE_CARD_CHECK]: MsgRestoreCardCheck;
  [MSG_RESTORE_CARD_REPORT]: MsgRestoreCardReport;
  [NOTIFY_RESTORE_CARD_UPDATE]: NotifyRestoreCardUpdate;
  [key: string]: any;
}
