{"ast": null, "code": "/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { DenseMatrixDependencies } from './dependenciesDenseMatrixClass.generated.js';\nimport { SparseMatrixDependencies } from './dependenciesSparseMatrixClass.generated.js';\nimport { addScalarDependencies } from './dependenciesAddScalar.generated.js';\nimport { concatDependencies } from './dependenciesConcat.generated.js';\nimport { equalScalarDependencies } from './dependenciesEqualScalar.generated.js';\nimport { matrixDependencies } from './dependenciesMatrix.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createAdd } from '../../factoriesAny.js';\nexport var addDependencies = {\n  DenseMatrixDependencies,\n  SparseMatrixDependencies,\n  addScalarDependencies,\n  concatDependencies,\n  equalScalarDependencies,\n  matrixDependencies,\n  typedDependencies,\n  createAdd\n};", "map": null, "metadata": {}, "sourceType": "module"}