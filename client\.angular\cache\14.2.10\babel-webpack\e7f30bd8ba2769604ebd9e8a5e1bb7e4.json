{"ast": null, "code": "/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { parseDependencies } from './dependenciesParse.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createLeafCount } from '../../factoriesAny.js';\nexport var leafCountDependencies = {\n  parseDependencies,\n  typedDependencies,\n  createLeafCount\n};", "map": {"version": 3, "names": ["parseDependencies", "typedDependencies", "createLeafCount", "leafCountDependencies"], "sources": ["D:/work/joyserver/client/node_modules/mathjs/lib/esm/entry/dependenciesAny/dependenciesLeafCount.generated.js"], "sourcesContent": ["/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { parseDependencies } from './dependenciesParse.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createLeafCount } from '../../factoriesAny.js';\nexport var leafCountDependencies = {\n  parseDependencies,\n  typedDependencies,\n  createLeafCount\n};"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,iBAAT,QAAkC,kCAAlC;AACA,SAASC,iBAAT,QAAkC,kCAAlC;AACA,SAASC,eAAT,QAAgC,uBAAhC;AACA,OAAO,IAAIC,qBAAqB,GAAG;EACjCH,iBADiC;EAEjCC,iBAFiC;EAGjCC;AAHiC,CAA5B"}, "metadata": {}, "sourceType": "module"}