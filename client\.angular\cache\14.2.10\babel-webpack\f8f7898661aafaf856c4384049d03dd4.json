{"ast": null, "code": "export var stringDocs = {\n  name: 'string',\n  category: 'Construction',\n  syntax: ['\"text\"', 'string(x)'],\n  description: 'Create a string or convert a value to a string',\n  examples: ['\"Hello World!\"', 'string(4.2)', 'string(3 + 2i)'],\n  seealso: ['bignumber', 'boolean', 'complex', 'index', 'matrix', 'number', 'unit']\n};", "map": null, "metadata": {}, "sourceType": "module"}