{"ast": null, "code": "// Copyright (c) 2006-2024, <PERSON>, All Rights Reserved.\n// SPDX-License-Identifier: LGPL-2.1+\n// https://github.com/DrTimothyAldenDavis/SuiteSparse/tree/dev/CSparse/Source\nimport { csFlip } from './csFlip.js';\n/**\n * Flips the value if it is negative of returns the same value otherwise.\n *\n * @param {Number}  i               The value to flip\n */\n\nexport function csUnflip(i) {\n  // flip the value if it is negative\n  return i < 0 ? csFlip(i) : i;\n}", "map": {"version": 3, "names": ["csFlip", "csUnflip", "i"], "sources": ["D:/work/joyserver/client/node_modules/mathjs/lib/esm/function/algebra/sparse/csUnflip.js"], "sourcesContent": ["// Copyright (c) 2006-2024, <PERSON>, All Rights Reserved.\n// SPDX-License-Identifier: LGPL-2.1+\n// https://github.com/DrTimothyAldenDavis/SuiteSparse/tree/dev/CSparse/Source\nimport { csFlip } from './csFlip.js';\n\n/**\n * Flips the value if it is negative of returns the same value otherwise.\n *\n * @param {Number}  i               The value to flip\n */\nexport function csUnflip(i) {\n  // flip the value if it is negative\n  return i < 0 ? csFlip(i) : i;\n}"], "mappings": "AAAA;AACA;AACA;AACA,SAASA,MAAT,QAAuB,aAAvB;AAEA;AACA;AACA;AACA;AACA;;AACA,OAAO,SAASC,QAAT,CAAkBC,CAAlB,EAAqB;EAC1B;EACA,OAAOA,CAAC,GAAG,CAAJ,GAAQF,MAAM,CAACE,CAAD,CAAd,GAAoBA,CAA3B;AACD"}, "metadata": {}, "sourceType": "module"}