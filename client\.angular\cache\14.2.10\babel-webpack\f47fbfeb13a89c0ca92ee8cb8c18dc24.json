{"ast": null, "code": "import { isFunction } from './isFunction';\nimport { isScheduler } from './isScheduler';\n\nfunction last(arr) {\n  return arr[arr.length - 1];\n}\n\nexport function popResultSelector(args) {\n  return isFunction(last(args)) ? args.pop() : undefined;\n}\nexport function popScheduler(args) {\n  return isScheduler(last(args)) ? args.pop() : undefined;\n}\nexport function popNumber(args, defaultValue) {\n  return typeof last(args) === 'number' ? args.pop() : defaultValue;\n}", "map": {"version": 3, "names": ["isFunction", "isScheduler", "last", "arr", "length", "popResultSelector", "args", "pop", "undefined", "popScheduler", "popNumber", "defaultValue"], "sources": ["D:/work/joyserver/client/node_modules/rxjs/dist/esm/internal/util/args.js"], "sourcesContent": ["import { isFunction } from './isFunction';\nimport { isScheduler } from './isScheduler';\nfunction last(arr) {\n    return arr[arr.length - 1];\n}\nexport function popResultSelector(args) {\n    return isFunction(last(args)) ? args.pop() : undefined;\n}\nexport function popScheduler(args) {\n    return isScheduler(last(args)) ? args.pop() : undefined;\n}\nexport function popNumber(args, defaultValue) {\n    return typeof last(args) === 'number' ? args.pop() : defaultValue;\n}\n"], "mappings": "AAAA,SAASA,UAAT,QAA2B,cAA3B;AACA,SAASC,WAAT,QAA4B,eAA5B;;AACA,SAASC,IAAT,CAAcC,GAAd,EAAmB;EACf,OAAOA,GAAG,CAACA,GAAG,CAACC,MAAJ,GAAa,CAAd,CAAV;AACH;;AACD,OAAO,SAASC,iBAAT,CAA2BC,IAA3B,EAAiC;EACpC,OAAON,UAAU,CAACE,IAAI,CAACI,IAAD,CAAL,CAAV,GAAyBA,IAAI,CAACC,GAAL,EAAzB,GAAsCC,SAA7C;AACH;AACD,OAAO,SAASC,YAAT,CAAsBH,IAAtB,EAA4B;EAC/B,OAAOL,WAAW,CAACC,IAAI,CAACI,IAAD,CAAL,CAAX,GAA0BA,IAAI,CAACC,GAAL,EAA1B,GAAuCC,SAA9C;AACH;AACD,OAAO,SAASE,SAAT,CAAmBJ,IAAnB,EAAyBK,YAAzB,EAAuC;EAC1C,OAAO,OAAOT,IAAI,CAACI,IAAD,CAAX,KAAsB,QAAtB,GAAiCA,IAAI,CAACC,GAAL,EAAjC,GAA8CI,YAArD;AACH"}, "metadata": {}, "sourceType": "module"}