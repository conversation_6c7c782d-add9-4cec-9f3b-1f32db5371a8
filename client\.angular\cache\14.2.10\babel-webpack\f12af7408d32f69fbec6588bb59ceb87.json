{"ast": null, "code": "export var eigsDocs = {\n  name: 'eigs',\n  category: 'Matrix',\n  syntax: ['eigs(x)'],\n  description: 'Calculate the eigenvalues and optionally eigenvectors of a square matrix',\n  examples: ['eigs([[5, 2.3], [2.3, 1]])', 'eigs([[1, 2, 3], [4, 5, 6], [7, 8, 9]], { precision: 1e-6, eigenvectors: false })'],\n  seealso: ['inv']\n};", "map": {"version": 3, "names": ["eigsDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["D:/work/joyserver/client/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/matrix/eigs.js"], "sourcesContent": ["export var eigsDocs = {\n  name: 'eigs',\n  category: 'Matrix',\n  syntax: ['eigs(x)'],\n  description: 'Calculate the eigenvalues and optionally eigenvectors of a square matrix',\n  examples: ['eigs([[5, 2.3], [2.3, 1]])', 'eigs([[1, 2, 3], [4, 5, 6], [7, 8, 9]], { precision: 1e-6, eigenvectors: false })'],\n  seealso: ['inv']\n};"], "mappings": "AAAA,OAAO,IAAIA,QAAQ,GAAG;EACpBC,IAAI,EAAE,MADc;EAEpBC,QAAQ,EAAE,QAFU;EAGpBC,MAAM,EAAE,CAAC,SAAD,CAHY;EAIpBC,WAAW,EAAE,0EAJO;EAKpBC,QAAQ,EAAE,CAAC,4BAAD,EAA+B,mFAA/B,CALU;EAMpBC,OAAO,EAAE,CAAC,KAAD;AANW,CAAf"}, "metadata": {}, "sourceType": "module"}