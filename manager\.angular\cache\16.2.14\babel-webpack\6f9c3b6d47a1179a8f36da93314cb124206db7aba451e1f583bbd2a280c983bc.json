{"ast": null, "code": "import { Subject } from \"rxjs\";\nimport { map, takeUntil } from \"rxjs/operators\";\nimport * as M from \"@app/core/io\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./restore-card-check.service\";\nimport * as i2 from \"@app/core/services/socket.service\";\nimport * as i3 from \"ng-zorro-antd/button\";\nimport * as i4 from \"ng-zorro-antd/core/transition-patch\";\nimport * as i5 from \"ng-zorro-antd/core/wave\";\nimport * as i6 from \"ng-zorro-antd/icon\";\nimport * as i7 from \"ng-zorro-antd/table\";\nimport * as i8 from \"ng-zorro-antd/tooltip\";\nimport * as i9 from \"ng-zorro-antd/progress\";\nimport * as i10 from \"@angular/common\";\nimport * as i11 from \"@shared/components/custom-modal\";\nimport * as i12 from \"@ngx-translate/core\";\nfunction ClientRestoreCardModalComponent_custom_modal_0_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 28)(1, \"div\", 29);\n    i0.ɵɵelement(2, \"nz-progress\", 30);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const progress_r4 = ctx.ngIf;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"nzPercent\", progress_r4.percentage)(\"nzFormat\", ctx_r1.formatProgress);\n  }\n}\nfunction ClientRestoreCardModalComponent_custom_modal_0_tr_62_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 39)(1, \"small\", 40);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const client_r5 = i0.ɵɵnextContext().$implicit;\n    const ctx_r6 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r6.getRetryStatusText(client_r5.seat_number));\n  }\n}\nfunction ClientRestoreCardModalComponent_custom_modal_0_tr_62_button_21_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 41);\n    i0.ɵɵlistener(\"click\", function ClientRestoreCardModalComponent_custom_modal_0_tr_62_button_21_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const client_r5 = i0.ɵɵnextContext().$implicit;\n      const ctx_r9 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r9.onManualRetryClient(client_r5.seat_number));\n    });\n    i0.ɵɵpipe(1, \"async\");\n    i0.ɵɵtext(2, \" \\u91CD\\u8BD5 \");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const client_r5 = i0.ɵɵnextContext().$implicit;\n    const ctx_r7 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"disabled\", !client_r5.is_online || !ctx_r7.canRetryClient(client_r5.seat_number) || i0.ɵɵpipeBind1(1, 1, ctx_r7.clientCheckLoading$));\n  }\n}\nfunction ClientRestoreCardModalComponent_custom_modal_0_tr_62_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\")(6, \"span\", 31);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"td\")(9, \"div\", 32)(10, \"div\", 33)(11, \"span\", 34);\n    i0.ɵɵelement(12, \"i\", 35);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(14, ClientRestoreCardModalComponent_custom_modal_0_tr_62_div_14_Template, 3, 1, \"div\", 36);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(15, \"td\")(16, \"div\", 15)(17, \"button\", 37);\n    i0.ɵɵlistener(\"click\", function ClientRestoreCardModalComponent_custom_modal_0_tr_62_Template_button_click_17_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r14);\n      const client_r5 = restoredCtx.$implicit;\n      const ctx_r13 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r13.onCheckSingleClient(client_r5.seat_number));\n    });\n    i0.ɵɵpipe(18, \"async\");\n    i0.ɵɵpipe(19, \"async\");\n    i0.ɵɵtext(20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(21, ClientRestoreCardModalComponent_custom_modal_0_tr_62_button_21_Template, 3, 3, \"button\", 38);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const client_r5 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(client_r5.seat_number);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(client_r5.ip);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", client_r5.is_online ? \"connection-status-online\" : \"connection-status-offline\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", client_r5.is_online ? \"\\u5728\\u7EBF\" : \"\\u79BB\\u7EBF\", \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngClass\", \"status-\" + ctx_r3.getClientStatus(client_r5.seat_number));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"nzType\", ctx_r3.getStatusDisplay(ctx_r3.getClientStatus(client_r5.seat_number)).icon)(\"nzSpin\", ctx_r3.isStatusLoading(ctx_r3.getClientStatus(client_r5.seat_number)));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.getStatusDisplay(ctx_r3.getClientStatus(client_r5.seat_number)).text, \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.isClientTimeout(client_r5.seat_number) && ctx_r3.getRetryStatusText(client_r5.seat_number));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"nzLoading\", i0.ɵɵpipeBind1(18, 13, ctx_r3.isSingleClientLoading(client_r5.seat_number)))(\"disabled\", !client_r5.is_online || i0.ɵɵpipeBind1(19, 15, ctx_r3.clientCheckLoading$));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.getSingleClientCheckAction(client_r5.seat_number) === \"verify\" ? \"\\u9A8C\\u8BC1\" : \"\\u68C0\\u67E5\", \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.isClientTimeout(client_r5.seat_number));\n  }\n}\nconst _c0 = function () {\n  return {\n    y: \"300px\"\n  };\n};\nfunction ClientRestoreCardModalComponent_custom_modal_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"custom-modal\", 1);\n    i0.ɵɵlistener(\"onClose\", function ClientRestoreCardModalComponent_custom_modal_0_Template_custom_modal_onClose_0_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r15 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r15.onClose());\n    });\n    i0.ɵɵelementStart(1, \"div\", 2);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 3)(5, \"p\");\n    i0.ɵɵtext(6, \"\\u5F53\\u524D\\u64CD\\u4F5C\\u4E3A\\u68C0\\u67E5\\u8FD8\\u539F\\u5361\\u662F\\u5426\\u5904\\u4E8E\\u5173\\u95ED\\u72B6\\u6001\\uFF0C\\u7CFB\\u7EDF\\u5C06\\u81EA\\u52A8\\u5173\\u95ED\\u60A8\\u7684\\u7535\\u8111\\u5E76\\u91CD\\u542F\\u3002\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\");\n    i0.ɵɵtext(8, \"\\u8BF7\\u8FDB\\u884C\\u8003\\u8BD5\\u673A\\u8FD8\\u539F\\u5361\\u68C0\\u67E5\\u3002\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 4)(10, \"div\", 5);\n    i0.ɵɵtemplate(11, ClientRestoreCardModalComponent_custom_modal_0_div_11_Template, 3, 2, \"div\", 6);\n    i0.ɵɵpipe(12, \"async\");\n    i0.ɵɵelementStart(13, \"div\", 7)(14, \"div\", 8)(15, \"div\", 9)(16, \"span\", 10);\n    i0.ɵɵtext(17, \"\\u6210\\u529F\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"span\", 11);\n    i0.ɵɵtext(19);\n    i0.ɵɵpipe(20, \"async\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(21, \"div\", 9)(22, \"span\", 10);\n    i0.ɵɵtext(23, \"\\u5931\\u8D25\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"span\", 12);\n    i0.ɵɵtext(25);\n    i0.ɵɵpipe(26, \"async\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(27, \"div\", 9)(28, \"span\", 10);\n    i0.ɵɵtext(29, \"\\u603B\\u6570\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"span\", 13);\n    i0.ɵɵtext(31);\n    i0.ɵɵpipe(32, \"async\");\n    i0.ɵɵelementEnd()()()()()();\n    i0.ɵɵelementStart(33, \"div\", 14)(34, \"span\");\n    i0.ɵɵtext(35, \"\\u8003\\u8BD5\\u673A\\u68C0\\u67E5\\u7ED3\\u679C\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(36, \"div\", 15)(37, \"button\", 16);\n    i0.ɵɵlistener(\"click\", function ClientRestoreCardModalComponent_custom_modal_0_Template_button_click_37_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r17 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r17.onRefreshResult());\n    });\n    i0.ɵɵpipe(38, \"async\");\n    i0.ɵɵtext(39, \" \\u5237\\u65B0\\u7ED3\\u679C \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(40, \"button\", 17);\n    i0.ɵɵlistener(\"click\", function ClientRestoreCardModalComponent_custom_modal_0_Template_button_click_40_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r18 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r18.onToggleRetry());\n    });\n    i0.ɵɵtext(41);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(42, \"nz-table\", 18, 19);\n    i0.ɵɵpipe(44, \"async\");\n    i0.ɵɵpipe(45, \"async\");\n    i0.ɵɵelementStart(46, \"thead\")(47, \"tr\")(48, \"th\", 20);\n    i0.ɵɵtext(49);\n    i0.ɵɵpipe(50, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(51, \"th\", 21);\n    i0.ɵɵtext(52);\n    i0.ɵɵpipe(53, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(54, \"th\", 22);\n    i0.ɵɵtext(55, \"\\u8FDE\\u63A5\\u72B6\\u6001\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(56, \"th\");\n    i0.ɵɵtext(57);\n    i0.ɵɵpipe(58, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(59, \"th\", 20);\n    i0.ɵɵtext(60, \"\\u64CD\\u4F5C\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(61, \"tbody\");\n    i0.ɵɵtemplate(62, ClientRestoreCardModalComponent_custom_modal_0_tr_62_Template, 22, 17, \"tr\", 23);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(63, \"div\", 24)(64, \"button\", 25);\n    i0.ɵɵlistener(\"click\", function ClientRestoreCardModalComponent_custom_modal_0_Template_button_click_64_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r19 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r19.onStartCheck());\n    });\n    i0.ɵɵpipe(65, \"async\");\n    i0.ɵɵtext(66, \" \\u5F00\\u59CB\\u68C0\\u67E5 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(67, \"button\", 26);\n    i0.ɵɵlistener(\"click\", function ClientRestoreCardModalComponent_custom_modal_0_Template_button_click_67_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r20 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r20.onSaveCheckResult());\n    });\n    i0.ɵɵtext(68, \"\\u4FDD\\u5B58\\u7ED3\\u679C\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(69, \"button\", 27);\n    i0.ɵɵlistener(\"click\", function ClientRestoreCardModalComponent_custom_modal_0_Template_button_click_69_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r21 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r21.onClose());\n    });\n    i0.ɵɵtext(70, \"\\u5173\\u95ED\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const _r2 = i0.ɵɵreference(43);\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"width\", \"850px\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 20, \"prepare.restoreCardCheck\"), \" \");\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(12, 22, ctx_r0.getCheckProgress()));\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(20, 24, ctx_r0.successCount$), \" \");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(26, 26, ctx_r0.failedCount$), \" \");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(32, 28, ctx_r0.getTotalClientCount()), \" \");\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"nzLoading\", i0.ɵɵpipeBind1(38, 30, ctx_r0.clientListLoading$));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"nzType\", ctx_r0.isRetryEnabled() ? \"primary\" : \"default\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.isRetryEnabled() ? \"\\u505C\\u6B62\\u91CD\\u8BD5\" : \"\\u542F\\u7528\\u91CD\\u8BD5\", \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"nzData\", i0.ɵɵpipeBind1(44, 32, ctx_r0.clientList$))(\"nzLoading\", i0.ɵɵpipeBind1(45, 34, ctx_r0.clientListLoading$))(\"nzShowPagination\", false)(\"nzFrontPagination\", false)(\"nzSize\", \"small\")(\"nzScroll\", i0.ɵɵpureFunction0(44, _c0));\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(50, 36, \"table.seatNumber\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(53, 38, \"table.ipAddress\"));\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(58, 40, \"table.status\"));\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", _r2.data);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"nzLoading\", i0.ɵɵpipeBind1(65, 42, ctx_r0.clientCheckLoading$));\n  }\n}\nexport class ClientRestoreCardModalComponent {\n  constructor(restoreCardService, socketService) {\n    this.restoreCardService = restoreCardService;\n    this.socketService = socketService;\n    this.destroy$ = new Subject();\n    this.formatProgress = percent => {\n      return `${percent}%`;\n    };\n    this.visible$ = this.restoreCardService.clientCheckModal$;\n    this.clientList$ = this.restoreCardService.clientList$;\n    this.loading$ = this.restoreCardService.loading$;\n    this.counts$ = this.restoreCardService.counts$;\n    this.serviceState$ = this.restoreCardService.serviceState$;\n    this.successCount$ = this.counts$.pipe(map(counts => counts.success));\n    this.failedCount$ = this.counts$.pipe(map(counts => counts.failed));\n    this.clientCheckLoading$ = this.loading$;\n    this.clientListLoading$ = this.loading$;\n  }\n  ngOnInit() {\n    this.serviceState$.pipe(takeUntil(this.destroy$)).subscribe(state => {\n      console.log(\"ClientRestoreCardModal: Service state updated\", {\n        clientCount: state.clientStates.size,\n        successCount: state.counts.success,\n        failedCount: state.counts.failed,\n        isLoading: state.isLoading\n      });\n    });\n    this.socketService.getMsg$(M.NOTIFY_RESTORE_CARD_UPDATE).pipe(takeUntil(this.destroy$)).subscribe(msg => {\n      console.log(\"RestoreCardUpdate\", msg);\n      this.restoreCardService.handleRestoreCardUpdate(msg.req.params);\n    });\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  // 事件处理方法\n  onClose() {\n    this.restoreCardService.closeClientCheckModal();\n  }\n  onStartCheck() {\n    this.restoreCardService.checkClientWithRefresh();\n  }\n  onRefreshResult() {\n    this.restoreCardService.checkClientVerify();\n  }\n  // 新增：状态显示相关方法\n  getClientStatus(seatNumber) {\n    return this.restoreCardService.getClientStatus(seatNumber);\n  }\n  getStatusDisplay(status) {\n    const statusMap = {\n      NotChecked: {\n        text: \"未检查\",\n        icon: \"clock-circle\",\n        color: \"default\"\n      },\n      Writing: {\n        text: \"检查中\",\n        icon: \"loading\",\n        color: \"processing\",\n        spinning: true\n      },\n      Restarting: {\n        text: \"重启中\",\n        icon: \"reload\",\n        color: \"warning\",\n        spinning: true\n      },\n      Verifying: {\n        text: \"验证中\",\n        icon: \"search\",\n        color: \"processing\",\n        spinning: true\n      },\n      Success: {\n        text: \"通过\",\n        icon: \"check-circle\",\n        color: \"success\"\n      },\n      Failed: {\n        text: \"未通过\",\n        icon: \"close-circle\",\n        color: \"error\"\n      },\n      Timeout: {\n        text: \"超时\",\n        icon: \"exclamation-circle\",\n        color: \"error\"\n      }\n    };\n    return statusMap[status] || statusMap[\"NotChecked\"];\n  }\n  isStatusLoading(status) {\n    return [\"Writing\", \"Restarting\", \"Verifying\"].includes(status);\n  }\n  showProgress(status) {\n    return this.isStatusLoading(status);\n  }\n  getProgressPercent(status) {\n    const progressMap = {\n      Writing: 33,\n      Restarting: 66,\n      Verifying: 90\n    };\n    return progressMap[status] || 0;\n  }\n  // 新增：获取客户端状态详情\n  getClientStateDetail(seatNumber) {\n    const clientState = this.restoreCardService.getClientState(seatNumber);\n    if (!clientState) {\n      return null;\n    }\n    return {\n      clientInfo: clientState.clientInfo,\n      checkInfo: clientState.checkInfo,\n      lastUpdated: clientState.lastUpdated,\n      statusDisplay: this.getStatusDisplay(clientState.checkInfo.status)\n    };\n  }\n  hasClientData() {\n    return this.serviceState$.pipe(map(state => state.clientStates.size > 0));\n  }\n  getTotalClientCount() {\n    return this.serviceState$.pipe(map(state => state.counts.total));\n  }\n  isAllClientsCompleted() {\n    return this.serviceState$.pipe(map(state => {\n      const clientStates = Array.from(state.clientStates.values());\n      return clientStates.length > 0 && clientStates.every(clientState => clientState.checkInfo.status === \"Success\" || clientState.checkInfo.status === \"Failed\" || clientState.checkInfo.status === \"Timeout\");\n    }));\n  }\n  onResetClient(seatNumber) {\n    console.log(`ClientRestoreCardModal: Resetting seat ${seatNumber}`);\n    this.restoreCardService.resetClientStatus(seatNumber);\n  }\n  onResetAll() {\n    console.log(\"ClientRestoreCardModal: Resetting all clients\");\n    this.restoreCardService.resetAllClientStatus();\n  }\n  // 手动触发保存检查结果\n  onSaveCheckResult() {\n    console.log(\"ClientRestoreCardModal: Manually triggering save check result\");\n    this.restoreCardService.triggerSaveCheckResult(\"manual_from_modal\");\n  }\n  // 检查完成后自动保存\n  onCheckCompleted() {\n    console.log(\"ClientRestoreCardModal: Check completed, triggering auto-save\");\n    this.restoreCardService.triggerSaveCheckResult(\"check_completed_from_modal\");\n  }\n  // 批量操作方法\n  onStartWriteAndReboot() {\n    console.log(\"ClientRestoreCardModal: Starting write and reboot sequence\");\n    this.restoreCardService.checkClientWriteAndReboot();\n  }\n  onStartRebootOnly() {\n    console.log(\"ClientRestoreCardModal: Starting reboot only\");\n    this.restoreCardService.checkClientReboot();\n  }\n  // 获取检查进度信息\n  getCheckProgress() {\n    return this.serviceState$.pipe(map(state => {\n      const total = state.counts.total;\n      const completed = state.counts.success + state.counts.failed;\n      const percentage = total > 0 ? Math.round(completed / total * 100) : 0;\n      return {\n        completed,\n        total,\n        percentage\n      };\n    }));\n  }\n  // 检查是否可以开始检查\n  canStartCheck() {\n    return this.serviceState$.pipe(map(state => !state.isLoading && state.clientStates.size > 0));\n  }\n  onCheckSingleClient(seatNumber) {\n    const action = this.getSingleClientCheckAction(seatNumber);\n    console.log(`ClientRestoreCardModal: Check single client ${seatNumber} with action ${action}`);\n    this.restoreCardService.checkSingleClient(seatNumber, action);\n  }\n  // 获取单个客户端检查动作\n  getSingleClientCheckAction(seatNumber) {\n    const status = this.getClientStatus(seatNumber);\n    return status === \"NotChecked\" || status === \"Success\" || status === \"Failed\" ? [\"write\", \"reboot\", \"verify\"] : \"verify\";\n  }\n  // 获取单个客户端加载状态\n  isSingleClientLoading(seatNumber) {\n    return this.restoreCardService.isSingleClientLoading(seatNumber);\n  }\n  // 重试相关方法\n  onToggleRetry() {\n    if (this.restoreCardService.isRetryEnabled()) {\n      this.restoreCardService.disableRetry();\n      console.log(\"ClientRestoreCardModal: Retry mechanism disabled by user\");\n    } else {\n      this.restoreCardService.enableRetry();\n      console.log(\"ClientRestoreCardModal: Retry mechanism enabled by user\");\n    }\n  }\n  isRetryEnabled() {\n    return this.restoreCardService.isRetryEnabled();\n  }\n  onManualRetryClient(seatNumber) {\n    console.log(`ClientRestoreCardModal: Manual retry triggered for client ${seatNumber}`);\n    this.restoreCardService.manualRetryClient(seatNumber);\n  }\n  getClientRetryInfo(seatNumber) {\n    return this.restoreCardService.getClientRetryInfo(seatNumber);\n  }\n  isClientTimeout(seatNumber) {\n    return this.getClientStatus(seatNumber) === \"Timeout\";\n  }\n  canRetryClient(seatNumber) {\n    const retryInfo = this.getClientRetryInfo(seatNumber);\n    if (!retryInfo) return true;\n    const now = Date.now();\n    const maxRetryCount = 10; // 与服务中的 MAX_RETRY_COUNT 保持一致\n    const maxRetryDuration = 600000; // 与服务中的 MAX_RETRY_DURATION 保持一致\n    return retryInfo.retryCount < maxRetryCount && now - retryInfo.firstTimeoutTime < maxRetryDuration;\n  }\n  getRetryStatusText(seatNumber) {\n    const retryInfo = this.getClientRetryInfo(seatNumber);\n    if (!retryInfo) return \"\";\n    const maxRetryCount = 10;\n    return `重试 ${retryInfo.retryCount}/${maxRetryCount}`;\n  }\n  static #_ = this.ɵfac = function ClientRestoreCardModalComponent_Factory(t) {\n    return new (t || ClientRestoreCardModalComponent)(i0.ɵɵdirectiveInject(i1.RestoreCardCheckService), i0.ɵɵdirectiveInject(i2.SocketService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: ClientRestoreCardModalComponent,\n    selectors: [[\"app-client-restore-card-modal\"]],\n    decls: 2,\n    vars: 3,\n    consts: [[3, \"width\", \"onClose\", 4, \"ngIf\"], [3, \"width\", \"onClose\"], [1, \"header\"], [1, \"body\"], [1, \"status-overview\"], [1, \"overview-row\"], [\"class\", \"progress-section\", 4, \"ngIf\"], [1, \"statistics-section\"], [1, \"statistics-group\"], [1, \"statistic-item\"], [1, \"statistic-title\"], [1, \"statistic-value\", \"success\"], [1, \"statistic-value\", \"failed\"], [1, \"statistic-value\", \"total\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\", \"mb-3\"], [1, \"action-buttons\"], [\"nz-button\", \"\", \"nzType\", \"default\", \"nzSize\", \"small\", 1, \"mr-2\", 3, \"nzLoading\", \"click\"], [\"nz-button\", \"\", \"nzSize\", \"small\", 1, \"mr-2\", 3, \"nzType\", \"click\"], [3, \"nzData\", \"nzLoading\", \"nzShowPagination\", \"nzFrontPagination\", \"nzSize\", \"nzScroll\"], [\"clientListTable\", \"\"], [\"nzWidth\", \"120px\"], [\"nzWidth\", \"180px\"], [\"nzWidth\", \"100px\"], [4, \"ngFor\", \"ngForOf\"], [1, \"footer\"], [\"nz-button\", \"\", \"nzShape\", \"round\", \"type\", \"button\", 1, \"custom-btn-secondary\", 3, \"nzLoading\", \"click\"], [\"type\", \"button\", 1, \"custom-btn-primary\", 3, \"click\"], [\"nz-button\", \"\", \"nzShape\", \"round\", 1, \"custom-btn-secondary\", 3, \"click\"], [1, \"progress-section\"], [1, \"progress-bar\"], [\"nzStrokeColor\", \"#52c41a\", 3, \"nzPercent\", \"nzFormat\"], [1, \"connection-status-tag\", 3, \"ngClass\"], [1, \"status-container\"], [1, \"status-display\"], [3, \"ngClass\"], [\"nz-icon\", \"\", 3, \"nzType\", \"nzSpin\"], [\"class\", \"retry-status\", 4, \"ngIf\"], [\"nz-button\", \"\", \"nzType\", \"primary\", \"nzSize\", \"small\", 1, \"mr-1\", 3, \"nzLoading\", \"disabled\", \"click\"], [\"nz-button\", \"\", \"nzType\", \"default\", \"nzSize\", \"small\", \"nz-tooltip\", \"\\u624B\\u52A8\\u91CD\\u8BD5\\u8D85\\u65F6\\u7684\\u8FD8\\u539F\\u5361\\u68C0\\u67E5\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [1, \"retry-status\"], [1, \"text-muted\"], [\"nz-button\", \"\", \"nzType\", \"default\", \"nzSize\", \"small\", \"nz-tooltip\", \"\\u624B\\u52A8\\u91CD\\u8BD5\\u8D85\\u65F6\\u7684\\u8FD8\\u539F\\u5361\\u68C0\\u67E5\", 3, \"disabled\", \"click\"]],\n    template: function ClientRestoreCardModalComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, ClientRestoreCardModalComponent_custom_modal_0_Template, 71, 45, \"custom-modal\", 0);\n        i0.ɵɵpipe(1, \"async\");\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(1, 1, ctx.visible$));\n      }\n    },\n    dependencies: [i3.NzButtonComponent, i4.ɵNzTransitionPatchDirective, i5.NzWaveDirective, i6.NzIconDirective, i7.NzTableComponent, i7.NzTableCellDirective, i7.NzThMeasureDirective, i7.NzTheadComponent, i7.NzTbodyComponent, i7.NzTrDirective, i8.NzTooltipDirective, i9.NzProgressComponent, i10.NgClass, i10.NgForOf, i10.NgIf, i11.CustomModalComponent, i10.AsyncPipe, i12.TranslatePipe],\n    styles: [\".status-failed[_ngcontent-%COMP%] {\\n  color: #fd6464;\\n}\\n\\n.status-success[_ngcontent-%COMP%] {\\n  color: #73d391;\\n}\\n\\n.status-NotChecked[_ngcontent-%COMP%] {\\n  color: #8c8c8c;\\n}\\n\\n.status-Writing[_ngcontent-%COMP%] {\\n  color: #1890ff;\\n}\\n\\n.status-Restarting[_ngcontent-%COMP%] {\\n  color: #faad14;\\n}\\n\\n.status-Verifying[_ngcontent-%COMP%] {\\n  color: #1890ff;\\n}\\n\\n.status-Success[_ngcontent-%COMP%] {\\n  color: #52c41a;\\n}\\n\\n.status-Failed[_ngcontent-%COMP%] {\\n  color: #ff4d4f;\\n}\\n\\n.status-Timeout[_ngcontent-%COMP%] {\\n  color: #ff4d4f;\\n}\\n\\n.status-Writing[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .status-Restarting[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .status-Verifying[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_spin 1s linear infinite;\\n}\\n\\n@keyframes _ngcontent-%COMP%_spin {\\n  from {\\n    transform: rotate(0deg);\\n  }\\n  to {\\n    transform: rotate(360deg);\\n  }\\n}\\nnz-progress[_ngcontent-%COMP%] {\\n  max-width: 120px;\\n  margin-top: 4px;\\n}\\n\\n.d-flex[_ngcontent-%COMP%] {\\n  display: flex;\\n}\\n\\n.justify-content-between[_ngcontent-%COMP%] {\\n  justify-content: space-between;\\n}\\n\\n.align-items-center[_ngcontent-%COMP%] {\\n  align-items: center;\\n}\\n\\n.mb-3[_ngcontent-%COMP%] {\\n  margin-bottom: 1rem;\\n}\\n\\n.status-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  width: 100%;\\n}\\n.status-container[_ngcontent-%COMP%]   .status-display[_ngcontent-%COMP%] {\\n  flex: 1;\\n  min-width: 0;\\n}\\n.status-container[_ngcontent-%COMP%]   .client-actions[_ngcontent-%COMP%] {\\n  margin-left: 8px;\\n  opacity: 0.6;\\n  transition: opacity 0.2s;\\n}\\n.status-container[_ngcontent-%COMP%]   .client-actions[_ngcontent-%COMP%]:hover {\\n  opacity: 1;\\n}\\n.status-container[_ngcontent-%COMP%]   .client-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  border: none;\\n  padding: 2px 4px;\\n  height: 24px;\\n}\\n.status-container[_ngcontent-%COMP%]   .client-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n}\\n.status-container[_ngcontent-%COMP%]   .client-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:hover {\\n  background-color: #f0f0f0;\\n}\\n\\n.status-overview[_ngcontent-%COMP%] {\\n  background: #fafafa;\\n  padding: 8px 12px;\\n  border-radius: 6px;\\n  border: 1px solid #e8e8e8;\\n  margin: 12px 0;\\n}\\n.status-overview[_ngcontent-%COMP%]   .overview-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 16px;\\n}\\n.status-overview[_ngcontent-%COMP%]   .progress-section[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n}\\n.status-overview[_ngcontent-%COMP%]   .progress-section[_ngcontent-%COMP%]   .progress-bar[_ngcontent-%COMP%] {\\n  width: 100%;\\n  margin-bottom: 4px;\\n}\\n.status-overview[_ngcontent-%COMP%]   .progress-section[_ngcontent-%COMP%]   .progress-bar[_ngcontent-%COMP%]   nz-progress[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n.status-overview[_ngcontent-%COMP%]   .progress-section[_ngcontent-%COMP%]   .progress-text[_ngcontent-%COMP%] {\\n  color: #595959;\\n  font-size: 12px;\\n  font-weight: 500;\\n  text-align: center;\\n}\\n.status-overview[_ngcontent-%COMP%]   .statistics-section[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  justify-content: center;\\n}\\n.status-overview[_ngcontent-%COMP%]   .statistics-section[_ngcontent-%COMP%]   .statistics-group[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  flex-wrap: wrap;\\n  gap: 16px;\\n}\\n.status-overview[_ngcontent-%COMP%]   .statistic-item[_ngcontent-%COMP%], .status-overview[_ngcontent-%COMP%]   .current-phase[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  text-align: center;\\n  gap: 6px;\\n}\\n.status-overview[_ngcontent-%COMP%]   .statistic-item[_ngcontent-%COMP%]   .statistic-title[_ngcontent-%COMP%], .status-overview[_ngcontent-%COMP%]   .current-phase[_ngcontent-%COMP%]   .statistic-title[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #8c8c8c;\\n  font-weight: 500;\\n}\\n.status-overview[_ngcontent-%COMP%]   .statistic-item[_ngcontent-%COMP%]   .statistic-value[_ngcontent-%COMP%], .status-overview[_ngcontent-%COMP%]   .current-phase[_ngcontent-%COMP%]   .statistic-value[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  font-weight: 600;\\n  line-height: 1;\\n}\\n.status-overview[_ngcontent-%COMP%]   .statistic-item[_ngcontent-%COMP%]   .statistic-value.success[_ngcontent-%COMP%], .status-overview[_ngcontent-%COMP%]   .current-phase[_ngcontent-%COMP%]   .statistic-value.success[_ngcontent-%COMP%] {\\n  color: #52c41a;\\n}\\n.status-overview[_ngcontent-%COMP%]   .statistic-item[_ngcontent-%COMP%]   .statistic-value.failed[_ngcontent-%COMP%], .status-overview[_ngcontent-%COMP%]   .current-phase[_ngcontent-%COMP%]   .statistic-value.failed[_ngcontent-%COMP%] {\\n  color: #ff4d4f;\\n}\\n.status-overview[_ngcontent-%COMP%]   .statistic-item[_ngcontent-%COMP%]   .statistic-value.total[_ngcontent-%COMP%], .status-overview[_ngcontent-%COMP%]   .current-phase[_ngcontent-%COMP%]   .statistic-value.total[_ngcontent-%COMP%] {\\n  color: #1890ff;\\n}\\n.status-overview[_ngcontent-%COMP%]   .current-phase[_ngcontent-%COMP%]   .phase-text[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  font-weight: 500;\\n  color: #1890ff;\\n  padding: 2px 8px;\\n  background: rgba(24, 144, 255, 0.1);\\n  border-radius: 4px;\\n  border: 1px solid rgba(24, 144, 255, 0.2);\\n  display: inline-block;\\n  line-height: 20px;\\n}\\n\\n.action-buttons[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 8px;\\n  align-items: center;\\n}\\n.action-buttons[_ngcontent-%COMP%]   .batch-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 6px;\\n}\\n.action-buttons[_ngcontent-%COMP%]   .batch-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  height: 28px;\\n  padding: 0 8px;\\n  border-radius: 4px;\\n}\\n.action-buttons[_ngcontent-%COMP%]   .batch-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-1px);\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n}\\n\\n.footer[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n}\\n.footer[_ngcontent-%COMP%]   .test-controls[_ngcontent-%COMP%]   .ant-btn-group[_ngcontent-%COMP%]   .ant-btn[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  height: 28px;\\n  padding: 0 8px;\\n}\\n.footer[_ngcontent-%COMP%]   .test-controls[_ngcontent-%COMP%]   .ant-btn-group[_ngcontent-%COMP%]   .ant-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  margin-right: 4px;\\n}\\n\\n.connection-status-tag[_ngcontent-%COMP%] {\\n  display: inline-flex;\\n  align-items: center;\\n  gap: 4px;\\n  font-size: 12px;\\n  padding: 2px 8px;\\n  border-radius: 4px;\\n  border: 1px solid;\\n}\\n.connection-status-tag[_ngcontent-%COMP%]   .status-icon[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n}\\n\\n.connection-status-online[_ngcontent-%COMP%] {\\n  color: #52c41a;\\n  background-color: #f6ffed;\\n  border-color: #b7eb8f;\\n}\\n\\n.connection-status-offline[_ngcontent-%COMP%] {\\n  color: #8c8c8c;\\n  background-color: #fafafa;\\n  border-color: #d9d9d9;\\n}\\n\\n.retry-status[_ngcontent-%COMP%] {\\n  margin-top: 4px;\\n}\\n.retry-status[_ngcontent-%COMP%]   .text-muted[_ngcontent-%COMP%] {\\n  color: #8c8c8c;\\n  font-size: 11px;\\n  font-style: italic;\\n}\\n\\ntd[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 4px;\\n  align-items: center;\\n}\\ntd[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .mr-1[_ngcontent-%COMP%] {\\n  margin-right: 4px;\\n}\\ntd[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  height: 24px;\\n  padding: 0 6px;\\n}\\ntd[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.5;\\n  cursor: not-allowed;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvZGFzaGJvYXJkL3Nlc3Npb24tcHJlcGFyZS9wcmVwYXJlLWRldGFpbC9jaGVjay1yZXN0b3JlLWNhcmQvY2xpZW50LXJlc3RvcmUtY2FyZC1tb2RhbC5jb21wb25lbnQuc2NzcyIsIndlYnBhY2s6Ly8uL3NyYy9hc3NldHMvc2Nzcy9fdmFyaWFibGVzLnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBSUE7RUFDRSxjQ09hO0FEVmY7O0FBTUE7RUFDRSxjQ05ZO0FER2Q7O0FBTUE7RUFDRSxjQUFBO0FBSEY7O0FBTUE7RUFDRSxjQUFBO0FBSEY7O0FBTUE7RUFDRSxjQUFBO0FBSEY7O0FBTUE7RUFDRSxjQUFBO0FBSEY7O0FBTUE7RUFDRSxjQUFBO0FBSEY7O0FBTUE7RUFDRSxjQUFBO0FBSEY7O0FBTUE7RUFDRSxjQUFBO0FBSEY7O0FBT0E7OztFQUdFLGtDQUFBO0FBSkY7O0FBT0E7RUFDRTtJQUNFLHVCQUFBO0VBSkY7RUFNQTtJQUNFLHlCQUFBO0VBSkY7QUFDRjtBQVFBO0VBQ0UsZ0JBQUE7RUFDQSxlQUFBO0FBTkY7O0FBU0E7RUFDRSxhQUFBO0FBTkY7O0FBU0E7RUFDRSw4QkFBQTtBQU5GOztBQVNBO0VBQ0UsbUJBQUE7QUFORjs7QUFTQTtFQUNFLG1CQUFBO0FBTkY7O0FBVUE7RUFDRSxhQUFBO0VBQ0EsOEJBQUE7RUFDQSxtQkFBQTtFQUNBLFdBQUE7QUFQRjtBQVNFO0VBQ0UsT0FBQTtFQUNBLFlBQUE7QUFQSjtBQVVFO0VBQ0UsZ0JBQUE7RUFDQSxZQUFBO0VBQ0Esd0JBQUE7QUFSSjtBQVVJO0VBQ0UsVUFBQTtBQVJOO0FBV0k7RUFDRSxZQUFBO0VBQ0EsZ0JBQUE7RUFDQSxZQUFBO0FBVE47QUFXTTtFQUNFLGVBQUE7QUFUUjtBQVlNO0VBQ0UseUJBQUE7QUFWUjs7QUFnQkE7RUFDRSxtQkFBQTtFQUNBLGlCQUFBO0VBQ0Esa0JBQUE7RUFDQSx5QkFBQTtFQUNBLGNBQUE7QUFiRjtBQWVFO0VBQ0UsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsU0FBQTtBQWJKO0FBZ0JFO0VBQ0UsT0FBQTtFQUNBLGFBQUE7RUFDQSxzQkFBQTtFQUNBLG1CQUFBO0FBZEo7QUFnQkk7RUFDRSxXQUFBO0VBQ0Esa0JBQUE7QUFkTjtBQWdCTTtFQUNFLFdBQUE7QUFkUjtBQWtCSTtFQUNFLGNBQUE7RUFDQSxlQUFBO0VBQ0EsZ0JBQUE7RUFDQSxrQkFBQTtBQWhCTjtBQW9CRTtFQUNFLE9BQUE7RUFDQSxhQUFBO0VBQ0EsdUJBQUE7QUFsQko7QUFvQkk7RUFDRSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSx1QkFBQTtFQUNBLGVBQUE7RUFDQSxTQUFBO0FBbEJOO0FBc0JFOztFQUVFLGFBQUE7RUFDQSxtQkFBQTtFQUNBLGtCQUFBO0VBQ0EsUUFBQTtBQXBCSjtBQXNCSTs7RUFDRSxlQUFBO0VBQ0EsY0FBQTtFQUNBLGdCQUFBO0FBbkJOO0FBc0JJOztFQUNFLGVBQUE7RUFDQSxnQkFBQTtFQUNBLGNBQUE7QUFuQk47QUFxQk07O0VBQ0UsY0FBQTtBQWxCUjtBQXFCTTs7RUFDRSxjQUFBO0FBbEJSO0FBcUJNOztFQUNFLGNBQUE7QUFsQlI7QUF3Qkk7RUFDRSxlQUFBO0VBQ0EsZ0JBQUE7RUFDQSxjQUFBO0VBQ0EsZ0JBQUE7RUFDQSxtQ0FBQTtFQUNBLGtCQUFBO0VBQ0EseUNBQUE7RUFDQSxxQkFBQTtFQUNBLGlCQUFBO0FBdEJOOztBQTRCQTtFQUNFLGFBQUE7RUFDQSxRQUFBO0VBQ0EsbUJBQUE7QUF6QkY7QUEyQkU7RUFDRSxhQUFBO0VBQ0EsUUFBQTtBQXpCSjtBQTJCSTtFQUNFLGVBQUE7RUFDQSxZQUFBO0VBQ0EsY0FBQTtFQUNBLGtCQUFBO0FBekJOO0FBMkJNO0VBQ0UsMkJBQUE7RUFDQSx3Q0FBQTtBQXpCUjs7QUFnQ0E7RUFDRSxhQUFBO0VBQ0EsOEJBQUE7RUFDQSxtQkFBQTtBQTdCRjtBQWlDTTtFQUNFLGVBQUE7RUFDQSxZQUFBO0VBQ0EsY0FBQTtBQS9CUjtBQWlDUTtFQUNFLGlCQUFBO0FBL0JWOztBQXVDQTtFQUNFLG9CQUFBO0VBQ0EsbUJBQUE7RUFDQSxRQUFBO0VBQ0EsZUFBQTtFQUNBLGdCQUFBO0VBQ0Esa0JBQUE7RUFDQSxpQkFBQTtBQXBDRjtBQXNDRTtFQUNFLGVBQUE7QUFwQ0o7O0FBeUNBO0VBQ0UsY0FBQTtFQUNBLHlCQUFBO0VBQ0EscUJBQUE7QUF0Q0Y7O0FBMENBO0VBQ0UsY0FBQTtFQUNBLHlCQUFBO0VBQ0EscUJBQUE7QUF2Q0Y7O0FBMkNBO0VBQ0UsZUFBQTtBQXhDRjtBQTBDRTtFQUNFLGNBQUE7RUFDQSxlQUFBO0VBQ0Esa0JBQUE7QUF4Q0o7O0FBNkNBO0VBQ0UsYUFBQTtFQUNBLFFBQUE7RUFDQSxtQkFBQTtBQTFDRjtBQTRDRTtFQUNFLGlCQUFBO0FBMUNKO0FBNkNFO0VBQ0UsZUFBQTtFQUNBLFlBQUE7RUFDQSxjQUFBO0FBM0NKO0FBNkNJO0VBQ0UsWUFBQTtFQUNBLG1CQUFBO0FBM0NOIiwic291cmNlc0NvbnRlbnQiOlsiQGltcG9ydCBcInZhcmlhYmxlc1wiO1xuQGltcG9ydCBcIm1peGluXCI7XG5cbi8vIMOmwpfCp8OmwqDCt8OlwrzCj8Okwr/CncOmwozCgcOlwoXCvMOlwq7CuVxuLnN0YXR1cy1mYWlsZWQge1xuICBjb2xvcjogJGNvbG9yLWFzc2lzdDtcbn1cblxuLnN0YXR1cy1zdWNjZXNzIHtcbiAgY29sb3I6ICRjb2xvci1ncmVlbjtcbn1cblxuLnN0YXR1cy1Ob3RDaGVja2VkIHtcbiAgY29sb3I6ICM4YzhjOGM7XG59XG5cbi5zdGF0dXMtV3JpdGluZyB7XG4gIGNvbG9yOiAjMTg5MGZmO1xufVxuXG4uc3RhdHVzLVJlc3RhcnRpbmcge1xuICBjb2xvcjogI2ZhYWQxNDtcbn1cblxuLnN0YXR1cy1WZXJpZnlpbmcge1xuICBjb2xvcjogIzE4OTBmZjtcbn1cblxuLnN0YXR1cy1TdWNjZXNzIHtcbiAgY29sb3I6ICM1MmM0MWE7XG59XG5cbi5zdGF0dXMtRmFpbGVkIHtcbiAgY29sb3I6ICNmZjRkNGY7XG59XG5cbi5zdGF0dXMtVGltZW91dCB7XG4gIGNvbG9yOiAjZmY0ZDRmO1xufVxuXG4vLyDDpcKKwqjDp8KUwrvDpsKVwojDpsKewpxcbi5zdGF0dXMtV3JpdGluZyBpLFxuLnN0YXR1cy1SZXN0YXJ0aW5nIGksXG4uc3RhdHVzLVZlcmlmeWluZyBpIHtcbiAgYW5pbWF0aW9uOiBzcGluIDFzIGxpbmVhciBpbmZpbml0ZTtcbn1cblxuQGtleWZyYW1lcyBzcGluIHtcbiAgZnJvbSB7XG4gICAgdHJhbnNmb3JtOiByb3RhdGUoMGRlZyk7XG4gIH1cbiAgdG8ge1xuICAgIHRyYW5zZm9ybTogcm90YXRlKDM2MGRlZyk7XG4gIH1cbn1cblxuLy8gw6jCv8Kbw6XCusKmw6bCncKhw6bCoMK3w6XCvMKPXG5uei1wcm9ncmVzcyB7XG4gIG1heC13aWR0aDogMTIwcHg7XG4gIG1hcmdpbi10b3A6IDRweDtcbn1cblxuLmQtZmxleCB7XG4gIGRpc3BsYXk6IGZsZXg7XG59XG5cbi5qdXN0aWZ5LWNvbnRlbnQtYmV0d2VlbiB7XG4gIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2Vlbjtcbn1cblxuLmFsaWduLWl0ZW1zLWNlbnRlciB7XG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG59XG5cbi5tYi0zIHtcbiAgbWFyZ2luLWJvdHRvbTogMXJlbTtcbn1cblxuLy8gw6bClsKww6XCosKew6/CvMKaw6fCisK2w6bCgMKBw6XCrsK5w6XCmcKow6XCksKMw6bCk8KNw6TCvcKcw6bCjMKJw6nCksKuw6bCoMK3w6XCvMKPXG4uc3RhdHVzLWNvbnRhaW5lciB7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjtcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgd2lkdGg6IDEwMCU7XG5cbiAgLnN0YXR1cy1kaXNwbGF5IHtcbiAgICBmbGV4OiAxO1xuICAgIG1pbi13aWR0aDogMDsgLy8gw6XChcKBw6jCrsK4w6bClMK2w6fCvMKpXG4gIH1cblxuICAuY2xpZW50LWFjdGlvbnMge1xuICAgIG1hcmdpbi1sZWZ0OiA4cHg7XG4gICAgb3BhY2l0eTogMC42O1xuICAgIHRyYW5zaXRpb246IG9wYWNpdHkgMC4ycztcblxuICAgICY6aG92ZXIge1xuICAgICAgb3BhY2l0eTogMTtcbiAgICB9XG5cbiAgICBidXR0b24ge1xuICAgICAgYm9yZGVyOiBub25lO1xuICAgICAgcGFkZGluZzogMnB4IDRweDtcbiAgICAgIGhlaWdodDogMjRweDtcblxuICAgICAgaSB7XG4gICAgICAgIGZvbnQtc2l6ZTogMTJweDtcbiAgICAgIH1cblxuICAgICAgJjpob3ZlciB7XG4gICAgICAgIGJhY2tncm91bmQtY29sb3I6ICNmMGYwZjA7XG4gICAgICB9XG4gICAgfVxuICB9XG59XG5cbi5zdGF0dXMtb3ZlcnZpZXcge1xuICBiYWNrZ3JvdW5kOiAjZmFmYWZhO1xuICBwYWRkaW5nOiA4cHggMTJweDtcbiAgYm9yZGVyLXJhZGl1czogNnB4O1xuICBib3JkZXI6IDFweCBzb2xpZCAjZThlOGU4O1xuICBtYXJnaW46IDEycHggMDtcblxuICAub3ZlcnZpZXctcm93IHtcbiAgICBkaXNwbGF5OiBmbGV4O1xuICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gICAgZ2FwOiAxNnB4O1xuICB9XG5cbiAgLnByb2dyZXNzLXNlY3Rpb24ge1xuICAgIGZsZXg6IDE7XG4gICAgZGlzcGxheTogZmxleDtcbiAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xuICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG5cbiAgICAucHJvZ3Jlc3MtYmFyIHtcbiAgICAgIHdpZHRoOiAxMDAlO1xuICAgICAgbWFyZ2luLWJvdHRvbTogNHB4O1xuXG4gICAgICBuei1wcm9ncmVzcyB7XG4gICAgICAgIHdpZHRoOiAxMDAlO1xuICAgICAgfVxuICAgIH1cblxuICAgIC5wcm9ncmVzcy10ZXh0IHtcbiAgICAgIGNvbG9yOiAjNTk1OTU5O1xuICAgICAgZm9udC1zaXplOiAxMnB4O1xuICAgICAgZm9udC13ZWlnaHQ6IDUwMDtcbiAgICAgIHRleHQtYWxpZ246IGNlbnRlcjtcbiAgICB9XG4gIH1cblxuICAuc3RhdGlzdGljcy1zZWN0aW9uIHtcbiAgICBmbGV4OiAxO1xuICAgIGRpc3BsYXk6IGZsZXg7XG4gICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XG5cbiAgICAuc3RhdGlzdGljcy1ncm91cCB7XG4gICAgICBkaXNwbGF5OiBmbGV4O1xuICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgICAgIGp1c3RpZnktY29udGVudDogY2VudGVyO1xuICAgICAgZmxleC13cmFwOiB3cmFwO1xuICAgICAgZ2FwOiAxNnB4O1xuICAgIH1cbiAgfVxuXG4gIC5zdGF0aXN0aWMtaXRlbSxcbiAgLmN1cnJlbnQtcGhhc2Uge1xuICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgICB0ZXh0LWFsaWduOiBjZW50ZXI7XG4gICAgZ2FwOiA2cHg7XG5cbiAgICAuc3RhdGlzdGljLXRpdGxlIHtcbiAgICAgIGZvbnQtc2l6ZTogMTJweDtcbiAgICAgIGNvbG9yOiAjOGM4YzhjO1xuICAgICAgZm9udC13ZWlnaHQ6IDUwMDtcbiAgICB9XG5cbiAgICAuc3RhdGlzdGljLXZhbHVlIHtcbiAgICAgIGZvbnQtc2l6ZTogMThweDtcbiAgICAgIGZvbnQtd2VpZ2h0OiA2MDA7XG4gICAgICBsaW5lLWhlaWdodDogMTtcblxuICAgICAgJi5zdWNjZXNzIHtcbiAgICAgICAgY29sb3I6ICM1MmM0MWE7XG4gICAgICB9XG5cbiAgICAgICYuZmFpbGVkIHtcbiAgICAgICAgY29sb3I6ICNmZjRkNGY7XG4gICAgICB9XG5cbiAgICAgICYudG90YWwge1xuICAgICAgICBjb2xvcjogIzE4OTBmZjtcbiAgICAgIH1cbiAgICB9XG4gIH1cblxuICAuY3VycmVudC1waGFzZSB7XG4gICAgLnBoYXNlLXRleHQge1xuICAgICAgZm9udC1zaXplOiAxMnB4O1xuICAgICAgZm9udC13ZWlnaHQ6IDUwMDtcbiAgICAgIGNvbG9yOiAjMTg5MGZmO1xuICAgICAgcGFkZGluZzogMnB4IDhweDtcbiAgICAgIGJhY2tncm91bmQ6IHJnYmEoMjQsIDE0NCwgMjU1LCAwLjEpO1xuICAgICAgYm9yZGVyLXJhZGl1czogNHB4O1xuICAgICAgYm9yZGVyOiAxcHggc29saWQgcmdiYSgyNCwgMTQ0LCAyNTUsIDAuMik7XG4gICAgICBkaXNwbGF5OiBpbmxpbmUtYmxvY2s7XG4gICAgICBsaW5lLWhlaWdodDogMjBweDtcbiAgICB9XG4gIH1cbn1cblxuLy8gw6bCk8KNw6TCvcKcw6bCjMKJw6nCksKuw6XCjMK6w6XCn8Kfw6bCoMK3w6XCvMKPXG4uYWN0aW9uLWJ1dHRvbnMge1xuICBkaXNwbGF5OiBmbGV4O1xuICBnYXA6IDhweDtcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcblxuICAuYmF0Y2gtYWN0aW9ucyB7XG4gICAgZGlzcGxheTogZmxleDtcbiAgICBnYXA6IDZweDtcblxuICAgIGJ1dHRvbiB7XG4gICAgICBmb250LXNpemU6IDEycHg7XG4gICAgICBoZWlnaHQ6IDI4cHg7XG4gICAgICBwYWRkaW5nOiAwIDhweDtcbiAgICAgIGJvcmRlci1yYWRpdXM6IDRweDtcblxuICAgICAgJjpob3ZlciB7XG4gICAgICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtMXB4KTtcbiAgICAgICAgYm94LXNoYWRvdzogMCAycHggNHB4IHJnYmEoMCwgMCwgMCwgMC4xKTtcbiAgICAgIH1cbiAgICB9XG4gIH1cbn1cblxuLy8gw6bCtcKLw6jCr8KVw6bCjsKnw6XCiMK2w6bCjMKJw6nCksKuw6bCoMK3w6XCvMKPXG4uZm9vdGVyIHtcbiAgZGlzcGxheTogZmxleDtcbiAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuO1xuICBhbGlnbi1pdGVtczogY2VudGVyO1xuXG4gIC50ZXN0LWNvbnRyb2xzIHtcbiAgICAuYW50LWJ0bi1ncm91cCB7XG4gICAgICAuYW50LWJ0biB7XG4gICAgICAgIGZvbnQtc2l6ZTogMTJweDtcbiAgICAgICAgaGVpZ2h0OiAyOHB4O1xuICAgICAgICBwYWRkaW5nOiAwIDhweDtcblxuICAgICAgICBpIHtcbiAgICAgICAgICBtYXJnaW4tcmlnaHQ6IDRweDtcbiAgICAgICAgfVxuICAgICAgfVxuICAgIH1cbiAgfVxufVxuXG4vLyDDqMK/wp7DpsKOwqXDp8KKwrbDpsKAwoHDpsKgwofDp8Ktwr7DpsKgwrfDpcK8wo9cbi5jb25uZWN0aW9uLXN0YXR1cy10YWcge1xuICBkaXNwbGF5OiBpbmxpbmUtZmxleDtcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgZ2FwOiA0cHg7XG4gIGZvbnQtc2l6ZTogMTJweDtcbiAgcGFkZGluZzogMnB4IDhweDtcbiAgYm9yZGVyLXJhZGl1czogNHB4O1xuICBib3JkZXI6IDFweCBzb2xpZDtcblxuICAuc3RhdHVzLWljb24ge1xuICAgIGZvbnQtc2l6ZTogMTJweDtcbiAgfVxufVxuXG4vLyDDpcKcwqjDp8K6wr/Dp8KKwrbDpsKAwoHDpsKgwrfDpcK8wo9cbi5jb25uZWN0aW9uLXN0YXR1cy1vbmxpbmUge1xuICBjb2xvcjogIzUyYzQxYTtcbiAgYmFja2dyb3VuZC1jb2xvcjogI2Y2ZmZlZDtcbiAgYm9yZGVyLWNvbG9yOiAjYjdlYjhmO1xufVxuXG4vLyDDp8KmwrvDp8K6wr/Dp8KKwrbDpsKAwoHDpsKgwrfDpcK8wo9cbi5jb25uZWN0aW9uLXN0YXR1cy1vZmZsaW5lIHtcbiAgY29sb3I6ICM4YzhjOGM7XG4gIGJhY2tncm91bmQtY29sb3I6ICNmYWZhZmE7XG4gIGJvcmRlci1jb2xvcjogI2Q5ZDlkOTtcbn1cblxuLy8gw6nCh8KNw6jCr8KVw6fCisK2w6bCgMKBw6bCoMK3w6XCvMKPXG4ucmV0cnktc3RhdHVzIHtcbiAgbWFyZ2luLXRvcDogNHB4O1xuXG4gIC50ZXh0LW11dGVkIHtcbiAgICBjb2xvcjogIzhjOGM4YztcbiAgICBmb250LXNpemU6IDExcHg7XG4gICAgZm9udC1zdHlsZTogaXRhbGljO1xuICB9XG59XG5cbi8vIMOowqHCqMOmwqDCvMOmwpPCjcOkwr3CnMOmwozCicOpwpLCrsOnwrvChMOmwqDCt8OlwrzCj1xudGQgLmFjdGlvbi1idXR0b25zIHtcbiAgZGlzcGxheTogZmxleDtcbiAgZ2FwOiA0cHg7XG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG5cbiAgLm1yLTEge1xuICAgIG1hcmdpbi1yaWdodDogNHB4O1xuICB9XG5cbiAgYnV0dG9uIHtcbiAgICBmb250LXNpemU6IDEycHg7XG4gICAgaGVpZ2h0OiAyNHB4O1xuICAgIHBhZGRpbmc6IDAgNnB4O1xuXG4gICAgJjpkaXNhYmxlZCB7XG4gICAgICBvcGFjaXR5OiAwLjU7XG4gICAgICBjdXJzb3I6IG5vdC1hbGxvd2VkO1xuICAgIH1cbiAgfVxufVxuIiwiJGNvbG9yLWJhc2U6ICM1Nzg2ZmY7XG4kY29sb3ItZ3JleTogIzUwNTM2OTtcbiRjb2xvci1saWd0aC1ncmV5OiAjOWRhNmI2O1xuJGNvbG9yLWdyZWVuOiAjNzNkMzkxO1xuJGNvbG9yLWJvcmRlcjogI2JjYzRkNDtcblxuJGNvbG9yLWhvdmVyLWJsdWU6ICM2NmFhZmY7XG4kY29sb3ItaG92ZXItYmFzZTogIzY2YWFmZjtcbiRjb2xvci1ob3Zlci1iZy1saWdodDogIzliYzhmZjtcbiRjb2xvci1ob3Zlci1ncmVlbjogIzc3ZTQ5OTtcbiRjb2xvci1ob3Zlci1yZWQ6ICNmYTgwODA7XG5cbiRjb2xvci1hc3Npc3Q6ICNmZDY0NjQ7XG4vLyAkY29sb3ItYm9yZGVyOiAjZWJlZWY0O1xuJGNvbG9yLXllbGxvdzogI2ZmY2MyZjtcbi8vICRjb2xvci1ncmVlbjogIzdhYzY0MDtcbiRjb2xvci1wdXJwbGU6ICAjODc4RUZFO1xuXG4kY29sb3ItYmFja2dyb3VuZDogI2U4ZWRmMztcbiRjb2xvci1iYWNrZ3JvdW5kLWxpZ2h0OiAjZmFmYmZkO1xuXG4kZm9udC1zaXplLWh1Z2U6IDM4cHg7XG4kZm9udC1zaXplLWJpZzogMjBweDtcbiRmb250LXNpemUtdGl0bGU6IDE4cHg7XG4kZm9udC1zaXplLXN1YnRpdGxlOiAxNnB4O1xuJGZvbnQtc2l6ZS1ib2R5OiAxNHB4O1xuJGZvbnQtc2l6ZS1zbWFsbDogMTJweDtcblxuJGZvbnQtY29sb3Itb25lOiAjNDQ0O1xuJGZvbnQtY29sb3ItdHdvOiAjNjY2O1xuJGZvbnQtY29sb3ItdGhyZWU6ICM5OTk7XG5cbiRib3JkZXItc29saWQ6IDFweCBzb2xpZCAkY29sb3ItYm9yZGVyO1xuJGJvcmRlci1kYXNoZWQ6IDFweCBkYXNoZWQgIzk5OTtcblxuJGJvcmRlci1yYWRpdXMtc2l6ZTogNnB4O1xuXG4iXSwic291cmNlUm9vdCI6IiJ9 */\"]\n  });\n}", "map": {"version": 3, "names": ["Subject", "map", "takeUntil", "M", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵadvance", "ɵɵproperty", "progress_r4", "percentage", "ctx_r1", "formatProgress", "ɵɵtext", "ɵɵtextInterpolate", "ctx_r6", "getRetryStatusText", "client_r5", "seat_number", "ɵɵlistener", "ClientRestoreCardModalComponent_custom_modal_0_tr_62_button_21_Template_button_click_0_listener", "ɵɵrestoreView", "_r11", "ɵɵnextContext", "$implicit", "ctx_r9", "ɵɵresetView", "onManualRetryClient", "is_online", "ctx_r7", "canRetryClient", "ɵɵpipeBind1", "clientCheckLoading$", "ɵɵtemplate", "ClientRestoreCardModalComponent_custom_modal_0_tr_62_div_14_Template", "ClientRestoreCardModalComponent_custom_modal_0_tr_62_Template_button_click_17_listener", "restoredCtx", "_r14", "ctx_r13", "onCheckSingleClient", "ClientRestoreCardModalComponent_custom_modal_0_tr_62_button_21_Template", "ip", "ɵɵtextInterpolate1", "ctx_r3", "getClientStatus", "getStatusDisplay", "icon", "isStatusLoading", "text", "isClientTimeout", "isSingleClientLoading", "getSingleClientCheckAction", "ClientRestoreCardModalComponent_custom_modal_0_Template_custom_modal_onClose_0_listener", "_r16", "ctx_r15", "onClose", "ClientRestoreCardModalComponent_custom_modal_0_div_11_Template", "ClientRestoreCardModalComponent_custom_modal_0_Template_button_click_37_listener", "ctx_r17", "onRefreshResult", "ClientRestoreCardModalComponent_custom_modal_0_Template_button_click_40_listener", "ctx_r18", "onToggleRetry", "ClientRestoreCardModalComponent_custom_modal_0_tr_62_Template", "ClientRestoreCardModalComponent_custom_modal_0_Template_button_click_64_listener", "ctx_r19", "onStartCheck", "ClientRestoreCardModalComponent_custom_modal_0_Template_button_click_67_listener", "ctx_r20", "onSaveCheckResult", "ClientRestoreCardModalComponent_custom_modal_0_Template_button_click_69_listener", "ctx_r21", "ctx_r0", "getCheckProgress", "successCount$", "failedCount$", "getTotalClientCount", "clientListLoading$", "isRetryEnabled", "clientList$", "ɵɵpureFunction0", "_c0", "_r2", "data", "ClientRestoreCardModalComponent", "constructor", "restoreCardService", "socketService", "destroy$", "percent", "visible$", "clientCheckModal$", "loading$", "counts$", "serviceState$", "pipe", "counts", "success", "failed", "ngOnInit", "subscribe", "state", "console", "log", "clientCount", "clientStates", "size", "successCount", "failedCount", "isLoading", "getMsg$", "NOTIFY_RESTORE_CARD_UPDATE", "msg", "handleRestoreCardUpdate", "req", "params", "ngOnDestroy", "next", "complete", "closeClientCheckModal", "checkClientWithRefresh", "checkClientVerify", "seatNumber", "status", "statusMap", "NotChecked", "color", "Writing", "spinning", "Restarting", "Verifying", "Success", "Failed", "Timeout", "includes", "showProgress", "getProgressPercent", "progressMap", "getClientStateDetail", "clientState", "getClientState", "clientInfo", "checkInfo", "lastUpdated", "statusDisplay", "hasClientData", "total", "isAllClientsCompleted", "Array", "from", "values", "length", "every", "onResetClient", "resetClientStatus", "onResetAll", "resetAllClientStatus", "triggerSaveCheckResult", "onCheckCompleted", "onStartWriteAndReboot", "checkClientWriteAndReboot", "onStartRebootOnly", "checkClientReboot", "completed", "Math", "round", "canStartCheck", "action", "checkSingleClient", "disableRetry", "enableRetry", "manualRetryClient", "getClientRetryInfo", "retryInfo", "now", "Date", "maxRetryCount", "maxRetryDuration", "retryCount", "firstTimeoutTime", "_", "ɵɵdirectiveInject", "i1", "RestoreCardCheckService", "i2", "SocketService", "_2", "selectors", "decls", "vars", "consts", "template", "ClientRestoreCardModalComponent_Template", "rf", "ctx", "ClientRestoreCardModalComponent_custom_modal_0_Template"], "sources": ["D:\\work\\joyserver\\manager\\src\\app\\dashboard\\session-prepare\\prepare-detail\\check-restore-card\\client-restore-card-modal.component.ts", "D:\\work\\joyserver\\manager\\src\\app\\dashboard\\session-prepare\\prepare-detail\\check-restore-card\\client-restore-card-modal.component.html"], "sourcesContent": ["import { Component, On<PERSON><PERSON>roy, OnInit } from \"@angular/core\";\nimport { Observable, Subject } from \"rxjs\";\nimport { map, takeUntil } from \"rxjs/operators\";\nimport { RestoreCardCheckService } from \"./restore-card-check.service\";\nimport { RestoreCardCheckStatus } from \"@app/core/io/io.message.consts\";\nimport { RestoreCardServiceState, ClientStatusDisplay } from \"./restore-card-check.types\";\nimport { SocketService } from \"@app/core/services/socket.service\";\nimport * as M from \"@app/core/io\";\n\n@Component({\n  selector: \"app-client-restore-card-modal\",\n  templateUrl: \"./client-restore-card-modal.component.html\",\n  styleUrls: [\"./client-restore-card-modal.component.scss\"],\n})\nexport class ClientRestoreCardModalComponent implements OnInit, OnDestroy {\n  private destroy$ = new Subject<void>();\n\n  visible$: Observable<boolean>;\n  clientList$: Observable<any[]>;\n  loading$: Observable<boolean>;\n  counts$: Observable<{ success: number; failed: number; total: number }>;\n  serviceState$: Observable<RestoreCardServiceState>;\n\n  successCount$: Observable<number>;\n  failedCount$: Observable<number>;\n  clientCheckLoading$: Observable<boolean>;\n  clientListLoading$: Observable<boolean>;\n\n  constructor(private restoreCardService: RestoreCardCheckService, private socketService: SocketService) {\n    this.visible$ = this.restoreCardService.clientCheckModal$;\n    this.clientList$ = this.restoreCardService.clientList$;\n    this.loading$ = this.restoreCardService.loading$;\n    this.counts$ = this.restoreCardService.counts$;\n    this.serviceState$ = this.restoreCardService.serviceState$;\n\n    this.successCount$ = this.counts$.pipe(map((counts) => counts.success));\n    this.failedCount$ = this.counts$.pipe(map((counts) => counts.failed));\n    this.clientCheckLoading$ = this.loading$;\n    this.clientListLoading$ = this.loading$;\n  }\n\n  ngOnInit(): void {\n    this.serviceState$.pipe(takeUntil(this.destroy$)).subscribe((state) => {\n      console.log(\"ClientRestoreCardModal: Service state updated\", {\n        clientCount: state.clientStates.size,\n        successCount: state.counts.success,\n        failedCount: state.counts.failed,\n        isLoading: state.isLoading,\n      });\n    });\n\n    this.socketService\n      .getMsg$(M.NOTIFY_RESTORE_CARD_UPDATE)\n      .pipe(takeUntil(this.destroy$))\n      .subscribe((msg) => {\n        console.log(\"RestoreCardUpdate\", msg);\n        this.restoreCardService.handleRestoreCardUpdate(msg.req.params);\n      });\n  }\n\n  ngOnDestroy(): void {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n\n  // 事件处理方法\n  onClose(): void {\n    this.restoreCardService.closeClientCheckModal();\n  }\n\n  onStartCheck(): void {\n    this.restoreCardService.checkClientWithRefresh();\n  }\n\n  onRefreshResult(): void {\n    this.restoreCardService.checkClientVerify();\n  }\n\n  // 新增：状态显示相关方法\n  getClientStatus(seatNumber: number): RestoreCardCheckStatus {\n    return this.restoreCardService.getClientStatus(seatNumber);\n  }\n\n  getStatusDisplay(status: RestoreCardCheckStatus): ClientStatusDisplay {\n    const statusMap: { [key in RestoreCardCheckStatus]: ClientStatusDisplay } = {\n      NotChecked: { text: \"未检查\", icon: \"clock-circle\", color: \"default\" },\n      Writing: { text: \"检查中\", icon: \"loading\", color: \"processing\", spinning: true },\n      Restarting: { text: \"重启中\", icon: \"reload\", color: \"warning\", spinning: true },\n      Verifying: { text: \"验证中\", icon: \"search\", color: \"processing\", spinning: true },\n      Success: { text: \"通过\", icon: \"check-circle\", color: \"success\" },\n      Failed: { text: \"未通过\", icon: \"close-circle\", color: \"error\" },\n      Timeout: { text: \"超时\", icon: \"exclamation-circle\", color: \"error\" },\n    };\n    return statusMap[status] || statusMap[\"NotChecked\"];\n  }\n\n  isStatusLoading(status: RestoreCardCheckStatus): boolean {\n    return [\"Writing\", \"Restarting\", \"Verifying\"].includes(status);\n  }\n\n  showProgress(status: RestoreCardCheckStatus): boolean {\n    return this.isStatusLoading(status);\n  }\n\n  getProgressPercent(status: RestoreCardCheckStatus): number {\n    const progressMap = {\n      Writing: 33,\n      Restarting: 66,\n      Verifying: 90,\n    };\n    return progressMap[status] || 0;\n  }\n\n  // 新增：获取客户端状态详情\n  getClientStateDetail(seatNumber: number) {\n    const clientState = this.restoreCardService.getClientState(seatNumber);\n    if (!clientState) {\n      return null;\n    }\n\n    return {\n      clientInfo: clientState.clientInfo,\n      checkInfo: clientState.checkInfo,\n      lastUpdated: clientState.lastUpdated,\n      statusDisplay: this.getStatusDisplay(clientState.checkInfo.status),\n    };\n  }\n\n  hasClientData(): Observable<boolean> {\n    return this.serviceState$.pipe(map((state) => state.clientStates.size > 0));\n  }\n\n  getTotalClientCount(): Observable<number> {\n    return this.serviceState$.pipe(map((state) => state.counts.total));\n  }\n\n  isAllClientsCompleted(): Observable<boolean> {\n    return this.serviceState$.pipe(\n      map((state) => {\n        const clientStates = Array.from(state.clientStates.values());\n        return (\n          clientStates.length > 0 &&\n          clientStates.every(\n            (clientState) =>\n              clientState.checkInfo.status === \"Success\" ||\n              clientState.checkInfo.status === \"Failed\" ||\n              clientState.checkInfo.status === \"Timeout\"\n          )\n        );\n      })\n    );\n  }\n\n  onResetClient(seatNumber: number): void {\n    console.log(`ClientRestoreCardModal: Resetting seat ${seatNumber}`);\n    this.restoreCardService.resetClientStatus(seatNumber);\n  }\n\n  onResetAll(): void {\n    console.log(\"ClientRestoreCardModal: Resetting all clients\");\n    this.restoreCardService.resetAllClientStatus();\n  }\n\n  // 手动触发保存检查结果\n  onSaveCheckResult(): void {\n    console.log(\"ClientRestoreCardModal: Manually triggering save check result\");\n    this.restoreCardService.triggerSaveCheckResult(\"manual_from_modal\");\n  }\n\n  // 检查完成后自动保存\n  onCheckCompleted(): void {\n    console.log(\"ClientRestoreCardModal: Check completed, triggering auto-save\");\n    this.restoreCardService.triggerSaveCheckResult(\"check_completed_from_modal\");\n  }\n\n  // 批量操作方法\n  onStartWriteAndReboot(): void {\n    console.log(\"ClientRestoreCardModal: Starting write and reboot sequence\");\n    this.restoreCardService.checkClientWriteAndReboot();\n  }\n\n  onStartRebootOnly(): void {\n    console.log(\"ClientRestoreCardModal: Starting reboot only\");\n    this.restoreCardService.checkClientReboot();\n  }\n\n  // 获取检查进度信息\n  getCheckProgress(): Observable<{ completed: number; total: number; percentage: number }> {\n    return this.serviceState$.pipe(\n      map((state) => {\n        const total = state.counts.total;\n        const completed = state.counts.success + state.counts.failed;\n        const percentage = total > 0 ? Math.round((completed / total) * 100) : 0;\n\n        return {\n          completed,\n          total,\n          percentage,\n        };\n      })\n    );\n  }\n\n  // 检查是否可以开始检查\n  canStartCheck(): Observable<boolean> {\n    return this.serviceState$.pipe(map((state) => !state.isLoading && state.clientStates.size > 0));\n  }\n\n  formatProgress = (percent: number): string => {\n    return `${percent}%`;\n  };\n\n  onCheckSingleClient(seatNumber: number): void {\n    const action = this.getSingleClientCheckAction(seatNumber);\n    console.log(`ClientRestoreCardModal: Check single client ${seatNumber} with action ${action}`);\n    this.restoreCardService.checkSingleClient(seatNumber, action);\n  }\n\n  // 获取单个客户端检查动作\n  getSingleClientCheckAction(seatNumber: number): \"verify\" | [\"write\", \"reboot\", \"verify\"] {\n    const status = this.getClientStatus(seatNumber);\n    return status === \"NotChecked\" || status === \"Success\" || status === \"Failed\"\n      ? [\"write\", \"reboot\", \"verify\"]\n      : \"verify\";\n  }\n\n  // 获取单个客户端加载状态\n  isSingleClientLoading(seatNumber: number): Observable<boolean> {\n    return this.restoreCardService.isSingleClientLoading(seatNumber);\n  }\n\n  // 重试相关方法\n  onToggleRetry(): void {\n    if (this.restoreCardService.isRetryEnabled()) {\n      this.restoreCardService.disableRetry();\n      console.log(\"ClientRestoreCardModal: Retry mechanism disabled by user\");\n    } else {\n      this.restoreCardService.enableRetry();\n      console.log(\"ClientRestoreCardModal: Retry mechanism enabled by user\");\n    }\n  }\n\n  isRetryEnabled(): boolean {\n    return this.restoreCardService.isRetryEnabled();\n  }\n\n  onManualRetryClient(seatNumber: number): void {\n    console.log(`ClientRestoreCardModal: Manual retry triggered for client ${seatNumber}`);\n    this.restoreCardService.manualRetryClient(seatNumber);\n  }\n\n  getClientRetryInfo(seatNumber: number) {\n    return this.restoreCardService.getClientRetryInfo(seatNumber);\n  }\n\n  isClientTimeout(seatNumber: number): boolean {\n    return this.getClientStatus(seatNumber) === \"Timeout\";\n  }\n\n  canRetryClient(seatNumber: number): boolean {\n    const retryInfo = this.getClientRetryInfo(seatNumber);\n    if (!retryInfo) return true;\n\n    const now = Date.now();\n    const maxRetryCount = 10; // 与服务中的 MAX_RETRY_COUNT 保持一致\n    const maxRetryDuration = 600000; // 与服务中的 MAX_RETRY_DURATION 保持一致\n\n    return retryInfo.retryCount < maxRetryCount && now - retryInfo.firstTimeoutTime < maxRetryDuration;\n  }\n\n  getRetryStatusText(seatNumber: number): string {\n    const retryInfo = this.getClientRetryInfo(seatNumber);\n    if (!retryInfo) return \"\";\n\n    const maxRetryCount = 10;\n    return `重试 ${retryInfo.retryCount}/${maxRetryCount}`;\n  }\n}\n", "<custom-modal *ngIf=\"visible$ | async\" [width]=\"'850px'\" (onClose)=\"onClose()\">\n  <div class=\"header\">\n    {{ \"prepare.restoreCardCheck\" | translate }}\n  </div>\n  <div class=\"body\">\n    <p>当前操作为检查还原卡是否处于关闭状态，系统将自动关闭您的电脑并重启。</p>\n    <p>请进行考试机还原卡检查。</p>\n\n    <!-- 状态概览 -->\n    <div class=\"status-overview\">\n      <div class=\"overview-row\">\n        <div class=\"progress-section\" *ngIf=\"getCheckProgress() | async as progress\">\n          <div class=\"progress-bar\">\n            <nz-progress\n              [nzPercent]=\"progress.percentage\"\n              [nzFormat]=\"formatProgress\"\n              nzStrokeColor=\"#52c41a\"\n            ></nz-progress>\n          </div>\n          <!-- <div class=\"progress-text\">\n            {{ getCurrentPhase() | async }}\n          </div> -->\n        </div>\n        <div class=\"statistics-section\">\n          <div class=\"statistics-group\">\n            <div class=\"statistic-item\">\n              <span class=\"statistic-title\">成功</span>\n              <span class=\"statistic-value success\">\n                {{ successCount$ | async }}\n              </span>\n            </div>\n            <div class=\"statistic-item\">\n              <span class=\"statistic-title\">失败</span>\n              <span class=\"statistic-value failed\">\n                {{ failedCount$ | async }}\n              </span>\n            </div>\n            <div class=\"statistic-item\">\n              <span class=\"statistic-title\">总数</span>\n              <span class=\"statistic-value total\">\n                {{ getTotalClientCount() | async }}\n              </span>\n            </div>\n            <!-- <div class=\"current-phase\">\n              <div class=\"statistic-title\">当前状态</div>\n              <div class=\"phase-text\">\n                {{ getCurrentPhase() | async }}\n              </div>\n            </div> -->\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <div class=\"d-flex justify-content-between align-items-center mb-3\">\n      <span>考试机检查结果</span>\n      <div class=\"action-buttons\">\n        <button\n          nz-button\n          nzType=\"default\"\n          nzSize=\"small\"\n          [nzLoading]=\"clientListLoading$ | async\"\n          (click)=\"onRefreshResult()\"\n          class=\"mr-2\"\n        >\n          刷新结果\n        </button>\n        <button\n          nz-button\n          [nzType]=\"isRetryEnabled() ? 'primary' : 'default'\"\n          nzSize=\"small\"\n          (click)=\"onToggleRetry()\"\n          class=\"mr-2\"\n        >\n          {{ isRetryEnabled() ? \"停止重试\" : \"启用重试\" }}\n        </button>\n      </div>\n    </div>\n    <nz-table\n      #clientListTable\n      [nzData]=\"clientList$ | async\"\n      [nzLoading]=\"clientListLoading$ | async\"\n      [nzShowPagination]=\"false\"\n      [nzFrontPagination]=\"false\"\n      [nzSize]=\"'small'\"\n      [nzScroll]=\"{ y: '300px' }\"\n    >\n      <thead>\n        <tr>\n          <th nzWidth=\"120px\">{{ \"table.seatNumber\" | translate }}</th>\n          <th nzWidth=\"180px\">{{ \"table.ipAddress\" | translate }}</th>\n          <th nzWidth=\"100px\">连接状态</th>\n          <th>{{ \"table.status\" | translate }}</th>\n          <th nzWidth=\"120px\">操作</th>\n        </tr>\n      </thead>\n      <tbody>\n        <tr *ngFor=\"let client of clientListTable.data\">\n          <td>{{ client.seat_number }}</td>\n          <td>{{ client.ip }}</td>\n\n          <!-- 新增：在线/离线状态列 -->\n          <td>\n            <span\n              [ngClass]=\"client.is_online ? 'connection-status-online' : 'connection-status-offline'\"\n              class=\"connection-status-tag\"\n            >\n              {{ client.is_online ? \"在线\" : \"离线\" }}\n            </span>\n          </td>\n\n          <!-- 还原卡检查状态列 -->\n          <td>\n            <div class=\"status-container\">\n              <div class=\"status-display\">\n                <span [ngClass]=\"'status-' + getClientStatus(client.seat_number)\">\n                  <i\n                    nz-icon\n                    [nzType]=\"getStatusDisplay(getClientStatus(client.seat_number)).icon\"\n                    [nzSpin]=\"isStatusLoading(getClientStatus(client.seat_number))\"\n                  ></i>\n                  {{ getStatusDisplay(getClientStatus(client.seat_number)).text }}\n                </span>\n                <!-- 重试状态显示 -->\n                <div\n                  *ngIf=\"isClientTimeout(client.seat_number) && getRetryStatusText(client.seat_number)\"\n                  class=\"retry-status\"\n                >\n                  <small class=\"text-muted\">{{ getRetryStatusText(client.seat_number) }}</small>\n                </div>\n              </div>\n            </div>\n          </td>\n\n          <!-- 操作列，离线时禁用按钮 -->\n          <td>\n            <div class=\"action-buttons\">\n              <button\n                nz-button\n                nzType=\"primary\"\n                nzSize=\"small\"\n                [nzLoading]=\"isSingleClientLoading(client.seat_number) | async\"\n                [disabled]=\"!client.is_online || (clientCheckLoading$ | async)\"\n                (click)=\"onCheckSingleClient(client.seat_number)\"\n                class=\"mr-1\"\n              >\n                {{ getSingleClientCheckAction(client.seat_number) === \"verify\" ? \"验证\" : \"检查\" }}\n              </button>\n              <!-- 手动重试按钮，仅对超时客户端显示 -->\n              <button\n                *ngIf=\"isClientTimeout(client.seat_number)\"\n                nz-button\n                nzType=\"default\"\n                nzSize=\"small\"\n                [disabled]=\"!client.is_online || !canRetryClient(client.seat_number) || (clientCheckLoading$ | async)\"\n                (click)=\"onManualRetryClient(client.seat_number)\"\n                nz-tooltip=\"手动重试超时的还原卡检查\"\n              >\n                重试\n              </button>\n            </div>\n          </td>\n        </tr>\n      </tbody>\n    </nz-table>\n  </div>\n\n  <div class=\"footer\">\n    <button\n      nz-button\n      [nzLoading]=\"clientCheckLoading$ | async\"\n      nzShape=\"round\"\n      class=\"custom-btn-secondary\"\n      type=\"button\"\n      (click)=\"onStartCheck()\"\n    >\n      开始检查\n    </button>\n\n    <!-- 保存检查结果按钮 -->\n    <button class=\"custom-btn-primary\" type=\"button\" (click)=\"onSaveCheckResult()\">保存结果</button>\n    <button nz-button nzShape=\"round\" class=\"custom-btn-secondary\" (click)=\"onClose()\">关闭</button>\n  </div>\n</custom-modal>\n"], "mappings": "AACA,SAAqBA,OAAO,QAAQ,MAAM;AAC1C,SAASC,GAAG,EAAEC,SAAS,QAAQ,gBAAgB;AAK/C,OAAO,KAAKC,CAAC,MAAM,cAAc;;;;;;;;;;;;;;;;ICIzBC,EAAA,CAAAC,cAAA,cAA6E;IAEzED,EAAA,CAAAE,SAAA,sBAIe;IACjBF,EAAA,CAAAG,YAAA,EAAM;;;;;IAJFH,EAAA,CAAAI,SAAA,GAAiC;IAAjCJ,EAAA,CAAAK,UAAA,cAAAC,WAAA,CAAAC,UAAA,CAAiC,aAAAC,MAAA,CAAAC,cAAA;;;;;IA8G/BT,EAAA,CAAAC,cAAA,cAGC;IAC2BD,EAAA,CAAAU,MAAA,GAA4C;IAAAV,EAAA,CAAAG,YAAA,EAAQ;;;;;IAApDH,EAAA,CAAAI,SAAA,GAA4C;IAA5CJ,EAAA,CAAAW,iBAAA,CAAAC,MAAA,CAAAC,kBAAA,CAAAC,SAAA,CAAAC,WAAA,EAA4C;;;;;;IAqB1Ef,EAAA,CAAAC,cAAA,iBAQC;IAFCD,EAAA,CAAAgB,UAAA,mBAAAC,gGAAA;MAAAjB,EAAA,CAAAkB,aAAA,CAAAC,IAAA;MAAA,MAAAL,SAAA,GAAAd,EAAA,CAAAoB,aAAA,GAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAtB,EAAA,CAAAoB,aAAA;MAAA,OAASpB,EAAA,CAAAuB,WAAA,CAAAD,MAAA,CAAAE,mBAAA,CAAAV,SAAA,CAAAC,WAAA,CAAuC;IAAA,EAAC;;IAGjDf,EAAA,CAAAU,MAAA,qBACF;IAAAV,EAAA,CAAAG,YAAA,EAAS;;;;;IALPH,EAAA,CAAAK,UAAA,cAAAS,SAAA,CAAAW,SAAA,KAAAC,MAAA,CAAAC,cAAA,CAAAb,SAAA,CAAAC,WAAA,KAAAf,EAAA,CAAA4B,WAAA,OAAAF,MAAA,CAAAG,mBAAA,EAAsG;;;;;;IAzD9G7B,EAAA,CAAAC,cAAA,SAAgD;IAC1CD,EAAA,CAAAU,MAAA,GAAwB;IAAAV,EAAA,CAAAG,YAAA,EAAK;IACjCH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAU,MAAA,GAAe;IAAAV,EAAA,CAAAG,YAAA,EAAK;IAGxBH,EAAA,CAAAC,cAAA,SAAI;IAKAD,EAAA,CAAAU,MAAA,GACF;IAAAV,EAAA,CAAAG,YAAA,EAAO;IAITH,EAAA,CAAAC,cAAA,SAAI;IAIID,EAAA,CAAAE,SAAA,aAIK;IACLF,EAAA,CAAAU,MAAA,IACF;IAAAV,EAAA,CAAAG,YAAA,EAAO;IAEPH,EAAA,CAAA8B,UAAA,KAAAC,oEAAA,kBAKM;IACR/B,EAAA,CAAAG,YAAA,EAAM;IAKVH,EAAA,CAAAC,cAAA,UAAI;IAQED,EAAA,CAAAgB,UAAA,mBAAAgB,uFAAA;MAAA,MAAAC,WAAA,GAAAjC,EAAA,CAAAkB,aAAA,CAAAgB,IAAA;MAAA,MAAApB,SAAA,GAAAmB,WAAA,CAAAZ,SAAA;MAAA,MAAAc,OAAA,GAAAnC,EAAA,CAAAoB,aAAA;MAAA,OAASpB,EAAA,CAAAuB,WAAA,CAAAY,OAAA,CAAAC,mBAAA,CAAAtB,SAAA,CAAAC,WAAA,CAAuC;IAAA,EAAC;;;IAGjDf,EAAA,CAAAU,MAAA,IACF;IAAAV,EAAA,CAAAG,YAAA,EAAS;IAETH,EAAA,CAAA8B,UAAA,KAAAO,uEAAA,qBAUS;IACXrC,EAAA,CAAAG,YAAA,EAAM;;;;;IA9DJH,EAAA,CAAAI,SAAA,GAAwB;IAAxBJ,EAAA,CAAAW,iBAAA,CAAAG,SAAA,CAAAC,WAAA,CAAwB;IACxBf,EAAA,CAAAI,SAAA,GAAe;IAAfJ,EAAA,CAAAW,iBAAA,CAAAG,SAAA,CAAAwB,EAAA,CAAe;IAKftC,EAAA,CAAAI,SAAA,GAAuF;IAAvFJ,EAAA,CAAAK,UAAA,YAAAS,SAAA,CAAAW,SAAA,4DAAuF;IAGvFzB,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAuC,kBAAA,MAAAzB,SAAA,CAAAW,SAAA,wCACF;IAOUzB,EAAA,CAAAI,SAAA,GAA2D;IAA3DJ,EAAA,CAAAK,UAAA,wBAAAmC,MAAA,CAAAC,eAAA,CAAA3B,SAAA,CAAAC,WAAA,EAA2D;IAG7Df,EAAA,CAAAI,SAAA,GAAqE;IAArEJ,EAAA,CAAAK,UAAA,WAAAmC,MAAA,CAAAE,gBAAA,CAAAF,MAAA,CAAAC,eAAA,CAAA3B,SAAA,CAAAC,WAAA,GAAA4B,IAAA,CAAqE,WAAAH,MAAA,CAAAI,eAAA,CAAAJ,MAAA,CAAAC,eAAA,CAAA3B,SAAA,CAAAC,WAAA;IAGvEf,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAuC,kBAAA,MAAAC,MAAA,CAAAE,gBAAA,CAAAF,MAAA,CAAAC,eAAA,CAAA3B,SAAA,CAAAC,WAAA,GAAA8B,IAAA,MACF;IAGG7C,EAAA,CAAAI,SAAA,GAAmF;IAAnFJ,EAAA,CAAAK,UAAA,SAAAmC,MAAA,CAAAM,eAAA,CAAAhC,SAAA,CAAAC,WAAA,KAAAyB,MAAA,CAAA3B,kBAAA,CAAAC,SAAA,CAAAC,WAAA,EAAmF;IAgBtFf,EAAA,CAAAI,SAAA,GAA+D;IAA/DJ,EAAA,CAAAK,UAAA,cAAAL,EAAA,CAAA4B,WAAA,SAAAY,MAAA,CAAAO,qBAAA,CAAAjC,SAAA,CAAAC,WAAA,GAA+D,cAAAD,SAAA,CAAAW,SAAA,IAAAzB,EAAA,CAAA4B,WAAA,SAAAY,MAAA,CAAAX,mBAAA;IAK/D7B,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAuC,kBAAA,MAAAC,MAAA,CAAAQ,0BAAA,CAAAlC,SAAA,CAAAC,WAAA,sDACF;IAGGf,EAAA,CAAAI,SAAA,GAAyC;IAAzCJ,EAAA,CAAAK,UAAA,SAAAmC,MAAA,CAAAM,eAAA,CAAAhC,SAAA,CAAAC,WAAA,EAAyC;;;;;;;;;;;IAtJ1Df,EAAA,CAAAC,cAAA,sBAA+E;IAAtBD,EAAA,CAAAgB,UAAA,qBAAAiC,wFAAA;MAAAjD,EAAA,CAAAkB,aAAA,CAAAgC,IAAA;MAAA,MAAAC,OAAA,GAAAnD,EAAA,CAAAoB,aAAA;MAAA,OAAWpB,EAAA,CAAAuB,WAAA,CAAA4B,OAAA,CAAAC,OAAA,EAAS;IAAA,EAAC;IAC5EpD,EAAA,CAAAC,cAAA,aAAoB;IAClBD,EAAA,CAAAU,MAAA,GACF;;IAAAV,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,aAAkB;IACbD,EAAA,CAAAU,MAAA,mNAAkC;IAAAV,EAAA,CAAAG,YAAA,EAAI;IACzCH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAU,MAAA,+EAAY;IAAAV,EAAA,CAAAG,YAAA,EAAI;IAGnBH,EAAA,CAAAC,cAAA,aAA6B;IAEzBD,EAAA,CAAA8B,UAAA,KAAAuB,8DAAA,iBAWM;;IACNrD,EAAA,CAAAC,cAAA,cAAgC;IAGID,EAAA,CAAAU,MAAA,oBAAE;IAAAV,EAAA,CAAAG,YAAA,EAAO;IACvCH,EAAA,CAAAC,cAAA,gBAAsC;IACpCD,EAAA,CAAAU,MAAA,IACF;;IAAAV,EAAA,CAAAG,YAAA,EAAO;IAETH,EAAA,CAAAC,cAAA,cAA4B;IACID,EAAA,CAAAU,MAAA,oBAAE;IAAAV,EAAA,CAAAG,YAAA,EAAO;IACvCH,EAAA,CAAAC,cAAA,gBAAqC;IACnCD,EAAA,CAAAU,MAAA,IACF;;IAAAV,EAAA,CAAAG,YAAA,EAAO;IAETH,EAAA,CAAAC,cAAA,cAA4B;IACID,EAAA,CAAAU,MAAA,oBAAE;IAAAV,EAAA,CAAAG,YAAA,EAAO;IACvCH,EAAA,CAAAC,cAAA,gBAAoC;IAClCD,EAAA,CAAAU,MAAA,IACF;;IAAAV,EAAA,CAAAG,YAAA,EAAO;IAajBH,EAAA,CAAAC,cAAA,eAAoE;IAC5DD,EAAA,CAAAU,MAAA,kDAAO;IAAAV,EAAA,CAAAG,YAAA,EAAO;IACpBH,EAAA,CAAAC,cAAA,eAA4B;IAMxBD,EAAA,CAAAgB,UAAA,mBAAAsC,iFAAA;MAAAtD,EAAA,CAAAkB,aAAA,CAAAgC,IAAA;MAAA,MAAAK,OAAA,GAAAvD,EAAA,CAAAoB,aAAA;MAAA,OAASpB,EAAA,CAAAuB,WAAA,CAAAgC,OAAA,CAAAC,eAAA,EAAiB;IAAA,EAAC;;IAG3BxD,EAAA,CAAAU,MAAA,kCACF;IAAAV,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAMC;IAFCD,EAAA,CAAAgB,UAAA,mBAAAyC,iFAAA;MAAAzD,EAAA,CAAAkB,aAAA,CAAAgC,IAAA;MAAA,MAAAQ,OAAA,GAAA1D,EAAA,CAAAoB,aAAA;MAAA,OAASpB,EAAA,CAAAuB,WAAA,CAAAmC,OAAA,CAAAC,aAAA,EAAe;IAAA,EAAC;IAGzB3D,EAAA,CAAAU,MAAA,IACF;IAAAV,EAAA,CAAAG,YAAA,EAAS;IAGbH,EAAA,CAAAC,cAAA,wBAQC;;;IACCD,EAAA,CAAAC,cAAA,aAAO;IAEiBD,EAAA,CAAAU,MAAA,IAAoC;;IAAAV,EAAA,CAAAG,YAAA,EAAK;IAC7DH,EAAA,CAAAC,cAAA,cAAoB;IAAAD,EAAA,CAAAU,MAAA,IAAmC;;IAAAV,EAAA,CAAAG,YAAA,EAAK;IAC5DH,EAAA,CAAAC,cAAA,cAAoB;IAAAD,EAAA,CAAAU,MAAA,gCAAI;IAAAV,EAAA,CAAAG,YAAA,EAAK;IAC7BH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAU,MAAA,IAAgC;;IAAAV,EAAA,CAAAG,YAAA,EAAK;IACzCH,EAAA,CAAAC,cAAA,cAAoB;IAAAD,EAAA,CAAAU,MAAA,oBAAE;IAAAV,EAAA,CAAAG,YAAA,EAAK;IAG/BH,EAAA,CAAAC,cAAA,aAAO;IACLD,EAAA,CAAA8B,UAAA,KAAA8B,6DAAA,mBAiEK;IACP5D,EAAA,CAAAG,YAAA,EAAQ;IAIZH,EAAA,CAAAC,cAAA,eAAoB;IAOhBD,EAAA,CAAAgB,UAAA,mBAAA6C,iFAAA;MAAA7D,EAAA,CAAAkB,aAAA,CAAAgC,IAAA;MAAA,MAAAY,OAAA,GAAA9D,EAAA,CAAAoB,aAAA;MAAA,OAASpB,EAAA,CAAAuB,WAAA,CAAAuC,OAAA,CAAAC,YAAA,EAAc;IAAA,EAAC;;IAExB/D,EAAA,CAAAU,MAAA,kCACF;IAAAV,EAAA,CAAAG,YAAA,EAAS;IAGTH,EAAA,CAAAC,cAAA,kBAA+E;IAA9BD,EAAA,CAAAgB,UAAA,mBAAAgD,iFAAA;MAAAhE,EAAA,CAAAkB,aAAA,CAAAgC,IAAA;MAAA,MAAAe,OAAA,GAAAjE,EAAA,CAAAoB,aAAA;MAAA,OAASpB,EAAA,CAAAuB,WAAA,CAAA0C,OAAA,CAAAC,iBAAA,EAAmB;IAAA,EAAC;IAAClE,EAAA,CAAAU,MAAA,gCAAI;IAAAV,EAAA,CAAAG,YAAA,EAAS;IAC5FH,EAAA,CAAAC,cAAA,kBAAmF;IAApBD,EAAA,CAAAgB,UAAA,mBAAAmD,iFAAA;MAAAnE,EAAA,CAAAkB,aAAA,CAAAgC,IAAA;MAAA,MAAAkB,OAAA,GAAApE,EAAA,CAAAoB,aAAA;MAAA,OAASpB,EAAA,CAAAuB,WAAA,CAAA6C,OAAA,CAAAhB,OAAA,EAAS;IAAA,EAAC;IAACpD,EAAA,CAAAU,MAAA,oBAAE;IAAAV,EAAA,CAAAG,YAAA,EAAS;;;;;IArL3DH,EAAA,CAAAK,UAAA,kBAAiB;IAEpDL,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAuC,kBAAA,MAAAvC,EAAA,CAAA4B,WAAA,yCACF;IAQqC5B,EAAA,CAAAI,SAAA,GAAiC;IAAjCJ,EAAA,CAAAK,UAAA,SAAAL,EAAA,CAAA4B,WAAA,SAAAyC,MAAA,CAAAC,gBAAA,IAAiC;IAiBxDtE,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAuC,kBAAA,MAAAvC,EAAA,CAAA4B,WAAA,SAAAyC,MAAA,CAAAE,aAAA,OACF;IAKEvE,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAuC,kBAAA,MAAAvC,EAAA,CAAA4B,WAAA,SAAAyC,MAAA,CAAAG,YAAA,OACF;IAKExE,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAuC,kBAAA,MAAAvC,EAAA,CAAA4B,WAAA,SAAAyC,MAAA,CAAAI,mBAAA,SACF;IAoBJzE,EAAA,CAAAI,SAAA,GAAwC;IAAxCJ,EAAA,CAAAK,UAAA,cAAAL,EAAA,CAAA4B,WAAA,SAAAyC,MAAA,CAAAK,kBAAA,EAAwC;IAQxC1E,EAAA,CAAAI,SAAA,GAAmD;IAAnDJ,EAAA,CAAAK,UAAA,WAAAgE,MAAA,CAAAM,cAAA,2BAAmD;IAKnD3E,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAuC,kBAAA,MAAA8B,MAAA,CAAAM,cAAA,kEACF;IAKF3E,EAAA,CAAAI,SAAA,GAA8B;IAA9BJ,EAAA,CAAAK,UAAA,WAAAL,EAAA,CAAA4B,WAAA,SAAAyC,MAAA,CAAAO,WAAA,EAA8B,cAAA5E,EAAA,CAAA4B,WAAA,SAAAyC,MAAA,CAAAK,kBAAA,yFAAA1E,EAAA,CAAA6E,eAAA,KAAAC,GAAA;IASN9E,EAAA,CAAAI,SAAA,GAAoC;IAApCJ,EAAA,CAAAW,iBAAA,CAAAX,EAAA,CAAA4B,WAAA,6BAAoC;IACpC5B,EAAA,CAAAI,SAAA,GAAmC;IAAnCJ,EAAA,CAAAW,iBAAA,CAAAX,EAAA,CAAA4B,WAAA,4BAAmC;IAEnD5B,EAAA,CAAAI,SAAA,GAAgC;IAAhCJ,EAAA,CAAAW,iBAAA,CAAAX,EAAA,CAAA4B,WAAA,yBAAgC;IAKf5B,EAAA,CAAAI,SAAA,GAAuB;IAAvBJ,EAAA,CAAAK,UAAA,YAAA0E,GAAA,CAAAC,IAAA,CAAuB;IAyEhDhF,EAAA,CAAAI,SAAA,GAAyC;IAAzCJ,EAAA,CAAAK,UAAA,cAAAL,EAAA,CAAA4B,WAAA,SAAAyC,MAAA,CAAAxC,mBAAA,EAAyC;;;AD5J/C,OAAM,MAAOoD,+BAA+B;EAc1CC,YAAoBC,kBAA2C,EAAUC,aAA4B;IAAjF,KAAAD,kBAAkB,GAAlBA,kBAAkB;IAAmC,KAAAC,aAAa,GAAbA,aAAa;IAb9E,KAAAC,QAAQ,GAAG,IAAIzF,OAAO,EAAQ;IAiMtC,KAAAa,cAAc,GAAI6E,OAAe,IAAY;MAC3C,OAAO,GAAGA,OAAO,GAAG;IACtB,CAAC;IArLC,IAAI,CAACC,QAAQ,GAAG,IAAI,CAACJ,kBAAkB,CAACK,iBAAiB;IACzD,IAAI,CAACZ,WAAW,GAAG,IAAI,CAACO,kBAAkB,CAACP,WAAW;IACtD,IAAI,CAACa,QAAQ,GAAG,IAAI,CAACN,kBAAkB,CAACM,QAAQ;IAChD,IAAI,CAACC,OAAO,GAAG,IAAI,CAACP,kBAAkB,CAACO,OAAO;IAC9C,IAAI,CAACC,aAAa,GAAG,IAAI,CAACR,kBAAkB,CAACQ,aAAa;IAE1D,IAAI,CAACpB,aAAa,GAAG,IAAI,CAACmB,OAAO,CAACE,IAAI,CAAC/F,GAAG,CAAEgG,MAAM,IAAKA,MAAM,CAACC,OAAO,CAAC,CAAC;IACvE,IAAI,CAACtB,YAAY,GAAG,IAAI,CAACkB,OAAO,CAACE,IAAI,CAAC/F,GAAG,CAAEgG,MAAM,IAAKA,MAAM,CAACE,MAAM,CAAC,CAAC;IACrE,IAAI,CAAClE,mBAAmB,GAAG,IAAI,CAAC4D,QAAQ;IACxC,IAAI,CAACf,kBAAkB,GAAG,IAAI,CAACe,QAAQ;EACzC;EAEAO,QAAQA,CAAA;IACN,IAAI,CAACL,aAAa,CAACC,IAAI,CAAC9F,SAAS,CAAC,IAAI,CAACuF,QAAQ,CAAC,CAAC,CAACY,SAAS,CAAEC,KAAK,IAAI;MACpEC,OAAO,CAACC,GAAG,CAAC,+CAA+C,EAAE;QAC3DC,WAAW,EAAEH,KAAK,CAACI,YAAY,CAACC,IAAI;QACpCC,YAAY,EAAEN,KAAK,CAACL,MAAM,CAACC,OAAO;QAClCW,WAAW,EAAEP,KAAK,CAACL,MAAM,CAACE,MAAM;QAChCW,SAAS,EAAER,KAAK,CAACQ;OAClB,CAAC;IACJ,CAAC,CAAC;IAEF,IAAI,CAACtB,aAAa,CACfuB,OAAO,CAAC5G,CAAC,CAAC6G,0BAA0B,CAAC,CACrChB,IAAI,CAAC9F,SAAS,CAAC,IAAI,CAACuF,QAAQ,CAAC,CAAC,CAC9BY,SAAS,CAAEY,GAAG,IAAI;MACjBV,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAES,GAAG,CAAC;MACrC,IAAI,CAAC1B,kBAAkB,CAAC2B,uBAAuB,CAACD,GAAG,CAACE,GAAG,CAACC,MAAM,CAAC;IACjE,CAAC,CAAC;EACN;EAEAC,WAAWA,CAAA;IACT,IAAI,CAAC5B,QAAQ,CAAC6B,IAAI,EAAE;IACpB,IAAI,CAAC7B,QAAQ,CAAC8B,QAAQ,EAAE;EAC1B;EAEA;EACA/D,OAAOA,CAAA;IACL,IAAI,CAAC+B,kBAAkB,CAACiC,qBAAqB,EAAE;EACjD;EAEArD,YAAYA,CAAA;IACV,IAAI,CAACoB,kBAAkB,CAACkC,sBAAsB,EAAE;EAClD;EAEA7D,eAAeA,CAAA;IACb,IAAI,CAAC2B,kBAAkB,CAACmC,iBAAiB,EAAE;EAC7C;EAEA;EACA7E,eAAeA,CAAC8E,UAAkB;IAChC,OAAO,IAAI,CAACpC,kBAAkB,CAAC1C,eAAe,CAAC8E,UAAU,CAAC;EAC5D;EAEA7E,gBAAgBA,CAAC8E,MAA8B;IAC7C,MAAMC,SAAS,GAA6D;MAC1EC,UAAU,EAAE;QAAE7E,IAAI,EAAE,KAAK;QAAEF,IAAI,EAAE,cAAc;QAAEgF,KAAK,EAAE;MAAS,CAAE;MACnEC,OAAO,EAAE;QAAE/E,IAAI,EAAE,KAAK;QAAEF,IAAI,EAAE,SAAS;QAAEgF,KAAK,EAAE,YAAY;QAAEE,QAAQ,EAAE;MAAI,CAAE;MAC9EC,UAAU,EAAE;QAAEjF,IAAI,EAAE,KAAK;QAAEF,IAAI,EAAE,QAAQ;QAAEgF,KAAK,EAAE,SAAS;QAAEE,QAAQ,EAAE;MAAI,CAAE;MAC7EE,SAAS,EAAE;QAAElF,IAAI,EAAE,KAAK;QAAEF,IAAI,EAAE,QAAQ;QAAEgF,KAAK,EAAE,YAAY;QAAEE,QAAQ,EAAE;MAAI,CAAE;MAC/EG,OAAO,EAAE;QAAEnF,IAAI,EAAE,IAAI;QAAEF,IAAI,EAAE,cAAc;QAAEgF,KAAK,EAAE;MAAS,CAAE;MAC/DM,MAAM,EAAE;QAAEpF,IAAI,EAAE,KAAK;QAAEF,IAAI,EAAE,cAAc;QAAEgF,KAAK,EAAE;MAAO,CAAE;MAC7DO,OAAO,EAAE;QAAErF,IAAI,EAAE,IAAI;QAAEF,IAAI,EAAE,oBAAoB;QAAEgF,KAAK,EAAE;MAAO;KAClE;IACD,OAAOF,SAAS,CAACD,MAAM,CAAC,IAAIC,SAAS,CAAC,YAAY,CAAC;EACrD;EAEA7E,eAAeA,CAAC4E,MAA8B;IAC5C,OAAO,CAAC,SAAS,EAAE,YAAY,EAAE,WAAW,CAAC,CAACW,QAAQ,CAACX,MAAM,CAAC;EAChE;EAEAY,YAAYA,CAACZ,MAA8B;IACzC,OAAO,IAAI,CAAC5E,eAAe,CAAC4E,MAAM,CAAC;EACrC;EAEAa,kBAAkBA,CAACb,MAA8B;IAC/C,MAAMc,WAAW,GAAG;MAClBV,OAAO,EAAE,EAAE;MACXE,UAAU,EAAE,EAAE;MACdC,SAAS,EAAE;KACZ;IACD,OAAOO,WAAW,CAACd,MAAM,CAAC,IAAI,CAAC;EACjC;EAEA;EACAe,oBAAoBA,CAAChB,UAAkB;IACrC,MAAMiB,WAAW,GAAG,IAAI,CAACrD,kBAAkB,CAACsD,cAAc,CAAClB,UAAU,CAAC;IACtE,IAAI,CAACiB,WAAW,EAAE;MAChB,OAAO,IAAI;;IAGb,OAAO;MACLE,UAAU,EAAEF,WAAW,CAACE,UAAU;MAClCC,SAAS,EAAEH,WAAW,CAACG,SAAS;MAChCC,WAAW,EAAEJ,WAAW,CAACI,WAAW;MACpCC,aAAa,EAAE,IAAI,CAACnG,gBAAgB,CAAC8F,WAAW,CAACG,SAAS,CAACnB,MAAM;KAClE;EACH;EAEAsB,aAAaA,CAAA;IACX,OAAO,IAAI,CAACnD,aAAa,CAACC,IAAI,CAAC/F,GAAG,CAAEqG,KAAK,IAAKA,KAAK,CAACI,YAAY,CAACC,IAAI,GAAG,CAAC,CAAC,CAAC;EAC7E;EAEA9B,mBAAmBA,CAAA;IACjB,OAAO,IAAI,CAACkB,aAAa,CAACC,IAAI,CAAC/F,GAAG,CAAEqG,KAAK,IAAKA,KAAK,CAACL,MAAM,CAACkD,KAAK,CAAC,CAAC;EACpE;EAEAC,qBAAqBA,CAAA;IACnB,OAAO,IAAI,CAACrD,aAAa,CAACC,IAAI,CAC5B/F,GAAG,CAAEqG,KAAK,IAAI;MACZ,MAAMI,YAAY,GAAG2C,KAAK,CAACC,IAAI,CAAChD,KAAK,CAACI,YAAY,CAAC6C,MAAM,EAAE,CAAC;MAC5D,OACE7C,YAAY,CAAC8C,MAAM,GAAG,CAAC,IACvB9C,YAAY,CAAC+C,KAAK,CACfb,WAAW,IACVA,WAAW,CAACG,SAAS,CAACnB,MAAM,KAAK,SAAS,IAC1CgB,WAAW,CAACG,SAAS,CAACnB,MAAM,KAAK,QAAQ,IACzCgB,WAAW,CAACG,SAAS,CAACnB,MAAM,KAAK,SAAS,CAC7C;IAEL,CAAC,CAAC,CACH;EACH;EAEA8B,aAAaA,CAAC/B,UAAkB;IAC9BpB,OAAO,CAACC,GAAG,CAAC,0CAA0CmB,UAAU,EAAE,CAAC;IACnE,IAAI,CAACpC,kBAAkB,CAACoE,iBAAiB,CAAChC,UAAU,CAAC;EACvD;EAEAiC,UAAUA,CAAA;IACRrD,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;IAC5D,IAAI,CAACjB,kBAAkB,CAACsE,oBAAoB,EAAE;EAChD;EAEA;EACAvF,iBAAiBA,CAAA;IACfiC,OAAO,CAACC,GAAG,CAAC,+DAA+D,CAAC;IAC5E,IAAI,CAACjB,kBAAkB,CAACuE,sBAAsB,CAAC,mBAAmB,CAAC;EACrE;EAEA;EACAC,gBAAgBA,CAAA;IACdxD,OAAO,CAACC,GAAG,CAAC,+DAA+D,CAAC;IAC5E,IAAI,CAACjB,kBAAkB,CAACuE,sBAAsB,CAAC,4BAA4B,CAAC;EAC9E;EAEA;EACAE,qBAAqBA,CAAA;IACnBzD,OAAO,CAACC,GAAG,CAAC,4DAA4D,CAAC;IACzE,IAAI,CAACjB,kBAAkB,CAAC0E,yBAAyB,EAAE;EACrD;EAEAC,iBAAiBA,CAAA;IACf3D,OAAO,CAACC,GAAG,CAAC,8CAA8C,CAAC;IAC3D,IAAI,CAACjB,kBAAkB,CAAC4E,iBAAiB,EAAE;EAC7C;EAEA;EACAzF,gBAAgBA,CAAA;IACd,OAAO,IAAI,CAACqB,aAAa,CAACC,IAAI,CAC5B/F,GAAG,CAAEqG,KAAK,IAAI;MACZ,MAAM6C,KAAK,GAAG7C,KAAK,CAACL,MAAM,CAACkD,KAAK;MAChC,MAAMiB,SAAS,GAAG9D,KAAK,CAACL,MAAM,CAACC,OAAO,GAAGI,KAAK,CAACL,MAAM,CAACE,MAAM;MAC5D,MAAMxF,UAAU,GAAGwI,KAAK,GAAG,CAAC,GAAGkB,IAAI,CAACC,KAAK,CAAEF,SAAS,GAAGjB,KAAK,GAAI,GAAG,CAAC,GAAG,CAAC;MAExE,OAAO;QACLiB,SAAS;QACTjB,KAAK;QACLxI;OACD;IACH,CAAC,CAAC,CACH;EACH;EAEA;EACA4J,aAAaA,CAAA;IACX,OAAO,IAAI,CAACxE,aAAa,CAACC,IAAI,CAAC/F,GAAG,CAAEqG,KAAK,IAAK,CAACA,KAAK,CAACQ,SAAS,IAAIR,KAAK,CAACI,YAAY,CAACC,IAAI,GAAG,CAAC,CAAC,CAAC;EACjG;EAMAnE,mBAAmBA,CAACmF,UAAkB;IACpC,MAAM6C,MAAM,GAAG,IAAI,CAACpH,0BAA0B,CAACuE,UAAU,CAAC;IAC1DpB,OAAO,CAACC,GAAG,CAAC,+CAA+CmB,UAAU,gBAAgB6C,MAAM,EAAE,CAAC;IAC9F,IAAI,CAACjF,kBAAkB,CAACkF,iBAAiB,CAAC9C,UAAU,EAAE6C,MAAM,CAAC;EAC/D;EAEA;EACApH,0BAA0BA,CAACuE,UAAkB;IAC3C,MAAMC,MAAM,GAAG,IAAI,CAAC/E,eAAe,CAAC8E,UAAU,CAAC;IAC/C,OAAOC,MAAM,KAAK,YAAY,IAAIA,MAAM,KAAK,SAAS,IAAIA,MAAM,KAAK,QAAQ,GACzE,CAAC,OAAO,EAAE,QAAQ,EAAE,QAAQ,CAAC,GAC7B,QAAQ;EACd;EAEA;EACAzE,qBAAqBA,CAACwE,UAAkB;IACtC,OAAO,IAAI,CAACpC,kBAAkB,CAACpC,qBAAqB,CAACwE,UAAU,CAAC;EAClE;EAEA;EACA5D,aAAaA,CAAA;IACX,IAAI,IAAI,CAACwB,kBAAkB,CAACR,cAAc,EAAE,EAAE;MAC5C,IAAI,CAACQ,kBAAkB,CAACmF,YAAY,EAAE;MACtCnE,OAAO,CAACC,GAAG,CAAC,0DAA0D,CAAC;KACxE,MAAM;MACL,IAAI,CAACjB,kBAAkB,CAACoF,WAAW,EAAE;MACrCpE,OAAO,CAACC,GAAG,CAAC,yDAAyD,CAAC;;EAE1E;EAEAzB,cAAcA,CAAA;IACZ,OAAO,IAAI,CAACQ,kBAAkB,CAACR,cAAc,EAAE;EACjD;EAEAnD,mBAAmBA,CAAC+F,UAAkB;IACpCpB,OAAO,CAACC,GAAG,CAAC,6DAA6DmB,UAAU,EAAE,CAAC;IACtF,IAAI,CAACpC,kBAAkB,CAACqF,iBAAiB,CAACjD,UAAU,CAAC;EACvD;EAEAkD,kBAAkBA,CAAClD,UAAkB;IACnC,OAAO,IAAI,CAACpC,kBAAkB,CAACsF,kBAAkB,CAAClD,UAAU,CAAC;EAC/D;EAEAzE,eAAeA,CAACyE,UAAkB;IAChC,OAAO,IAAI,CAAC9E,eAAe,CAAC8E,UAAU,CAAC,KAAK,SAAS;EACvD;EAEA5F,cAAcA,CAAC4F,UAAkB;IAC/B,MAAMmD,SAAS,GAAG,IAAI,CAACD,kBAAkB,CAAClD,UAAU,CAAC;IACrD,IAAI,CAACmD,SAAS,EAAE,OAAO,IAAI;IAE3B,MAAMC,GAAG,GAAGC,IAAI,CAACD,GAAG,EAAE;IACtB,MAAME,aAAa,GAAG,EAAE,CAAC,CAAC;IAC1B,MAAMC,gBAAgB,GAAG,MAAM,CAAC,CAAC;IAEjC,OAAOJ,SAAS,CAACK,UAAU,GAAGF,aAAa,IAAIF,GAAG,GAAGD,SAAS,CAACM,gBAAgB,GAAGF,gBAAgB;EACpG;EAEAjK,kBAAkBA,CAAC0G,UAAkB;IACnC,MAAMmD,SAAS,GAAG,IAAI,CAACD,kBAAkB,CAAClD,UAAU,CAAC;IACrD,IAAI,CAACmD,SAAS,EAAE,OAAO,EAAE;IAEzB,MAAMG,aAAa,GAAG,EAAE;IACxB,OAAO,MAAMH,SAAS,CAACK,UAAU,IAAIF,aAAa,EAAE;EACtD;EAAC,QAAAI,CAAA,G;qBAtQUhG,+BAA+B,EAAAjF,EAAA,CAAAkL,iBAAA,CAAAC,EAAA,CAAAC,uBAAA,GAAApL,EAAA,CAAAkL,iBAAA,CAAAG,EAAA,CAAAC,aAAA;EAAA;EAAA,QAAAC,EAAA,G;UAA/BtG,+BAA+B;IAAAuG,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,yCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCd5C9L,EAAA,CAAA8B,UAAA,IAAAkK,uDAAA,4BAuLe;;;;QAvLAhM,EAAA,CAAAK,UAAA,SAAAL,EAAA,CAAA4B,WAAA,OAAAmK,GAAA,CAAAxG,QAAA,EAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}