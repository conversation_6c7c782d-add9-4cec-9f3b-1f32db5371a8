{"ast": null, "code": "import { deepMap } from '../../utils/collection.js';\nimport { factory } from '../../utils/factory.js';\nimport { isNegativeNumber } from '../../plain/number/index.js';\nimport { nearlyEqual as bigNearlyEqual } from '../../utils/bignumber/nearlyEqual.js';\nimport { nearlyEqual } from '../../utils/number.js';\nvar name = 'isNegative';\nvar dependencies = ['typed', 'config'];\nexport var createIsNegative = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed,\n    config\n  } = _ref;\n  /**\n   * Test whether a value is negative: smaller than zero.\n   * The function supports types `number`, `BigNumber`, `Fraction`, and `Unit`.\n   *\n   * The function is evaluated element-wise in case of Array or Matrix input.\n   *\n   * Syntax:\n   *\n   *     math.isNegative(x)\n   *\n   * Examples:\n   *\n   *    math.isNegative(3)                     // returns false\n   *    math.isNegative(-2)                    // returns true\n   *    math.isNegative(0)                     // returns false\n   *    math.isNegative(-0)                    // returns false\n   *    math.isNegative(math.bignumber(2))     // returns false\n   *    math.isNegative(math.fraction(-2, 5))  // returns true\n   *    math.isNegative('-2')                  // returns true\n   *    math.isNegative([2, 0, -3])            // returns [false, false, true]\n   *\n   * See also:\n   *\n   *    isNumeric, isPositive, isZero, isInteger\n   *\n   * @param {number | BigNumber | bigint | Fraction | Unit | Array | Matrix} x  Value to be tested\n   * @return {boolean}  Returns true when `x` is larger than zero.\n   *                    Throws an error in case of an unknown data type.\n   */\n\n  return typed(name, {\n    number: x => nearlyEqual(x, 0, config.relTol, config.absTol) ? false : isNegativeNumber(x),\n    BigNumber: x => bigNearlyEqual(x, new x.constructor(0), config.relTol, config.absTol) ? false : x.isNeg() && !x.isZero() && !x.isNaN(),\n    bigint: x => x < 0n,\n    Fraction: x => x.s < 0n,\n    // It's enough to decide on the sign\n    Unit: typed.referToSelf(self => x => typed.find(self, x.valueType())(x.value)),\n    'Array | Matrix': typed.referToSelf(self => x => deepMap(x, self))\n  });\n});", "map": {"version": 3, "names": ["deepMap", "factory", "isNegativeNumber", "nearlyEqual", "bigNearlyEqual", "name", "dependencies", "createIsNegative", "_ref", "typed", "config", "number", "x", "relTol", "absTol", "BigNumber", "constructor", "isNeg", "isZero", "isNaN", "bigint", "Fraction", "s", "Unit", "referToSelf", "self", "find", "valueType", "value"], "sources": ["D:/work/joyserver/client/node_modules/mathjs/lib/esm/function/utils/isNegative.js"], "sourcesContent": ["import { deepMap } from '../../utils/collection.js';\nimport { factory } from '../../utils/factory.js';\nimport { isNegativeNumber } from '../../plain/number/index.js';\nimport { nearlyEqual as bigNearlyEqual } from '../../utils/bignumber/nearlyEqual.js';\nimport { nearlyEqual } from '../../utils/number.js';\nvar name = 'isNegative';\nvar dependencies = ['typed', 'config'];\nexport var createIsNegative = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed,\n    config\n  } = _ref;\n  /**\n   * Test whether a value is negative: smaller than zero.\n   * The function supports types `number`, `BigNumber`, `Fraction`, and `Unit`.\n   *\n   * The function is evaluated element-wise in case of Array or Matrix input.\n   *\n   * Syntax:\n   *\n   *     math.isNegative(x)\n   *\n   * Examples:\n   *\n   *    math.isNegative(3)                     // returns false\n   *    math.isNegative(-2)                    // returns true\n   *    math.isNegative(0)                     // returns false\n   *    math.isNegative(-0)                    // returns false\n   *    math.isNegative(math.bignumber(2))     // returns false\n   *    math.isNegative(math.fraction(-2, 5))  // returns true\n   *    math.isNegative('-2')                  // returns true\n   *    math.isNegative([2, 0, -3])            // returns [false, false, true]\n   *\n   * See also:\n   *\n   *    isNumeric, isPositive, isZero, isInteger\n   *\n   * @param {number | BigNumber | bigint | Fraction | Unit | Array | Matrix} x  Value to be tested\n   * @return {boolean}  Returns true when `x` is larger than zero.\n   *                    Throws an error in case of an unknown data type.\n   */\n  return typed(name, {\n    number: x => nearlyEqual(x, 0, config.relTol, config.absTol) ? false : isNegativeNumber(x),\n    BigNumber: x => bigNearlyEqual(x, new x.constructor(0), config.relTol, config.absTol) ? false : x.isNeg() && !x.isZero() && !x.isNaN(),\n    bigint: x => x < 0n,\n    Fraction: x => x.s < 0n,\n    // It's enough to decide on the sign\n\n    Unit: typed.referToSelf(self => x => typed.find(self, x.valueType())(x.value)),\n    'Array | Matrix': typed.referToSelf(self => x => deepMap(x, self))\n  });\n});"], "mappings": "AAAA,SAASA,OAAT,QAAwB,2BAAxB;AACA,SAASC,OAAT,QAAwB,wBAAxB;AACA,SAASC,gBAAT,QAAiC,6BAAjC;AACA,SAASC,WAAW,IAAIC,cAAxB,QAA8C,sCAA9C;AACA,SAASD,WAAT,QAA4B,uBAA5B;AACA,IAAIE,IAAI,GAAG,YAAX;AACA,IAAIC,YAAY,GAAG,CAAC,OAAD,EAAU,QAAV,CAAnB;AACA,OAAO,IAAIC,gBAAgB,GAAG,eAAeN,OAAO,CAACI,IAAD,EAAOC,YAAP,EAAqBE,IAAI,IAAI;EAC/E,IAAI;IACFC,KADE;IAEFC;EAFE,IAGAF,IAHJ;EAIA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EACE,OAAOC,KAAK,CAACJ,IAAD,EAAO;IACjBM,MAAM,EAAEC,CAAC,IAAIT,WAAW,CAACS,CAAD,EAAI,CAAJ,EAAOF,MAAM,CAACG,MAAd,EAAsBH,MAAM,CAACI,MAA7B,CAAX,GAAkD,KAAlD,GAA0DZ,gBAAgB,CAACU,CAAD,CADtE;IAEjBG,SAAS,EAAEH,CAAC,IAAIR,cAAc,CAACQ,CAAD,EAAI,IAAIA,CAAC,CAACI,WAAN,CAAkB,CAAlB,CAAJ,EAA0BN,MAAM,CAACG,MAAjC,EAAyCH,MAAM,CAACI,MAAhD,CAAd,GAAwE,KAAxE,GAAgFF,CAAC,CAACK,KAAF,MAAa,CAACL,CAAC,CAACM,MAAF,EAAd,IAA4B,CAACN,CAAC,CAACO,KAAF,EAF5G;IAGjBC,MAAM,EAAER,CAAC,IAAIA,CAAC,GAAG,EAHA;IAIjBS,QAAQ,EAAET,CAAC,IAAIA,CAAC,CAACU,CAAF,GAAM,EAJJ;IAKjB;IAEAC,IAAI,EAAEd,KAAK,CAACe,WAAN,CAAkBC,IAAI,IAAIb,CAAC,IAAIH,KAAK,CAACiB,IAAN,CAAWD,IAAX,EAAiBb,CAAC,CAACe,SAAF,EAAjB,EAAgCf,CAAC,CAACgB,KAAlC,CAA/B,CAPW;IAQjB,kBAAkBnB,KAAK,CAACe,WAAN,CAAkBC,IAAI,IAAIb,CAAC,IAAIZ,OAAO,CAACY,CAAD,EAAIa,IAAJ,CAAtC;EARD,CAAP,CAAZ;AAUD,CA5CmD,CAA7C"}, "metadata": {}, "sourceType": "module"}