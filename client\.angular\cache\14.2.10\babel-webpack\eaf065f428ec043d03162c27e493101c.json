{"ast": null, "code": "/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { createFractionClass } from '../../factoriesAny.js';\nexport var FractionDependencies = {\n  createFractionClass\n};", "map": {"version": 3, "names": ["createFractionClass", "FractionDependencies"], "sources": ["D:/work/joyserver/client/node_modules/mathjs/lib/esm/entry/dependenciesAny/dependenciesFractionClass.generated.js"], "sourcesContent": ["/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { createFractionClass } from '../../factoriesAny.js';\nexport var FractionDependencies = {\n  createFractionClass\n};"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,mBAAT,QAAoC,uBAApC;AACA,OAAO,IAAIC,oBAAoB,GAAG;EAChCD;AADgC,CAA3B"}, "metadata": {}, "sourceType": "module"}