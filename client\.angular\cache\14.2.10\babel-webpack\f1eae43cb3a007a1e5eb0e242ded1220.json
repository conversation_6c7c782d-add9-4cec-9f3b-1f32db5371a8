{"ast": null, "code": "/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { createVersion } from '../../factoriesAny.js';\nexport var versionDependencies = {\n  createVersion\n};", "map": {"version": 3, "names": ["createVersion", "versionDependencies"], "sources": ["D:/work/joyserver/client/node_modules/mathjs/lib/esm/entry/dependenciesAny/dependenciesVersion.generated.js"], "sourcesContent": ["/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { createVersion } from '../../factoriesAny.js';\nexport var versionDependencies = {\n  createVersion\n};"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,aAAT,QAA8B,uBAA9B;AACA,OAAO,IAAIC,mBAAmB,GAAG;EAC/BD;AAD+B,CAA1B"}, "metadata": {}, "sourceType": "module"}