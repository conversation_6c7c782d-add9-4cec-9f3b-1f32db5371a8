{"ast": null, "code": "export var gcdDocs = {\n  name: 'gcd',\n  category: 'Arithmetic',\n  syntax: ['gcd(a, b)', 'gcd(a, b, c, ...)'],\n  description: 'Compute the greatest common divisor.',\n  examples: ['gcd(8, 12)', 'gcd(-4, 6)', 'gcd(25, 15, -10)'],\n  seealso: ['lcm', 'xgcd']\n};", "map": {"version": 3, "names": ["gcdDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["D:/work/joyserver/client/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/arithmetic/gcd.js"], "sourcesContent": ["export var gcdDocs = {\n  name: 'gcd',\n  category: 'Arithmetic',\n  syntax: ['gcd(a, b)', 'gcd(a, b, c, ...)'],\n  description: 'Compute the greatest common divisor.',\n  examples: ['gcd(8, 12)', 'gcd(-4, 6)', 'gcd(25, 15, -10)'],\n  seealso: ['lcm', 'xgcd']\n};"], "mappings": "AAAA,OAAO,IAAIA,OAAO,GAAG;EACnBC,IAAI,EAAE,KADa;EAEnBC,QAAQ,EAAE,YAFS;EAGnBC,MAAM,EAAE,CAAC,WAAD,EAAc,mBAAd,CAHW;EAInBC,WAAW,EAAE,sCAJM;EAKnBC,QAAQ,EAAE,CAAC,YAAD,EAAe,YAAf,EAA6B,kBAA7B,CALS;EAMnBC,OAAO,EAAE,CAAC,KAAD,EAAQ,MAAR;AANU,CAAd"}, "metadata": {}, "sourceType": "module"}