{"ast": null, "code": "/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createAtan } from '../../factoriesAny.js';\nexport var atanDependencies = {\n  typedDependencies,\n  createAtan\n};", "map": {"version": 3, "names": ["typedDependencies", "createAtan", "atanDependencies"], "sources": ["D:/work/joyserver/client/node_modules/mathjs/lib/esm/entry/dependenciesAny/dependenciesAtan.generated.js"], "sourcesContent": ["/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createAtan } from '../../factoriesAny.js';\nexport var atanDependencies = {\n  typedDependencies,\n  createAtan\n};"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,iBAAT,QAAkC,kCAAlC;AACA,SAASC,UAAT,QAA2B,uBAA3B;AACA,OAAO,IAAIC,gBAAgB,GAAG;EAC5BF,iBAD4B;EAE5BC;AAF4B,CAAvB"}, "metadata": {}, "sourceType": "module"}