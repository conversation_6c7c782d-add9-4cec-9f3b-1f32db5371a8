{"ast": null, "code": "const ArgumentOutOfRangeErrorImpl = (() => {\n  function ArgumentOutOfRangeErrorImpl() {\n    Error.call(this);\n    this.message = 'argument out of range';\n    this.name = 'ArgumentOutOfRangeError';\n    return this;\n  }\n\n  ArgumentOutOfRangeErrorImpl.prototype = Object.create(Error.prototype);\n  return ArgumentOutOfRangeErrorImpl;\n})();\n\nexport const ArgumentOutOfRangeError = ArgumentOutOfRangeErrorImpl; //# sourceMappingURL=ArgumentOutOfRangeError.js.map", "map": null, "metadata": {}, "sourceType": "module"}