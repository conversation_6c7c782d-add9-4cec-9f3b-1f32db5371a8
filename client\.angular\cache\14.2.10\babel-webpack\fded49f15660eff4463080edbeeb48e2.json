{"ast": null, "code": "/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { addDependencies } from './dependenciesAdd.generated.js';\nimport { matrixDependencies } from './dependenciesMatrix.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { zerosDependencies } from './dependenciesZeros.generated.js';\nimport { createSubsetTransform } from '../../factoriesAny.js';\nexport var subsetTransformDependencies = {\n  addDependencies,\n  matrixDependencies,\n  typedDependencies,\n  zerosDependencies,\n  createSubsetTransform\n};", "map": {"version": 3, "names": ["addDependencies", "matrixDependencies", "typedDependencies", "zerosDependencies", "createSubsetTransform", "subsetTransformDependencies"], "sources": ["D:/work/joyserver/client/node_modules/mathjs/lib/esm/entry/dependenciesAny/dependenciesSubsetTransform.generated.js"], "sourcesContent": ["/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { addDependencies } from './dependenciesAdd.generated.js';\nimport { matrixDependencies } from './dependenciesMatrix.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { zerosDependencies } from './dependenciesZeros.generated.js';\nimport { createSubsetTransform } from '../../factoriesAny.js';\nexport var subsetTransformDependencies = {\n  addDependencies,\n  matrixDependencies,\n  typedDependencies,\n  zerosDependencies,\n  createSubsetTransform\n};"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,eAAT,QAAgC,gCAAhC;AACA,SAASC,kBAAT,QAAmC,mCAAnC;AACA,SAASC,iBAAT,QAAkC,kCAAlC;AACA,SAASC,iBAAT,QAAkC,kCAAlC;AACA,SAASC,qBAAT,QAAsC,uBAAtC;AACA,OAAO,IAAIC,2BAA2B,GAAG;EACvCL,eADuC;EAEvCC,kBAFuC;EAGvCC,iBAHuC;EAIvCC,iBAJuC;EAKvCC;AALuC,CAAlC"}, "metadata": {}, "sourceType": "module"}