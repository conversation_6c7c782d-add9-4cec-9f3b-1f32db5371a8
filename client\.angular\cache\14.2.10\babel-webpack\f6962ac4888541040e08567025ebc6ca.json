{"ast": null, "code": "import { factory } from '../../../utils/factory.js';\nvar name = 'transformCallback';\nvar dependencies = ['typed'];\nexport var createTransformCallback = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed\n  } = _ref;\n  /**\n         * Transforms the given callback function based on its type and number of arrays.\n         *\n         * @param {Function} callback - The callback function to transform.\n         * @param {number} numberOfArrays - The number of arrays to pass to the callback function.\n         * @returns {*} - The transformed callback function.\n         */\n\n  return function (callback, numberOfArrays) {\n    if (typed.isTypedFunction(callback)) {\n      return _transformTypedCallbackFunction(callback, numberOfArrays);\n    } else {\n      return _transformCallbackFunction(callback, callback.length, numberOfArrays);\n    }\n  };\n  /**\n       * Transforms the given typed callback function based on the number of arrays.\n       *\n       * @param {Function} typedFunction - The typed callback function to transform.\n       * @param {number} numberOfArrays - The number of arrays to pass to the callback function.\n       * @returns {*} - The transformed callback function.\n       */\n\n  function _transformTypedCallbackFunction(typedFunction, numberOfArrays) {\n    var signatures = Object.fromEntries(Object.entries(typedFunction.signatures).map(_ref2 => {\n      var [signature, callbackFunction] = _ref2;\n      var numberOfCallbackInputs = signature.split(',').length;\n\n      if (typed.isTypedFunction(callbackFunction)) {\n        return [signature, _transformTypedCallbackFunction(callbackFunction, numberOfArrays)];\n      } else {\n        return [signature, _transformCallbackFunction(callbackFunction, numberOfCallbackInputs, numberOfArrays)];\n      }\n    }));\n\n    if (typeof typedFunction.name === 'string') {\n      return typed(typedFunction.name, signatures);\n    } else {\n      return typed(signatures);\n    }\n  }\n});\n/**\n     * Transforms the callback function based on the number of callback inputs and arrays.\n     * There are three cases:\n     * 1. The callback function has N arguments.\n     * 2. The callback function has N+1 arguments.\n     * 3. The callback function has 2N+1 arguments.\n     *\n     * @param {Function} callbackFunction - The callback function to transform.\n     * @param {number} numberOfCallbackInputs - The number of callback inputs.\n     * @param {number} numberOfArrays - The number of arrays.\n     * @returns {Function} The transformed callback function.\n     */\n\nfunction _transformCallbackFunction(callbackFunction, numberOfCallbackInputs, numberOfArrays) {\n  if (numberOfCallbackInputs === numberOfArrays) {\n    return callbackFunction;\n  } else if (numberOfCallbackInputs === numberOfArrays + 1) {\n    return function () {\n      for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n        args[_key] = arguments[_key];\n      }\n\n      var vals = args.slice(0, numberOfArrays);\n\n      var idx = _transformDims(args[numberOfArrays]);\n\n      return callbackFunction(...vals, idx);\n    };\n  } else if (numberOfCallbackInputs > numberOfArrays + 1) {\n    return function () {\n      for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n        args[_key2] = arguments[_key2];\n      }\n\n      var vals = args.slice(0, numberOfArrays);\n\n      var idx = _transformDims(args[numberOfArrays]);\n\n      var rest = args.slice(numberOfArrays + 1);\n      return callbackFunction(...vals, idx, ...rest);\n    };\n  } else {\n    return callbackFunction;\n  }\n}\n/**\n   * Transforms the dimensions by adding 1 to each dimension.\n   *\n   * @param {Array} dims - The dimensions to transform.\n   * @returns {Array} The transformed dimensions.\n   */\n\n\nfunction _transformDims(dims) {\n  return dims.map(dim => dim + 1);\n}", "map": null, "metadata": {}, "sourceType": "module"}