{"ast": null, "code": "import getScrollParent from \"./getScrollParent.js\";\nimport getParentNode from \"./getParentNode.js\";\nimport getWindow from \"./getWindow.js\";\nimport isScrollParent from \"./isScrollParent.js\";\n/*\ngiven a DOM element, return the list of all scroll parents, up the list of ancesors\nuntil we get to the top window object. This list is what we attach scroll listeners\nto, because if any of these parent elements scroll, we'll need to re-calculate the\nreference element's position.\n*/\n\nexport default function listScrollParents(element, list) {\n  var _element$ownerDocumen;\n\n  if (list === void 0) {\n    list = [];\n  }\n\n  var scrollParent = getScrollParent(element);\n  var isBody = scrollParent === ((_element$ownerDocumen = element.ownerDocument) == null ? void 0 : _element$ownerDocumen.body);\n  var win = getWindow(scrollParent);\n  var target = isBody ? [win].concat(win.visualViewport || [], isScrollParent(scrollParent) ? scrollParent : []) : scrollParent;\n  var updatedList = list.concat(target);\n  return isBody ? updatedList : // $FlowFixMe[incompatible-call]: isBody tells us target will be an HTMLElement here\n  updatedList.concat(listScrollParents(getParentNode(target)));\n}", "map": {"version": 3, "names": ["getScrollParent", "getParentNode", "getWindow", "isScrollParent", "listScrollParents", "element", "list", "_element$ownerDocumen", "scrollParent", "isBody", "ownerDocument", "body", "win", "target", "concat", "visualViewport", "updatedList"], "sources": ["D:/work/joyserver/client/node_modules/@popperjs/core/lib/dom-utils/listScrollParents.js"], "sourcesContent": ["import getScrollParent from \"./getScrollParent.js\";\nimport getParentNode from \"./getParentNode.js\";\nimport getWindow from \"./getWindow.js\";\nimport isScrollParent from \"./isScrollParent.js\";\n/*\ngiven a DOM element, return the list of all scroll parents, up the list of ancesors\nuntil we get to the top window object. This list is what we attach scroll listeners\nto, because if any of these parent elements scroll, we'll need to re-calculate the\nreference element's position.\n*/\n\nexport default function listScrollParents(element, list) {\n  var _element$ownerDocumen;\n\n  if (list === void 0) {\n    list = [];\n  }\n\n  var scrollParent = getScrollParent(element);\n  var isBody = scrollParent === ((_element$ownerDocumen = element.ownerDocument) == null ? void 0 : _element$ownerDocumen.body);\n  var win = getWindow(scrollParent);\n  var target = isBody ? [win].concat(win.visualViewport || [], isScrollParent(scrollParent) ? scrollParent : []) : scrollParent;\n  var updatedList = list.concat(target);\n  return isBody ? updatedList : // $FlowFixMe[incompatible-call]: isBody tells us target will be an HTMLElement here\n  updatedList.concat(listScrollParents(getParentNode(target)));\n}"], "mappings": "AAAA,OAAOA,eAAP,MAA4B,sBAA5B;AACA,OAAOC,aAAP,MAA0B,oBAA1B;AACA,OAAOC,SAAP,MAAsB,gBAAtB;AACA,OAAOC,cAAP,MAA2B,qBAA3B;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,eAAe,SAASC,iBAAT,CAA2BC,OAA3B,EAAoCC,IAApC,EAA0C;EACvD,IAAIC,qBAAJ;;EAEA,IAAID,IAAI,KAAK,KAAK,CAAlB,EAAqB;IACnBA,IAAI,GAAG,EAAP;EACD;;EAED,IAAIE,YAAY,GAAGR,eAAe,CAACK,OAAD,CAAlC;EACA,IAAII,MAAM,GAAGD,YAAY,MAAM,CAACD,qBAAqB,GAAGF,OAAO,CAACK,aAAjC,KAAmD,IAAnD,GAA0D,KAAK,CAA/D,GAAmEH,qBAAqB,CAACI,IAA/F,CAAzB;EACA,IAAIC,GAAG,GAAGV,SAAS,CAACM,YAAD,CAAnB;EACA,IAAIK,MAAM,GAAGJ,MAAM,GAAG,CAACG,GAAD,EAAME,MAAN,CAAaF,GAAG,CAACG,cAAJ,IAAsB,EAAnC,EAAuCZ,cAAc,CAACK,YAAD,CAAd,GAA+BA,YAA/B,GAA8C,EAArF,CAAH,GAA8FA,YAAjH;EACA,IAAIQ,WAAW,GAAGV,IAAI,CAACQ,MAAL,CAAYD,MAAZ,CAAlB;EACA,OAAOJ,MAAM,GAAGO,WAAH,GAAiB;EAC9BA,WAAW,CAACF,MAAZ,CAAmBV,iBAAiB,CAACH,aAAa,CAACY,MAAD,CAAd,CAApC,CADA;AAED"}, "metadata": {}, "sourceType": "module"}