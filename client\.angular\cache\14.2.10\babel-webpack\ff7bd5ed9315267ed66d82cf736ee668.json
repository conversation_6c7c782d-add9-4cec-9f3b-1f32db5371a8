{"ast": null, "code": "/**\n * Implementation of a subset of node.js Buffer methods for the browser.\n * Based on https://github.com/feross/buffer\n */\n\n/* eslint-disable no-proto */\n'use strict';\n\nvar isArray = require('isarray');\n\nfunction typedArraySupport() {\n  // Can typed array instances be augmented?\n  try {\n    var arr = new Uint8Array(1);\n    arr.__proto__ = {\n      __proto__: Uint8Array.prototype,\n      foo: function () {\n        return 42;\n      }\n    };\n    return arr.foo() === 42;\n  } catch (e) {\n    return false;\n  }\n}\n\nBuffer.TYPED_ARRAY_SUPPORT = typedArraySupport();\nvar K_MAX_LENGTH = Buffer.TYPED_ARRAY_SUPPORT ? 0x7fffffff : 0x3fffffff;\n\nfunction Buffer(arg, offset, length) {\n  if (!Buffer.TYPED_ARRAY_SUPPORT && !(this instanceof Buffer)) {\n    return new Buffer(arg, offset, length);\n  }\n\n  if (typeof arg === 'number') {\n    return allocUnsafe(this, arg);\n  }\n\n  return from(this, arg, offset, length);\n}\n\nif (Buffer.TYPED_ARRAY_SUPPORT) {\n  Buffer.prototype.__proto__ = Uint8Array.prototype;\n  Buffer.__proto__ = Uint8Array; // Fix subarray() in ES2016. See: https://github.com/feross/buffer/pull/97\n\n  if (typeof Symbol !== 'undefined' && Symbol.species && Buffer[Symbol.species] === Buffer) {\n    Object.defineProperty(Buffer, Symbol.species, {\n      value: null,\n      configurable: true,\n      enumerable: false,\n      writable: false\n    });\n  }\n}\n\nfunction checked(length) {\n  // Note: cannot use `length < K_MAX_LENGTH` here because that fails when\n  // length is NaN (which is otherwise coerced to zero.)\n  if (length >= K_MAX_LENGTH) {\n    throw new RangeError('Attempt to allocate Buffer larger than maximum ' + 'size: 0x' + K_MAX_LENGTH.toString(16) + ' bytes');\n  }\n\n  return length | 0;\n}\n\nfunction isnan(val) {\n  return val !== val; // eslint-disable-line no-self-compare\n}\n\nfunction createBuffer(that, length) {\n  var buf;\n\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    buf = new Uint8Array(length);\n    buf.__proto__ = Buffer.prototype;\n  } else {\n    // Fallback: Return an object instance of the Buffer class\n    buf = that;\n\n    if (buf === null) {\n      buf = new Buffer(length);\n    }\n\n    buf.length = length;\n  }\n\n  return buf;\n}\n\nfunction allocUnsafe(that, size) {\n  var buf = createBuffer(that, size < 0 ? 0 : checked(size) | 0);\n\n  if (!Buffer.TYPED_ARRAY_SUPPORT) {\n    for (var i = 0; i < size; ++i) {\n      buf[i] = 0;\n    }\n  }\n\n  return buf;\n}\n\nfunction fromString(that, string) {\n  var length = byteLength(string) | 0;\n  var buf = createBuffer(that, length);\n  var actual = buf.write(string);\n\n  if (actual !== length) {\n    // Writing a hex string, for example, that contains invalid characters will\n    // cause everything after the first invalid character to be ignored. (e.g.\n    // 'abxxcd' will be treated as 'ab')\n    buf = buf.slice(0, actual);\n  }\n\n  return buf;\n}\n\nfunction fromArrayLike(that, array) {\n  var length = array.length < 0 ? 0 : checked(array.length) | 0;\n  var buf = createBuffer(that, length);\n\n  for (var i = 0; i < length; i += 1) {\n    buf[i] = array[i] & 255;\n  }\n\n  return buf;\n}\n\nfunction fromArrayBuffer(that, array, byteOffset, length) {\n  if (byteOffset < 0 || array.byteLength < byteOffset) {\n    throw new RangeError('\\'offset\\' is out of bounds');\n  }\n\n  if (array.byteLength < byteOffset + (length || 0)) {\n    throw new RangeError('\\'length\\' is out of bounds');\n  }\n\n  var buf;\n\n  if (byteOffset === undefined && length === undefined) {\n    buf = new Uint8Array(array);\n  } else if (length === undefined) {\n    buf = new Uint8Array(array, byteOffset);\n  } else {\n    buf = new Uint8Array(array, byteOffset, length);\n  }\n\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    // Return an augmented `Uint8Array` instance, for best performance\n    buf.__proto__ = Buffer.prototype;\n  } else {\n    // Fallback: Return an object instance of the Buffer class\n    buf = fromArrayLike(that, buf);\n  }\n\n  return buf;\n}\n\nfunction fromObject(that, obj) {\n  if (Buffer.isBuffer(obj)) {\n    var len = checked(obj.length) | 0;\n    var buf = createBuffer(that, len);\n\n    if (buf.length === 0) {\n      return buf;\n    }\n\n    obj.copy(buf, 0, 0, len);\n    return buf;\n  }\n\n  if (obj) {\n    if (typeof ArrayBuffer !== 'undefined' && obj.buffer instanceof ArrayBuffer || 'length' in obj) {\n      if (typeof obj.length !== 'number' || isnan(obj.length)) {\n        return createBuffer(that, 0);\n      }\n\n      return fromArrayLike(that, obj);\n    }\n\n    if (obj.type === 'Buffer' && Array.isArray(obj.data)) {\n      return fromArrayLike(that, obj.data);\n    }\n  }\n\n  throw new TypeError('First argument must be a string, Buffer, ArrayBuffer, Array, or array-like object.');\n}\n\nfunction utf8ToBytes(string, units) {\n  units = units || Infinity;\n  var codePoint;\n  var length = string.length;\n  var leadSurrogate = null;\n  var bytes = [];\n\n  for (var i = 0; i < length; ++i) {\n    codePoint = string.charCodeAt(i); // is surrogate component\n\n    if (codePoint > 0xD7FF && codePoint < 0xE000) {\n      // last char was a lead\n      if (!leadSurrogate) {\n        // no lead yet\n        if (codePoint > 0xDBFF) {\n          // unexpected trail\n          if ((units -= 3) > -1) bytes.push(0xEF, 0xBF, 0xBD);\n          continue;\n        } else if (i + 1 === length) {\n          // unpaired lead\n          if ((units -= 3) > -1) bytes.push(0xEF, 0xBF, 0xBD);\n          continue;\n        } // valid lead\n\n\n        leadSurrogate = codePoint;\n        continue;\n      } // 2 leads in a row\n\n\n      if (codePoint < 0xDC00) {\n        if ((units -= 3) > -1) bytes.push(0xEF, 0xBF, 0xBD);\n        leadSurrogate = codePoint;\n        continue;\n      } // valid surrogate pair\n\n\n      codePoint = (leadSurrogate - 0xD800 << 10 | codePoint - 0xDC00) + 0x10000;\n    } else if (leadSurrogate) {\n      // valid bmp char, but last char was a lead\n      if ((units -= 3) > -1) bytes.push(0xEF, 0xBF, 0xBD);\n    }\n\n    leadSurrogate = null; // encode utf8\n\n    if (codePoint < 0x80) {\n      if ((units -= 1) < 0) break;\n      bytes.push(codePoint);\n    } else if (codePoint < 0x800) {\n      if ((units -= 2) < 0) break;\n      bytes.push(codePoint >> 0x6 | 0xC0, codePoint & 0x3F | 0x80);\n    } else if (codePoint < 0x10000) {\n      if ((units -= 3) < 0) break;\n      bytes.push(codePoint >> 0xC | 0xE0, codePoint >> 0x6 & 0x3F | 0x80, codePoint & 0x3F | 0x80);\n    } else if (codePoint < 0x110000) {\n      if ((units -= 4) < 0) break;\n      bytes.push(codePoint >> 0x12 | 0xF0, codePoint >> 0xC & 0x3F | 0x80, codePoint >> 0x6 & 0x3F | 0x80, codePoint & 0x3F | 0x80);\n    } else {\n      throw new Error('Invalid code point');\n    }\n  }\n\n  return bytes;\n}\n\nfunction byteLength(string) {\n  if (Buffer.isBuffer(string)) {\n    return string.length;\n  }\n\n  if (typeof ArrayBuffer !== 'undefined' && typeof ArrayBuffer.isView === 'function' && (ArrayBuffer.isView(string) || string instanceof ArrayBuffer)) {\n    return string.byteLength;\n  }\n\n  if (typeof string !== 'string') {\n    string = '' + string;\n  }\n\n  var len = string.length;\n  if (len === 0) return 0;\n  return utf8ToBytes(string).length;\n}\n\nfunction blitBuffer(src, dst, offset, length) {\n  for (var i = 0; i < length; ++i) {\n    if (i + offset >= dst.length || i >= src.length) break;\n    dst[i + offset] = src[i];\n  }\n\n  return i;\n}\n\nfunction utf8Write(buf, string, offset, length) {\n  return blitBuffer(utf8ToBytes(string, buf.length - offset), buf, offset, length);\n}\n\nfunction from(that, value, offset, length) {\n  if (typeof value === 'number') {\n    throw new TypeError('\"value\" argument must not be a number');\n  }\n\n  if (typeof ArrayBuffer !== 'undefined' && value instanceof ArrayBuffer) {\n    return fromArrayBuffer(that, value, offset, length);\n  }\n\n  if (typeof value === 'string') {\n    return fromString(that, value, offset);\n  }\n\n  return fromObject(that, value);\n}\n\nBuffer.prototype.write = function write(string, offset, length) {\n  // Buffer#write(string)\n  if (offset === undefined) {\n    length = this.length;\n    offset = 0; // Buffer#write(string, encoding)\n  } else if (length === undefined && typeof offset === 'string') {\n    length = this.length;\n    offset = 0; // Buffer#write(string, offset[, length])\n  } else if (isFinite(offset)) {\n    offset = offset | 0;\n\n    if (isFinite(length)) {\n      length = length | 0;\n    } else {\n      length = undefined;\n    }\n  }\n\n  var remaining = this.length - offset;\n  if (length === undefined || length > remaining) length = remaining;\n\n  if (string.length > 0 && (length < 0 || offset < 0) || offset > this.length) {\n    throw new RangeError('Attempt to write outside buffer bounds');\n  }\n\n  return utf8Write(this, string, offset, length);\n};\n\nBuffer.prototype.slice = function slice(start, end) {\n  var len = this.length;\n  start = ~~start;\n  end = end === undefined ? len : ~~end;\n\n  if (start < 0) {\n    start += len;\n    if (start < 0) start = 0;\n  } else if (start > len) {\n    start = len;\n  }\n\n  if (end < 0) {\n    end += len;\n    if (end < 0) end = 0;\n  } else if (end > len) {\n    end = len;\n  }\n\n  if (end < start) end = start;\n  var newBuf;\n\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    newBuf = this.subarray(start, end); // Return an augmented `Uint8Array` instance\n\n    newBuf.__proto__ = Buffer.prototype;\n  } else {\n    var sliceLen = end - start;\n    newBuf = new Buffer(sliceLen, undefined);\n\n    for (var i = 0; i < sliceLen; ++i) {\n      newBuf[i] = this[i + start];\n    }\n  }\n\n  return newBuf;\n};\n\nBuffer.prototype.copy = function copy(target, targetStart, start, end) {\n  if (!start) start = 0;\n  if (!end && end !== 0) end = this.length;\n  if (targetStart >= target.length) targetStart = target.length;\n  if (!targetStart) targetStart = 0;\n  if (end > 0 && end < start) end = start; // Copy 0 bytes; we're done\n\n  if (end === start) return 0;\n  if (target.length === 0 || this.length === 0) return 0; // Fatal error conditions\n\n  if (targetStart < 0) {\n    throw new RangeError('targetStart out of bounds');\n  }\n\n  if (start < 0 || start >= this.length) throw new RangeError('sourceStart out of bounds');\n  if (end < 0) throw new RangeError('sourceEnd out of bounds'); // Are we oob?\n\n  if (end > this.length) end = this.length;\n\n  if (target.length - targetStart < end - start) {\n    end = target.length - targetStart + start;\n  }\n\n  var len = end - start;\n  var i;\n\n  if (this === target && start < targetStart && targetStart < end) {\n    // descending copy from end\n    for (i = len - 1; i >= 0; --i) {\n      target[i + targetStart] = this[i + start];\n    }\n  } else if (len < 1000 || !Buffer.TYPED_ARRAY_SUPPORT) {\n    // ascending copy from start\n    for (i = 0; i < len; ++i) {\n      target[i + targetStart] = this[i + start];\n    }\n  } else {\n    Uint8Array.prototype.set.call(target, this.subarray(start, start + len), targetStart);\n  }\n\n  return len;\n};\n\nBuffer.prototype.fill = function fill(val, start, end) {\n  // Handle string cases:\n  if (typeof val === 'string') {\n    if (typeof start === 'string') {\n      start = 0;\n      end = this.length;\n    } else if (typeof end === 'string') {\n      end = this.length;\n    }\n\n    if (val.length === 1) {\n      var code = val.charCodeAt(0);\n\n      if (code < 256) {\n        val = code;\n      }\n    }\n  } else if (typeof val === 'number') {\n    val = val & 255;\n  } // Invalid ranges are not set to a default, so can range check early.\n\n\n  if (start < 0 || this.length < start || this.length < end) {\n    throw new RangeError('Out of range index');\n  }\n\n  if (end <= start) {\n    return this;\n  }\n\n  start = start >>> 0;\n  end = end === undefined ? this.length : end >>> 0;\n  if (!val) val = 0;\n  var i;\n\n  if (typeof val === 'number') {\n    for (i = start; i < end; ++i) {\n      this[i] = val;\n    }\n  } else {\n    var bytes = Buffer.isBuffer(val) ? val : new Buffer(val);\n    var len = bytes.length;\n\n    for (i = 0; i < end - start; ++i) {\n      this[i + start] = bytes[i % len];\n    }\n  }\n\n  return this;\n};\n\nBuffer.concat = function concat(list, length) {\n  if (!isArray(list)) {\n    throw new TypeError('\"list\" argument must be an Array of Buffers');\n  }\n\n  if (list.length === 0) {\n    return createBuffer(null, 0);\n  }\n\n  var i;\n\n  if (length === undefined) {\n    length = 0;\n\n    for (i = 0; i < list.length; ++i) {\n      length += list[i].length;\n    }\n  }\n\n  var buffer = allocUnsafe(null, length);\n  var pos = 0;\n\n  for (i = 0; i < list.length; ++i) {\n    var buf = list[i];\n\n    if (!Buffer.isBuffer(buf)) {\n      throw new TypeError('\"list\" argument must be an Array of Buffers');\n    }\n\n    buf.copy(buffer, pos);\n    pos += buf.length;\n  }\n\n  return buffer;\n};\n\nBuffer.byteLength = byteLength;\nBuffer.prototype._isBuffer = true;\n\nBuffer.isBuffer = function isBuffer(b) {\n  return !!(b != null && b._isBuffer);\n};\n\nmodule.exports = Buffer;", "map": {"version": 3, "names": ["isArray", "require", "typedArraySupport", "arr", "Uint8Array", "__proto__", "prototype", "foo", "e", "<PERSON><PERSON><PERSON>", "TYPED_ARRAY_SUPPORT", "K_MAX_LENGTH", "arg", "offset", "length", "allocUnsafe", "from", "Symbol", "species", "Object", "defineProperty", "value", "configurable", "enumerable", "writable", "checked", "RangeError", "toString", "isnan", "val", "createBuffer", "that", "buf", "size", "i", "fromString", "string", "byteLength", "actual", "write", "slice", "fromArrayLike", "array", "fromArrayBuffer", "byteOffset", "undefined", "fromObject", "obj", "<PERSON><PERSON><PERSON><PERSON>", "len", "copy", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "buffer", "type", "Array", "data", "TypeError", "utf8ToBytes", "units", "Infinity", "codePoint", "leadSurrogate", "bytes", "charCodeAt", "push", "Error", "<PERSON><PERSON><PERSON><PERSON>", "blit<PERSON><PERSON>er", "src", "dst", "utf8Write", "isFinite", "remaining", "start", "end", "newBuf", "subarray", "sliceLen", "target", "targetStart", "set", "call", "fill", "code", "concat", "list", "pos", "_isBuffer", "b", "module", "exports"], "sources": ["D:/work/joyserver/client/node_modules/qrcode/lib/utils/typedarray-buffer.js"], "sourcesContent": ["/**\n * Implementation of a subset of node.js Buffer methods for the browser.\n * Based on https://github.com/feross/buffer\n */\n\n/* eslint-disable no-proto */\n\n'use strict'\n\nvar isArray = require('isarray')\n\nfunction typedArraySupport () {\n  // Can typed array instances be augmented?\n  try {\n    var arr = new Uint8Array(1)\n    arr.__proto__ = {__proto__: Uint8Array.prototype, foo: function () { return 42 }}\n    return arr.foo() === 42\n  } catch (e) {\n    return false\n  }\n}\n\nBuffer.TYPED_ARRAY_SUPPORT = typedArraySupport()\n\nvar K_MAX_LENGTH = Buffer.TYPED_ARRAY_SUPPORT\n    ? 0x7fffffff\n    : 0x3fffffff\n\nfunction Buffer (arg, offset, length) {\n  if (!Buffer.TYPED_ARRAY_SUPPORT && !(this instanceof Buffer)) {\n    return new Buffer(arg, offset, length)\n  }\n\n  if (typeof arg === 'number') {\n    return allocUnsafe(this, arg)\n  }\n\n  return from(this, arg, offset, length)\n}\n\nif (Buffer.TYPED_ARRAY_SUPPORT) {\n  Buffer.prototype.__proto__ = Uint8Array.prototype\n  Buffer.__proto__ = Uint8Array\n\n  // Fix subarray() in ES2016. See: https://github.com/feross/buffer/pull/97\n  if (typeof Symbol !== 'undefined' && Symbol.species &&\n      Buffer[Symbol.species] === Buffer) {\n    Object.defineProperty(Buffer, Symbol.species, {\n      value: null,\n      configurable: true,\n      enumerable: false,\n      writable: false\n    })\n  }\n}\n\nfunction checked (length) {\n  // Note: cannot use `length < K_MAX_LENGTH` here because that fails when\n  // length is NaN (which is otherwise coerced to zero.)\n  if (length >= K_MAX_LENGTH) {\n    throw new RangeError('Attempt to allocate Buffer larger than maximum ' +\n                         'size: 0x' + K_MAX_LENGTH.toString(16) + ' bytes')\n  }\n  return length | 0\n}\n\nfunction isnan (val) {\n  return val !== val // eslint-disable-line no-self-compare\n}\n\nfunction createBuffer (that, length) {\n  var buf\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    buf = new Uint8Array(length)\n    buf.__proto__ = Buffer.prototype\n  } else {\n    // Fallback: Return an object instance of the Buffer class\n    buf = that\n    if (buf === null) {\n      buf = new Buffer(length)\n    }\n    buf.length = length\n  }\n\n  return buf\n}\n\nfunction allocUnsafe (that, size) {\n  var buf = createBuffer(that, size < 0 ? 0 : checked(size) | 0)\n\n  if (!Buffer.TYPED_ARRAY_SUPPORT) {\n    for (var i = 0; i < size; ++i) {\n      buf[i] = 0\n    }\n  }\n\n  return buf\n}\n\nfunction fromString (that, string) {\n  var length = byteLength(string) | 0\n  var buf = createBuffer(that, length)\n\n  var actual = buf.write(string)\n\n  if (actual !== length) {\n    // Writing a hex string, for example, that contains invalid characters will\n    // cause everything after the first invalid character to be ignored. (e.g.\n    // 'abxxcd' will be treated as 'ab')\n    buf = buf.slice(0, actual)\n  }\n\n  return buf\n}\n\nfunction fromArrayLike (that, array) {\n  var length = array.length < 0 ? 0 : checked(array.length) | 0\n  var buf = createBuffer(that, length)\n  for (var i = 0; i < length; i += 1) {\n    buf[i] = array[i] & 255\n  }\n  return buf\n}\n\nfunction fromArrayBuffer (that, array, byteOffset, length) {\n  if (byteOffset < 0 || array.byteLength < byteOffset) {\n    throw new RangeError('\\'offset\\' is out of bounds')\n  }\n\n  if (array.byteLength < byteOffset + (length || 0)) {\n    throw new RangeError('\\'length\\' is out of bounds')\n  }\n\n  var buf\n  if (byteOffset === undefined && length === undefined) {\n    buf = new Uint8Array(array)\n  } else if (length === undefined) {\n    buf = new Uint8Array(array, byteOffset)\n  } else {\n    buf = new Uint8Array(array, byteOffset, length)\n  }\n\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    // Return an augmented `Uint8Array` instance, for best performance\n    buf.__proto__ = Buffer.prototype\n  } else {\n    // Fallback: Return an object instance of the Buffer class\n    buf = fromArrayLike(that, buf)\n  }\n\n  return buf\n}\n\nfunction fromObject (that, obj) {\n  if (Buffer.isBuffer(obj)) {\n    var len = checked(obj.length) | 0\n    var buf = createBuffer(that, len)\n\n    if (buf.length === 0) {\n      return buf\n    }\n\n    obj.copy(buf, 0, 0, len)\n    return buf\n  }\n\n  if (obj) {\n    if ((typeof ArrayBuffer !== 'undefined' &&\n        obj.buffer instanceof ArrayBuffer) || 'length' in obj) {\n      if (typeof obj.length !== 'number' || isnan(obj.length)) {\n        return createBuffer(that, 0)\n      }\n      return fromArrayLike(that, obj)\n    }\n\n    if (obj.type === 'Buffer' && Array.isArray(obj.data)) {\n      return fromArrayLike(that, obj.data)\n    }\n  }\n\n  throw new TypeError('First argument must be a string, Buffer, ArrayBuffer, Array, or array-like object.')\n}\n\nfunction utf8ToBytes (string, units) {\n  units = units || Infinity\n  var codePoint\n  var length = string.length\n  var leadSurrogate = null\n  var bytes = []\n\n  for (var i = 0; i < length; ++i) {\n    codePoint = string.charCodeAt(i)\n\n    // is surrogate component\n    if (codePoint > 0xD7FF && codePoint < 0xE000) {\n      // last char was a lead\n      if (!leadSurrogate) {\n        // no lead yet\n        if (codePoint > 0xDBFF) {\n          // unexpected trail\n          if ((units -= 3) > -1) bytes.push(0xEF, 0xBF, 0xBD)\n          continue\n        } else if (i + 1 === length) {\n          // unpaired lead\n          if ((units -= 3) > -1) bytes.push(0xEF, 0xBF, 0xBD)\n          continue\n        }\n\n        // valid lead\n        leadSurrogate = codePoint\n\n        continue\n      }\n\n      // 2 leads in a row\n      if (codePoint < 0xDC00) {\n        if ((units -= 3) > -1) bytes.push(0xEF, 0xBF, 0xBD)\n        leadSurrogate = codePoint\n        continue\n      }\n\n      // valid surrogate pair\n      codePoint = (leadSurrogate - 0xD800 << 10 | codePoint - 0xDC00) + 0x10000\n    } else if (leadSurrogate) {\n      // valid bmp char, but last char was a lead\n      if ((units -= 3) > -1) bytes.push(0xEF, 0xBF, 0xBD)\n    }\n\n    leadSurrogate = null\n\n    // encode utf8\n    if (codePoint < 0x80) {\n      if ((units -= 1) < 0) break\n      bytes.push(codePoint)\n    } else if (codePoint < 0x800) {\n      if ((units -= 2) < 0) break\n      bytes.push(\n        codePoint >> 0x6 | 0xC0,\n        codePoint & 0x3F | 0x80\n      )\n    } else if (codePoint < 0x10000) {\n      if ((units -= 3) < 0) break\n      bytes.push(\n        codePoint >> 0xC | 0xE0,\n        codePoint >> 0x6 & 0x3F | 0x80,\n        codePoint & 0x3F | 0x80\n      )\n    } else if (codePoint < 0x110000) {\n      if ((units -= 4) < 0) break\n      bytes.push(\n        codePoint >> 0x12 | 0xF0,\n        codePoint >> 0xC & 0x3F | 0x80,\n        codePoint >> 0x6 & 0x3F | 0x80,\n        codePoint & 0x3F | 0x80\n      )\n    } else {\n      throw new Error('Invalid code point')\n    }\n  }\n\n  return bytes\n}\n\nfunction byteLength (string) {\n  if (Buffer.isBuffer(string)) {\n    return string.length\n  }\n  if (typeof ArrayBuffer !== 'undefined' && typeof ArrayBuffer.isView === 'function' &&\n      (ArrayBuffer.isView(string) || string instanceof ArrayBuffer)) {\n    return string.byteLength\n  }\n  if (typeof string !== 'string') {\n    string = '' + string\n  }\n\n  var len = string.length\n  if (len === 0) return 0\n\n  return utf8ToBytes(string).length\n}\n\nfunction blitBuffer (src, dst, offset, length) {\n  for (var i = 0; i < length; ++i) {\n    if ((i + offset >= dst.length) || (i >= src.length)) break\n    dst[i + offset] = src[i]\n  }\n  return i\n}\n\nfunction utf8Write (buf, string, offset, length) {\n  return blitBuffer(utf8ToBytes(string, buf.length - offset), buf, offset, length)\n}\n\nfunction from (that, value, offset, length) {\n  if (typeof value === 'number') {\n    throw new TypeError('\"value\" argument must not be a number')\n  }\n\n  if (typeof ArrayBuffer !== 'undefined' && value instanceof ArrayBuffer) {\n    return fromArrayBuffer(that, value, offset, length)\n  }\n\n  if (typeof value === 'string') {\n    return fromString(that, value, offset)\n  }\n\n  return fromObject(that, value)\n}\n\nBuffer.prototype.write = function write (string, offset, length) {\n  // Buffer#write(string)\n  if (offset === undefined) {\n    length = this.length\n    offset = 0\n  // Buffer#write(string, encoding)\n  } else if (length === undefined && typeof offset === 'string') {\n    length = this.length\n    offset = 0\n  // Buffer#write(string, offset[, length])\n  } else if (isFinite(offset)) {\n    offset = offset | 0\n    if (isFinite(length)) {\n      length = length | 0\n    } else {\n      length = undefined\n    }\n  }\n\n  var remaining = this.length - offset\n  if (length === undefined || length > remaining) length = remaining\n\n  if ((string.length > 0 && (length < 0 || offset < 0)) || offset > this.length) {\n    throw new RangeError('Attempt to write outside buffer bounds')\n  }\n\n  return utf8Write(this, string, offset, length)\n}\n\nBuffer.prototype.slice = function slice (start, end) {\n  var len = this.length\n  start = ~~start\n  end = end === undefined ? len : ~~end\n\n  if (start < 0) {\n    start += len\n    if (start < 0) start = 0\n  } else if (start > len) {\n    start = len\n  }\n\n  if (end < 0) {\n    end += len\n    if (end < 0) end = 0\n  } else if (end > len) {\n    end = len\n  }\n\n  if (end < start) end = start\n\n  var newBuf\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    newBuf = this.subarray(start, end)\n    // Return an augmented `Uint8Array` instance\n    newBuf.__proto__ = Buffer.prototype\n  } else {\n    var sliceLen = end - start\n    newBuf = new Buffer(sliceLen, undefined)\n    for (var i = 0; i < sliceLen; ++i) {\n      newBuf[i] = this[i + start]\n    }\n  }\n\n  return newBuf\n}\n\nBuffer.prototype.copy = function copy (target, targetStart, start, end) {\n  if (!start) start = 0\n  if (!end && end !== 0) end = this.length\n  if (targetStart >= target.length) targetStart = target.length\n  if (!targetStart) targetStart = 0\n  if (end > 0 && end < start) end = start\n\n  // Copy 0 bytes; we're done\n  if (end === start) return 0\n  if (target.length === 0 || this.length === 0) return 0\n\n  // Fatal error conditions\n  if (targetStart < 0) {\n    throw new RangeError('targetStart out of bounds')\n  }\n  if (start < 0 || start >= this.length) throw new RangeError('sourceStart out of bounds')\n  if (end < 0) throw new RangeError('sourceEnd out of bounds')\n\n  // Are we oob?\n  if (end > this.length) end = this.length\n  if (target.length - targetStart < end - start) {\n    end = target.length - targetStart + start\n  }\n\n  var len = end - start\n  var i\n\n  if (this === target && start < targetStart && targetStart < end) {\n    // descending copy from end\n    for (i = len - 1; i >= 0; --i) {\n      target[i + targetStart] = this[i + start]\n    }\n  } else if (len < 1000 || !Buffer.TYPED_ARRAY_SUPPORT) {\n    // ascending copy from start\n    for (i = 0; i < len; ++i) {\n      target[i + targetStart] = this[i + start]\n    }\n  } else {\n    Uint8Array.prototype.set.call(\n      target,\n      this.subarray(start, start + len),\n      targetStart\n    )\n  }\n\n  return len\n}\n\nBuffer.prototype.fill = function fill (val, start, end) {\n  // Handle string cases:\n  if (typeof val === 'string') {\n    if (typeof start === 'string') {\n      start = 0\n      end = this.length\n    } else if (typeof end === 'string') {\n      end = this.length\n    }\n    if (val.length === 1) {\n      var code = val.charCodeAt(0)\n      if (code < 256) {\n        val = code\n      }\n    }\n  } else if (typeof val === 'number') {\n    val = val & 255\n  }\n\n  // Invalid ranges are not set to a default, so can range check early.\n  if (start < 0 || this.length < start || this.length < end) {\n    throw new RangeError('Out of range index')\n  }\n\n  if (end <= start) {\n    return this\n  }\n\n  start = start >>> 0\n  end = end === undefined ? this.length : end >>> 0\n\n  if (!val) val = 0\n\n  var i\n  if (typeof val === 'number') {\n    for (i = start; i < end; ++i) {\n      this[i] = val\n    }\n  } else {\n    var bytes = Buffer.isBuffer(val)\n      ? val\n      : new Buffer(val)\n    var len = bytes.length\n    for (i = 0; i < end - start; ++i) {\n      this[i + start] = bytes[i % len]\n    }\n  }\n\n  return this\n}\n\nBuffer.concat = function concat (list, length) {\n  if (!isArray(list)) {\n    throw new TypeError('\"list\" argument must be an Array of Buffers')\n  }\n\n  if (list.length === 0) {\n    return createBuffer(null, 0)\n  }\n\n  var i\n  if (length === undefined) {\n    length = 0\n    for (i = 0; i < list.length; ++i) {\n      length += list[i].length\n    }\n  }\n\n  var buffer = allocUnsafe(null, length)\n  var pos = 0\n  for (i = 0; i < list.length; ++i) {\n    var buf = list[i]\n    if (!Buffer.isBuffer(buf)) {\n      throw new TypeError('\"list\" argument must be an Array of Buffers')\n    }\n    buf.copy(buffer, pos)\n    pos += buf.length\n  }\n  return buffer\n}\n\nBuffer.byteLength = byteLength\n\nBuffer.prototype._isBuffer = true\nBuffer.isBuffer = function isBuffer (b) {\n  return !!(b != null && b._isBuffer)\n}\n\nmodule.exports = Buffer\n"], "mappings": "AAAA;AACA;AACA;AACA;;AAEA;AAEA;;AAEA,IAAIA,OAAO,GAAGC,OAAO,CAAC,SAAD,CAArB;;AAEA,SAASC,iBAAT,GAA8B;EAC5B;EACA,IAAI;IACF,IAAIC,GAAG,GAAG,IAAIC,UAAJ,CAAe,CAAf,CAAV;IACAD,GAAG,CAACE,SAAJ,GAAgB;MAACA,SAAS,EAAED,UAAU,CAACE,SAAvB;MAAkCC,GAAG,EAAE,YAAY;QAAE,OAAO,EAAP;MAAW;IAAhE,CAAhB;IACA,OAAOJ,GAAG,CAACI,GAAJ,OAAc,EAArB;EACD,CAJD,CAIE,OAAOC,CAAP,EAAU;IACV,OAAO,KAAP;EACD;AACF;;AAEDC,MAAM,CAACC,mBAAP,GAA6BR,iBAAiB,EAA9C;AAEA,IAAIS,YAAY,GAAGF,MAAM,CAACC,mBAAP,GACb,UADa,GAEb,UAFN;;AAIA,SAASD,MAAT,CAAiBG,GAAjB,EAAsBC,MAAtB,EAA8BC,MAA9B,EAAsC;EACpC,IAAI,CAACL,MAAM,CAACC,mBAAR,IAA+B,EAAE,gBAAgBD,MAAlB,CAAnC,EAA8D;IAC5D,OAAO,IAAIA,MAAJ,CAAWG,GAAX,EAAgBC,MAAhB,EAAwBC,MAAxB,CAAP;EACD;;EAED,IAAI,OAAOF,GAAP,KAAe,QAAnB,EAA6B;IAC3B,OAAOG,WAAW,CAAC,IAAD,EAAOH,GAAP,CAAlB;EACD;;EAED,OAAOI,IAAI,CAAC,IAAD,EAAOJ,GAAP,EAAYC,MAAZ,EAAoBC,MAApB,CAAX;AACD;;AAED,IAAIL,MAAM,CAACC,mBAAX,EAAgC;EAC9BD,MAAM,CAACH,SAAP,CAAiBD,SAAjB,GAA6BD,UAAU,CAACE,SAAxC;EACAG,MAAM,CAACJ,SAAP,GAAmBD,UAAnB,CAF8B,CAI9B;;EACA,IAAI,OAAOa,MAAP,KAAkB,WAAlB,IAAiCA,MAAM,CAACC,OAAxC,IACAT,MAAM,CAACQ,MAAM,CAACC,OAAR,CAAN,KAA2BT,MAD/B,EACuC;IACrCU,MAAM,CAACC,cAAP,CAAsBX,MAAtB,EAA8BQ,MAAM,CAACC,OAArC,EAA8C;MAC5CG,KAAK,EAAE,IADqC;MAE5CC,YAAY,EAAE,IAF8B;MAG5CC,UAAU,EAAE,KAHgC;MAI5CC,QAAQ,EAAE;IAJkC,CAA9C;EAMD;AACF;;AAED,SAASC,OAAT,CAAkBX,MAAlB,EAA0B;EACxB;EACA;EACA,IAAIA,MAAM,IAAIH,YAAd,EAA4B;IAC1B,MAAM,IAAIe,UAAJ,CAAe,oDACA,UADA,GACaf,YAAY,CAACgB,QAAb,CAAsB,EAAtB,CADb,GACyC,QADxD,CAAN;EAED;;EACD,OAAOb,MAAM,GAAG,CAAhB;AACD;;AAED,SAASc,KAAT,CAAgBC,GAAhB,EAAqB;EACnB,OAAOA,GAAG,KAAKA,GAAf,CADmB,CACA;AACpB;;AAED,SAASC,YAAT,CAAuBC,IAAvB,EAA6BjB,MAA7B,EAAqC;EACnC,IAAIkB,GAAJ;;EACA,IAAIvB,MAAM,CAACC,mBAAX,EAAgC;IAC9BsB,GAAG,GAAG,IAAI5B,UAAJ,CAAeU,MAAf,CAAN;IACAkB,GAAG,CAAC3B,SAAJ,GAAgBI,MAAM,CAACH,SAAvB;EACD,CAHD,MAGO;IACL;IACA0B,GAAG,GAAGD,IAAN;;IACA,IAAIC,GAAG,KAAK,IAAZ,EAAkB;MAChBA,GAAG,GAAG,IAAIvB,MAAJ,CAAWK,MAAX,CAAN;IACD;;IACDkB,GAAG,CAAClB,MAAJ,GAAaA,MAAb;EACD;;EAED,OAAOkB,GAAP;AACD;;AAED,SAASjB,WAAT,CAAsBgB,IAAtB,EAA4BE,IAA5B,EAAkC;EAChC,IAAID,GAAG,GAAGF,YAAY,CAACC,IAAD,EAAOE,IAAI,GAAG,CAAP,GAAW,CAAX,GAAeR,OAAO,CAACQ,IAAD,CAAP,GAAgB,CAAtC,CAAtB;;EAEA,IAAI,CAACxB,MAAM,CAACC,mBAAZ,EAAiC;IAC/B,KAAK,IAAIwB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGD,IAApB,EAA0B,EAAEC,CAA5B,EAA+B;MAC7BF,GAAG,CAACE,CAAD,CAAH,GAAS,CAAT;IACD;EACF;;EAED,OAAOF,GAAP;AACD;;AAED,SAASG,UAAT,CAAqBJ,IAArB,EAA2BK,MAA3B,EAAmC;EACjC,IAAItB,MAAM,GAAGuB,UAAU,CAACD,MAAD,CAAV,GAAqB,CAAlC;EACA,IAAIJ,GAAG,GAAGF,YAAY,CAACC,IAAD,EAAOjB,MAAP,CAAtB;EAEA,IAAIwB,MAAM,GAAGN,GAAG,CAACO,KAAJ,CAAUH,MAAV,CAAb;;EAEA,IAAIE,MAAM,KAAKxB,MAAf,EAAuB;IACrB;IACA;IACA;IACAkB,GAAG,GAAGA,GAAG,CAACQ,KAAJ,CAAU,CAAV,EAAaF,MAAb,CAAN;EACD;;EAED,OAAON,GAAP;AACD;;AAED,SAASS,aAAT,CAAwBV,IAAxB,EAA8BW,KAA9B,EAAqC;EACnC,IAAI5B,MAAM,GAAG4B,KAAK,CAAC5B,MAAN,GAAe,CAAf,GAAmB,CAAnB,GAAuBW,OAAO,CAACiB,KAAK,CAAC5B,MAAP,CAAP,GAAwB,CAA5D;EACA,IAAIkB,GAAG,GAAGF,YAAY,CAACC,IAAD,EAAOjB,MAAP,CAAtB;;EACA,KAAK,IAAIoB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGpB,MAApB,EAA4BoB,CAAC,IAAI,CAAjC,EAAoC;IAClCF,GAAG,CAACE,CAAD,CAAH,GAASQ,KAAK,CAACR,CAAD,CAAL,GAAW,GAApB;EACD;;EACD,OAAOF,GAAP;AACD;;AAED,SAASW,eAAT,CAA0BZ,IAA1B,EAAgCW,KAAhC,EAAuCE,UAAvC,EAAmD9B,MAAnD,EAA2D;EACzD,IAAI8B,UAAU,GAAG,CAAb,IAAkBF,KAAK,CAACL,UAAN,GAAmBO,UAAzC,EAAqD;IACnD,MAAM,IAAIlB,UAAJ,CAAe,6BAAf,CAAN;EACD;;EAED,IAAIgB,KAAK,CAACL,UAAN,GAAmBO,UAAU,IAAI9B,MAAM,IAAI,CAAd,CAAjC,EAAmD;IACjD,MAAM,IAAIY,UAAJ,CAAe,6BAAf,CAAN;EACD;;EAED,IAAIM,GAAJ;;EACA,IAAIY,UAAU,KAAKC,SAAf,IAA4B/B,MAAM,KAAK+B,SAA3C,EAAsD;IACpDb,GAAG,GAAG,IAAI5B,UAAJ,CAAesC,KAAf,CAAN;EACD,CAFD,MAEO,IAAI5B,MAAM,KAAK+B,SAAf,EAA0B;IAC/Bb,GAAG,GAAG,IAAI5B,UAAJ,CAAesC,KAAf,EAAsBE,UAAtB,CAAN;EACD,CAFM,MAEA;IACLZ,GAAG,GAAG,IAAI5B,UAAJ,CAAesC,KAAf,EAAsBE,UAAtB,EAAkC9B,MAAlC,CAAN;EACD;;EAED,IAAIL,MAAM,CAACC,mBAAX,EAAgC;IAC9B;IACAsB,GAAG,CAAC3B,SAAJ,GAAgBI,MAAM,CAACH,SAAvB;EACD,CAHD,MAGO;IACL;IACA0B,GAAG,GAAGS,aAAa,CAACV,IAAD,EAAOC,GAAP,CAAnB;EACD;;EAED,OAAOA,GAAP;AACD;;AAED,SAASc,UAAT,CAAqBf,IAArB,EAA2BgB,GAA3B,EAAgC;EAC9B,IAAItC,MAAM,CAACuC,QAAP,CAAgBD,GAAhB,CAAJ,EAA0B;IACxB,IAAIE,GAAG,GAAGxB,OAAO,CAACsB,GAAG,CAACjC,MAAL,CAAP,GAAsB,CAAhC;IACA,IAAIkB,GAAG,GAAGF,YAAY,CAACC,IAAD,EAAOkB,GAAP,CAAtB;;IAEA,IAAIjB,GAAG,CAAClB,MAAJ,KAAe,CAAnB,EAAsB;MACpB,OAAOkB,GAAP;IACD;;IAEDe,GAAG,CAACG,IAAJ,CAASlB,GAAT,EAAc,CAAd,EAAiB,CAAjB,EAAoBiB,GAApB;IACA,OAAOjB,GAAP;EACD;;EAED,IAAIe,GAAJ,EAAS;IACP,IAAK,OAAOI,WAAP,KAAuB,WAAvB,IACDJ,GAAG,CAACK,MAAJ,YAAsBD,WADtB,IACsC,YAAYJ,GADtD,EAC2D;MACzD,IAAI,OAAOA,GAAG,CAACjC,MAAX,KAAsB,QAAtB,IAAkCc,KAAK,CAACmB,GAAG,CAACjC,MAAL,CAA3C,EAAyD;QACvD,OAAOgB,YAAY,CAACC,IAAD,EAAO,CAAP,CAAnB;MACD;;MACD,OAAOU,aAAa,CAACV,IAAD,EAAOgB,GAAP,CAApB;IACD;;IAED,IAAIA,GAAG,CAACM,IAAJ,KAAa,QAAb,IAAyBC,KAAK,CAACtD,OAAN,CAAc+C,GAAG,CAACQ,IAAlB,CAA7B,EAAsD;MACpD,OAAOd,aAAa,CAACV,IAAD,EAAOgB,GAAG,CAACQ,IAAX,CAApB;IACD;EACF;;EAED,MAAM,IAAIC,SAAJ,CAAc,oFAAd,CAAN;AACD;;AAED,SAASC,WAAT,CAAsBrB,MAAtB,EAA8BsB,KAA9B,EAAqC;EACnCA,KAAK,GAAGA,KAAK,IAAIC,QAAjB;EACA,IAAIC,SAAJ;EACA,IAAI9C,MAAM,GAAGsB,MAAM,CAACtB,MAApB;EACA,IAAI+C,aAAa,GAAG,IAApB;EACA,IAAIC,KAAK,GAAG,EAAZ;;EAEA,KAAK,IAAI5B,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGpB,MAApB,EAA4B,EAAEoB,CAA9B,EAAiC;IAC/B0B,SAAS,GAAGxB,MAAM,CAAC2B,UAAP,CAAkB7B,CAAlB,CAAZ,CAD+B,CAG/B;;IACA,IAAI0B,SAAS,GAAG,MAAZ,IAAsBA,SAAS,GAAG,MAAtC,EAA8C;MAC5C;MACA,IAAI,CAACC,aAAL,EAAoB;QAClB;QACA,IAAID,SAAS,GAAG,MAAhB,EAAwB;UACtB;UACA,IAAI,CAACF,KAAK,IAAI,CAAV,IAAe,CAAC,CAApB,EAAuBI,KAAK,CAACE,IAAN,CAAW,IAAX,EAAiB,IAAjB,EAAuB,IAAvB;UACvB;QACD,CAJD,MAIO,IAAI9B,CAAC,GAAG,CAAJ,KAAUpB,MAAd,EAAsB;UAC3B;UACA,IAAI,CAAC4C,KAAK,IAAI,CAAV,IAAe,CAAC,CAApB,EAAuBI,KAAK,CAACE,IAAN,CAAW,IAAX,EAAiB,IAAjB,EAAuB,IAAvB;UACvB;QACD,CAViB,CAYlB;;;QACAH,aAAa,GAAGD,SAAhB;QAEA;MACD,CAlB2C,CAoB5C;;;MACA,IAAIA,SAAS,GAAG,MAAhB,EAAwB;QACtB,IAAI,CAACF,KAAK,IAAI,CAAV,IAAe,CAAC,CAApB,EAAuBI,KAAK,CAACE,IAAN,CAAW,IAAX,EAAiB,IAAjB,EAAuB,IAAvB;QACvBH,aAAa,GAAGD,SAAhB;QACA;MACD,CAzB2C,CA2B5C;;;MACAA,SAAS,GAAG,CAACC,aAAa,GAAG,MAAhB,IAA0B,EAA1B,GAA+BD,SAAS,GAAG,MAA5C,IAAsD,OAAlE;IACD,CA7BD,MA6BO,IAAIC,aAAJ,EAAmB;MACxB;MACA,IAAI,CAACH,KAAK,IAAI,CAAV,IAAe,CAAC,CAApB,EAAuBI,KAAK,CAACE,IAAN,CAAW,IAAX,EAAiB,IAAjB,EAAuB,IAAvB;IACxB;;IAEDH,aAAa,GAAG,IAAhB,CAtC+B,CAwC/B;;IACA,IAAID,SAAS,GAAG,IAAhB,EAAsB;MACpB,IAAI,CAACF,KAAK,IAAI,CAAV,IAAe,CAAnB,EAAsB;MACtBI,KAAK,CAACE,IAAN,CAAWJ,SAAX;IACD,CAHD,MAGO,IAAIA,SAAS,GAAG,KAAhB,EAAuB;MAC5B,IAAI,CAACF,KAAK,IAAI,CAAV,IAAe,CAAnB,EAAsB;MACtBI,KAAK,CAACE,IAAN,CACEJ,SAAS,IAAI,GAAb,GAAmB,IADrB,EAEEA,SAAS,GAAG,IAAZ,GAAmB,IAFrB;IAID,CANM,MAMA,IAAIA,SAAS,GAAG,OAAhB,EAAyB;MAC9B,IAAI,CAACF,KAAK,IAAI,CAAV,IAAe,CAAnB,EAAsB;MACtBI,KAAK,CAACE,IAAN,CACEJ,SAAS,IAAI,GAAb,GAAmB,IADrB,EAEEA,SAAS,IAAI,GAAb,GAAmB,IAAnB,GAA0B,IAF5B,EAGEA,SAAS,GAAG,IAAZ,GAAmB,IAHrB;IAKD,CAPM,MAOA,IAAIA,SAAS,GAAG,QAAhB,EAA0B;MAC/B,IAAI,CAACF,KAAK,IAAI,CAAV,IAAe,CAAnB,EAAsB;MACtBI,KAAK,CAACE,IAAN,CACEJ,SAAS,IAAI,IAAb,GAAoB,IADtB,EAEEA,SAAS,IAAI,GAAb,GAAmB,IAAnB,GAA0B,IAF5B,EAGEA,SAAS,IAAI,GAAb,GAAmB,IAAnB,GAA0B,IAH5B,EAIEA,SAAS,GAAG,IAAZ,GAAmB,IAJrB;IAMD,CARM,MAQA;MACL,MAAM,IAAIK,KAAJ,CAAU,oBAAV,CAAN;IACD;EACF;;EAED,OAAOH,KAAP;AACD;;AAED,SAASzB,UAAT,CAAqBD,MAArB,EAA6B;EAC3B,IAAI3B,MAAM,CAACuC,QAAP,CAAgBZ,MAAhB,CAAJ,EAA6B;IAC3B,OAAOA,MAAM,CAACtB,MAAd;EACD;;EACD,IAAI,OAAOqC,WAAP,KAAuB,WAAvB,IAAsC,OAAOA,WAAW,CAACe,MAAnB,KAA8B,UAApE,KACCf,WAAW,CAACe,MAAZ,CAAmB9B,MAAnB,KAA8BA,MAAM,YAAYe,WADjD,CAAJ,EACmE;IACjE,OAAOf,MAAM,CAACC,UAAd;EACD;;EACD,IAAI,OAAOD,MAAP,KAAkB,QAAtB,EAAgC;IAC9BA,MAAM,GAAG,KAAKA,MAAd;EACD;;EAED,IAAIa,GAAG,GAAGb,MAAM,CAACtB,MAAjB;EACA,IAAImC,GAAG,KAAK,CAAZ,EAAe,OAAO,CAAP;EAEf,OAAOQ,WAAW,CAACrB,MAAD,CAAX,CAAoBtB,MAA3B;AACD;;AAED,SAASqD,UAAT,CAAqBC,GAArB,EAA0BC,GAA1B,EAA+BxD,MAA/B,EAAuCC,MAAvC,EAA+C;EAC7C,KAAK,IAAIoB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGpB,MAApB,EAA4B,EAAEoB,CAA9B,EAAiC;IAC/B,IAAKA,CAAC,GAAGrB,MAAJ,IAAcwD,GAAG,CAACvD,MAAnB,IAA+BoB,CAAC,IAAIkC,GAAG,CAACtD,MAA5C,EAAqD;IACrDuD,GAAG,CAACnC,CAAC,GAAGrB,MAAL,CAAH,GAAkBuD,GAAG,CAAClC,CAAD,CAArB;EACD;;EACD,OAAOA,CAAP;AACD;;AAED,SAASoC,SAAT,CAAoBtC,GAApB,EAAyBI,MAAzB,EAAiCvB,MAAjC,EAAyCC,MAAzC,EAAiD;EAC/C,OAAOqD,UAAU,CAACV,WAAW,CAACrB,MAAD,EAASJ,GAAG,CAAClB,MAAJ,GAAaD,MAAtB,CAAZ,EAA2CmB,GAA3C,EAAgDnB,MAAhD,EAAwDC,MAAxD,CAAjB;AACD;;AAED,SAASE,IAAT,CAAee,IAAf,EAAqBV,KAArB,EAA4BR,MAA5B,EAAoCC,MAApC,EAA4C;EAC1C,IAAI,OAAOO,KAAP,KAAiB,QAArB,EAA+B;IAC7B,MAAM,IAAImC,SAAJ,CAAc,uCAAd,CAAN;EACD;;EAED,IAAI,OAAOL,WAAP,KAAuB,WAAvB,IAAsC9B,KAAK,YAAY8B,WAA3D,EAAwE;IACtE,OAAOR,eAAe,CAACZ,IAAD,EAAOV,KAAP,EAAcR,MAAd,EAAsBC,MAAtB,CAAtB;EACD;;EAED,IAAI,OAAOO,KAAP,KAAiB,QAArB,EAA+B;IAC7B,OAAOc,UAAU,CAACJ,IAAD,EAAOV,KAAP,EAAcR,MAAd,CAAjB;EACD;;EAED,OAAOiC,UAAU,CAACf,IAAD,EAAOV,KAAP,CAAjB;AACD;;AAEDZ,MAAM,CAACH,SAAP,CAAiBiC,KAAjB,GAAyB,SAASA,KAAT,CAAgBH,MAAhB,EAAwBvB,MAAxB,EAAgCC,MAAhC,EAAwC;EAC/D;EACA,IAAID,MAAM,KAAKgC,SAAf,EAA0B;IACxB/B,MAAM,GAAG,KAAKA,MAAd;IACAD,MAAM,GAAG,CAAT,CAFwB,CAG1B;EACC,CAJD,MAIO,IAAIC,MAAM,KAAK+B,SAAX,IAAwB,OAAOhC,MAAP,KAAkB,QAA9C,EAAwD;IAC7DC,MAAM,GAAG,KAAKA,MAAd;IACAD,MAAM,GAAG,CAAT,CAF6D,CAG/D;EACC,CAJM,MAIA,IAAI0D,QAAQ,CAAC1D,MAAD,CAAZ,EAAsB;IAC3BA,MAAM,GAAGA,MAAM,GAAG,CAAlB;;IACA,IAAI0D,QAAQ,CAACzD,MAAD,CAAZ,EAAsB;MACpBA,MAAM,GAAGA,MAAM,GAAG,CAAlB;IACD,CAFD,MAEO;MACLA,MAAM,GAAG+B,SAAT;IACD;EACF;;EAED,IAAI2B,SAAS,GAAG,KAAK1D,MAAL,GAAcD,MAA9B;EACA,IAAIC,MAAM,KAAK+B,SAAX,IAAwB/B,MAAM,GAAG0D,SAArC,EAAgD1D,MAAM,GAAG0D,SAAT;;EAEhD,IAAKpC,MAAM,CAACtB,MAAP,GAAgB,CAAhB,KAAsBA,MAAM,GAAG,CAAT,IAAcD,MAAM,GAAG,CAA7C,CAAD,IAAqDA,MAAM,GAAG,KAAKC,MAAvE,EAA+E;IAC7E,MAAM,IAAIY,UAAJ,CAAe,wCAAf,CAAN;EACD;;EAED,OAAO4C,SAAS,CAAC,IAAD,EAAOlC,MAAP,EAAevB,MAAf,EAAuBC,MAAvB,CAAhB;AACD,CA3BD;;AA6BAL,MAAM,CAACH,SAAP,CAAiBkC,KAAjB,GAAyB,SAASA,KAAT,CAAgBiC,KAAhB,EAAuBC,GAAvB,EAA4B;EACnD,IAAIzB,GAAG,GAAG,KAAKnC,MAAf;EACA2D,KAAK,GAAG,CAAC,CAACA,KAAV;EACAC,GAAG,GAAGA,GAAG,KAAK7B,SAAR,GAAoBI,GAApB,GAA0B,CAAC,CAACyB,GAAlC;;EAEA,IAAID,KAAK,GAAG,CAAZ,EAAe;IACbA,KAAK,IAAIxB,GAAT;IACA,IAAIwB,KAAK,GAAG,CAAZ,EAAeA,KAAK,GAAG,CAAR;EAChB,CAHD,MAGO,IAAIA,KAAK,GAAGxB,GAAZ,EAAiB;IACtBwB,KAAK,GAAGxB,GAAR;EACD;;EAED,IAAIyB,GAAG,GAAG,CAAV,EAAa;IACXA,GAAG,IAAIzB,GAAP;IACA,IAAIyB,GAAG,GAAG,CAAV,EAAaA,GAAG,GAAG,CAAN;EACd,CAHD,MAGO,IAAIA,GAAG,GAAGzB,GAAV,EAAe;IACpByB,GAAG,GAAGzB,GAAN;EACD;;EAED,IAAIyB,GAAG,GAAGD,KAAV,EAAiBC,GAAG,GAAGD,KAAN;EAEjB,IAAIE,MAAJ;;EACA,IAAIlE,MAAM,CAACC,mBAAX,EAAgC;IAC9BiE,MAAM,GAAG,KAAKC,QAAL,CAAcH,KAAd,EAAqBC,GAArB,CAAT,CAD8B,CAE9B;;IACAC,MAAM,CAACtE,SAAP,GAAmBI,MAAM,CAACH,SAA1B;EACD,CAJD,MAIO;IACL,IAAIuE,QAAQ,GAAGH,GAAG,GAAGD,KAArB;IACAE,MAAM,GAAG,IAAIlE,MAAJ,CAAWoE,QAAX,EAAqBhC,SAArB,CAAT;;IACA,KAAK,IAAIX,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG2C,QAApB,EAA8B,EAAE3C,CAAhC,EAAmC;MACjCyC,MAAM,CAACzC,CAAD,CAAN,GAAY,KAAKA,CAAC,GAAGuC,KAAT,CAAZ;IACD;EACF;;EAED,OAAOE,MAAP;AACD,CAnCD;;AAqCAlE,MAAM,CAACH,SAAP,CAAiB4C,IAAjB,GAAwB,SAASA,IAAT,CAAe4B,MAAf,EAAuBC,WAAvB,EAAoCN,KAApC,EAA2CC,GAA3C,EAAgD;EACtE,IAAI,CAACD,KAAL,EAAYA,KAAK,GAAG,CAAR;EACZ,IAAI,CAACC,GAAD,IAAQA,GAAG,KAAK,CAApB,EAAuBA,GAAG,GAAG,KAAK5D,MAAX;EACvB,IAAIiE,WAAW,IAAID,MAAM,CAAChE,MAA1B,EAAkCiE,WAAW,GAAGD,MAAM,CAAChE,MAArB;EAClC,IAAI,CAACiE,WAAL,EAAkBA,WAAW,GAAG,CAAd;EAClB,IAAIL,GAAG,GAAG,CAAN,IAAWA,GAAG,GAAGD,KAArB,EAA4BC,GAAG,GAAGD,KAAN,CAL0C,CAOtE;;EACA,IAAIC,GAAG,KAAKD,KAAZ,EAAmB,OAAO,CAAP;EACnB,IAAIK,MAAM,CAAChE,MAAP,KAAkB,CAAlB,IAAuB,KAAKA,MAAL,KAAgB,CAA3C,EAA8C,OAAO,CAAP,CATwB,CAWtE;;EACA,IAAIiE,WAAW,GAAG,CAAlB,EAAqB;IACnB,MAAM,IAAIrD,UAAJ,CAAe,2BAAf,CAAN;EACD;;EACD,IAAI+C,KAAK,GAAG,CAAR,IAAaA,KAAK,IAAI,KAAK3D,MAA/B,EAAuC,MAAM,IAAIY,UAAJ,CAAe,2BAAf,CAAN;EACvC,IAAIgD,GAAG,GAAG,CAAV,EAAa,MAAM,IAAIhD,UAAJ,CAAe,yBAAf,CAAN,CAhByD,CAkBtE;;EACA,IAAIgD,GAAG,GAAG,KAAK5D,MAAf,EAAuB4D,GAAG,GAAG,KAAK5D,MAAX;;EACvB,IAAIgE,MAAM,CAAChE,MAAP,GAAgBiE,WAAhB,GAA8BL,GAAG,GAAGD,KAAxC,EAA+C;IAC7CC,GAAG,GAAGI,MAAM,CAAChE,MAAP,GAAgBiE,WAAhB,GAA8BN,KAApC;EACD;;EAED,IAAIxB,GAAG,GAAGyB,GAAG,GAAGD,KAAhB;EACA,IAAIvC,CAAJ;;EAEA,IAAI,SAAS4C,MAAT,IAAmBL,KAAK,GAAGM,WAA3B,IAA0CA,WAAW,GAAGL,GAA5D,EAAiE;IAC/D;IACA,KAAKxC,CAAC,GAAGe,GAAG,GAAG,CAAf,EAAkBf,CAAC,IAAI,CAAvB,EAA0B,EAAEA,CAA5B,EAA+B;MAC7B4C,MAAM,CAAC5C,CAAC,GAAG6C,WAAL,CAAN,GAA0B,KAAK7C,CAAC,GAAGuC,KAAT,CAA1B;IACD;EACF,CALD,MAKO,IAAIxB,GAAG,GAAG,IAAN,IAAc,CAACxC,MAAM,CAACC,mBAA1B,EAA+C;IACpD;IACA,KAAKwB,CAAC,GAAG,CAAT,EAAYA,CAAC,GAAGe,GAAhB,EAAqB,EAAEf,CAAvB,EAA0B;MACxB4C,MAAM,CAAC5C,CAAC,GAAG6C,WAAL,CAAN,GAA0B,KAAK7C,CAAC,GAAGuC,KAAT,CAA1B;IACD;EACF,CALM,MAKA;IACLrE,UAAU,CAACE,SAAX,CAAqB0E,GAArB,CAAyBC,IAAzB,CACEH,MADF,EAEE,KAAKF,QAAL,CAAcH,KAAd,EAAqBA,KAAK,GAAGxB,GAA7B,CAFF,EAGE8B,WAHF;EAKD;;EAED,OAAO9B,GAAP;AACD,CA9CD;;AAgDAxC,MAAM,CAACH,SAAP,CAAiB4E,IAAjB,GAAwB,SAASA,IAAT,CAAerD,GAAf,EAAoB4C,KAApB,EAA2BC,GAA3B,EAAgC;EACtD;EACA,IAAI,OAAO7C,GAAP,KAAe,QAAnB,EAA6B;IAC3B,IAAI,OAAO4C,KAAP,KAAiB,QAArB,EAA+B;MAC7BA,KAAK,GAAG,CAAR;MACAC,GAAG,GAAG,KAAK5D,MAAX;IACD,CAHD,MAGO,IAAI,OAAO4D,GAAP,KAAe,QAAnB,EAA6B;MAClCA,GAAG,GAAG,KAAK5D,MAAX;IACD;;IACD,IAAIe,GAAG,CAACf,MAAJ,KAAe,CAAnB,EAAsB;MACpB,IAAIqE,IAAI,GAAGtD,GAAG,CAACkC,UAAJ,CAAe,CAAf,CAAX;;MACA,IAAIoB,IAAI,GAAG,GAAX,EAAgB;QACdtD,GAAG,GAAGsD,IAAN;MACD;IACF;EACF,CAbD,MAaO,IAAI,OAAOtD,GAAP,KAAe,QAAnB,EAA6B;IAClCA,GAAG,GAAGA,GAAG,GAAG,GAAZ;EACD,CAjBqD,CAmBtD;;;EACA,IAAI4C,KAAK,GAAG,CAAR,IAAa,KAAK3D,MAAL,GAAc2D,KAA3B,IAAoC,KAAK3D,MAAL,GAAc4D,GAAtD,EAA2D;IACzD,MAAM,IAAIhD,UAAJ,CAAe,oBAAf,CAAN;EACD;;EAED,IAAIgD,GAAG,IAAID,KAAX,EAAkB;IAChB,OAAO,IAAP;EACD;;EAEDA,KAAK,GAAGA,KAAK,KAAK,CAAlB;EACAC,GAAG,GAAGA,GAAG,KAAK7B,SAAR,GAAoB,KAAK/B,MAAzB,GAAkC4D,GAAG,KAAK,CAAhD;EAEA,IAAI,CAAC7C,GAAL,EAAUA,GAAG,GAAG,CAAN;EAEV,IAAIK,CAAJ;;EACA,IAAI,OAAOL,GAAP,KAAe,QAAnB,EAA6B;IAC3B,KAAKK,CAAC,GAAGuC,KAAT,EAAgBvC,CAAC,GAAGwC,GAApB,EAAyB,EAAExC,CAA3B,EAA8B;MAC5B,KAAKA,CAAL,IAAUL,GAAV;IACD;EACF,CAJD,MAIO;IACL,IAAIiC,KAAK,GAAGrD,MAAM,CAACuC,QAAP,CAAgBnB,GAAhB,IACRA,GADQ,GAER,IAAIpB,MAAJ,CAAWoB,GAAX,CAFJ;IAGA,IAAIoB,GAAG,GAAGa,KAAK,CAAChD,MAAhB;;IACA,KAAKoB,CAAC,GAAG,CAAT,EAAYA,CAAC,GAAGwC,GAAG,GAAGD,KAAtB,EAA6B,EAAEvC,CAA/B,EAAkC;MAChC,KAAKA,CAAC,GAAGuC,KAAT,IAAkBX,KAAK,CAAC5B,CAAC,GAAGe,GAAL,CAAvB;IACD;EACF;;EAED,OAAO,IAAP;AACD,CAjDD;;AAmDAxC,MAAM,CAAC2E,MAAP,GAAgB,SAASA,MAAT,CAAiBC,IAAjB,EAAuBvE,MAAvB,EAA+B;EAC7C,IAAI,CAACd,OAAO,CAACqF,IAAD,CAAZ,EAAoB;IAClB,MAAM,IAAI7B,SAAJ,CAAc,6CAAd,CAAN;EACD;;EAED,IAAI6B,IAAI,CAACvE,MAAL,KAAgB,CAApB,EAAuB;IACrB,OAAOgB,YAAY,CAAC,IAAD,EAAO,CAAP,CAAnB;EACD;;EAED,IAAII,CAAJ;;EACA,IAAIpB,MAAM,KAAK+B,SAAf,EAA0B;IACxB/B,MAAM,GAAG,CAAT;;IACA,KAAKoB,CAAC,GAAG,CAAT,EAAYA,CAAC,GAAGmD,IAAI,CAACvE,MAArB,EAA6B,EAAEoB,CAA/B,EAAkC;MAChCpB,MAAM,IAAIuE,IAAI,CAACnD,CAAD,CAAJ,CAAQpB,MAAlB;IACD;EACF;;EAED,IAAIsC,MAAM,GAAGrC,WAAW,CAAC,IAAD,EAAOD,MAAP,CAAxB;EACA,IAAIwE,GAAG,GAAG,CAAV;;EACA,KAAKpD,CAAC,GAAG,CAAT,EAAYA,CAAC,GAAGmD,IAAI,CAACvE,MAArB,EAA6B,EAAEoB,CAA/B,EAAkC;IAChC,IAAIF,GAAG,GAAGqD,IAAI,CAACnD,CAAD,CAAd;;IACA,IAAI,CAACzB,MAAM,CAACuC,QAAP,CAAgBhB,GAAhB,CAAL,EAA2B;MACzB,MAAM,IAAIwB,SAAJ,CAAc,6CAAd,CAAN;IACD;;IACDxB,GAAG,CAACkB,IAAJ,CAASE,MAAT,EAAiBkC,GAAjB;IACAA,GAAG,IAAItD,GAAG,CAAClB,MAAX;EACD;;EACD,OAAOsC,MAAP;AACD,CA5BD;;AA8BA3C,MAAM,CAAC4B,UAAP,GAAoBA,UAApB;AAEA5B,MAAM,CAACH,SAAP,CAAiBiF,SAAjB,GAA6B,IAA7B;;AACA9E,MAAM,CAACuC,QAAP,GAAkB,SAASA,QAAT,CAAmBwC,CAAnB,EAAsB;EACtC,OAAO,CAAC,EAAEA,CAAC,IAAI,IAAL,IAAaA,CAAC,CAACD,SAAjB,CAAR;AACD,CAFD;;AAIAE,MAAM,CAACC,OAAP,GAAiBjF,MAAjB"}, "metadata": {}, "sourceType": "script"}