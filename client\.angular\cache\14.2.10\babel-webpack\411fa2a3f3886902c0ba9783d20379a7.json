{"ast": null, "code": "import { factory } from '../../utils/factory.js';\nimport { createQuantileSeq } from '../../function/statistics/quantileSeq.js';\nimport { lastDimToZeroBase } from './utils/lastDimToZeroBase.js';\nvar name = 'quantileSeq';\nvar dependencies = ['typed', 'bignumber', 'add', 'subtract', 'divide', 'multiply', 'partitionSelect', 'compare', 'isInteger', 'smaller', 'smallerEq', 'larger', 'mapSlices'];\n/**\n * Attach a transform function to math.quantileSeq\n * Adds a property transform containing the transform function.\n *\n * This transform changed the `dim` parameter of function std\n * from one-based to zero based\n */\n\nexport var createQuantileSeqTransform = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed,\n    bignumber,\n    add,\n    subtract,\n    divide,\n    multiply,\n    partitionSelect,\n    compare,\n    isInteger,\n    smaller,\n    smallerEq,\n    larger,\n    mapSlices\n  } = _ref;\n  var quantileSeq = createQuantileSeq({\n    typed,\n    bignumber,\n    add,\n    subtract,\n    divide,\n    multiply,\n    partitionSelect,\n    compare,\n    isInteger,\n    smaller,\n    smallerEq,\n    larger,\n    mapSlices\n  });\n  return typed('quantileSeq', {\n    'Array | Matrix, number | BigNumber': quantileSeq,\n    'Array | Matrix, number | BigNumber, number': (arr, prob, dim) => quantileSeq(arr, prob, dimToZeroBase(dim)),\n    'Array | Matrix, number | BigNumber, boolean': quantileSeq,\n    'Array | Matrix, number | BigNumber, boolean, number': (arr, prob, sorted, dim) => quantileSeq(arr, prob, sorted, dimToZeroBase(dim)),\n    'Array | Matrix, Array | Matrix': quantileSeq,\n    'Array | Matrix, Array | Matrix, number': (data, prob, dim) => quantileSeq(data, prob, dimToZeroBase(dim)),\n    'Array | Matrix, Array | Matrix, boolean': quantileSeq,\n    'Array | Matrix, Array | Matrix, boolean, number': (data, prob, sorted, dim) => quantileSeq(data, prob, sorted, dimToZeroBase(dim))\n  });\n\n  function dimToZeroBase(dim) {\n    // TODO: find a better way, maybe lastDimToZeroBase could apply to more cases.\n    return lastDimToZeroBase([[], dim])[1];\n  }\n}, {\n  isTransformFunction: true\n});", "map": null, "metadata": {}, "sourceType": "module"}