{"ast": null, "code": "var canPromise = require('./can-promise');\n\nvar QRCode = require('./core/qrcode');\n\nvar CanvasRenderer = require('./renderer/canvas');\n\nvar SvgRenderer = require('./renderer/svg-tag.js');\n\nfunction renderCanvas(renderFunc, canvas, text, opts, cb) {\n  var args = [].slice.call(arguments, 1);\n  var argsNum = args.length;\n  var isLastArgCb = typeof args[argsNum - 1] === 'function';\n\n  if (!isLastArgCb && !canPromise()) {\n    throw new Error('Callback required as last argument');\n  }\n\n  if (isLastArgCb) {\n    if (argsNum < 2) {\n      throw new Error('Too few arguments provided');\n    }\n\n    if (argsNum === 2) {\n      cb = text;\n      text = canvas;\n      canvas = opts = undefined;\n    } else if (argsNum === 3) {\n      if (canvas.getContext && typeof cb === 'undefined') {\n        cb = opts;\n        opts = undefined;\n      } else {\n        cb = opts;\n        opts = text;\n        text = canvas;\n        canvas = undefined;\n      }\n    }\n  } else {\n    if (argsNum < 1) {\n      throw new Error('Too few arguments provided');\n    }\n\n    if (argsNum === 1) {\n      text = canvas;\n      canvas = opts = undefined;\n    } else if (argsNum === 2 && !canvas.getContext) {\n      opts = text;\n      text = canvas;\n      canvas = undefined;\n    }\n\n    return new Promise(function (resolve, reject) {\n      try {\n        var data = QRCode.create(text, opts);\n        resolve(renderFunc(data, canvas, opts));\n      } catch (e) {\n        reject(e);\n      }\n    });\n  }\n\n  try {\n    var data = QRCode.create(text, opts);\n    cb(null, renderFunc(data, canvas, opts));\n  } catch (e) {\n    cb(e);\n  }\n}\n\nexports.create = QRCode.create;\nexports.toCanvas = renderCanvas.bind(null, CanvasRenderer.render);\nexports.toDataURL = renderCanvas.bind(null, CanvasRenderer.renderToDataURL); // only svg for now.\n\nexports.toString = renderCanvas.bind(null, function (data, _, opts) {\n  return SvgRenderer.render(data, opts);\n});", "map": null, "metadata": {}, "sourceType": "script"}