{"ast": null, "code": "import _asyncToGenerator from \"D:/work/joyserver/client/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { NgZone } from \"@angular/core\";\nimport { firstValueFrom, Subject } from \"rxjs\";\nimport { catchError, timeout, switchMap } from \"rxjs/operators\";\nimport { DOCUMENT } from \"@angular/common\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"../../service/url.service\";\nimport * as i3 from \"@core/service/utils/util.service\";\nexport class SkinService {\n  constructor(document, http, zone, urlService, utilService) {\n    this.document = document;\n    this.http = http;\n    this.zone = zone;\n    this.urlService = urlService;\n    this.utilService = utilService;\n    this.customJsLoaded = new Subject(); //  自定义js加载完成\n\n    this.skinLoaded = false; //  skin 执行完毕\n\n    this.curSkin = {};\n    this.registerTagName = new Set(); // 注册的页面名称\n\n    this.appliedTagName = new Set(); // 已经应用的页面名称\n  }\n\n  getSkin(session_id) {\n    var _this = this;\n\n    return _asyncToGenerator(function* () {\n      const serverUrl = _this.urlService.getServerUrl();\n\n      const URL = `${serverUrl}/seat/css/${session_id}`;\n      return firstValueFrom(_this.http.get(URL).pipe(timeout(5 * 60 * 1000), // 5 minutes\n      catchError(_this.utilService.commonHandleError(\"getSkin\", [])), switchMap(skinContent => _this.setSkinContent(skinContent))));\n    })();\n  }\n\n  setSkinContent(skinContent) {\n    var _this2 = this;\n\n    return _asyncToGenerator(function* () {\n      if (skinContent.index_html && skinContent.name !== _this2.cookie(\"s\")) {\n        // app变化，切换到该app\n        console.log(\"skin: switch app to\", skinContent.name);\n        document.cookie = \"s=\" + skinContent.name + \"; path=/\";\n\n        _this2.reload();\n\n        return;\n      }\n\n      if (!window.is_online && !skinContent.index_html && _this2.cookie(\"s\") !== \"default\") {\n        // 切换到默认app(#OnlineExam：在线考试不需要)\n        console.log(\"skin: default app + \", skinContent.name);\n        document.cookie = \"s=default; path=/\";\n\n        _this2.reload();\n\n        return;\n      }\n\n      if (_this2.skinLoaded) {\n        // skin变化\n        if (_this2.curSkin.md5 !== skinContent.md5) {\n          console.log(\"skin: file changed! \", _this2.curSkin.md5, skinContent.md5);\n\n          _this2.reload();\n        }\n\n        return;\n      }\n\n      _this2.skinLoaded = true;\n      _this2.curSkin = skinContent;\n      window[\"VERSION\"] = _this2.curSkin.md5; // set for 自定义html引用的js\n\n      _this2.cycleAddStyle();\n\n      return _this2.addJsFile();\n    })();\n  }\n\n  registerStyle(tagName) {\n    for (const tag in this.curSkin) {\n      if (tag === tagName) {\n        this.addStyleSheet(tag, this.curSkin[tag]);\n        return true;\n      }\n    }\n\n    this.registerTagName.add(tagName);\n    return false;\n  }\n\n  cancelStyle(tagName) {\n    if (this.registerTagName.has(tagName)) {\n      this.registerTagName.delete(tagName);\n    } else {\n      this.removeStyleSheet(tagName);\n    }\n  }\n\n  cycleAddStyle() {\n    const globalTag = \"global\";\n\n    if (!this.curSkin[globalTag]) {\n      this.registerTagName.add(globalTag);\n    }\n\n    this.appliedTagName.forEach(tag => {\n      this.cancelStyle(tag);\n      this.registerStyle(tag);\n    });\n    this.registerTagName.forEach(tag => {\n      if (this.registerStyle(tag)) {\n        this.registerTagName.delete(tag);\n        this.appliedTagName.add(tag);\n      }\n    });\n  }\n\n  addJsFile() {\n    var _this3 = this;\n\n    return _asyncToGenerator(function* () {\n      const customjs = _this3.curSkin.customjs;\n\n      if (!customjs) {\n        window[\"customJs\"] = false;\n        return;\n      }\n\n      window[\"customJs\"] = true;\n      console.log(`* Skin has custom js: ${_this3.curSkin.customjs}`);\n      const script = document.createElement(\"script\");\n      script.type = \"text/javascript\";\n      script.src = `/seat/skin/${_this3.curSkin.name}/${customjs}?_version=` + _this3.curSkin.md5;\n      document.getElementsByTagName(\"head\")[0].appendChild(script);\n      return new Promise((resolve, reject) => {\n        script.onload = () => {\n          _this3.zone.run(() => {\n            _this3.customJsLoaded.next();\n          });\n\n          resolve(null);\n        };\n\n        script.onerror = err => {\n          reject();\n        };\n      });\n    })();\n  } // 清除所有已注册的skin\n\n\n  clearAppliedStyle() {\n    this.appliedTagName.forEach(tag => {\n      this.removeStyleSheet(tag);\n    });\n    this.appliedTagName.clear();\n  } // dom移除style\n\n\n  removeStyleSheet(tagName) {\n    const style = this.document.getElementById(tagName);\n\n    if (style) {\n      this.document.head.removeChild(style);\n    }\n  } // dom添加style\n\n\n  addStyleSheet(tagName, styleSheet) {\n    const head = this.document.head || this.document.getElementsByTagName(\"head\")[0];\n    const style = this.document.createElement(\"style\");\n    style.innerHTML = styleSheet;\n    style.type = \"text/css\";\n    style.id = tagName;\n    head.appendChild(style);\n  }\n\n  cookie(key) {\n    const m = new RegExp(\"(?:^|; )\" + encodeURIComponent(key) + \"=([^;]*)\").exec(document.cookie);\n    return m ? m[1] : null;\n  }\n\n  delete_cookie(name) {\n    document.cookie = name + \"=; Path=/; Expires=Thu, 01 Jan 1970 00:00:01 GMT;\";\n  }\n\n  reload() {\n    console.log(\"skin: reload\");\n\n    if (joyshell) {\n      // 强制刷新\n      joyshell.Reload();\n      return;\n    }\n\n    window.location.reload();\n  }\n\n}\n\nSkinService.ɵfac = function SkinService_Factory(t) {\n  return new (t || SkinService)(i0.ɵɵinject(DOCUMENT), i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i0.NgZone), i0.ɵɵinject(i2.URLService), i0.ɵɵinject(i3.UtilService));\n};\n\nSkinService.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n  token: SkinService,\n  factory: SkinService.ɵfac,\n  providedIn: \"root\"\n});", "map": {"version": 3, "mappings": ";AAAA,SAA6BA,MAA7B,QAA2C,eAA3C;AACA,SAASC,cAAT,EAAyBC,OAAzB,QAAwC,MAAxC;AACA,SAAcC,UAAd,EAA0BC,OAA1B,EAAmCC,SAAnC,QAAoD,gBAApD;AAGA,SAASC,QAAT,QAAyB,iBAAzB;;;;;AAaA,OAAM,MAAOC,WAAP,CAAkB;EAOtBC,YAC4BC,QAD5B,EAEUC,IAFV,EAGUC,IAHV,EAIUC,UAJV,EAKUC,WALV,EAKkC;IAJN;IAClB;IACA;IACA;IACA;IAXH,sBAAiB,IAAIX,OAAJ,EAAjB,CAW2B,CAXW;;IACtC,kBAAa,KAAb,CAU2B,CAVP;;IACpB,eAAuB,EAAvB;IACC,uBAAkB,IAAIY,GAAJ,EAAlB,CAQ0B,CARW;;IACrC,sBAAiB,IAAIA,GAAJ,EAAjB,CAO0B,CAPU;EAQxC;;EAESC,OAAO,CAACC,UAAD,EAAmB;IAAA;;IAAA;MACrC,MAAMC,SAAS,GAAG,KAAI,CAACL,UAAL,CAAgBM,YAAhB,EAAlB;;MACA,MAAMC,GAAG,GAAG,GAAGF,SAAS,aAAaD,UAAU,EAA/C;MACA,OAAOf,cAAc,CACnB,KAAI,CAACS,IAAL,CAAUU,GAAV,CAAcD,GAAd,EAAmBE,IAAnB,CACEjB,OAAO,CAAC,IAAI,EAAJ,GAAS,IAAV,CADT,EAC0B;MACxBD,UAAU,CAAC,KAAI,CAACU,WAAL,CAAiBS,iBAAjB,CAAwC,SAAxC,EAAmD,EAAnD,CAAD,CAFZ,EAGEjB,SAAS,CAAEkB,WAAD,IAA8B,KAAI,CAACC,cAAL,CAAoBD,WAApB,CAA/B,CAHX,CADmB,CAArB;IAHqC;EAUtC;;EAEYC,cAAc,CAACD,WAAD,EAAyB;IAAA;;IAAA;MAClD,IAAIA,WAAW,CAACE,UAAZ,IAA0BF,WAAW,CAACG,IAAZ,KAAqB,MAAI,CAACC,MAAL,CAAY,GAAZ,CAAnD,EAAqE;QACnE;QACAC,OAAO,CAACC,GAAR,CAAY,qBAAZ,EAAmCN,WAAW,CAACG,IAA/C;QACAjB,QAAQ,CAACkB,MAAT,GAAkB,OAAOJ,WAAW,CAACG,IAAnB,GAA0B,UAA5C;;QACA,MAAI,CAACI,MAAL;;QACA;MACD;;MAED,IAAI,CAACC,MAAM,CAACC,SAAR,IAAqB,CAACT,WAAW,CAACE,UAAlC,IAAgD,MAAI,CAACE,MAAL,CAAY,GAAZ,MAAqB,SAAzE,EAAoF;QAClF;QACAC,OAAO,CAACC,GAAR,CAAY,sBAAZ,EAAoCN,WAAW,CAACG,IAAhD;QACAjB,QAAQ,CAACkB,MAAT,GAAkB,mBAAlB;;QACA,MAAI,CAACG,MAAL;;QACA;MACD;;MAED,IAAI,MAAI,CAACG,UAAT,EAAqB;QACnB;QACA,IAAI,MAAI,CAACC,OAAL,CAAaC,GAAb,KAAqBZ,WAAW,CAACY,GAArC,EAA0C;UACxCP,OAAO,CAACC,GAAR,CAAY,sBAAZ,EAAoC,MAAI,CAACK,OAAL,CAAaC,GAAjD,EAAsDZ,WAAW,CAACY,GAAlE;;UACA,MAAI,CAACL,MAAL;QACD;;QACD;MACD;;MAED,MAAI,CAACG,UAAL,GAAkB,IAAlB;MACA,MAAI,CAACC,OAAL,GAAeX,WAAf;MACAQ,MAAM,CAAC,SAAD,CAAN,GAAoB,MAAI,CAACG,OAAL,CAAaC,GAAjC,CA5BkD,CA4BZ;;MACtC,MAAI,CAACC,aAAL;;MACA,OAAO,MAAI,CAACC,SAAL,EAAP;IA9BkD;EA+BnD;;EAEMC,aAAa,CAACC,OAAD,EAAgB;IAClC,KAAK,MAAMC,GAAX,IAAkB,KAAKN,OAAvB,EAAgC;MAC9B,IAAIM,GAAG,KAAKD,OAAZ,EAAqB;QACnB,KAAKE,aAAL,CAAmBD,GAAnB,EAAwB,KAAKN,OAAL,CAAaM,GAAb,CAAxB;QACA,OAAO,IAAP;MACD;IACF;;IACD,KAAKE,eAAL,CAAqBC,GAArB,CAAyBJ,OAAzB;IACA,OAAO,KAAP;EACD;;EAEMK,WAAW,CAACL,OAAD,EAAgB;IAChC,IAAI,KAAKG,eAAL,CAAqBG,GAArB,CAAyBN,OAAzB,CAAJ,EAAuC;MACrC,KAAKG,eAAL,CAAqBI,MAArB,CAA4BP,OAA5B;IACD,CAFD,MAEO;MACL,KAAKQ,gBAAL,CAAsBR,OAAtB;IACD;EACF;;EAEOH,aAAa;IACnB,MAAMY,SAAS,GAAG,QAAlB;;IACA,IAAI,CAAC,KAAKd,OAAL,CAAac,SAAb,CAAL,EAA8B;MAC5B,KAAKN,eAAL,CAAqBC,GAArB,CAAyBK,SAAzB;IACD;;IACD,KAAKC,cAAL,CAAoBC,OAApB,CAA6BV,GAAD,IAAQ;MAClC,KAAKI,WAAL,CAAiBJ,GAAjB;MACA,KAAKF,aAAL,CAAmBE,GAAnB;IACD,CAHD;IAIA,KAAKE,eAAL,CAAqBQ,OAArB,CAA8BV,GAAD,IAAQ;MACnC,IAAI,KAAKF,aAAL,CAAmBE,GAAnB,CAAJ,EAA6B;QAC3B,KAAKE,eAAL,CAAqBI,MAArB,CAA4BN,GAA5B;QACA,KAAKS,cAAL,CAAoBN,GAApB,CAAwBH,GAAxB;MACD;IACF,CALD;EAMD;;EAEaH,SAAS;IAAA;;IAAA;MACrB,MAAMc,QAAQ,GAAG,MAAI,CAACjB,OAAL,CAAaiB,QAA9B;;MACA,IAAI,CAACA,QAAL,EAAe;QACbpB,MAAM,CAAC,UAAD,CAAN,GAAqB,KAArB;QACA;MACD;;MACDA,MAAM,CAAC,UAAD,CAAN,GAAqB,IAArB;MACAH,OAAO,CAACC,GAAR,CAAY,yBAAyB,MAAI,CAACK,OAAL,CAAaiB,QAAQ,EAA1D;MACA,MAAMC,MAAM,GAAG3C,QAAQ,CAAC4C,aAAT,CAAuB,QAAvB,CAAf;MACAD,MAAM,CAACE,IAAP,GAAc,iBAAd;MACAF,MAAM,CAACG,GAAP,GAAa,cAAc,MAAI,CAACrB,OAAL,CAAaR,IAAI,IAAIyB,QAAQ,YAA3C,GAA0D,MAAI,CAACjB,OAAL,CAAaC,GAApF;MACA1B,QAAQ,CAAC+C,oBAAT,CAA8B,MAA9B,EAAsC,CAAtC,EAAyCC,WAAzC,CAAqDL,MAArD;MACA,OAAO,IAAIM,OAAJ,CAAY,CAACC,OAAD,EAAUC,MAAV,KAAoB;QACrCR,MAAM,CAACS,MAAP,GAAgB,MAAK;UACnB,MAAI,CAAClD,IAAL,CAAUmD,GAAV,CAAc,MAAK;YACjB,MAAI,CAACC,cAAL,CAAoBC,IAApB;UACD,CAFD;;UAGAL,OAAO,CAAC,IAAD,CAAP;QACD,CALD;;QAMAP,MAAM,CAACa,OAAP,GAAkBC,GAAD,IAAQ;UACvBN,MAAM;QACP,CAFD;MAGD,CAVM,CAAP;IAZqB;EAuBtB,CAvHqB,CAyHtB;;;EACQO,iBAAiB;IACvB,KAAKlB,cAAL,CAAoBC,OAApB,CAA6BV,GAAD,IAAQ;MAClC,KAAKO,gBAAL,CAAsBP,GAAtB;IACD,CAFD;IAGA,KAAKS,cAAL,CAAoBmB,KAApB;EACD,CA/HqB,CAiItB;;;EACQrB,gBAAgB,CAACR,OAAD,EAAgB;IACtC,MAAM8B,KAAK,GAAG,KAAK5D,QAAL,CAAc6D,cAAd,CAA6B/B,OAA7B,CAAd;;IACA,IAAI8B,KAAJ,EAAW;MACT,KAAK5D,QAAL,CAAc8D,IAAd,CAAmBC,WAAnB,CAA+BH,KAA/B;IACD;EACF,CAvIqB,CAyItB;;;EACQ5B,aAAa,CAACF,OAAD,EAAkBkC,UAAlB,EAAoC;IACvD,MAAMF,IAAI,GAAG,KAAK9D,QAAL,CAAc8D,IAAd,IAAsB,KAAK9D,QAAL,CAAc+C,oBAAd,CAAmC,MAAnC,EAA2C,CAA3C,CAAnC;IACA,MAAMa,KAAK,GAAG,KAAK5D,QAAL,CAAc4C,aAAd,CAA4B,OAA5B,CAAd;IACAgB,KAAK,CAACK,SAAN,GAAkBD,UAAlB;IACAJ,KAAK,CAACf,IAAN,GAAa,UAAb;IACAe,KAAK,CAACM,EAAN,GAAWpC,OAAX;IACAgC,IAAI,CAACd,WAAL,CAAiBY,KAAjB;EACD;;EAEO1C,MAAM,CAACiD,GAAD,EAAY;IACxB,MAAMC,CAAC,GAAG,IAAIC,MAAJ,CAAW,aAAaC,kBAAkB,CAACH,GAAD,CAA/B,GAAuC,UAAlD,EAA8DI,IAA9D,CAAmEvE,QAAQ,CAACkB,MAA5E,CAAV;IACA,OAAOkD,CAAC,GAAGA,CAAC,CAAC,CAAD,CAAJ,GAAU,IAAlB;EACD;;EACOI,aAAa,CAACvD,IAAD,EAAa;IAChCjB,QAAQ,CAACkB,MAAT,GAAkBD,IAAI,GAAG,mDAAzB;EACD;;EAEOI,MAAM;IACZF,OAAO,CAACC,GAAR,CAAY,cAAZ;;IACA,IAAIqD,QAAJ,EAAc;MACZ;MACAA,QAAQ,CAACC,MAAT;MACA;IACD;;IACDpD,MAAM,CAACqD,QAAP,CAAgBtD,MAAhB;EACD;;AAnKqB;;;mBAAXvB,aAAW8E,YAQZ/E,QARY,GAQJ+E;AAAA;;;SARP9E;EAAW+E,SAAX/E,WAAW;EAAAgF,YADE", "names": ["NgZone", "firstValueFrom", "Subject", "catchError", "timeout", "switchMap", "DOCUMENT", "SkinService", "constructor", "document", "http", "zone", "urlService", "utilService", "Set", "getSkin", "session_id", "serverUrl", "getServerUrl", "URL", "get", "pipe", "commonHandleError", "skinContent", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "index_html", "name", "cookie", "console", "log", "reload", "window", "is_online", "skinLoaded", "cur<PERSON>kin", "md5", "cycleAddStyle", "addJsFile", "registerStyle", "tagName", "tag", "addStyleSheet", "registerTagName", "add", "cancelStyle", "has", "delete", "removeStyleSheet", "globalTag", "appliedTagName", "for<PERSON>ach", "customjs", "script", "createElement", "type", "src", "getElementsByTagName", "append<PERSON><PERSON><PERSON>", "Promise", "resolve", "reject", "onload", "run", "customJsLoaded", "next", "onerror", "err", "clearAppliedStyle", "clear", "style", "getElementById", "head", "<PERSON><PERSON><PERSON><PERSON>", "styleSheet", "innerHTML", "id", "key", "m", "RegExp", "encodeURIComponent", "exec", "delete_cookie", "joyshell", "Reload", "location", "i0", "factory", "providedIn"], "sourceRoot": "", "sources": ["D:\\work\\joyserver\\client\\src\\app\\core\\directive\\skin\\skin.service.ts"], "sourcesContent": ["import { Inject, Injectable, NgZone } from \"@angular/core\";\nimport { firstValueFrom, Subject } from \"rxjs\";\nimport { map, catchError, timeout, switchMap } from \"rxjs/operators\";\nimport { HttpClient } from \"@angular/common/http\";\nimport { URLService } from \"../../service/url.service\";\nimport { DOCUMENT } from \"@angular/common\";\nimport { UtilService } from \"@core/service/utils/util.service\";\n\ninterface SkinContent {\n  name?: string;\n  dir?: string; // skin文件存放路径\n  md5?: string; // skin文件md5码\n  index_html?: string;\n  [tagName: string]: string;\n  customjs?: string;\n}\n\n@Injectable({ providedIn: \"root\" })\nexport class SkinService {\n  public customJsLoaded = new Subject<void>(); //  自定义js加载完成\n  public skinLoaded = false; //  skin 执行完毕\n  public curSkin: SkinContent = {};\n  private registerTagName = new Set<string>(); // 注册的页面名称\n  private appliedTagName = new Set<string>(); // 已经应用的页面名称\n\n  constructor(\n    @Inject(DOCUMENT) private document: Document,\n    private http: HttpClient,\n    private zone: NgZone,\n    private urlService: URLService,\n    private utilService: UtilService\n  ) {}\n\n  public async getSkin(session_id: string) {\n    const serverUrl = this.urlService.getServerUrl();\n    const URL = `${serverUrl}/seat/css/${session_id}`;\n    return firstValueFrom(\n      this.http.get(URL).pipe(\n        timeout(5 * 60 * 1000), // 5 minutes\n        catchError(this.utilService.commonHandleError<any>(\"getSkin\", [])),\n        switchMap((skinContent: SkinContent) => this.setSkinContent(skinContent))\n      )\n    );\n  }\n\n  public async setSkinContent(skinContent: SkinContent) {\n    if (skinContent.index_html && skinContent.name !== this.cookie(\"s\")) {\n      // app变化，切换到该app\n      console.log(\"skin: switch app to\", skinContent.name);\n      document.cookie = \"s=\" + skinContent.name + \"; path=/\";\n      this.reload();\n      return;\n    }\n\n    if (!window.is_online && !skinContent.index_html && this.cookie(\"s\") !== \"default\") {\n      // 切换到默认app(#OnlineExam：在线考试不需要)\n      console.log(\"skin: default app + \", skinContent.name);\n      document.cookie = \"s=default; path=/\";\n      this.reload();\n      return;\n    }\n\n    if (this.skinLoaded) {\n      // skin变化\n      if (this.curSkin.md5 !== skinContent.md5) {\n        console.log(\"skin: file changed! \", this.curSkin.md5, skinContent.md5);\n        this.reload();\n      }\n      return;\n    }\n\n    this.skinLoaded = true;\n    this.curSkin = skinContent;\n    window[\"VERSION\"] = this.curSkin.md5; // set for 自定义html引用的js\n    this.cycleAddStyle();\n    return this.addJsFile();\n  }\n\n  public registerStyle(tagName: string): boolean {\n    for (const tag in this.curSkin) {\n      if (tag === tagName) {\n        this.addStyleSheet(tag, this.curSkin[tag]);\n        return true;\n      }\n    }\n    this.registerTagName.add(tagName);\n    return false;\n  }\n\n  public cancelStyle(tagName: string) {\n    if (this.registerTagName.has(tagName)) {\n      this.registerTagName.delete(tagName);\n    } else {\n      this.removeStyleSheet(tagName);\n    }\n  }\n\n  private cycleAddStyle(): void {\n    const globalTag = \"global\";\n    if (!this.curSkin[globalTag]) {\n      this.registerTagName.add(globalTag);\n    }\n    this.appliedTagName.forEach((tag) => {\n      this.cancelStyle(tag);\n      this.registerStyle(tag);\n    });\n    this.registerTagName.forEach((tag) => {\n      if (this.registerStyle(tag)) {\n        this.registerTagName.delete(tag);\n        this.appliedTagName.add(tag);\n      }\n    });\n  }\n\n  private async addJsFile(): Promise<any> {\n    const customjs = this.curSkin.customjs;\n    if (!customjs) {\n      window[\"customJs\"] = false;\n      return;\n    }\n    window[\"customJs\"] = true;\n    console.log(`* Skin has custom js: ${this.curSkin.customjs}`);\n    const script = document.createElement(\"script\");\n    script.type = \"text/javascript\";\n    script.src = `/seat/skin/${this.curSkin.name}/${customjs}?_version=` + this.curSkin.md5;\n    document.getElementsByTagName(\"head\")[0].appendChild(script);\n    return new Promise((resolve, reject) => {\n      script.onload = () => {\n        this.zone.run(() => {\n          this.customJsLoaded.next();\n        });\n        resolve(null);\n      };\n      script.onerror = (err) => {\n        reject();\n      };\n    });\n  }\n\n  // 清除所有已注册的skin\n  private clearAppliedStyle() {\n    this.appliedTagName.forEach((tag) => {\n      this.removeStyleSheet(tag);\n    });\n    this.appliedTagName.clear();\n  }\n\n  // dom移除style\n  private removeStyleSheet(tagName: string): void {\n    const style = this.document.getElementById(tagName);\n    if (style) {\n      this.document.head.removeChild(style);\n    }\n  }\n\n  // dom添加style\n  private addStyleSheet(tagName: string, styleSheet: string): void {\n    const head = this.document.head || this.document.getElementsByTagName(\"head\")[0];\n    const style = this.document.createElement(\"style\");\n    style.innerHTML = styleSheet;\n    style.type = \"text/css\";\n    style.id = tagName;\n    head.appendChild(style);\n  }\n\n  private cookie(key: string): string {\n    const m = new RegExp(\"(?:^|; )\" + encodeURIComponent(key) + \"=([^;]*)\").exec(document.cookie);\n    return m ? m[1] : null;\n  }\n  private delete_cookie(name: string) {\n    document.cookie = name + \"=; Path=/; Expires=Thu, 01 Jan 1970 00:00:01 GMT;\";\n  }\n\n  private reload() {\n    console.log(\"skin: reload\");\n    if (joyshell) {\n      // 强制刷新\n      joyshell.Reload();\n      return;\n    }\n    window.location.reload();\n  }\n}\n"]}, "metadata": {}, "sourceType": "module"}