{"ast": null, "code": "import { Subscriber } from '../Subscriber';\nexport function distinctUntilChanged(compare, keySelector) {\n  return source => source.lift(new DistinctUntilChangedOperator(compare, keySelector));\n}\n\nclass DistinctUntilChangedOperator {\n  constructor(compare, keySelector) {\n    this.compare = compare;\n    this.keySelector = keySelector;\n  }\n\n  call(subscriber, source) {\n    return source.subscribe(new DistinctUntilChangedSubscriber(subscriber, this.compare, this.keySelector));\n  }\n\n}\n\nclass DistinctUntilChangedSubscriber extends Subscriber {\n  constructor(destination, compare, keySelector) {\n    super(destination);\n    this.keySelector = keySelector;\n    this.hasKey = false;\n\n    if (typeof compare === 'function') {\n      this.compare = compare;\n    }\n  }\n\n  compare(x, y) {\n    return x === y;\n  }\n\n  _next(value) {\n    let key;\n\n    try {\n      const {\n        keySelector\n      } = this;\n      key = keySelector ? keySelector(value) : value;\n    } catch (err) {\n      return this.destination.error(err);\n    }\n\n    let result = false;\n\n    if (this.hasKey) {\n      try {\n        const {\n          compare\n        } = this;\n        result = compare(this.key, key);\n      } catch (err) {\n        return this.destination.error(err);\n      }\n    } else {\n      this.hasKey = true;\n    }\n\n    if (!result) {\n      this.key = key;\n      this.destination.next(value);\n    }\n  }\n\n} //# sourceMappingURL=distinctUntilChanged.js.map", "map": null, "metadata": {}, "sourceType": "module"}