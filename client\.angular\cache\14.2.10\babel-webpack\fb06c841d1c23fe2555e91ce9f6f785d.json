{"ast": null, "code": "import { isElement, isHTMLElement } from \"./instanceOf.js\";\nimport { round } from \"../utils/math.js\";\nimport getWindow from \"./getWindow.js\";\nimport isLayoutViewport from \"./isLayoutViewport.js\";\nexport default function getBoundingClientRect(element, includeScale, isFixedStrategy) {\n  if (includeScale === void 0) {\n    includeScale = false;\n  }\n\n  if (isFixedStrategy === void 0) {\n    isFixedStrategy = false;\n  }\n\n  var clientRect = element.getBoundingClientRect();\n  var scaleX = 1;\n  var scaleY = 1;\n\n  if (includeScale && isHTMLElement(element)) {\n    scaleX = element.offsetWidth > 0 ? round(clientRect.width) / element.offsetWidth || 1 : 1;\n    scaleY = element.offsetHeight > 0 ? round(clientRect.height) / element.offsetHeight || 1 : 1;\n  }\n\n  var _ref = isElement(element) ? getWindow(element) : window,\n      visualViewport = _ref.visualViewport;\n\n  var addVisualOffsets = !isLayoutViewport() && isFixedStrategy;\n  var x = (clientRect.left + (addVisualOffsets && visualViewport ? visualViewport.offsetLeft : 0)) / scaleX;\n  var y = (clientRect.top + (addVisualOffsets && visualViewport ? visualViewport.offsetTop : 0)) / scaleY;\n  var width = clientRect.width / scaleX;\n  var height = clientRect.height / scaleY;\n  return {\n    width: width,\n    height: height,\n    top: y,\n    right: x + width,\n    bottom: y + height,\n    left: x,\n    x: x,\n    y: y\n  };\n}", "map": {"version": 3, "names": ["isElement", "isHTMLElement", "round", "getWindow", "isLayoutViewport", "getBoundingClientRect", "element", "includeScale", "isFixedStrategy", "clientRect", "scaleX", "scaleY", "offsetWidth", "width", "offsetHeight", "height", "_ref", "window", "visualViewport", "addVisualOffsets", "x", "left", "offsetLeft", "y", "top", "offsetTop", "right", "bottom"], "sources": ["D:/work/joyserver/client/node_modules/@popperjs/core/lib/dom-utils/getBoundingClientRect.js"], "sourcesContent": ["import { isElement, isHTMLElement } from \"./instanceOf.js\";\nimport { round } from \"../utils/math.js\";\nimport getWindow from \"./getWindow.js\";\nimport isLayoutViewport from \"./isLayoutViewport.js\";\nexport default function getBoundingClientRect(element, includeScale, isFixedStrategy) {\n  if (includeScale === void 0) {\n    includeScale = false;\n  }\n\n  if (isFixedStrategy === void 0) {\n    isFixedStrategy = false;\n  }\n\n  var clientRect = element.getBoundingClientRect();\n  var scaleX = 1;\n  var scaleY = 1;\n\n  if (includeScale && isHTMLElement(element)) {\n    scaleX = element.offsetWidth > 0 ? round(clientRect.width) / element.offsetWidth || 1 : 1;\n    scaleY = element.offsetHeight > 0 ? round(clientRect.height) / element.offsetHeight || 1 : 1;\n  }\n\n  var _ref = isElement(element) ? getWindow(element) : window,\n      visualViewport = _ref.visualViewport;\n\n  var addVisualOffsets = !isLayoutViewport() && isFixedStrategy;\n  var x = (clientRect.left + (addVisualOffsets && visualViewport ? visualViewport.offsetLeft : 0)) / scaleX;\n  var y = (clientRect.top + (addVisualOffsets && visualViewport ? visualViewport.offsetTop : 0)) / scaleY;\n  var width = clientRect.width / scaleX;\n  var height = clientRect.height / scaleY;\n  return {\n    width: width,\n    height: height,\n    top: y,\n    right: x + width,\n    bottom: y + height,\n    left: x,\n    x: x,\n    y: y\n  };\n}"], "mappings": "AAAA,SAASA,SAAT,EAAoBC,aAApB,QAAyC,iBAAzC;AACA,SAASC,KAAT,QAAsB,kBAAtB;AACA,OAAOC,SAAP,MAAsB,gBAAtB;AACA,OAAOC,gBAAP,MAA6B,uBAA7B;AACA,eAAe,SAASC,qBAAT,CAA+BC,OAA/B,EAAwCC,YAAxC,EAAsDC,eAAtD,EAAuE;EACpF,IAAID,YAAY,KAAK,KAAK,CAA1B,EAA6B;IAC3BA,YAAY,GAAG,KAAf;EACD;;EAED,IAAIC,eAAe,KAAK,KAAK,CAA7B,EAAgC;IAC9BA,eAAe,GAAG,KAAlB;EACD;;EAED,IAAIC,UAAU,GAAGH,OAAO,CAACD,qBAAR,EAAjB;EACA,IAAIK,MAAM,GAAG,CAAb;EACA,IAAIC,MAAM,GAAG,CAAb;;EAEA,IAAIJ,YAAY,IAAIN,aAAa,CAACK,OAAD,CAAjC,EAA4C;IAC1CI,MAAM,GAAGJ,OAAO,CAACM,WAAR,GAAsB,CAAtB,GAA0BV,KAAK,CAACO,UAAU,CAACI,KAAZ,CAAL,GAA0BP,OAAO,CAACM,WAAlC,IAAiD,CAA3E,GAA+E,CAAxF;IACAD,MAAM,GAAGL,OAAO,CAACQ,YAAR,GAAuB,CAAvB,GAA2BZ,KAAK,CAACO,UAAU,CAACM,MAAZ,CAAL,GAA2BT,OAAO,CAACQ,YAAnC,IAAmD,CAA9E,GAAkF,CAA3F;EACD;;EAED,IAAIE,IAAI,GAAGhB,SAAS,CAACM,OAAD,CAAT,GAAqBH,SAAS,CAACG,OAAD,CAA9B,GAA0CW,MAArD;EAAA,IACIC,cAAc,GAAGF,IAAI,CAACE,cAD1B;;EAGA,IAAIC,gBAAgB,GAAG,CAACf,gBAAgB,EAAjB,IAAuBI,eAA9C;EACA,IAAIY,CAAC,GAAG,CAACX,UAAU,CAACY,IAAX,IAAmBF,gBAAgB,IAAID,cAApB,GAAqCA,cAAc,CAACI,UAApD,GAAiE,CAApF,CAAD,IAA2FZ,MAAnG;EACA,IAAIa,CAAC,GAAG,CAACd,UAAU,CAACe,GAAX,IAAkBL,gBAAgB,IAAID,cAApB,GAAqCA,cAAc,CAACO,SAApD,GAAgE,CAAlF,CAAD,IAAyFd,MAAjG;EACA,IAAIE,KAAK,GAAGJ,UAAU,CAACI,KAAX,GAAmBH,MAA/B;EACA,IAAIK,MAAM,GAAGN,UAAU,CAACM,MAAX,GAAoBJ,MAAjC;EACA,OAAO;IACLE,KAAK,EAAEA,KADF;IAELE,MAAM,EAAEA,MAFH;IAGLS,GAAG,EAAED,CAHA;IAILG,KAAK,EAAEN,CAAC,GAAGP,KAJN;IAKLc,MAAM,EAAEJ,CAAC,GAAGR,MALP;IAMLM,IAAI,EAAED,CAND;IAOLA,CAAC,EAAEA,CAPE;IAQLG,CAAC,EAAEA;EARE,CAAP;AAUD"}, "metadata": {}, "sourceType": "module"}