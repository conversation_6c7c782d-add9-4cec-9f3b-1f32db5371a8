{"ast": null, "code": "import getNodeName from \"./getNodeName.js\";\nimport getDocumentElement from \"./getDocumentElement.js\";\nimport { isShadowRoot } from \"./instanceOf.js\";\nexport default function getParentNode(element) {\n  if (getNodeName(element) === 'html') {\n    return element;\n  }\n\n  return (// this is a quicker (but less type safe) way to save quite some bytes from the bundle\n    // $FlowFixMe[incompatible-return]\n    // $FlowFixMe[prop-missing]\n    element.assignedSlot || // step into the shadow DOM of the parent of a slotted node\n    element.parentNode || ( // DOM Element detected\n    isShadowRoot(element) ? element.host : null) || // ShadowRoot detected\n    // $FlowFixMe[incompatible-call]: HTMLElement is a Node\n    getDocumentElement(element) // fallback\n\n  );\n}", "map": {"version": 3, "names": ["getNodeName", "getDocumentElement", "isShadowRoot", "getParentNode", "element", "assignedSlot", "parentNode", "host"], "sources": ["D:/work/joyserver/client/node_modules/@popperjs/core/lib/dom-utils/getParentNode.js"], "sourcesContent": ["import getNodeName from \"./getNodeName.js\";\nimport getDocumentElement from \"./getDocumentElement.js\";\nimport { isShadowRoot } from \"./instanceOf.js\";\nexport default function getParentNode(element) {\n  if (getNodeName(element) === 'html') {\n    return element;\n  }\n\n  return (// this is a quicker (but less type safe) way to save quite some bytes from the bundle\n    // $FlowFixMe[incompatible-return]\n    // $FlowFixMe[prop-missing]\n    element.assignedSlot || // step into the shadow DOM of the parent of a slotted node\n    element.parentNode || ( // DOM Element detected\n    isShadowRoot(element) ? element.host : null) || // ShadowRoot detected\n    // $FlowFixMe[incompatible-call]: HTMLElement is a Node\n    getDocumentElement(element) // fallback\n\n  );\n}"], "mappings": "AAAA,OAAOA,WAAP,MAAwB,kBAAxB;AACA,OAAOC,kBAAP,MAA+B,yBAA/B;AACA,SAASC,YAAT,QAA6B,iBAA7B;AACA,eAAe,SAASC,aAAT,CAAuBC,OAAvB,EAAgC;EAC7C,IAAIJ,WAAW,CAACI,OAAD,CAAX,KAAyB,MAA7B,EAAqC;IACnC,OAAOA,OAAP;EACD;;EAED,OAAQ;IACN;IACA;IACAA,OAAO,CAACC,YAAR,IAAwB;IACxBD,OAAO,CAACE,UADR,MACwB;IACxBJ,YAAY,CAACE,OAAD,CAAZ,GAAwBA,OAAO,CAACG,IAAhC,GAAuC,IAFvC,KAEgD;IAChD;IACAN,kBAAkB,CAACG,OAAD,CAPpB,CAO8B;;EAP9B;AAUD"}, "metadata": {}, "sourceType": "module"}