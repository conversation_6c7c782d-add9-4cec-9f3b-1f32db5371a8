{"ast": null, "code": "import { Observable } from '../Observable';\nimport { from } from './from';\nimport { EMPTY } from './empty';\nexport function using(resourceFactory, observableFactory) {\n  return new Observable(subscriber => {\n    let resource;\n\n    try {\n      resource = resourceFactory();\n    } catch (err) {\n      subscriber.error(err);\n      return undefined;\n    }\n\n    let result;\n\n    try {\n      result = observableFactory(resource);\n    } catch (err) {\n      subscriber.error(err);\n      return undefined;\n    }\n\n    const source = result ? from(result) : EMPTY;\n    const subscription = source.subscribe(subscriber);\n    return () => {\n      subscription.unsubscribe();\n\n      if (resource) {\n        resource.unsubscribe();\n      }\n    };\n  });\n} //# sourceMappingURL=using.js.map", "map": null, "metadata": {}, "sourceType": "module"}