{"ast": null, "code": "import { Observable } from '../Observable';\nexport function throwError(error, scheduler) {\n  if (!scheduler) {\n    return new Observable(subscriber => subscriber.error(error));\n  } else {\n    return new Observable(subscriber => scheduler.schedule(dispatch, 0, {\n      error,\n      subscriber\n    }));\n  }\n}\n\nfunction dispatch({\n  error,\n  subscriber\n}) {\n  subscriber.error(error);\n}", "map": {"version": 3, "names": ["Observable", "throwError", "error", "scheduler", "subscriber", "schedule", "dispatch"], "sources": ["D:/work/joyserver/client/node_modules/@angular-slider/ngx-slider/node_modules/rxjs/_esm2015/internal/observable/throwError.js"], "sourcesContent": ["import { Observable } from '../Observable';\nexport function throwError(error, scheduler) {\n    if (!scheduler) {\n        return new Observable(subscriber => subscriber.error(error));\n    }\n    else {\n        return new Observable(subscriber => scheduler.schedule(dispatch, 0, { error, subscriber }));\n    }\n}\nfunction dispatch({ error, subscriber }) {\n    subscriber.error(error);\n}\n"], "mappings": "AAAA,SAASA,UAAT,QAA2B,eAA3B;AACA,OAAO,SAASC,UAAT,CAAoBC,KAApB,EAA2BC,SAA3B,EAAsC;EACzC,IAAI,CAACA,SAAL,EAAgB;IACZ,OAAO,IAAIH,UAAJ,CAAeI,UAAU,IAAIA,UAAU,CAACF,KAAX,CAAiBA,KAAjB,CAA7B,CAAP;EACH,CAFD,MAGK;IACD,OAAO,IAAIF,UAAJ,CAAeI,UAAU,IAAID,SAAS,CAACE,QAAV,CAAmBC,QAAnB,EAA6B,CAA7B,EAAgC;MAAEJ,KAAF;MAASE;IAAT,CAAhC,CAA7B,CAAP;EACH;AACJ;;AACD,SAASE,QAAT,CAAkB;EAAEJ,KAAF;EAASE;AAAT,CAAlB,EAAyC;EACrCA,UAAU,CAACF,KAAX,CAAiBA,KAAjB;AACH"}, "metadata": {}, "sourceType": "module"}