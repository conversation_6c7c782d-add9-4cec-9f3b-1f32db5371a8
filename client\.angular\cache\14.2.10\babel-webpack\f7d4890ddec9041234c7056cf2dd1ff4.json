{"ast": null, "code": "import { factory } from '../../utils/factory.js';\nvar name = 'nthRoots';\nvar dependencies = ['config', 'typed', 'divideScalar', 'Complex'];\nexport var createNthRoots = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed,\n    config,\n    divideScalar,\n    Complex\n  } = _ref;\n  /**\n   * Each function here returns a real multiple of i as a Complex value.\n   * @param  {number} val\n   * @return {Complex} val, i*val, -val or -i*val for index 0, 1, 2, 3\n   */\n  // This is used to fix float artifacts for zero-valued components.\n\n  var _calculateExactResult = [function realPos(val) {\n    return new Complex(val, 0);\n  }, function imagPos(val) {\n    return new Complex(0, val);\n  }, function realNeg(val) {\n    return new Complex(-val, 0);\n  }, function imagNeg(val) {\n    return new Complex(0, -val);\n  }];\n  /**\n   * Calculate the nth root of a Complex Number a using <PERSON> Movire's Theorem.\n   * @param  {Complex} a\n   * @param  {number} root\n   * @return {Array} array of n Complex Roots\n   */\n\n  function _nthComplexRoots(a, root) {\n    if (root < 0) throw new Error('Root must be greater than zero');\n    if (root === 0) throw new Error('Root must be non-zero');\n    if (root % 1 !== 0) throw new Error('Root must be an integer');\n    if (a === 0 || a.abs() === 0) return [new Complex(0, 0)];\n    var aIsNumeric = typeof a === 'number';\n    var offset; // determine the offset (argument of a)/(pi/2)\n\n    if (aIsNumeric || a.re === 0 || a.im === 0) {\n      if (aIsNumeric) {\n        offset = 2 * +(a < 0); // numeric value on the real axis\n      } else if (a.im === 0) {\n        offset = 2 * +(a.re < 0); // complex value on the real axis\n      } else {\n        offset = 2 * +(a.im < 0) + 1; // complex value on the imaginary axis\n      }\n    }\n\n    var arg = a.arg();\n    var abs = a.abs();\n    var roots = [];\n    var r = Math.pow(abs, 1 / root);\n\n    for (var k = 0; k < root; k++) {\n      var halfPiFactor = (offset + 4 * k) / root;\n      /**\n       * If (offset + 4*k)/root is an integral multiple of pi/2\n       * then we can produce a more exact result.\n       */\n\n      if (halfPiFactor === Math.round(halfPiFactor)) {\n        roots.push(_calculateExactResult[halfPiFactor % 4](r));\n        continue;\n      }\n\n      roots.push(new Complex({\n        r,\n        phi: (arg + 2 * Math.PI * k) / root\n      }));\n    }\n\n    return roots;\n  }\n  /**\n   * Calculate the nth roots of a value.\n   * An nth root of a positive real number A,\n   * is a positive real solution of the equation \"x^root = A\".\n   * This function returns an array of complex values.\n   *\n   * Syntax:\n   *\n   *    math.nthRoots(x)\n   *    math.nthRoots(x, root)\n   *\n   * Examples:\n   *\n   *    math.nthRoots(1)\n   *    // returns [\n   *    //   {re: 1, im: 0},\n   *    //   {re: -1, im: 0}\n   *    // ]\n   *    math.nthRoots(1, 3)\n   *    // returns [\n   *    //   { re: 1, im: 0 },\n   *    //   { re: -0.4999999999999998, im: 0.8660254037844387 },\n   *    //   { re: -0.5000000000000004, im: -0.8660254037844385 }\n   *    // ]\n   *\n   * See also:\n   *\n   *    nthRoot, pow, sqrt\n   *\n   * @param {number | BigNumber | Fraction | Complex} x Number to be rounded\n   * @param {number} [root=2] Optional root, default value is 2\n   * @return {number | BigNumber | Fraction | Complex} Returns the nth roots\n   */\n\n\n  return typed(name, {\n    Complex: function Complex(x) {\n      return _nthComplexRoots(x, 2);\n    },\n    'Complex, number': _nthComplexRoots\n  });\n});", "map": null, "metadata": {}, "sourceType": "module"}