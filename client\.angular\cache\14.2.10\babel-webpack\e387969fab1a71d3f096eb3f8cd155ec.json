{"ast": null, "code": "import { factory } from '../../utils/factory.js';\nimport { randomMatrix } from './util/randomMatrix.js';\nimport { createRng } from './util/seededRNG.js';\nimport { isMatrix } from '../../utils/is.js';\nvar name = 'randomInt';\nvar dependencies = ['typed', 'config', 'log2', '?on'];\nvar simpleCutoff = 2n ** 30n;\nexport var createRandomInt = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed,\n    config,\n    log2,\n    on\n  } = _ref; // seeded pseudo random number generator\n\n  var rng = createRng(config.randomSeed);\n\n  if (on) {\n    on('config', function (curr, prev) {\n      if (curr.randomSeed !== prev.randomSeed) {\n        rng = createRng(curr.randomSeed);\n      }\n    });\n  }\n  /**\n   * Return a random integer number larger or equal to `min` and smaller than `max`\n   * using a uniform distribution.\n   *\n   * Syntax:\n   *\n   *     math.randomInt()                // generate either 0 or 1, randomly\n   *     math.randomInt(max)             // generate a random integer between 0 and max\n   *     math.randomInt(min, max)        // generate a random integer between min and max\n   *     math.randomInt(size)            // generate a matrix with random integer between 0 and 1\n   *     math.randomInt(size, max)       // generate a matrix with random integer between 0 and max\n   *     math.randomInt(size, min, max)  // generate a matrix with random integer between min and max\n   *\n   * Examples:\n   *\n   *     math.randomInt(100)    // returns a random integer between 0 and 100\n   *     math.randomInt(30, 40) // returns a random integer between 30 and 40\n   *     math.randomInt([2, 3]) // returns a 2x3 matrix with random integers between 0 and 1\n   *\n   * See also:\n   *\n   *     random, pickRandom\n   *\n   * @param {Array | Matrix} [size] If provided, an array or matrix with given\n   *                                size and filled with random values is returned\n   * @param {number} [min]  Minimum boundary for the random value, included\n   * @param {number} [max]  Maximum boundary for the random value, excluded\n   * @return {number | Array | Matrix} A random integer value\n   */\n\n\n  return typed(name, {\n    '': () => _randomInt(0, 2),\n    number: max => _randomInt(0, max),\n    'number, number': (min, max) => _randomInt(min, max),\n    bigint: max => _randomBigint(0n, max),\n    'bigint, bigint': _randomBigint,\n    'Array | Matrix': size => _randomIntMatrix(size, 0, 1),\n    'Array | Matrix, number': (size, max) => _randomIntMatrix(size, 0, max),\n    'Array | Matrix, number, number': (size, min, max) => _randomIntMatrix(size, min, max)\n  });\n\n  function _randomIntMatrix(size, min, max) {\n    var res = randomMatrix(size.valueOf(), () => _randomInt(min, max));\n    return isMatrix(size) ? size.create(res, 'number') : res;\n  }\n\n  function _randomInt(min, max) {\n    return Math.floor(min + rng() * (max - min));\n  }\n\n  function _randomBigint(min, max) {\n    var width = max - min; // number of choices\n\n    if (width <= simpleCutoff) {\n      // do it with number type\n      return min + BigInt(_randomInt(0, Number(width)));\n    } // Too big to choose accurately that way. Instead, choose the correct\n    // number of random bits to cover the width, and repeat until the\n    // resulting number falls within the width\n\n\n    var bits = log2(width);\n    var picked = width;\n\n    while (picked >= width) {\n      picked = 0n;\n\n      for (var i = 0; i < bits; ++i) {\n        picked = 2n * picked + (rng() < 0.5 ? 0n : 1n);\n      }\n    }\n\n    return min + picked;\n  }\n});", "map": null, "metadata": {}, "sourceType": "module"}