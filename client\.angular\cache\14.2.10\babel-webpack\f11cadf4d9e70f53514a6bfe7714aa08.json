{"ast": null, "code": "/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { MatrixDependencies } from './dependenciesMatrixClass.generated.js';\nimport { equalScalarDependencies } from './dependenciesEqualScalar.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createSparseMatrixClass } from '../../factoriesAny.js';\nexport var SparseMatrixDependencies = {\n  MatrixDependencies,\n  equalScalarDependencies,\n  typedDependencies,\n  createSparseMatrixClass\n};", "map": {"version": 3, "names": ["MatrixDependencies", "equalScalarDependencies", "typedDependencies", "createSparseMatrixClass", "SparseMatrixDependencies"], "sources": ["D:/work/joyserver/client/node_modules/mathjs/lib/esm/entry/dependenciesAny/dependenciesSparseMatrixClass.generated.js"], "sourcesContent": ["/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { MatrixDependencies } from './dependenciesMatrixClass.generated.js';\nimport { equalScalarDependencies } from './dependenciesEqualScalar.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createSparseMatrixClass } from '../../factoriesAny.js';\nexport var SparseMatrixDependencies = {\n  MatrixDependencies,\n  equalScalarDependencies,\n  typedDependencies,\n  createSparseMatrixClass\n};"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,kBAAT,QAAmC,wCAAnC;AACA,SAASC,uBAAT,QAAwC,wCAAxC;AACA,SAASC,iBAAT,QAAkC,kCAAlC;AACA,SAASC,uBAAT,QAAwC,uBAAxC;AACA,OAAO,IAAIC,wBAAwB,GAAG;EACpCJ,kBADoC;EAEpCC,uBAFoC;EAGpCC,iBAHoC;EAIpCC;AAJoC,CAA/B"}, "metadata": {}, "sourceType": "module"}