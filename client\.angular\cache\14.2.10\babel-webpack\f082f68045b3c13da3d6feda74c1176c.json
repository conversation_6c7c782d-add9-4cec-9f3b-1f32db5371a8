{"ast": null, "code": "export var divideDocs = {\n  name: 'divide',\n  category: 'Operators',\n  syntax: ['x / y', 'divide(x, y)'],\n  description: 'Divide two values.',\n  examples: ['a = 2 / 3', 'a * 3', '4.5 / 2', '3 + 4 / 2', '(3 + 4) / 2', '18 km / 4.5'],\n  seealso: ['multiply']\n};", "map": {"version": 3, "names": ["divideDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["D:/work/joyserver/client/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/arithmetic/divide.js"], "sourcesContent": ["export var divideDocs = {\n  name: 'divide',\n  category: 'Operators',\n  syntax: ['x / y', 'divide(x, y)'],\n  description: 'Divide two values.',\n  examples: ['a = 2 / 3', 'a * 3', '4.5 / 2', '3 + 4 / 2', '(3 + 4) / 2', '18 km / 4.5'],\n  seealso: ['multiply']\n};"], "mappings": "AAAA,OAAO,IAAIA,UAAU,GAAG;EACtBC,IAAI,EAAE,QADgB;EAEtBC,QAAQ,EAAE,WAFY;EAGtBC,MAAM,EAAE,CAAC,OAAD,EAAU,cAAV,CAHc;EAItBC,WAAW,EAAE,oBAJS;EAKtBC,QAAQ,EAAE,CAAC,WAAD,EAAc,OAAd,EAAuB,SAAvB,EAAkC,WAAlC,EAA+C,aAA/C,EAA8D,aAA9D,CALY;EAMtBC,OAAO,EAAE,CAAC,UAAD;AANa,CAAjB"}, "metadata": {}, "sourceType": "module"}