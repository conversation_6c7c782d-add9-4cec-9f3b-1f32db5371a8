{"ast": null, "code": "import { mergeMap } from './mergeMap';\nimport { identity } from '../util/identity';\nexport function mergeAll(concurrent = Number.POSITIVE_INFINITY) {\n  return mergeMap(identity, concurrent);\n}", "map": {"version": 3, "names": ["mergeMap", "identity", "mergeAll", "concurrent", "Number", "POSITIVE_INFINITY"], "sources": ["D:/work/joyserver/client/node_modules/@angular-slider/ngx-slider/node_modules/rxjs/_esm2015/internal/operators/mergeAll.js"], "sourcesContent": ["import { mergeMap } from './mergeMap';\nimport { identity } from '../util/identity';\nexport function mergeAll(concurrent = Number.POSITIVE_INFINITY) {\n    return mergeMap(identity, concurrent);\n}\n"], "mappings": "AAAA,SAASA,QAAT,QAAyB,YAAzB;AACA,SAASC,QAAT,QAAyB,kBAAzB;AACA,OAAO,SAASC,QAAT,CAAkBC,UAAU,GAAGC,MAAM,CAACC,iBAAtC,EAAyD;EAC5D,OAAOL,QAAQ,CAACC,QAAD,EAAWE,UAAX,CAAf;AACH"}, "metadata": {}, "sourceType": "module"}