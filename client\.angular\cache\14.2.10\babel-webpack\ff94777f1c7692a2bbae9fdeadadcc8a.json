{"ast": null, "code": "import { createForEach } from '../../function/matrix/forEach.js';\nimport { createTransformCallback } from './utils/transformCallback.js';\nimport { factory } from '../../utils/factory.js';\nimport { isFunctionAssignmentNode, isSymbolNode } from '../../utils/is.js';\nimport { compileInlineExpression } from './utils/compileInlineExpression.js';\nvar name = 'forEach';\nvar dependencies = ['typed'];\nexport var createForEachTransform = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed\n  } = _ref;\n  /**\n   * Attach a transform function to math.forEach\n   * Adds a property transform containing the transform function.\n   *\n   * This transform creates a one-based index instead of a zero-based index\n   */\n\n  var forEach = createForEach({\n    typed\n  });\n  var transformCallback = createTransformCallback({\n    typed\n  });\n\n  function forEachTransform(args, math, scope) {\n    if (args.length === 0) {\n      return forEach();\n    }\n\n    var x = args[0];\n\n    if (args.length === 1) {\n      return forEach(x);\n    }\n\n    var N = args.length - 1;\n    var callback = args[N];\n\n    if (x) {\n      x = _compileAndEvaluate(x, scope);\n    }\n\n    if (callback) {\n      if (isSymbolNode(callback) || isFunctionAssignmentNode(callback)) {\n        // a function pointer, like filter([3, -2, 5], myTestFunction)\n        callback = _compileAndEvaluate(callback, scope);\n      } else {\n        // an expression like filter([3, -2, 5], x > 0)\n        callback = compileInlineExpression(callback, math, scope);\n      }\n    }\n\n    return forEach(x, transformCallback(callback, N));\n  }\n\n  forEachTransform.rawArgs = true;\n\n  function _compileAndEvaluate(arg, scope) {\n    return arg.compile().evaluate(scope);\n  }\n\n  return forEachTransform;\n}, {\n  isTransformFunction: true\n});", "map": {"version": 3, "names": ["createForEach", "createTransformCallback", "factory", "isFunctionAssignmentNode", "isSymbolNode", "compileInlineExpression", "name", "dependencies", "createForEachTransform", "_ref", "typed", "for<PERSON>ach", "transformCallback", "forEachTransform", "args", "math", "scope", "length", "x", "N", "callback", "_compileAndEvaluate", "rawArgs", "arg", "compile", "evaluate", "isTransformFunction"], "sources": ["D:/work/joyserver/client/node_modules/mathjs/lib/esm/expression/transform/forEach.transform.js"], "sourcesContent": ["import { createForEach } from '../../function/matrix/forEach.js';\nimport { createTransformCallback } from './utils/transformCallback.js';\nimport { factory } from '../../utils/factory.js';\nimport { isFunctionAssignmentNode, isSymbolNode } from '../../utils/is.js';\nimport { compileInlineExpression } from './utils/compileInlineExpression.js';\nvar name = 'forEach';\nvar dependencies = ['typed'];\nexport var createForEachTransform = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed\n  } = _ref;\n  /**\n   * Attach a transform function to math.forEach\n   * Adds a property transform containing the transform function.\n   *\n   * This transform creates a one-based index instead of a zero-based index\n   */\n  var forEach = createForEach({\n    typed\n  });\n  var transformCallback = createTransformCallback({\n    typed\n  });\n  function forEachTransform(args, math, scope) {\n    if (args.length === 0) {\n      return forEach();\n    }\n    var x = args[0];\n    if (args.length === 1) {\n      return forEach(x);\n    }\n    var N = args.length - 1;\n    var callback = args[N];\n    if (x) {\n      x = _compileAndEvaluate(x, scope);\n    }\n    if (callback) {\n      if (isSymbolNode(callback) || isFunctionAssignmentNode(callback)) {\n        // a function pointer, like filter([3, -2, 5], myTestFunction)\n        callback = _compileAndEvaluate(callback, scope);\n      } else {\n        // an expression like filter([3, -2, 5], x > 0)\n        callback = compileInlineExpression(callback, math, scope);\n      }\n    }\n    return forEach(x, transformCallback(callback, N));\n  }\n  forEachTransform.rawArgs = true;\n  function _compileAndEvaluate(arg, scope) {\n    return arg.compile().evaluate(scope);\n  }\n  return forEachTransform;\n}, {\n  isTransformFunction: true\n});"], "mappings": "AAAA,SAASA,aAAT,QAA8B,kCAA9B;AACA,SAASC,uBAAT,QAAwC,8BAAxC;AACA,SAASC,OAAT,QAAwB,wBAAxB;AACA,SAASC,wBAAT,EAAmCC,YAAnC,QAAuD,mBAAvD;AACA,SAASC,uBAAT,QAAwC,oCAAxC;AACA,IAAIC,IAAI,GAAG,SAAX;AACA,IAAIC,YAAY,GAAG,CAAC,OAAD,CAAnB;AACA,OAAO,IAAIC,sBAAsB,GAAG,eAAeN,OAAO,CAACI,IAAD,EAAOC,YAAP,EAAqBE,IAAI,IAAI;EACrF,IAAI;IACFC;EADE,IAEAD,IAFJ;EAGA;AACF;AACA;AACA;AACA;AACA;;EACE,IAAIE,OAAO,GAAGX,aAAa,CAAC;IAC1BU;EAD0B,CAAD,CAA3B;EAGA,IAAIE,iBAAiB,GAAGX,uBAAuB,CAAC;IAC9CS;EAD8C,CAAD,CAA/C;;EAGA,SAASG,gBAAT,CAA0BC,IAA1B,EAAgCC,IAAhC,EAAsCC,KAAtC,EAA6C;IAC3C,IAAIF,IAAI,CAACG,MAAL,KAAgB,CAApB,EAAuB;MACrB,OAAON,OAAO,EAAd;IACD;;IACD,IAAIO,CAAC,GAAGJ,IAAI,CAAC,CAAD,CAAZ;;IACA,IAAIA,IAAI,CAACG,MAAL,KAAgB,CAApB,EAAuB;MACrB,OAAON,OAAO,CAACO,CAAD,CAAd;IACD;;IACD,IAAIC,CAAC,GAAGL,IAAI,CAACG,MAAL,GAAc,CAAtB;IACA,IAAIG,QAAQ,GAAGN,IAAI,CAACK,CAAD,CAAnB;;IACA,IAAID,CAAJ,EAAO;MACLA,CAAC,GAAGG,mBAAmB,CAACH,CAAD,EAAIF,KAAJ,CAAvB;IACD;;IACD,IAAII,QAAJ,EAAc;MACZ,IAAIhB,YAAY,CAACgB,QAAD,CAAZ,IAA0BjB,wBAAwB,CAACiB,QAAD,CAAtD,EAAkE;QAChE;QACAA,QAAQ,GAAGC,mBAAmB,CAACD,QAAD,EAAWJ,KAAX,CAA9B;MACD,CAHD,MAGO;QACL;QACAI,QAAQ,GAAGf,uBAAuB,CAACe,QAAD,EAAWL,IAAX,EAAiBC,KAAjB,CAAlC;MACD;IACF;;IACD,OAAOL,OAAO,CAACO,CAAD,EAAIN,iBAAiB,CAACQ,QAAD,EAAWD,CAAX,CAArB,CAAd;EACD;;EACDN,gBAAgB,CAACS,OAAjB,GAA2B,IAA3B;;EACA,SAASD,mBAAT,CAA6BE,GAA7B,EAAkCP,KAAlC,EAAyC;IACvC,OAAOO,GAAG,CAACC,OAAJ,GAAcC,QAAd,CAAuBT,KAAvB,CAAP;EACD;;EACD,OAAOH,gBAAP;AACD,CA7CyD,EA6CvD;EACDa,mBAAmB,EAAE;AADpB,CA7CuD,CAAnD"}, "metadata": {}, "sourceType": "module"}