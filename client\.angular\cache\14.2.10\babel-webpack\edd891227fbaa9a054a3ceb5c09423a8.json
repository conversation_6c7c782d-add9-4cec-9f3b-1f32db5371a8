{"ast": null, "code": "export var nthRootDocs = {\n  name: 'nthRoot',\n  category: 'Arithmetic',\n  syntax: ['nthRoot(a)', 'nthRoot(a, root)'],\n  description: 'Calculate the nth root of a value. ' + 'The principal nth root of a positive real number A, ' + 'is the positive real solution of the equation \"x^root = A\".',\n  examples: ['4 ^ 3', 'nthRoot(64, 3)', 'nthRoot(9, 2)', 'sqrt(9)'],\n  seealso: ['nthRoots', 'pow', 'sqrt']\n};", "map": {"version": 3, "names": ["nthRootDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["D:/work/joyserver/client/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/arithmetic/nthRoot.js"], "sourcesContent": ["export var nthRootDocs = {\n  name: 'nthRoot',\n  category: 'Arithmetic',\n  syntax: ['nthRoot(a)', 'nthRoot(a, root)'],\n  description: 'Calculate the nth root of a value. ' + 'The principal nth root of a positive real number A, ' + 'is the positive real solution of the equation \"x^root = A\".',\n  examples: ['4 ^ 3', 'nthRoot(64, 3)', 'nthRoot(9, 2)', 'sqrt(9)'],\n  seealso: ['nthRoots', 'pow', 'sqrt']\n};"], "mappings": "AAAA,OAAO,IAAIA,WAAW,GAAG;EACvBC,IAAI,EAAE,SADiB;EAEvBC,QAAQ,EAAE,YAFa;EAGvBC,MAAM,EAAE,CAAC,YAAD,EAAe,kBAAf,CAHe;EAIvBC,WAAW,EAAE,wCAAwC,sDAAxC,GAAiG,6DAJvF;EAKvBC,QAAQ,EAAE,CAAC,OAAD,EAAU,gBAAV,EAA4B,eAA5B,EAA6C,SAA7C,CALa;EAMvBC,OAAO,EAAE,CAAC,UAAD,EAAa,KAAb,EAAoB,MAApB;AANc,CAAlB"}, "metadata": {}, "sourceType": "module"}