{"ast": null, "code": "export var symbolicEqualDocs = {\n  name: 'symbolicEqual',\n  category: 'Algebra',\n  syntax: ['symbolicEqual(expr1, expr2)', 'symbolicEqual(expr1, expr2, options)'],\n  description: 'Returns true if the difference of the expressions simplifies to 0',\n  examples: ['symbolicEqual(\"x*y\",\"y*x\")', 'symbolicEqual(\"abs(x^2)\", \"x^2\")', 'symbolicEqual(\"abs(x)\", \"x\", {context: {abs: {trivial: true}}})'],\n  seealso: ['simplify', 'evaluate']\n};", "map": null, "metadata": {}, "sourceType": "module"}