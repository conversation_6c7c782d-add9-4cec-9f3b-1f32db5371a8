{"ast": null, "code": "import { EmptyError } from '../util/EmptyError';\nimport { filter } from './filter';\nimport { takeLast } from './takeLast';\nimport { throwIfEmpty } from './throwIfEmpty';\nimport { defaultIfEmpty } from './defaultIfEmpty';\nimport { identity } from '../util/identity';\nexport function last(predicate, defaultValue) {\n  const hasDefaultValue = arguments.length >= 2;\n  return source => source.pipe(predicate ? filter((v, i) => predicate(v, i, source)) : identity, takeLast(1), hasDefaultValue ? defaultIfEmpty(defaultValue) : throwIfEmpty(() => new EmptyError()));\n}", "map": {"version": 3, "names": ["EmptyError", "filter", "takeLast", "throwIfEmpty", "defaultIfEmpty", "identity", "last", "predicate", "defaultValue", "hasDefaultValue", "arguments", "length", "source", "pipe", "v", "i"], "sources": ["D:/work/joyserver/client/node_modules/rxjs/dist/esm/internal/operators/last.js"], "sourcesContent": ["import { EmptyError } from '../util/EmptyError';\nimport { filter } from './filter';\nimport { takeLast } from './takeLast';\nimport { throwIfEmpty } from './throwIfEmpty';\nimport { defaultIfEmpty } from './defaultIfEmpty';\nimport { identity } from '../util/identity';\nexport function last(predicate, defaultValue) {\n    const hasDefaultValue = arguments.length >= 2;\n    return (source) => source.pipe(predicate ? filter((v, i) => predicate(v, i, source)) : identity, takeLast(1), hasDefaultValue ? defaultIfEmpty(defaultValue) : throwIfEmpty(() => new EmptyError()));\n}\n"], "mappings": "AAAA,SAASA,UAAT,QAA2B,oBAA3B;AACA,SAASC,MAAT,QAAuB,UAAvB;AACA,SAASC,QAAT,QAAyB,YAAzB;AACA,SAASC,YAAT,QAA6B,gBAA7B;AACA,SAASC,cAAT,QAA+B,kBAA/B;AACA,SAASC,QAAT,QAAyB,kBAAzB;AACA,OAAO,SAASC,IAAT,CAAcC,SAAd,EAAyBC,YAAzB,EAAuC;EAC1C,MAAMC,eAAe,GAAGC,SAAS,CAACC,MAAV,IAAoB,CAA5C;EACA,OAAQC,MAAD,IAAYA,MAAM,CAACC,IAAP,CAAYN,SAAS,GAAGN,MAAM,CAAC,CAACa,CAAD,EAAIC,CAAJ,KAAUR,SAAS,CAACO,CAAD,EAAIC,CAAJ,EAAOH,MAAP,CAApB,CAAT,GAA+CP,QAApE,EAA8EH,QAAQ,CAAC,CAAD,CAAtF,EAA2FO,eAAe,GAAGL,cAAc,CAACI,YAAD,CAAjB,GAAkCL,YAAY,CAAC,MAAM,IAAIH,UAAJ,EAAP,CAAxJ,CAAnB;AACH"}, "metadata": {}, "sourceType": "module"}