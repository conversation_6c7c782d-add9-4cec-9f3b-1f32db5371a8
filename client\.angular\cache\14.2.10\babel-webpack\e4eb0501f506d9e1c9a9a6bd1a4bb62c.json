{"ast": null, "code": "import Fraction from 'fraction.js';\nimport { factory } from '../../utils/factory.js';\nvar name = 'Fraction';\nvar dependencies = [];\nexport var createFractionClass = /* #__PURE__ */factory(name, dependencies, () => {\n  /**\n   * Attach type information\n   */\n  Object.defineProperty(Fraction, 'name', {\n    value: 'Fraction'\n  });\n  Fraction.prototype.constructor = Fraction;\n  Fraction.prototype.type = 'Fraction';\n  Fraction.prototype.isFraction = true;\n  /**\n   * Get a JSON representation of a Fraction containing type information\n   * @returns {Object} Returns a JSON object structured as:\n   *                   `{\"mathjs\": \"Fraction\", \"n\": \"3\", \"d\": \"8\"}`\n   */\n\n  Fraction.prototype.toJSON = function () {\n    return {\n      mathjs: 'Fraction',\n      n: String(this.s * this.n),\n      d: String(this.d)\n    };\n  };\n  /**\n   * Instantiate a Fraction from a JSON object\n   * @param {Object} json  a JSON object structured as:\n   *                       `{\"mathjs\": \"Fraction\", \"n\": \"3\", \"d\": \"8\"}`\n   * @return {BigNumber}\n   */\n\n\n  Fraction.fromJSON = function (json) {\n    return new Fraction(json);\n  };\n\n  return Fraction;\n}, {\n  isClass: true\n});", "map": null, "metadata": {}, "sourceType": "module"}