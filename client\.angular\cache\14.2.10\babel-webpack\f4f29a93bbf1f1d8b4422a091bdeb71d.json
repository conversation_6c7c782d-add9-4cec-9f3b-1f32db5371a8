{"ast": null, "code": "import { factory } from '../../../utils/factory.js';\nvar name = 'matrix';\nvar dependencies = ['typed', 'Matrix', 'DenseMatrix', 'SparseMatrix'];\nexport var createMatrix = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed,\n    Matrix,\n    DenseMatrix,\n    SparseMatrix\n  } = _ref;\n  /**\n   * Create a Matrix. The function creates a new `math.Matrix` object from\n   * an `Array`. A Matrix has utility functions to manipulate the data in the\n   * matrix, like getting the size and getting or setting values in the matrix.\n   * Supported storage formats are 'dense' and 'sparse'.\n   *\n   * Syntax:\n   *\n   *    math.matrix()                         // creates an empty matrix using default storage format (dense).\n   *    math.matrix(data)                     // creates a matrix with initial data using default storage format (dense).\n   *    math.matrix('dense')                  // creates an empty matrix using the given storage format.\n   *    math.matrix(data, 'dense')            // creates a matrix with initial data using the given storage format.\n   *    math.matrix(data, 'sparse')           // creates a sparse matrix with initial data.\n   *    math.matrix(data, 'sparse', 'number') // creates a sparse matrix with initial data, number data type.\n   *\n   * Examples:\n   *\n   *    let m = math.matrix([[1, 2], [3, 4]])\n   *    m.size()                        // Array [2, 2]\n   *    m.resize([3, 2], 5)\n   *    m.valueOf()                     // Array [[1, 2], [3, 4], [5, 5]]\n   *    m.get([1, 0])                    // number 3\n   *\n   * See also:\n   *\n   *    bignumber, boolean, complex, index, number, string, unit, sparse\n   *\n   * @param {Array | Matrix} [data]    A multi dimensional array\n   * @param {string} [format]          The Matrix storage format, either `'dense'` or `'sparse'`\n   * @param {string} [datatype]        Type of the values\n   *\n   * @return {Matrix} The created matrix\n   */\n\n  return typed(name, {\n    '': function _() {\n      return _create([]);\n    },\n    string: function string(format) {\n      return _create([], format);\n    },\n    'string, string': function string_string(format, datatype) {\n      return _create([], format, datatype);\n    },\n    Array: function Array(data) {\n      return _create(data);\n    },\n    Matrix: function Matrix(data) {\n      return _create(data, data.storage());\n    },\n    'Array | Matrix, string': _create,\n    'Array | Matrix, string, string': _create\n  });\n  /**\n   * Create a new Matrix with given storage format\n   * @param {Array} data\n   * @param {string} [format]\n   * @param {string} [datatype]\n   * @returns {Matrix} Returns a new Matrix\n   * @private\n   */\n\n  function _create(data, format, datatype) {\n    // get storage format constructor\n    if (format === 'dense' || format === 'default' || format === undefined) {\n      return new DenseMatrix(data, datatype);\n    }\n\n    if (format === 'sparse') {\n      return new SparseMatrix(data, datatype);\n    }\n\n    throw new TypeError('Unknown matrix type ' + JSON.stringify(format) + '.');\n  }\n});", "map": null, "metadata": {}, "sourceType": "module"}