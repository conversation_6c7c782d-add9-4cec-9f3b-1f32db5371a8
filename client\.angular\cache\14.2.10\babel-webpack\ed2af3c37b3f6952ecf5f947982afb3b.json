{"ast": null, "code": "import { factory } from '../../utils/factory.js';\nvar name = 'equalText';\nvar dependencies = ['typed', 'compareText', 'isZero'];\nexport var createEqualText = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed,\n    compareText,\n    isZero\n  } = _ref;\n  /**\n   * Check equality of two strings. Comparison is case sensitive.\n   *\n   * For matrices, the function is evaluated element wise.\n   *\n   * Syntax:\n   *\n   *    math.equalText(x, y)\n   *\n   * Examples:\n   *\n   *    math.equalText('Hello', 'Hello')     // returns true\n   *    math.equalText('a', 'A')             // returns false\n   *    math.equal('2e3', '2000')            // returns true\n   *    math.equalText('2e3', '2000')        // returns false\n   *\n   *    math.equalText('B', ['A', 'B', 'C']) // returns [false, true, false]\n   *\n   * See also:\n   *\n   *    equal, compareText, compare, compareNatural\n   *\n   * @param  {string | Array | DenseMatrix} x First string to compare\n   * @param  {string | Array | DenseMatrix} y Second string to compare\n   * @return {number | Array | DenseMatrix} Returns true if the values are equal, and false if not.\n   */\n\n  return typed(name, {\n    'any, any': function any_any(x, y) {\n      return isZero(compareText(x, y));\n    }\n  });\n});", "map": {"version": 3, "names": ["factory", "name", "dependencies", "createEqualText", "_ref", "typed", "compareText", "isZero", "any_any", "x", "y"], "sources": ["D:/work/joyserver/client/node_modules/mathjs/lib/esm/function/relational/equalText.js"], "sourcesContent": ["import { factory } from '../../utils/factory.js';\nvar name = 'equalText';\nvar dependencies = ['typed', 'compareText', 'isZero'];\nexport var createEqualText = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed,\n    compareText,\n    isZero\n  } = _ref;\n  /**\n   * Check equality of two strings. Comparison is case sensitive.\n   *\n   * For matrices, the function is evaluated element wise.\n   *\n   * Syntax:\n   *\n   *    math.equalText(x, y)\n   *\n   * Examples:\n   *\n   *    math.equalText('Hello', 'Hello')     // returns true\n   *    math.equalText('a', 'A')             // returns false\n   *    math.equal('2e3', '2000')            // returns true\n   *    math.equalText('2e3', '2000')        // returns false\n   *\n   *    math.equalText('B', ['A', 'B', 'C']) // returns [false, true, false]\n   *\n   * See also:\n   *\n   *    equal, compareText, compare, compareNatural\n   *\n   * @param  {string | Array | DenseMatrix} x First string to compare\n   * @param  {string | Array | DenseMatrix} y Second string to compare\n   * @return {number | Array | DenseMatrix} Returns true if the values are equal, and false if not.\n   */\n  return typed(name, {\n    'any, any': function any_any(x, y) {\n      return isZero(compareText(x, y));\n    }\n  });\n});"], "mappings": "AAAA,SAASA,OAAT,QAAwB,wBAAxB;AACA,IAAIC,IAAI,GAAG,WAAX;AACA,IAAIC,YAAY,GAAG,CAAC,OAAD,EAAU,aAAV,EAAyB,QAAzB,CAAnB;AACA,OAAO,IAAIC,eAAe,GAAG,eAAeH,OAAO,CAACC,IAAD,EAAOC,YAAP,EAAqBE,IAAI,IAAI;EAC9E,IAAI;IACFC,KADE;IAEFC,WAFE;IAGFC;EAHE,IAIAH,IAJJ;EAKA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EACE,OAAOC,KAAK,CAACJ,IAAD,EAAO;IACjB,YAAY,SAASO,OAAT,CAAiBC,CAAjB,EAAoBC,CAApB,EAAuB;MACjC,OAAOH,MAAM,CAACD,WAAW,CAACG,CAAD,EAAIC,CAAJ,CAAZ,CAAb;IACD;EAHgB,CAAP,CAAZ;AAKD,CArCkD,CAA5C"}, "metadata": {}, "sourceType": "module"}