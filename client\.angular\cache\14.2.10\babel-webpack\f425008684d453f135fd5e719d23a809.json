{"ast": null, "code": "/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { NodeDependencies } from './dependenciesNode.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createFunctionAssignmentNode } from '../../factoriesAny.js';\nexport var FunctionAssignmentNodeDependencies = {\n  NodeDependencies,\n  typedDependencies,\n  createFunctionAssignmentNode\n};", "map": null, "metadata": {}, "sourceType": "module"}