{"ast": null, "code": "import { EventEmitter, ElementRef } from \"@angular/core\";\nimport { Subscription, asyncScheduler, fromEvent, Observable } from \"rxjs\";\nimport { debounceTime, map, throttleTime } from \"rxjs/operators\";\nimport { Exam } from \"@core-types/exam.types\";\nimport { animate, style, transition, trigger } from \"@angular/animations\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@core/service/exam.service\";\nimport * as i2 from \"@core/service/toolbar.service\";\nimport * as i3 from \"@core-modal/custom-alert/custom-alert.service\";\nimport * as i4 from \"@ngx-translate/core\";\nimport * as i5 from \"@core/service/form.service\";\nimport * as i6 from \"@core/service/utils/util.service\";\nimport * as i7 from \"../../../../../core/directive/app-motation-observer.directive\";\nconst _c0 = [\"wrapItemAnswer\"];\nconst _c1 = [\"initAnswer\", \"\"];\nexport class InitAnswerComponent {\n  constructor(examSer, toolbarSer, _alertSvc, translate, formSer, utilSer) {\n    this.examSer = examSer;\n    this.toolbarSer = toolbarSer;\n    this._alertSvc = _alertSvc;\n    this.translate = translate;\n    this.formSer = formSer;\n    this.utilSer = utilSer;\n    this.changeAnswer = new EventEmitter();\n    this.updateItemCountNumEvt = new EventEmitter();\n    this.updateItemStatusEvt = new EventEmitter();\n    this.domInitedWithInitAnswer = new EventEmitter();\n    this.countNum = 0;\n    this.subscriptions = [];\n    this.answerChanged = false;\n\n    this.selectChange = function (s) {\n      this.setSelectVal(s);\n      this.changeAnswer.emit();\n    };\n\n    this.inputChange = function (input) {\n      input.setAttribute(\"value\", input.value);\n      this.changeAnswer.emit();\n    };\n\n    this.getCurFocusEle = evt => {\n      this.toolbarSer.getCurFocusEle(evt);\n    };\n\n    this.changeAnswerEmit = () => {\n      this.changeAnswer.emit();\n    };\n\n    this.checkAnswerLimit = e => {\n      const curItem = this.subItem ? this.subItem : this.item;\n\n      if (!curItem.count_type) {\n        return;\n      }\n\n      const itemId = this.subItem ? this.subItem.id : this.item.id;\n      const editableWrap = document.querySelector(\"#item-answer-\" + itemId);\n\n      if (!editableWrap) {\n        return;\n      }\n\n      const editableDoms = editableWrap.querySelectorAll(\"[contenteditable=true]\");\n      const key = e.which || e.keyCode || 0;\n      let answerTxt = \"\";\n      Array.from(editableDoms).forEach((item, index) => {\n        const itemDom = item;\n        answerTxt += itemDom.innerText;\n      });\n      this.countNum = this.getCountNum(curItem, answerTxt); // 超出试题词数、字数限制 (如果超过answer_limit，count_type:word，阻止英文输入preventDefault)\n\n      const filterKeys = [8, 46, 37, 38, 39, 40]; // backspace, delete, space, left, right, top, bottom\n\n      if (this.isAnswerLimited(curItem) && this.countNum > curItem.answer_limit && filterKeys.indexOf(key) === -1) {\n        // 强制失焦,阻止输入\n        window.getSelection() && window.getSelection().removeAllRanges();\n        e.preventDefault();\n        return false;\n      }\n    };\n  }\n\n  ngOnInit() {\n    this.answer = this.getItemRes();\n    this.changeAnswerFn = this.changeAnswerEmit.bind(this);\n    this.subscriptions.push(this.examSer.examEventsFromExamSvc.subscribe(event => {\n      if (event.type === Exam.EventFromExamService.AutoCollectAnswer) {\n        if (this.answerChanged) {\n          this.changeAnswer.emit();\n        }\n\n        this.examSer.autoCollectAnswerResolve(true);\n      }\n    }));\n  }\n\n  ngAfterViewInit() {\n    this.initDom(this.wrapItemAnswer.nativeElement);\n    this.subscriptions.push(this.mouseup$.subscribe(evt => {\n      try {\n        this.getCurFocusEle(evt);\n      } catch (error) {\n        console.error(\"init answer: mouseup error:\", error);\n      }\n    }));\n  }\n\n  ngOnChanges() {\n    this.countNum = 0;\n    this.answer = this.getItemRes();\n\n    if (this.wrapItemAnswer?.nativeElement) {\n      this.initDom(this.wrapItemAnswer.nativeElement);\n    }\n  }\n\n  ngOnDestroy() {\n    this.subscriptions.forEach(sub => sub && sub.unsubscribe());\n  } // 初始化dom，订阅事件\n\n\n  initDom(editableWrap) {\n    this.answerChanged = false;\n    this.inputSub && this.inputSub.unsubscribe();\n    this.keydownCheckItemDataSub && this.keydownCheckItemDataSub.unsubscribe();\n    this.inputCheckItemDataSub && this.inputCheckItemDataSub.unsubscribe();\n    this.inputCheckItemStatusSub && this.inputCheckItemStatusSub.unsubscribe();\n    editableWrap.innerHTML = this.answer;\n    this.domInitedWithInitAnswer.emit(); // 委托事件给父元素\n\n    this.input$ = fromEvent(editableWrap, \"input\").pipe(map(data => {\n      this.answerChanged = true;\n      return data;\n    }));\n    this.keydonw$ = fromEvent(editableWrap, \"keydown\");\n    const itemCont = document.querySelector(\".item>.wrap-item-cont.item-cont-\" + this.item.id);\n\n    if (!itemCont) {\n      return;\n    }\n\n    this.mouseup$ = fromEvent(itemCont, \"mouseup\");\n    this.inputSub = this.input$.pipe(throttleTime(5000, asyncScheduler, {\n      leading: false,\n      trailing: true\n    })).subscribe(event => {\n      this.changeAnswer.emit();\n      this.answerChanged = false;\n    }); // 更新答案字、词数，校验\n\n    this.keydownCheckItemDataSub = this.keydonw$.pipe(throttleTime(500, asyncScheduler, {\n      leading: true,\n      trailing: true\n    })).subscribe(event => {\n      this.checkAnswerLimit(event);\n      this.checkMaxAnswerLimit(event);\n      this.updateItemCountNumEvt.emit({\n        type: \"init_answer\",\n        countNum: this.countNum\n      });\n    });\n    this.inputCheckItemStatusSub = this.input$.pipe(debounceTime(500)).subscribe(event => {\n      this.checkAnswerLimit(event);\n      this.checkMaxAnswerLimit(event);\n      this.updateItemCountNumEvt.emit({\n        type: \"init_answer\",\n        countNum: this.countNum\n      });\n    });\n    this.inputCheckItemStatusSub = this.input$.pipe(debounceTime(1000)).subscribe(event => {\n      this.updateItemStatusEvt.emit();\n    });\n    this.subscriptions.push(this.inputSub, this.keydownCheckItemDataSub, this.inputCheckItemDataSub, this.inputCheckItemStatusSub);\n  }\n\n  contentRendered() {\n    // 主观题转客，select\\input[type='number']初始化数据，绑定事件\n    this.initObjectiveDom();\n  }\n\n  initObjectiveDom() {\n    const initedAnswerDom = document.querySelector(`#item-answer-${this.curItemId}`);\n    const selects = initedAnswerDom.querySelectorAll(\"select\");\n    const inputs = initedAnswerDom.querySelectorAll(\"input[type='number']\");\n    selects.forEach(s => {\n      const initValue = this.getSelectVal(s);\n\n      if (initValue) {\n        s.value = initValue;\n      }\n\n      s.addEventListener(\"change\", this.selectChange.bind(this, s));\n    });\n    inputs.forEach(input => {\n      input.addEventListener(\"change\", this.inputChange.bind(this, input));\n    });\n  }\n\n  getSelectVal(s) {\n    const options = s.querySelectorAll(\"option\");\n\n    for (let i = 0, len = options.length; i < len; i++) {\n      if (options[i].getAttribute(\"selected\")) {\n        return options[i].innerHTML;\n      }\n    }\n  }\n\n  setSelectVal(s) {\n    const options = s.querySelectorAll(\"option\");\n    const i = s.selectedIndex;\n    options.forEach(op => {\n      op.removeAttribute(\"selected\");\n    });\n    options[i].setAttribute(\"selected\", \"true\");\n  }\n\n  checkMaxAnswerLimit(e) {\n    const itemId = this.subItem ? this.subItem.id : this.item.id;\n    const editableWrap = document.querySelector(\"#item-answer-\" + itemId);\n\n    if (!editableWrap) {\n      return;\n    }\n\n    const editableDoms = editableWrap.querySelectorAll(\"[contenteditable=true]\");\n    const key = e.which || e.keyCode || 0;\n    let answerTxt = \"\";\n    Array.from(editableDoms).forEach((item, index) => {\n      const itemDom = item;\n      answerTxt += itemDom.innerText;\n    });\n\n    if (this.formSer.isExceedLimit(answerTxt, 20 * 1000) && key !== 8 && key !== 46) {\n      e.preventDefault(); // 强制失焦,阻止输入\n\n      window.getSelection() && window.getSelection().removeAllRanges();\n\n      this._alertSvc.setValue({\n        status: true,\n        info: {\n          bodyText: this.translate.instant(\"exam.sa.exceedMaxLen\")\n        }\n      });\n\n      return false;\n    }\n  } // 获取统计字符数(不包括空格换行，只是可见字符数)、词数\n\n\n  getCountNum(item, answerTxt) {\n    if (item.count_type === \"word\") {\n      return this.utilSer.countWords(answerTxt);\n    } else {\n      return this.utilSer.countChar(answerTxt);\n    }\n  }\n\n  isAnswerLimited(curItem) {\n    return typeof curItem.answer_limit === \"number\" && curItem.answer_limit;\n  }\n\n  getItemRes() {\n    const subItemId = this.subItem ? this.subItem.id : \"\";\n    const itemRes = this.examSer.getItemResById(this.item.id, subItemId);\n    return itemRes.answer.value;\n  }\n\n}\n\nInitAnswerComponent.ɵfac = function InitAnswerComponent_Factory(t) {\n  return new (t || InitAnswerComponent)(i0.ɵɵdirectiveInject(i1.ExamService), i0.ɵɵdirectiveInject(i2.ToolbarService), i0.ɵɵdirectiveInject(i3.CustomAlertService), i0.ɵɵdirectiveInject(i4.TranslateService), i0.ɵɵdirectiveInject(i5.FormService), i0.ɵɵdirectiveInject(i6.UtilService));\n};\n\nInitAnswerComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: InitAnswerComponent,\n  selectors: [[\"\", \"initAnswer\", \"\"]],\n  viewQuery: function InitAnswerComponent_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(_c0, 5);\n    }\n\n    if (rf & 2) {\n      let _t;\n\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.wrapItemAnswer = _t.first);\n    }\n  },\n  inputs: {\n    item: \"item\",\n    subItem: \"subItem\",\n    curItemId: \"curItemId\"\n  },\n  outputs: {\n    changeAnswer: \"changeAnswer\",\n    updateItemCountNumEvt: \"updateItemCountNumEvt\",\n    updateItemStatusEvt: \"updateItemStatusEvt\",\n    domInitedWithInitAnswer: \"domInitedWithInitAnswer\"\n  },\n  features: [i0.ɵɵNgOnChangesFeature],\n  attrs: _c1,\n  decls: 2,\n  vars: 5,\n  consts: [[\"appAppMotationObserver\", \"\", 3, \"id\", \"innerHtmlRendered\"], [\"wrapItemAnswer\", \"\"]],\n  template: function InitAnswerComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"div\", 0, 1);\n      i0.ɵɵlistener(\"innerHtmlRendered\", function InitAnswerComponent_Template_div_innerHtmlRendered_0_listener() {\n        return ctx.contentRendered();\n      });\n      i0.ɵɵelementEnd();\n    }\n\n    if (rf & 2) {\n      i0.ɵɵclassMapInterpolate1(\"sa-init-answer-wrap item-auto-collect-flag-\", ctx.curItemId, \"\");\n      i0.ɵɵpropertyInterpolate1(\"id\", \"item-answer-\", ctx.curItemId, \"\");\n      i0.ɵɵproperty(\"@fadeInOut\", undefined);\n    }\n  },\n  dependencies: [i7.AppMotationObserverDirective],\n  styles: [\"td[contenteditable=true]:focus,   div[contenteditable=true]:focus,   span[contenteditable=true]:focus{outline:none}\"],\n  data: {\n    animation: [trigger(\"fadeInOut\", [transition(\":enter\", [style({\n      opacity: 0\n    }), animate(\"150ms\", style({\n      opacity: 1\n    }))]), transition(\":leave\", [animate(\"150ms\", style({\n      opacity: 0\n    }))])])]\n  }\n});", "map": null, "metadata": {}, "sourceType": "module"}