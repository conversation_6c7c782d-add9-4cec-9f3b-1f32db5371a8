{"ast": null, "code": "import { Subscriber } from '../Subscriber';\nexport function every(predicate, thisArg) {\n  return source => source.lift(new EveryOperator(predicate, thisArg, source));\n}\n\nclass EveryOperator {\n  constructor(predicate, thisArg, source) {\n    this.predicate = predicate;\n    this.thisArg = thisArg;\n    this.source = source;\n  }\n\n  call(observer, source) {\n    return source.subscribe(new EverySubscriber(observer, this.predicate, this.thisArg, this.source));\n  }\n\n}\n\nclass EverySubscriber extends Subscriber {\n  constructor(destination, predicate, thisArg, source) {\n    super(destination);\n    this.predicate = predicate;\n    this.thisArg = thisArg;\n    this.source = source;\n    this.index = 0;\n    this.thisArg = thisArg || this;\n  }\n\n  notifyComplete(everyValueMatch) {\n    this.destination.next(everyValueMatch);\n    this.destination.complete();\n  }\n\n  _next(value) {\n    let result = false;\n\n    try {\n      result = this.predicate.call(this.thisArg, value, this.index++, this.source);\n    } catch (err) {\n      this.destination.error(err);\n      return;\n    }\n\n    if (!result) {\n      this.notifyComplete(false);\n    }\n  }\n\n  _complete() {\n    this.notifyComplete(true);\n  }\n\n}", "map": {"version": 3, "names": ["Subscriber", "every", "predicate", "thisArg", "source", "lift", "EveryOperator", "constructor", "call", "observer", "subscribe", "EverySubscriber", "destination", "index", "notifyComplete", "everyValueMatch", "next", "complete", "_next", "value", "result", "err", "error", "_complete"], "sources": ["D:/work/joyserver/client/node_modules/@angular-slider/ngx-slider/node_modules/rxjs/_esm2015/internal/operators/every.js"], "sourcesContent": ["import { Subscriber } from '../Subscriber';\nexport function every(predicate, thisArg) {\n    return (source) => source.lift(new EveryOperator(predicate, thisArg, source));\n}\nclass EveryOperator {\n    constructor(predicate, thisArg, source) {\n        this.predicate = predicate;\n        this.thisArg = thisArg;\n        this.source = source;\n    }\n    call(observer, source) {\n        return source.subscribe(new EverySubscriber(observer, this.predicate, this.thisArg, this.source));\n    }\n}\nclass EverySubscriber extends Subscriber {\n    constructor(destination, predicate, thisArg, source) {\n        super(destination);\n        this.predicate = predicate;\n        this.thisArg = thisArg;\n        this.source = source;\n        this.index = 0;\n        this.thisArg = thisArg || this;\n    }\n    notifyComplete(everyValueMatch) {\n        this.destination.next(everyValueMatch);\n        this.destination.complete();\n    }\n    _next(value) {\n        let result = false;\n        try {\n            result = this.predicate.call(this.thisArg, value, this.index++, this.source);\n        }\n        catch (err) {\n            this.destination.error(err);\n            return;\n        }\n        if (!result) {\n            this.notifyComplete(false);\n        }\n    }\n    _complete() {\n        this.notifyComplete(true);\n    }\n}\n"], "mappings": "AAAA,SAASA,UAAT,QAA2B,eAA3B;AACA,OAAO,SAASC,KAAT,CAAeC,SAAf,EAA0BC,OAA1B,EAAmC;EACtC,OAAQC,MAAD,IAAYA,MAAM,CAACC,IAAP,CAAY,IAAIC,aAAJ,CAAkBJ,SAAlB,EAA6BC,OAA7B,EAAsCC,MAAtC,CAAZ,CAAnB;AACH;;AACD,MAAME,aAAN,CAAoB;EAChBC,WAAW,CAACL,SAAD,EAAYC,OAAZ,EAAqBC,MAArB,EAA6B;IACpC,KAAKF,SAAL,GAAiBA,SAAjB;IACA,KAAKC,OAAL,GAAeA,OAAf;IACA,KAAKC,MAAL,GAAcA,MAAd;EACH;;EACDI,IAAI,CAACC,QAAD,EAAWL,MAAX,EAAmB;IACnB,OAAOA,MAAM,CAACM,SAAP,CAAiB,IAAIC,eAAJ,CAAoBF,QAApB,EAA8B,KAAKP,SAAnC,EAA8C,KAAKC,OAAnD,EAA4D,KAAKC,MAAjE,CAAjB,CAAP;EACH;;AARe;;AAUpB,MAAMO,eAAN,SAA8BX,UAA9B,CAAyC;EACrCO,WAAW,CAACK,WAAD,EAAcV,SAAd,EAAyBC,OAAzB,EAAkCC,MAAlC,EAA0C;IACjD,MAAMQ,WAAN;IACA,KAAKV,SAAL,GAAiBA,SAAjB;IACA,KAAKC,OAAL,GAAeA,OAAf;IACA,KAAKC,MAAL,GAAcA,MAAd;IACA,KAAKS,KAAL,GAAa,CAAb;IACA,KAAKV,OAAL,GAAeA,OAAO,IAAI,IAA1B;EACH;;EACDW,cAAc,CAACC,eAAD,EAAkB;IAC5B,KAAKH,WAAL,CAAiBI,IAAjB,CAAsBD,eAAtB;IACA,KAAKH,WAAL,CAAiBK,QAAjB;EACH;;EACDC,KAAK,CAACC,KAAD,EAAQ;IACT,IAAIC,MAAM,GAAG,KAAb;;IACA,IAAI;MACAA,MAAM,GAAG,KAAKlB,SAAL,CAAeM,IAAf,CAAoB,KAAKL,OAAzB,EAAkCgB,KAAlC,EAAyC,KAAKN,KAAL,EAAzC,EAAuD,KAAKT,MAA5D,CAAT;IACH,CAFD,CAGA,OAAOiB,GAAP,EAAY;MACR,KAAKT,WAAL,CAAiBU,KAAjB,CAAuBD,GAAvB;MACA;IACH;;IACD,IAAI,CAACD,MAAL,EAAa;MACT,KAAKN,cAAL,CAAoB,KAApB;IACH;EACJ;;EACDS,SAAS,GAAG;IACR,KAAKT,cAAL,CAAoB,IAApB;EACH;;AA5BoC"}, "metadata": {}, "sourceType": "module"}