{"ast": null, "code": "import { isConstantNode, typeOf } from '../../utils/is.js';\nimport { factory } from '../../utils/factory.js';\nimport { safeNumberType } from '../../utils/number.js';\nvar name = 'derivative';\nvar dependencies = ['typed', 'config', 'parse', 'simplify', 'equal', 'isZero', 'numeric', 'ConstantNode', 'FunctionNode', 'OperatorNode', 'ParenthesisNode', 'SymbolNode'];\nexport var createDerivative = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed,\n    config,\n    parse,\n    simplify,\n    equal,\n    isZero,\n    numeric,\n    ConstantNode,\n    FunctionNode,\n    OperatorNode,\n    ParenthesisNode,\n    SymbolNode\n  } = _ref;\n  /**\n   * Takes the derivative of an expression expressed in parser Nodes.\n   * The derivative will be taken over the supplied variable in the\n   * second parameter. If there are multiple variables in the expression,\n   * it will return a partial derivative.\n   *\n   * This uses rules of differentiation which can be found here:\n   *\n   * - [Differentiation rules (Wikipedia)](https://en.wikipedia.org/wiki/Differentiation_rules)\n   *\n   * Syntax:\n   *\n   *     math.derivative(expr, variable)\n   *     math.derivative(expr, variable, options)\n   *\n   * Examples:\n   *\n   *     math.derivative('x^2', 'x')                     // Node '2 * x'\n   *     math.derivative('x^2', 'x', {simplify: false})  // Node '2 * 1 * x ^ (2 - 1)'\n   *     math.derivative('sin(2x)', 'x'))                // Node '2 * cos(2 * x)'\n   *     math.derivative('2*x', 'x').evaluate()          // number 2\n   *     math.derivative('x^2', 'x').evaluate({x: 4})    // number 8\n   *     const f = math.parse('x^2')\n   *     const x = math.parse('x')\n   *     math.derivative(f, x)                           // Node {2 * x}\n   *\n   * See also:\n   *\n   *     simplify, parse, evaluate\n   *\n   * @param  {Node | string} expr           The expression to differentiate\n   * @param  {SymbolNode | string} variable The variable over which to differentiate\n   * @param  {{simplify: boolean}} [options]\n   *                         There is one option available, `simplify`, which\n   *                         is true by default. When false, output will not\n   *                         be simplified.\n   * @return {ConstantNode | SymbolNode | ParenthesisNode | FunctionNode | OperatorNode}    The derivative of `expr`\n   */\n\n  function plainDerivative(expr, variable) {\n    var options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {\n      simplify: true\n    };\n    var cache = new Map();\n    var variableName = variable.name;\n\n    function isConstCached(node) {\n      var cached = cache.get(node);\n\n      if (cached !== undefined) {\n        return cached;\n      }\n\n      var res = _isConst(isConstCached, node, variableName);\n\n      cache.set(node, res);\n      return res;\n    }\n\n    var res = _derivative(expr, isConstCached);\n\n    return options.simplify ? simplify(res) : res;\n  }\n\n  function parseIdentifier(string) {\n    var symbol = parse(string);\n\n    if (!symbol.isSymbolNode) {\n      throw new TypeError('Invalid variable. ' + \"Cannot parse \".concat(JSON.stringify(string), \" into a variable in function derivative\"));\n    }\n\n    return symbol;\n  }\n\n  var derivative = typed(name, {\n    'Node, SymbolNode': plainDerivative,\n    'Node, SymbolNode, Object': plainDerivative,\n    'Node, string': (node, symbol) => plainDerivative(node, parseIdentifier(symbol)),\n    'Node, string, Object': (node, symbol, options) => plainDerivative(node, parseIdentifier(symbol), options)\n    /* TODO: implement and test syntax with order of derivatives -> implement as an option {order: number}\n    'Node, SymbolNode, ConstantNode': function (expr, variable, {order}) {\n      let res = expr\n      for (let i = 0; i < order; i++) {\n        <create caching isConst>\n        res = _derivative(res, isConst)\n      }\n      return res\n    }\n    */\n\n  });\n  derivative._simplify = true;\n\n  derivative.toTex = function (deriv) {\n    return _derivTex.apply(null, deriv.args);\n  }; // FIXME: move the toTex method of derivative to latex.js. Difficulty is that it relies on parse.\n  // NOTE: the optional \"order\" parameter here is currently unused\n\n\n  var _derivTex = typed('_derivTex', {\n    'Node, SymbolNode': function Node_SymbolNode(expr, x) {\n      if (isConstantNode(expr) && typeOf(expr.value) === 'string') {\n        return _derivTex(parse(expr.value).toString(), x.toString(), 1);\n      } else {\n        return _derivTex(expr.toTex(), x.toString(), 1);\n      }\n    },\n    'Node, ConstantNode': function Node_ConstantNode(expr, x) {\n      if (typeOf(x.value) === 'string') {\n        return _derivTex(expr, parse(x.value));\n      } else {\n        throw new Error(\"The second parameter to 'derivative' is a non-string constant\");\n      }\n    },\n    'Node, SymbolNode, ConstantNode': function Node_SymbolNode_ConstantNode(expr, x, order) {\n      return _derivTex(expr.toString(), x.name, order.value);\n    },\n    'string, string, number': function string_string_number(expr, x, order) {\n      var d;\n\n      if (order === 1) {\n        d = '{d\\\\over d' + x + '}';\n      } else {\n        d = '{d^{' + order + '}\\\\over d' + x + '^{' + order + '}}';\n      }\n\n      return d + \"\\\\left[\".concat(expr, \"\\\\right]\");\n    }\n  });\n  /**\n   * Checks if a node is constants (e.g. 2 + 2).\n   * Accepts (usually memoized) version of self as the first parameter for recursive calls.\n   * Classification is done as follows:\n   *\n   *   1. ConstantNodes are constants.\n   *   2. If there exists a SymbolNode, of which we are differentiating over,\n   *      in the subtree it is not constant.\n   *\n   * @param  {function} isConst  Function that tells whether sub-expression is a constant\n   * @param  {ConstantNode | SymbolNode | ParenthesisNode | FunctionNode | OperatorNode} node\n   * @param  {string} varName     Variable that we are differentiating\n   * @return {boolean}  if node is constant\n   */\n\n\n  var _isConst = typed('_isConst', {\n    'function, ConstantNode, string': function function_ConstantNode_string() {\n      return true;\n    },\n    'function, SymbolNode, string': function function_SymbolNode_string(isConst, node, varName) {\n      // Treat other variables like constants. For reasoning, see:\n      //   https://en.wikipedia.org/wiki/Partial_derivative\n      return node.name !== varName;\n    },\n    'function, ParenthesisNode, string': function function_ParenthesisNode_string(isConst, node, varName) {\n      return isConst(node.content, varName);\n    },\n    'function, FunctionAssignmentNode, string': function function_FunctionAssignmentNode_string(isConst, node, varName) {\n      if (!node.params.includes(varName)) {\n        return true;\n      }\n\n      return isConst(node.expr, varName);\n    },\n    'function, FunctionNode | OperatorNode, string': function function_FunctionNode__OperatorNode_string(isConst, node, varName) {\n      return node.args.every(arg => isConst(arg, varName));\n    }\n  });\n  /**\n   * Applies differentiation rules.\n   *\n   * @param  {ConstantNode | SymbolNode | ParenthesisNode | FunctionNode | OperatorNode} node\n   * @param  {function} isConst  Function that tells if a node is constant\n   * @return {ConstantNode | SymbolNode | ParenthesisNode | FunctionNode | OperatorNode}    The derivative of `expr`\n   */\n\n\n  var _derivative = typed('_derivative', {\n    'ConstantNode, function': function ConstantNode_function() {\n      return createConstantNode(0);\n    },\n    'SymbolNode, function': function SymbolNode_function(node, isConst) {\n      if (isConst(node)) {\n        return createConstantNode(0);\n      }\n\n      return createConstantNode(1);\n    },\n    'ParenthesisNode, function': function ParenthesisNode_function(node, isConst) {\n      return new ParenthesisNode(_derivative(node.content, isConst));\n    },\n    'FunctionAssignmentNode, function': function FunctionAssignmentNode_function(node, isConst) {\n      if (isConst(node)) {\n        return createConstantNode(0);\n      }\n\n      return _derivative(node.expr, isConst);\n    },\n    'FunctionNode, function': function FunctionNode_function(node, isConst) {\n      if (isConst(node)) {\n        return createConstantNode(0);\n      }\n\n      var arg0 = node.args[0];\n      var arg1;\n      var div = false; // is output a fraction?\n\n      var negative = false; // is output negative?\n\n      var funcDerivative;\n\n      switch (node.name) {\n        case 'cbrt':\n          // d/dx(cbrt(x)) = 1 / (3x^(2/3))\n          div = true;\n          funcDerivative = new OperatorNode('*', 'multiply', [createConstantNode(3), new OperatorNode('^', 'pow', [arg0, new OperatorNode('/', 'divide', [createConstantNode(2), createConstantNode(3)])])]);\n          break;\n\n        case 'sqrt':\n        case 'nthRoot':\n          // d/dx(sqrt(x)) = 1 / (2*sqrt(x))\n          if (node.args.length === 1) {\n            div = true;\n            funcDerivative = new OperatorNode('*', 'multiply', [createConstantNode(2), new FunctionNode('sqrt', [arg0])]);\n          } else if (node.args.length === 2) {\n            // Rearrange from nthRoot(x, a) -> x^(1/a)\n            arg1 = new OperatorNode('/', 'divide', [createConstantNode(1), node.args[1]]);\n            return _derivative(new OperatorNode('^', 'pow', [arg0, arg1]), isConst);\n          }\n\n          break;\n\n        case 'log10':\n          arg1 = createConstantNode(10);\n\n        /* fall through! */\n\n        case 'log':\n          if (!arg1 && node.args.length === 1) {\n            // d/dx(log(x)) = 1 / x\n            funcDerivative = arg0.clone();\n            div = true;\n          } else if (node.args.length === 1 && arg1 || node.args.length === 2 && isConst(node.args[1])) {\n            // d/dx(log(x, c)) = 1 / (x*ln(c))\n            funcDerivative = new OperatorNode('*', 'multiply', [arg0.clone(), new FunctionNode('log', [arg1 || node.args[1]])]);\n            div = true;\n          } else if (node.args.length === 2) {\n            // d/dx(log(f(x), g(x))) = d/dx(log(f(x)) / log(g(x)))\n            return _derivative(new OperatorNode('/', 'divide', [new FunctionNode('log', [arg0]), new FunctionNode('log', [node.args[1]])]), isConst);\n          }\n\n          break;\n\n        case 'pow':\n          if (node.args.length === 2) {\n            // Pass to pow operator node parser\n            return _derivative(new OperatorNode('^', 'pow', [arg0, node.args[1]]), isConst);\n          }\n\n          break;\n\n        case 'exp':\n          // d/dx(e^x) = e^x\n          funcDerivative = new FunctionNode('exp', [arg0.clone()]);\n          break;\n\n        case 'sin':\n          // d/dx(sin(x)) = cos(x)\n          funcDerivative = new FunctionNode('cos', [arg0.clone()]);\n          break;\n\n        case 'cos':\n          // d/dx(cos(x)) = -sin(x)\n          funcDerivative = new OperatorNode('-', 'unaryMinus', [new FunctionNode('sin', [arg0.clone()])]);\n          break;\n\n        case 'tan':\n          // d/dx(tan(x)) = sec(x)^2\n          funcDerivative = new OperatorNode('^', 'pow', [new FunctionNode('sec', [arg0.clone()]), createConstantNode(2)]);\n          break;\n\n        case 'sec':\n          // d/dx(sec(x)) = sec(x)tan(x)\n          funcDerivative = new OperatorNode('*', 'multiply', [node, new FunctionNode('tan', [arg0.clone()])]);\n          break;\n\n        case 'csc':\n          // d/dx(csc(x)) = -csc(x)cot(x)\n          negative = true;\n          funcDerivative = new OperatorNode('*', 'multiply', [node, new FunctionNode('cot', [arg0.clone()])]);\n          break;\n\n        case 'cot':\n          // d/dx(cot(x)) = -csc(x)^2\n          negative = true;\n          funcDerivative = new OperatorNode('^', 'pow', [new FunctionNode('csc', [arg0.clone()]), createConstantNode(2)]);\n          break;\n\n        case 'asin':\n          // d/dx(asin(x)) = 1 / sqrt(1 - x^2)\n          div = true;\n          funcDerivative = new FunctionNode('sqrt', [new OperatorNode('-', 'subtract', [createConstantNode(1), new OperatorNode('^', 'pow', [arg0.clone(), createConstantNode(2)])])]);\n          break;\n\n        case 'acos':\n          // d/dx(acos(x)) = -1 / sqrt(1 - x^2)\n          div = true;\n          negative = true;\n          funcDerivative = new FunctionNode('sqrt', [new OperatorNode('-', 'subtract', [createConstantNode(1), new OperatorNode('^', 'pow', [arg0.clone(), createConstantNode(2)])])]);\n          break;\n\n        case 'atan':\n          // d/dx(atan(x)) = 1 / (x^2 + 1)\n          div = true;\n          funcDerivative = new OperatorNode('+', 'add', [new OperatorNode('^', 'pow', [arg0.clone(), createConstantNode(2)]), createConstantNode(1)]);\n          break;\n\n        case 'asec':\n          // d/dx(asec(x)) = 1 / (|x|*sqrt(x^2 - 1))\n          div = true;\n          funcDerivative = new OperatorNode('*', 'multiply', [new FunctionNode('abs', [arg0.clone()]), new FunctionNode('sqrt', [new OperatorNode('-', 'subtract', [new OperatorNode('^', 'pow', [arg0.clone(), createConstantNode(2)]), createConstantNode(1)])])]);\n          break;\n\n        case 'acsc':\n          // d/dx(acsc(x)) = -1 / (|x|*sqrt(x^2 - 1))\n          div = true;\n          negative = true;\n          funcDerivative = new OperatorNode('*', 'multiply', [new FunctionNode('abs', [arg0.clone()]), new FunctionNode('sqrt', [new OperatorNode('-', 'subtract', [new OperatorNode('^', 'pow', [arg0.clone(), createConstantNode(2)]), createConstantNode(1)])])]);\n          break;\n\n        case 'acot':\n          // d/dx(acot(x)) = -1 / (x^2 + 1)\n          div = true;\n          negative = true;\n          funcDerivative = new OperatorNode('+', 'add', [new OperatorNode('^', 'pow', [arg0.clone(), createConstantNode(2)]), createConstantNode(1)]);\n          break;\n\n        case 'sinh':\n          // d/dx(sinh(x)) = cosh(x)\n          funcDerivative = new FunctionNode('cosh', [arg0.clone()]);\n          break;\n\n        case 'cosh':\n          // d/dx(cosh(x)) = sinh(x)\n          funcDerivative = new FunctionNode('sinh', [arg0.clone()]);\n          break;\n\n        case 'tanh':\n          // d/dx(tanh(x)) = sech(x)^2\n          funcDerivative = new OperatorNode('^', 'pow', [new FunctionNode('sech', [arg0.clone()]), createConstantNode(2)]);\n          break;\n\n        case 'sech':\n          // d/dx(sech(x)) = -sech(x)tanh(x)\n          negative = true;\n          funcDerivative = new OperatorNode('*', 'multiply', [node, new FunctionNode('tanh', [arg0.clone()])]);\n          break;\n\n        case 'csch':\n          // d/dx(csch(x)) = -csch(x)coth(x)\n          negative = true;\n          funcDerivative = new OperatorNode('*', 'multiply', [node, new FunctionNode('coth', [arg0.clone()])]);\n          break;\n\n        case 'coth':\n          // d/dx(coth(x)) = -csch(x)^2\n          negative = true;\n          funcDerivative = new OperatorNode('^', 'pow', [new FunctionNode('csch', [arg0.clone()]), createConstantNode(2)]);\n          break;\n\n        case 'asinh':\n          // d/dx(asinh(x)) = 1 / sqrt(x^2 + 1)\n          div = true;\n          funcDerivative = new FunctionNode('sqrt', [new OperatorNode('+', 'add', [new OperatorNode('^', 'pow', [arg0.clone(), createConstantNode(2)]), createConstantNode(1)])]);\n          break;\n\n        case 'acosh':\n          // d/dx(acosh(x)) = 1 / sqrt(x^2 - 1); XXX potentially only for x >= 1 (the real spectrum)\n          div = true;\n          funcDerivative = new FunctionNode('sqrt', [new OperatorNode('-', 'subtract', [new OperatorNode('^', 'pow', [arg0.clone(), createConstantNode(2)]), createConstantNode(1)])]);\n          break;\n\n        case 'atanh':\n          // d/dx(atanh(x)) = 1 / (1 - x^2)\n          div = true;\n          funcDerivative = new OperatorNode('-', 'subtract', [createConstantNode(1), new OperatorNode('^', 'pow', [arg0.clone(), createConstantNode(2)])]);\n          break;\n\n        case 'asech':\n          // d/dx(asech(x)) = -1 / (x*sqrt(1 - x^2))\n          div = true;\n          negative = true;\n          funcDerivative = new OperatorNode('*', 'multiply', [arg0.clone(), new FunctionNode('sqrt', [new OperatorNode('-', 'subtract', [createConstantNode(1), new OperatorNode('^', 'pow', [arg0.clone(), createConstantNode(2)])])])]);\n          break;\n\n        case 'acsch':\n          // d/dx(acsch(x)) = -1 / (|x|*sqrt(x^2 + 1))\n          div = true;\n          negative = true;\n          funcDerivative = new OperatorNode('*', 'multiply', [new FunctionNode('abs', [arg0.clone()]), new FunctionNode('sqrt', [new OperatorNode('+', 'add', [new OperatorNode('^', 'pow', [arg0.clone(), createConstantNode(2)]), createConstantNode(1)])])]);\n          break;\n\n        case 'acoth':\n          // d/dx(acoth(x)) = -1 / (1 - x^2)\n          div = true;\n          negative = true;\n          funcDerivative = new OperatorNode('-', 'subtract', [createConstantNode(1), new OperatorNode('^', 'pow', [arg0.clone(), createConstantNode(2)])]);\n          break;\n\n        case 'abs':\n          // d/dx(abs(x)) = abs(x)/x\n          funcDerivative = new OperatorNode('/', 'divide', [new FunctionNode(new SymbolNode('abs'), [arg0.clone()]), arg0.clone()]);\n          break;\n\n        case 'gamma': // Needs digamma function, d/dx(gamma(x)) = gamma(x)digamma(x)\n\n        default:\n          throw new Error('Cannot process function \"' + node.name + '\" in derivative: ' + 'the function is not supported, undefined, or the number of arguments passed to it are not supported');\n      }\n\n      var op, func;\n\n      if (div) {\n        op = '/';\n        func = 'divide';\n      } else {\n        op = '*';\n        func = 'multiply';\n      }\n      /* Apply chain rule to all functions:\n         F(x)  = f(g(x))\n         F'(x) = g'(x)*f'(g(x)) */\n\n\n      var chainDerivative = _derivative(arg0, isConst);\n\n      if (negative) {\n        chainDerivative = new OperatorNode('-', 'unaryMinus', [chainDerivative]);\n      }\n\n      return new OperatorNode(op, func, [chainDerivative, funcDerivative]);\n    },\n    'OperatorNode, function': function OperatorNode_function(node, isConst) {\n      if (isConst(node)) {\n        return createConstantNode(0);\n      }\n\n      if (node.op === '+') {\n        // d/dx(sum(f(x)) = sum(f'(x))\n        return new OperatorNode(node.op, node.fn, node.args.map(function (arg) {\n          return _derivative(arg, isConst);\n        }));\n      }\n\n      if (node.op === '-') {\n        // d/dx(+/-f(x)) = +/-f'(x)\n        if (node.isUnary()) {\n          return new OperatorNode(node.op, node.fn, [_derivative(node.args[0], isConst)]);\n        } // Linearity of differentiation, d/dx(f(x) +/- g(x)) = f'(x) +/- g'(x)\n\n\n        if (node.isBinary()) {\n          return new OperatorNode(node.op, node.fn, [_derivative(node.args[0], isConst), _derivative(node.args[1], isConst)]);\n        }\n      }\n\n      if (node.op === '*') {\n        // d/dx(c*f(x)) = c*f'(x)\n        var constantTerms = node.args.filter(function (arg) {\n          return isConst(arg);\n        });\n\n        if (constantTerms.length > 0) {\n          var nonConstantTerms = node.args.filter(function (arg) {\n            return !isConst(arg);\n          });\n          var nonConstantNode = nonConstantTerms.length === 1 ? nonConstantTerms[0] : new OperatorNode('*', 'multiply', nonConstantTerms);\n          var newArgs = constantTerms.concat(_derivative(nonConstantNode, isConst));\n          return new OperatorNode('*', 'multiply', newArgs);\n        } // Product Rule, d/dx(f(x)*g(x)) = f'(x)*g(x) + f(x)*g'(x)\n\n\n        return new OperatorNode('+', 'add', node.args.map(function (argOuter) {\n          return new OperatorNode('*', 'multiply', node.args.map(function (argInner) {\n            return argInner === argOuter ? _derivative(argInner, isConst) : argInner.clone();\n          }));\n        }));\n      }\n\n      if (node.op === '/' && node.isBinary()) {\n        var arg0 = node.args[0];\n        var arg1 = node.args[1]; // d/dx(f(x) / c) = f'(x) / c\n\n        if (isConst(arg1)) {\n          return new OperatorNode('/', 'divide', [_derivative(arg0, isConst), arg1]);\n        } // Reciprocal Rule, d/dx(c / f(x)) = -c(f'(x)/f(x)^2)\n\n\n        if (isConst(arg0)) {\n          return new OperatorNode('*', 'multiply', [new OperatorNode('-', 'unaryMinus', [arg0]), new OperatorNode('/', 'divide', [_derivative(arg1, isConst), new OperatorNode('^', 'pow', [arg1.clone(), createConstantNode(2)])])]);\n        } // Quotient rule, d/dx(f(x) / g(x)) = (f'(x)g(x) - f(x)g'(x)) / g(x)^2\n\n\n        return new OperatorNode('/', 'divide', [new OperatorNode('-', 'subtract', [new OperatorNode('*', 'multiply', [_derivative(arg0, isConst), arg1.clone()]), new OperatorNode('*', 'multiply', [arg0.clone(), _derivative(arg1, isConst)])]), new OperatorNode('^', 'pow', [arg1.clone(), createConstantNode(2)])]);\n      }\n\n      if (node.op === '^' && node.isBinary()) {\n        var _arg = node.args[0];\n        var _arg2 = node.args[1];\n\n        if (isConst(_arg)) {\n          // If is secretly constant; 0^f(x) = 1 (in JS), 1^f(x) = 1\n          if (isConstantNode(_arg) && (isZero(_arg.value) || equal(_arg.value, 1))) {\n            return createConstantNode(0);\n          } // d/dx(c^f(x)) = c^f(x)*ln(c)*f'(x)\n\n\n          return new OperatorNode('*', 'multiply', [node, new OperatorNode('*', 'multiply', [new FunctionNode('log', [_arg.clone()]), _derivative(_arg2.clone(), isConst)])]);\n        }\n\n        if (isConst(_arg2)) {\n          if (isConstantNode(_arg2)) {\n            // If is secretly constant; f(x)^0 = 1 -> d/dx(1) = 0\n            if (isZero(_arg2.value)) {\n              return createConstantNode(0);\n            } // Ignore exponent; f(x)^1 = f(x)\n\n\n            if (equal(_arg2.value, 1)) {\n              return _derivative(_arg, isConst);\n            }\n          } // Elementary Power Rule, d/dx(f(x)^c) = c*f'(x)*f(x)^(c-1)\n\n\n          var powMinusOne = new OperatorNode('^', 'pow', [_arg.clone(), new OperatorNode('-', 'subtract', [_arg2, createConstantNode(1)])]);\n          return new OperatorNode('*', 'multiply', [_arg2.clone(), new OperatorNode('*', 'multiply', [_derivative(_arg, isConst), powMinusOne])]);\n        } // Functional Power Rule, d/dx(f^g) = f^g*[f'*(g/f) + g'ln(f)]\n\n\n        return new OperatorNode('*', 'multiply', [new OperatorNode('^', 'pow', [_arg.clone(), _arg2.clone()]), new OperatorNode('+', 'add', [new OperatorNode('*', 'multiply', [_derivative(_arg, isConst), new OperatorNode('/', 'divide', [_arg2.clone(), _arg.clone()])]), new OperatorNode('*', 'multiply', [_derivative(_arg2, isConst), new FunctionNode('log', [_arg.clone()])])])]);\n      }\n\n      throw new Error('Cannot process operator \"' + node.op + '\" in derivative: ' + 'the operator is not supported, undefined, or the number of arguments passed to it are not supported');\n    }\n  });\n  /**\n   * Helper function to create a constant node with a specific type\n   * (number, BigNumber, Fraction)\n   * @param {number} value\n   * @param {string} [valueType]\n   * @return {ConstantNode}\n   */\n\n\n  function createConstantNode(value, valueType) {\n    return new ConstantNode(numeric(value, valueType || safeNumberType(String(value), config)));\n  }\n\n  return derivative;\n});", "map": {"version": 3, "names": ["isConstantNode", "typeOf", "factory", "safeNumberType", "name", "dependencies", "createDerivative", "_ref", "typed", "config", "parse", "simplify", "equal", "isZero", "numeric", "ConstantNode", "FunctionNode", "OperatorNode", "ParenthesisNode", "SymbolNode", "plainDerivative", "expr", "variable", "options", "arguments", "length", "undefined", "cache", "Map", "variableName", "isConstCached", "node", "cached", "get", "res", "_isConst", "set", "_derivative", "parseIdentifier", "string", "symbol", "isSymbolNode", "TypeError", "concat", "JSON", "stringify", "derivative", "_simplify", "toTex", "deriv", "_derivTex", "apply", "args", "Node_SymbolNode", "x", "value", "toString", "Node_ConstantNode", "Error", "Node_SymbolNode_ConstantNode", "order", "string_string_number", "d", "function_ConstantNode_string", "function_SymbolNode_string", "isConst", "varName", "function_ParenthesisNode_string", "content", "function_FunctionAssignmentNode_string", "params", "includes", "function_FunctionNode__OperatorNode_string", "every", "arg", "ConstantNode_function", "createConstantNode", "SymbolNode_function", "ParenthesisNode_function", "FunctionAssignmentNode_function", "FunctionNode_function", "arg0", "arg1", "div", "negative", "funcDerivative", "clone", "op", "func", "chainDerivative", "OperatorNode_function", "fn", "map", "isUnary", "isBinary", "constantTerms", "filter", "nonConstantTerms", "nonConstantNode", "newArgs", "argOuter", "argInner", "_arg", "_arg2", "powMinusOne", "valueType", "String"], "sources": ["D:/work/joyserver/client/node_modules/mathjs/lib/esm/function/algebra/derivative.js"], "sourcesContent": ["import { isConstantNode, typeOf } from '../../utils/is.js';\nimport { factory } from '../../utils/factory.js';\nimport { safeNumberType } from '../../utils/number.js';\nvar name = 'derivative';\nvar dependencies = ['typed', 'config', 'parse', 'simplify', 'equal', 'isZero', 'numeric', 'ConstantNode', 'FunctionNode', 'OperatorNode', 'ParenthesisNode', 'SymbolNode'];\nexport var createDerivative = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed,\n    config,\n    parse,\n    simplify,\n    equal,\n    isZero,\n    numeric,\n    ConstantNode,\n    FunctionNode,\n    OperatorNode,\n    ParenthesisNode,\n    SymbolNode\n  } = _ref;\n  /**\n   * Takes the derivative of an expression expressed in parser Nodes.\n   * The derivative will be taken over the supplied variable in the\n   * second parameter. If there are multiple variables in the expression,\n   * it will return a partial derivative.\n   *\n   * This uses rules of differentiation which can be found here:\n   *\n   * - [Differentiation rules (Wikipedia)](https://en.wikipedia.org/wiki/Differentiation_rules)\n   *\n   * Syntax:\n   *\n   *     math.derivative(expr, variable)\n   *     math.derivative(expr, variable, options)\n   *\n   * Examples:\n   *\n   *     math.derivative('x^2', 'x')                     // Node '2 * x'\n   *     math.derivative('x^2', 'x', {simplify: false})  // Node '2 * 1 * x ^ (2 - 1)'\n   *     math.derivative('sin(2x)', 'x'))                // Node '2 * cos(2 * x)'\n   *     math.derivative('2*x', 'x').evaluate()          // number 2\n   *     math.derivative('x^2', 'x').evaluate({x: 4})    // number 8\n   *     const f = math.parse('x^2')\n   *     const x = math.parse('x')\n   *     math.derivative(f, x)                           // Node {2 * x}\n   *\n   * See also:\n   *\n   *     simplify, parse, evaluate\n   *\n   * @param  {Node | string} expr           The expression to differentiate\n   * @param  {SymbolNode | string} variable The variable over which to differentiate\n   * @param  {{simplify: boolean}} [options]\n   *                         There is one option available, `simplify`, which\n   *                         is true by default. When false, output will not\n   *                         be simplified.\n   * @return {ConstantNode | SymbolNode | ParenthesisNode | FunctionNode | OperatorNode}    The derivative of `expr`\n   */\n  function plainDerivative(expr, variable) {\n    var options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {\n      simplify: true\n    };\n    var cache = new Map();\n    var variableName = variable.name;\n    function isConstCached(node) {\n      var cached = cache.get(node);\n      if (cached !== undefined) {\n        return cached;\n      }\n      var res = _isConst(isConstCached, node, variableName);\n      cache.set(node, res);\n      return res;\n    }\n    var res = _derivative(expr, isConstCached);\n    return options.simplify ? simplify(res) : res;\n  }\n  function parseIdentifier(string) {\n    var symbol = parse(string);\n    if (!symbol.isSymbolNode) {\n      throw new TypeError('Invalid variable. ' + \"Cannot parse \".concat(JSON.stringify(string), \" into a variable in function derivative\"));\n    }\n    return symbol;\n  }\n  var derivative = typed(name, {\n    'Node, SymbolNode': plainDerivative,\n    'Node, SymbolNode, Object': plainDerivative,\n    'Node, string': (node, symbol) => plainDerivative(node, parseIdentifier(symbol)),\n    'Node, string, Object': (node, symbol, options) => plainDerivative(node, parseIdentifier(symbol), options)\n\n    /* TODO: implement and test syntax with order of derivatives -> implement as an option {order: number}\n    'Node, SymbolNode, ConstantNode': function (expr, variable, {order}) {\n      let res = expr\n      for (let i = 0; i < order; i++) {\n        <create caching isConst>\n        res = _derivative(res, isConst)\n      }\n      return res\n    }\n    */\n  });\n  derivative._simplify = true;\n  derivative.toTex = function (deriv) {\n    return _derivTex.apply(null, deriv.args);\n  };\n\n  // FIXME: move the toTex method of derivative to latex.js. Difficulty is that it relies on parse.\n  // NOTE: the optional \"order\" parameter here is currently unused\n  var _derivTex = typed('_derivTex', {\n    'Node, SymbolNode': function Node_SymbolNode(expr, x) {\n      if (isConstantNode(expr) && typeOf(expr.value) === 'string') {\n        return _derivTex(parse(expr.value).toString(), x.toString(), 1);\n      } else {\n        return _derivTex(expr.toTex(), x.toString(), 1);\n      }\n    },\n    'Node, ConstantNode': function Node_ConstantNode(expr, x) {\n      if (typeOf(x.value) === 'string') {\n        return _derivTex(expr, parse(x.value));\n      } else {\n        throw new Error(\"The second parameter to 'derivative' is a non-string constant\");\n      }\n    },\n    'Node, SymbolNode, ConstantNode': function Node_SymbolNode_ConstantNode(expr, x, order) {\n      return _derivTex(expr.toString(), x.name, order.value);\n    },\n    'string, string, number': function string_string_number(expr, x, order) {\n      var d;\n      if (order === 1) {\n        d = '{d\\\\over d' + x + '}';\n      } else {\n        d = '{d^{' + order + '}\\\\over d' + x + '^{' + order + '}}';\n      }\n      return d + \"\\\\left[\".concat(expr, \"\\\\right]\");\n    }\n  });\n\n  /**\n   * Checks if a node is constants (e.g. 2 + 2).\n   * Accepts (usually memoized) version of self as the first parameter for recursive calls.\n   * Classification is done as follows:\n   *\n   *   1. ConstantNodes are constants.\n   *   2. If there exists a SymbolNode, of which we are differentiating over,\n   *      in the subtree it is not constant.\n   *\n   * @param  {function} isConst  Function that tells whether sub-expression is a constant\n   * @param  {ConstantNode | SymbolNode | ParenthesisNode | FunctionNode | OperatorNode} node\n   * @param  {string} varName     Variable that we are differentiating\n   * @return {boolean}  if node is constant\n   */\n  var _isConst = typed('_isConst', {\n    'function, ConstantNode, string': function function_ConstantNode_string() {\n      return true;\n    },\n    'function, SymbolNode, string': function function_SymbolNode_string(isConst, node, varName) {\n      // Treat other variables like constants. For reasoning, see:\n      //   https://en.wikipedia.org/wiki/Partial_derivative\n      return node.name !== varName;\n    },\n    'function, ParenthesisNode, string': function function_ParenthesisNode_string(isConst, node, varName) {\n      return isConst(node.content, varName);\n    },\n    'function, FunctionAssignmentNode, string': function function_FunctionAssignmentNode_string(isConst, node, varName) {\n      if (!node.params.includes(varName)) {\n        return true;\n      }\n      return isConst(node.expr, varName);\n    },\n    'function, FunctionNode | OperatorNode, string': function function_FunctionNode__OperatorNode_string(isConst, node, varName) {\n      return node.args.every(arg => isConst(arg, varName));\n    }\n  });\n\n  /**\n   * Applies differentiation rules.\n   *\n   * @param  {ConstantNode | SymbolNode | ParenthesisNode | FunctionNode | OperatorNode} node\n   * @param  {function} isConst  Function that tells if a node is constant\n   * @return {ConstantNode | SymbolNode | ParenthesisNode | FunctionNode | OperatorNode}    The derivative of `expr`\n   */\n  var _derivative = typed('_derivative', {\n    'ConstantNode, function': function ConstantNode_function() {\n      return createConstantNode(0);\n    },\n    'SymbolNode, function': function SymbolNode_function(node, isConst) {\n      if (isConst(node)) {\n        return createConstantNode(0);\n      }\n      return createConstantNode(1);\n    },\n    'ParenthesisNode, function': function ParenthesisNode_function(node, isConst) {\n      return new ParenthesisNode(_derivative(node.content, isConst));\n    },\n    'FunctionAssignmentNode, function': function FunctionAssignmentNode_function(node, isConst) {\n      if (isConst(node)) {\n        return createConstantNode(0);\n      }\n      return _derivative(node.expr, isConst);\n    },\n    'FunctionNode, function': function FunctionNode_function(node, isConst) {\n      if (isConst(node)) {\n        return createConstantNode(0);\n      }\n      var arg0 = node.args[0];\n      var arg1;\n      var div = false; // is output a fraction?\n      var negative = false; // is output negative?\n\n      var funcDerivative;\n      switch (node.name) {\n        case 'cbrt':\n          // d/dx(cbrt(x)) = 1 / (3x^(2/3))\n          div = true;\n          funcDerivative = new OperatorNode('*', 'multiply', [createConstantNode(3), new OperatorNode('^', 'pow', [arg0, new OperatorNode('/', 'divide', [createConstantNode(2), createConstantNode(3)])])]);\n          break;\n        case 'sqrt':\n        case 'nthRoot':\n          // d/dx(sqrt(x)) = 1 / (2*sqrt(x))\n          if (node.args.length === 1) {\n            div = true;\n            funcDerivative = new OperatorNode('*', 'multiply', [createConstantNode(2), new FunctionNode('sqrt', [arg0])]);\n          } else if (node.args.length === 2) {\n            // Rearrange from nthRoot(x, a) -> x^(1/a)\n            arg1 = new OperatorNode('/', 'divide', [createConstantNode(1), node.args[1]]);\n            return _derivative(new OperatorNode('^', 'pow', [arg0, arg1]), isConst);\n          }\n          break;\n        case 'log10':\n          arg1 = createConstantNode(10);\n        /* fall through! */\n        case 'log':\n          if (!arg1 && node.args.length === 1) {\n            // d/dx(log(x)) = 1 / x\n            funcDerivative = arg0.clone();\n            div = true;\n          } else if (node.args.length === 1 && arg1 || node.args.length === 2 && isConst(node.args[1])) {\n            // d/dx(log(x, c)) = 1 / (x*ln(c))\n            funcDerivative = new OperatorNode('*', 'multiply', [arg0.clone(), new FunctionNode('log', [arg1 || node.args[1]])]);\n            div = true;\n          } else if (node.args.length === 2) {\n            // d/dx(log(f(x), g(x))) = d/dx(log(f(x)) / log(g(x)))\n            return _derivative(new OperatorNode('/', 'divide', [new FunctionNode('log', [arg0]), new FunctionNode('log', [node.args[1]])]), isConst);\n          }\n          break;\n        case 'pow':\n          if (node.args.length === 2) {\n            // Pass to pow operator node parser\n            return _derivative(new OperatorNode('^', 'pow', [arg0, node.args[1]]), isConst);\n          }\n          break;\n        case 'exp':\n          // d/dx(e^x) = e^x\n          funcDerivative = new FunctionNode('exp', [arg0.clone()]);\n          break;\n        case 'sin':\n          // d/dx(sin(x)) = cos(x)\n          funcDerivative = new FunctionNode('cos', [arg0.clone()]);\n          break;\n        case 'cos':\n          // d/dx(cos(x)) = -sin(x)\n          funcDerivative = new OperatorNode('-', 'unaryMinus', [new FunctionNode('sin', [arg0.clone()])]);\n          break;\n        case 'tan':\n          // d/dx(tan(x)) = sec(x)^2\n          funcDerivative = new OperatorNode('^', 'pow', [new FunctionNode('sec', [arg0.clone()]), createConstantNode(2)]);\n          break;\n        case 'sec':\n          // d/dx(sec(x)) = sec(x)tan(x)\n          funcDerivative = new OperatorNode('*', 'multiply', [node, new FunctionNode('tan', [arg0.clone()])]);\n          break;\n        case 'csc':\n          // d/dx(csc(x)) = -csc(x)cot(x)\n          negative = true;\n          funcDerivative = new OperatorNode('*', 'multiply', [node, new FunctionNode('cot', [arg0.clone()])]);\n          break;\n        case 'cot':\n          // d/dx(cot(x)) = -csc(x)^2\n          negative = true;\n          funcDerivative = new OperatorNode('^', 'pow', [new FunctionNode('csc', [arg0.clone()]), createConstantNode(2)]);\n          break;\n        case 'asin':\n          // d/dx(asin(x)) = 1 / sqrt(1 - x^2)\n          div = true;\n          funcDerivative = new FunctionNode('sqrt', [new OperatorNode('-', 'subtract', [createConstantNode(1), new OperatorNode('^', 'pow', [arg0.clone(), createConstantNode(2)])])]);\n          break;\n        case 'acos':\n          // d/dx(acos(x)) = -1 / sqrt(1 - x^2)\n          div = true;\n          negative = true;\n          funcDerivative = new FunctionNode('sqrt', [new OperatorNode('-', 'subtract', [createConstantNode(1), new OperatorNode('^', 'pow', [arg0.clone(), createConstantNode(2)])])]);\n          break;\n        case 'atan':\n          // d/dx(atan(x)) = 1 / (x^2 + 1)\n          div = true;\n          funcDerivative = new OperatorNode('+', 'add', [new OperatorNode('^', 'pow', [arg0.clone(), createConstantNode(2)]), createConstantNode(1)]);\n          break;\n        case 'asec':\n          // d/dx(asec(x)) = 1 / (|x|*sqrt(x^2 - 1))\n          div = true;\n          funcDerivative = new OperatorNode('*', 'multiply', [new FunctionNode('abs', [arg0.clone()]), new FunctionNode('sqrt', [new OperatorNode('-', 'subtract', [new OperatorNode('^', 'pow', [arg0.clone(), createConstantNode(2)]), createConstantNode(1)])])]);\n          break;\n        case 'acsc':\n          // d/dx(acsc(x)) = -1 / (|x|*sqrt(x^2 - 1))\n          div = true;\n          negative = true;\n          funcDerivative = new OperatorNode('*', 'multiply', [new FunctionNode('abs', [arg0.clone()]), new FunctionNode('sqrt', [new OperatorNode('-', 'subtract', [new OperatorNode('^', 'pow', [arg0.clone(), createConstantNode(2)]), createConstantNode(1)])])]);\n          break;\n        case 'acot':\n          // d/dx(acot(x)) = -1 / (x^2 + 1)\n          div = true;\n          negative = true;\n          funcDerivative = new OperatorNode('+', 'add', [new OperatorNode('^', 'pow', [arg0.clone(), createConstantNode(2)]), createConstantNode(1)]);\n          break;\n        case 'sinh':\n          // d/dx(sinh(x)) = cosh(x)\n          funcDerivative = new FunctionNode('cosh', [arg0.clone()]);\n          break;\n        case 'cosh':\n          // d/dx(cosh(x)) = sinh(x)\n          funcDerivative = new FunctionNode('sinh', [arg0.clone()]);\n          break;\n        case 'tanh':\n          // d/dx(tanh(x)) = sech(x)^2\n          funcDerivative = new OperatorNode('^', 'pow', [new FunctionNode('sech', [arg0.clone()]), createConstantNode(2)]);\n          break;\n        case 'sech':\n          // d/dx(sech(x)) = -sech(x)tanh(x)\n          negative = true;\n          funcDerivative = new OperatorNode('*', 'multiply', [node, new FunctionNode('tanh', [arg0.clone()])]);\n          break;\n        case 'csch':\n          // d/dx(csch(x)) = -csch(x)coth(x)\n          negative = true;\n          funcDerivative = new OperatorNode('*', 'multiply', [node, new FunctionNode('coth', [arg0.clone()])]);\n          break;\n        case 'coth':\n          // d/dx(coth(x)) = -csch(x)^2\n          negative = true;\n          funcDerivative = new OperatorNode('^', 'pow', [new FunctionNode('csch', [arg0.clone()]), createConstantNode(2)]);\n          break;\n        case 'asinh':\n          // d/dx(asinh(x)) = 1 / sqrt(x^2 + 1)\n          div = true;\n          funcDerivative = new FunctionNode('sqrt', [new OperatorNode('+', 'add', [new OperatorNode('^', 'pow', [arg0.clone(), createConstantNode(2)]), createConstantNode(1)])]);\n          break;\n        case 'acosh':\n          // d/dx(acosh(x)) = 1 / sqrt(x^2 - 1); XXX potentially only for x >= 1 (the real spectrum)\n          div = true;\n          funcDerivative = new FunctionNode('sqrt', [new OperatorNode('-', 'subtract', [new OperatorNode('^', 'pow', [arg0.clone(), createConstantNode(2)]), createConstantNode(1)])]);\n          break;\n        case 'atanh':\n          // d/dx(atanh(x)) = 1 / (1 - x^2)\n          div = true;\n          funcDerivative = new OperatorNode('-', 'subtract', [createConstantNode(1), new OperatorNode('^', 'pow', [arg0.clone(), createConstantNode(2)])]);\n          break;\n        case 'asech':\n          // d/dx(asech(x)) = -1 / (x*sqrt(1 - x^2))\n          div = true;\n          negative = true;\n          funcDerivative = new OperatorNode('*', 'multiply', [arg0.clone(), new FunctionNode('sqrt', [new OperatorNode('-', 'subtract', [createConstantNode(1), new OperatorNode('^', 'pow', [arg0.clone(), createConstantNode(2)])])])]);\n          break;\n        case 'acsch':\n          // d/dx(acsch(x)) = -1 / (|x|*sqrt(x^2 + 1))\n          div = true;\n          negative = true;\n          funcDerivative = new OperatorNode('*', 'multiply', [new FunctionNode('abs', [arg0.clone()]), new FunctionNode('sqrt', [new OperatorNode('+', 'add', [new OperatorNode('^', 'pow', [arg0.clone(), createConstantNode(2)]), createConstantNode(1)])])]);\n          break;\n        case 'acoth':\n          // d/dx(acoth(x)) = -1 / (1 - x^2)\n          div = true;\n          negative = true;\n          funcDerivative = new OperatorNode('-', 'subtract', [createConstantNode(1), new OperatorNode('^', 'pow', [arg0.clone(), createConstantNode(2)])]);\n          break;\n        case 'abs':\n          // d/dx(abs(x)) = abs(x)/x\n          funcDerivative = new OperatorNode('/', 'divide', [new FunctionNode(new SymbolNode('abs'), [arg0.clone()]), arg0.clone()]);\n          break;\n        case 'gamma': // Needs digamma function, d/dx(gamma(x)) = gamma(x)digamma(x)\n        default:\n          throw new Error('Cannot process function \"' + node.name + '\" in derivative: ' + 'the function is not supported, undefined, or the number of arguments passed to it are not supported');\n      }\n      var op, func;\n      if (div) {\n        op = '/';\n        func = 'divide';\n      } else {\n        op = '*';\n        func = 'multiply';\n      }\n\n      /* Apply chain rule to all functions:\n         F(x)  = f(g(x))\n         F'(x) = g'(x)*f'(g(x)) */\n      var chainDerivative = _derivative(arg0, isConst);\n      if (negative) {\n        chainDerivative = new OperatorNode('-', 'unaryMinus', [chainDerivative]);\n      }\n      return new OperatorNode(op, func, [chainDerivative, funcDerivative]);\n    },\n    'OperatorNode, function': function OperatorNode_function(node, isConst) {\n      if (isConst(node)) {\n        return createConstantNode(0);\n      }\n      if (node.op === '+') {\n        // d/dx(sum(f(x)) = sum(f'(x))\n        return new OperatorNode(node.op, node.fn, node.args.map(function (arg) {\n          return _derivative(arg, isConst);\n        }));\n      }\n      if (node.op === '-') {\n        // d/dx(+/-f(x)) = +/-f'(x)\n        if (node.isUnary()) {\n          return new OperatorNode(node.op, node.fn, [_derivative(node.args[0], isConst)]);\n        }\n\n        // Linearity of differentiation, d/dx(f(x) +/- g(x)) = f'(x) +/- g'(x)\n        if (node.isBinary()) {\n          return new OperatorNode(node.op, node.fn, [_derivative(node.args[0], isConst), _derivative(node.args[1], isConst)]);\n        }\n      }\n      if (node.op === '*') {\n        // d/dx(c*f(x)) = c*f'(x)\n        var constantTerms = node.args.filter(function (arg) {\n          return isConst(arg);\n        });\n        if (constantTerms.length > 0) {\n          var nonConstantTerms = node.args.filter(function (arg) {\n            return !isConst(arg);\n          });\n          var nonConstantNode = nonConstantTerms.length === 1 ? nonConstantTerms[0] : new OperatorNode('*', 'multiply', nonConstantTerms);\n          var newArgs = constantTerms.concat(_derivative(nonConstantNode, isConst));\n          return new OperatorNode('*', 'multiply', newArgs);\n        }\n\n        // Product Rule, d/dx(f(x)*g(x)) = f'(x)*g(x) + f(x)*g'(x)\n        return new OperatorNode('+', 'add', node.args.map(function (argOuter) {\n          return new OperatorNode('*', 'multiply', node.args.map(function (argInner) {\n            return argInner === argOuter ? _derivative(argInner, isConst) : argInner.clone();\n          }));\n        }));\n      }\n      if (node.op === '/' && node.isBinary()) {\n        var arg0 = node.args[0];\n        var arg1 = node.args[1];\n\n        // d/dx(f(x) / c) = f'(x) / c\n        if (isConst(arg1)) {\n          return new OperatorNode('/', 'divide', [_derivative(arg0, isConst), arg1]);\n        }\n\n        // Reciprocal Rule, d/dx(c / f(x)) = -c(f'(x)/f(x)^2)\n        if (isConst(arg0)) {\n          return new OperatorNode('*', 'multiply', [new OperatorNode('-', 'unaryMinus', [arg0]), new OperatorNode('/', 'divide', [_derivative(arg1, isConst), new OperatorNode('^', 'pow', [arg1.clone(), createConstantNode(2)])])]);\n        }\n\n        // Quotient rule, d/dx(f(x) / g(x)) = (f'(x)g(x) - f(x)g'(x)) / g(x)^2\n        return new OperatorNode('/', 'divide', [new OperatorNode('-', 'subtract', [new OperatorNode('*', 'multiply', [_derivative(arg0, isConst), arg1.clone()]), new OperatorNode('*', 'multiply', [arg0.clone(), _derivative(arg1, isConst)])]), new OperatorNode('^', 'pow', [arg1.clone(), createConstantNode(2)])]);\n      }\n      if (node.op === '^' && node.isBinary()) {\n        var _arg = node.args[0];\n        var _arg2 = node.args[1];\n        if (isConst(_arg)) {\n          // If is secretly constant; 0^f(x) = 1 (in JS), 1^f(x) = 1\n          if (isConstantNode(_arg) && (isZero(_arg.value) || equal(_arg.value, 1))) {\n            return createConstantNode(0);\n          }\n\n          // d/dx(c^f(x)) = c^f(x)*ln(c)*f'(x)\n          return new OperatorNode('*', 'multiply', [node, new OperatorNode('*', 'multiply', [new FunctionNode('log', [_arg.clone()]), _derivative(_arg2.clone(), isConst)])]);\n        }\n        if (isConst(_arg2)) {\n          if (isConstantNode(_arg2)) {\n            // If is secretly constant; f(x)^0 = 1 -> d/dx(1) = 0\n            if (isZero(_arg2.value)) {\n              return createConstantNode(0);\n            }\n            // Ignore exponent; f(x)^1 = f(x)\n            if (equal(_arg2.value, 1)) {\n              return _derivative(_arg, isConst);\n            }\n          }\n\n          // Elementary Power Rule, d/dx(f(x)^c) = c*f'(x)*f(x)^(c-1)\n          var powMinusOne = new OperatorNode('^', 'pow', [_arg.clone(), new OperatorNode('-', 'subtract', [_arg2, createConstantNode(1)])]);\n          return new OperatorNode('*', 'multiply', [_arg2.clone(), new OperatorNode('*', 'multiply', [_derivative(_arg, isConst), powMinusOne])]);\n        }\n\n        // Functional Power Rule, d/dx(f^g) = f^g*[f'*(g/f) + g'ln(f)]\n        return new OperatorNode('*', 'multiply', [new OperatorNode('^', 'pow', [_arg.clone(), _arg2.clone()]), new OperatorNode('+', 'add', [new OperatorNode('*', 'multiply', [_derivative(_arg, isConst), new OperatorNode('/', 'divide', [_arg2.clone(), _arg.clone()])]), new OperatorNode('*', 'multiply', [_derivative(_arg2, isConst), new FunctionNode('log', [_arg.clone()])])])]);\n      }\n      throw new Error('Cannot process operator \"' + node.op + '\" in derivative: ' + 'the operator is not supported, undefined, or the number of arguments passed to it are not supported');\n    }\n  });\n\n  /**\n   * Helper function to create a constant node with a specific type\n   * (number, BigNumber, Fraction)\n   * @param {number} value\n   * @param {string} [valueType]\n   * @return {ConstantNode}\n   */\n  function createConstantNode(value, valueType) {\n    return new ConstantNode(numeric(value, valueType || safeNumberType(String(value), config)));\n  }\n  return derivative;\n});"], "mappings": "AAAA,SAASA,cAAT,EAAyBC,MAAzB,QAAuC,mBAAvC;AACA,SAASC,OAAT,QAAwB,wBAAxB;AACA,SAASC,cAAT,QAA+B,uBAA/B;AACA,IAAIC,IAAI,GAAG,YAAX;AACA,IAAIC,YAAY,GAAG,CAAC,OAAD,EAAU,QAAV,EAAoB,OAApB,EAA6B,UAA7B,EAAyC,OAAzC,EAAkD,QAAlD,EAA4D,SAA5D,EAAuE,cAAvE,EAAuF,cAAvF,EAAuG,cAAvG,EAAuH,iBAAvH,EAA0I,YAA1I,CAAnB;AACA,OAAO,IAAIC,gBAAgB,GAAG,eAAeJ,OAAO,CAACE,IAAD,EAAOC,YAAP,EAAqBE,IAAI,IAAI;EAC/E,IAAI;IACFC,KADE;IAEFC,MAFE;IAGFC,KAHE;IAIFC,QAJE;IAKFC,KALE;IAMFC,MANE;IAOFC,OAPE;IAQFC,YARE;IASFC,YATE;IAUFC,YAVE;IAWFC,eAXE;IAYFC;EAZE,IAaAZ,IAbJ;EAcA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EACE,SAASa,eAAT,CAAyBC,IAAzB,EAA+BC,QAA/B,EAAyC;IACvC,IAAIC,OAAO,GAAGC,SAAS,CAACC,MAAV,GAAmB,CAAnB,IAAwBD,SAAS,CAAC,CAAD,CAAT,KAAiBE,SAAzC,GAAqDF,SAAS,CAAC,CAAD,CAA9D,GAAoE;MAChFb,QAAQ,EAAE;IADsE,CAAlF;IAGA,IAAIgB,KAAK,GAAG,IAAIC,GAAJ,EAAZ;IACA,IAAIC,YAAY,GAAGP,QAAQ,CAAClB,IAA5B;;IACA,SAAS0B,aAAT,CAAuBC,IAAvB,EAA6B;MAC3B,IAAIC,MAAM,GAAGL,KAAK,CAACM,GAAN,CAAUF,IAAV,CAAb;;MACA,IAAIC,MAAM,KAAKN,SAAf,EAA0B;QACxB,OAAOM,MAAP;MACD;;MACD,IAAIE,GAAG,GAAGC,QAAQ,CAACL,aAAD,EAAgBC,IAAhB,EAAsBF,YAAtB,CAAlB;;MACAF,KAAK,CAACS,GAAN,CAAUL,IAAV,EAAgBG,GAAhB;MACA,OAAOA,GAAP;IACD;;IACD,IAAIA,GAAG,GAAGG,WAAW,CAAChB,IAAD,EAAOS,aAAP,CAArB;;IACA,OAAOP,OAAO,CAACZ,QAAR,GAAmBA,QAAQ,CAACuB,GAAD,CAA3B,GAAmCA,GAA1C;EACD;;EACD,SAASI,eAAT,CAAyBC,MAAzB,EAAiC;IAC/B,IAAIC,MAAM,GAAG9B,KAAK,CAAC6B,MAAD,CAAlB;;IACA,IAAI,CAACC,MAAM,CAACC,YAAZ,EAA0B;MACxB,MAAM,IAAIC,SAAJ,CAAc,uBAAuB,gBAAgBC,MAAhB,CAAuBC,IAAI,CAACC,SAAL,CAAeN,MAAf,CAAvB,EAA+C,yCAA/C,CAArC,CAAN;IACD;;IACD,OAAOC,MAAP;EACD;;EACD,IAAIM,UAAU,GAAGtC,KAAK,CAACJ,IAAD,EAAO;IAC3B,oBAAoBgB,eADO;IAE3B,4BAA4BA,eAFD;IAG3B,gBAAgB,CAACW,IAAD,EAAOS,MAAP,KAAkBpB,eAAe,CAACW,IAAD,EAAOO,eAAe,CAACE,MAAD,CAAtB,CAHtB;IAI3B,wBAAwB,CAACT,IAAD,EAAOS,MAAP,EAAejB,OAAf,KAA2BH,eAAe,CAACW,IAAD,EAAOO,eAAe,CAACE,MAAD,CAAtB,EAAgCjB,OAAhC;IAElE;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EAf+B,CAAP,CAAtB;EAiBAuB,UAAU,CAACC,SAAX,GAAuB,IAAvB;;EACAD,UAAU,CAACE,KAAX,GAAmB,UAAUC,KAAV,EAAiB;IAClC,OAAOC,SAAS,CAACC,KAAV,CAAgB,IAAhB,EAAsBF,KAAK,CAACG,IAA5B,CAAP;EACD,CAFD,CAhG+E,CAoG/E;EACA;;;EACA,IAAIF,SAAS,GAAG1C,KAAK,CAAC,WAAD,EAAc;IACjC,oBAAoB,SAAS6C,eAAT,CAAyBhC,IAAzB,EAA+BiC,CAA/B,EAAkC;MACpD,IAAItD,cAAc,CAACqB,IAAD,CAAd,IAAwBpB,MAAM,CAACoB,IAAI,CAACkC,KAAN,CAAN,KAAuB,QAAnD,EAA6D;QAC3D,OAAOL,SAAS,CAACxC,KAAK,CAACW,IAAI,CAACkC,KAAN,CAAL,CAAkBC,QAAlB,EAAD,EAA+BF,CAAC,CAACE,QAAF,EAA/B,EAA6C,CAA7C,CAAhB;MACD,CAFD,MAEO;QACL,OAAON,SAAS,CAAC7B,IAAI,CAAC2B,KAAL,EAAD,EAAeM,CAAC,CAACE,QAAF,EAAf,EAA6B,CAA7B,CAAhB;MACD;IACF,CAPgC;IAQjC,sBAAsB,SAASC,iBAAT,CAA2BpC,IAA3B,EAAiCiC,CAAjC,EAAoC;MACxD,IAAIrD,MAAM,CAACqD,CAAC,CAACC,KAAH,CAAN,KAAoB,QAAxB,EAAkC;QAChC,OAAOL,SAAS,CAAC7B,IAAD,EAAOX,KAAK,CAAC4C,CAAC,CAACC,KAAH,CAAZ,CAAhB;MACD,CAFD,MAEO;QACL,MAAM,IAAIG,KAAJ,CAAU,+DAAV,CAAN;MACD;IACF,CAdgC;IAejC,kCAAkC,SAASC,4BAAT,CAAsCtC,IAAtC,EAA4CiC,CAA5C,EAA+CM,KAA/C,EAAsD;MACtF,OAAOV,SAAS,CAAC7B,IAAI,CAACmC,QAAL,EAAD,EAAkBF,CAAC,CAAClD,IAApB,EAA0BwD,KAAK,CAACL,KAAhC,CAAhB;IACD,CAjBgC;IAkBjC,0BAA0B,SAASM,oBAAT,CAA8BxC,IAA9B,EAAoCiC,CAApC,EAAuCM,KAAvC,EAA8C;MACtE,IAAIE,CAAJ;;MACA,IAAIF,KAAK,KAAK,CAAd,EAAiB;QACfE,CAAC,GAAG,eAAeR,CAAf,GAAmB,GAAvB;MACD,CAFD,MAEO;QACLQ,CAAC,GAAG,SAASF,KAAT,GAAiB,WAAjB,GAA+BN,CAA/B,GAAmC,IAAnC,GAA0CM,KAA1C,GAAkD,IAAtD;MACD;;MACD,OAAOE,CAAC,GAAG,UAAUnB,MAAV,CAAiBtB,IAAjB,EAAuB,UAAvB,CAAX;IACD;EA1BgC,CAAd,CAArB;EA6BA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;EACE,IAAIc,QAAQ,GAAG3B,KAAK,CAAC,UAAD,EAAa;IAC/B,kCAAkC,SAASuD,4BAAT,GAAwC;MACxE,OAAO,IAAP;IACD,CAH8B;IAI/B,gCAAgC,SAASC,0BAAT,CAAoCC,OAApC,EAA6ClC,IAA7C,EAAmDmC,OAAnD,EAA4D;MAC1F;MACA;MACA,OAAOnC,IAAI,CAAC3B,IAAL,KAAc8D,OAArB;IACD,CAR8B;IAS/B,qCAAqC,SAASC,+BAAT,CAAyCF,OAAzC,EAAkDlC,IAAlD,EAAwDmC,OAAxD,EAAiE;MACpG,OAAOD,OAAO,CAAClC,IAAI,CAACqC,OAAN,EAAeF,OAAf,CAAd;IACD,CAX8B;IAY/B,4CAA4C,SAASG,sCAAT,CAAgDJ,OAAhD,EAAyDlC,IAAzD,EAA+DmC,OAA/D,EAAwE;MAClH,IAAI,CAACnC,IAAI,CAACuC,MAAL,CAAYC,QAAZ,CAAqBL,OAArB,CAAL,EAAoC;QAClC,OAAO,IAAP;MACD;;MACD,OAAOD,OAAO,CAAClC,IAAI,CAACV,IAAN,EAAY6C,OAAZ,CAAd;IACD,CAjB8B;IAkB/B,iDAAiD,SAASM,0CAAT,CAAoDP,OAApD,EAA6DlC,IAA7D,EAAmEmC,OAAnE,EAA4E;MAC3H,OAAOnC,IAAI,CAACqB,IAAL,CAAUqB,KAAV,CAAgBC,GAAG,IAAIT,OAAO,CAACS,GAAD,EAAMR,OAAN,CAA9B,CAAP;IACD;EApB8B,CAAb,CAApB;EAuBA;AACF;AACA;AACA;AACA;AACA;AACA;;;EACE,IAAI7B,WAAW,GAAG7B,KAAK,CAAC,aAAD,EAAgB;IACrC,0BAA0B,SAASmE,qBAAT,GAAiC;MACzD,OAAOC,kBAAkB,CAAC,CAAD,CAAzB;IACD,CAHoC;IAIrC,wBAAwB,SAASC,mBAAT,CAA6B9C,IAA7B,EAAmCkC,OAAnC,EAA4C;MAClE,IAAIA,OAAO,CAAClC,IAAD,CAAX,EAAmB;QACjB,OAAO6C,kBAAkB,CAAC,CAAD,CAAzB;MACD;;MACD,OAAOA,kBAAkB,CAAC,CAAD,CAAzB;IACD,CAToC;IAUrC,6BAA6B,SAASE,wBAAT,CAAkC/C,IAAlC,EAAwCkC,OAAxC,EAAiD;MAC5E,OAAO,IAAI/C,eAAJ,CAAoBmB,WAAW,CAACN,IAAI,CAACqC,OAAN,EAAeH,OAAf,CAA/B,CAAP;IACD,CAZoC;IAarC,oCAAoC,SAASc,+BAAT,CAAyChD,IAAzC,EAA+CkC,OAA/C,EAAwD;MAC1F,IAAIA,OAAO,CAAClC,IAAD,CAAX,EAAmB;QACjB,OAAO6C,kBAAkB,CAAC,CAAD,CAAzB;MACD;;MACD,OAAOvC,WAAW,CAACN,IAAI,CAACV,IAAN,EAAY4C,OAAZ,CAAlB;IACD,CAlBoC;IAmBrC,0BAA0B,SAASe,qBAAT,CAA+BjD,IAA/B,EAAqCkC,OAArC,EAA8C;MACtE,IAAIA,OAAO,CAAClC,IAAD,CAAX,EAAmB;QACjB,OAAO6C,kBAAkB,CAAC,CAAD,CAAzB;MACD;;MACD,IAAIK,IAAI,GAAGlD,IAAI,CAACqB,IAAL,CAAU,CAAV,CAAX;MACA,IAAI8B,IAAJ;MACA,IAAIC,GAAG,GAAG,KAAV,CANsE,CAMrD;;MACjB,IAAIC,QAAQ,GAAG,KAAf,CAPsE,CAOhD;;MAEtB,IAAIC,cAAJ;;MACA,QAAQtD,IAAI,CAAC3B,IAAb;QACE,KAAK,MAAL;UACE;UACA+E,GAAG,GAAG,IAAN;UACAE,cAAc,GAAG,IAAIpE,YAAJ,CAAiB,GAAjB,EAAsB,UAAtB,EAAkC,CAAC2D,kBAAkB,CAAC,CAAD,CAAnB,EAAwB,IAAI3D,YAAJ,CAAiB,GAAjB,EAAsB,KAAtB,EAA6B,CAACgE,IAAD,EAAO,IAAIhE,YAAJ,CAAiB,GAAjB,EAAsB,QAAtB,EAAgC,CAAC2D,kBAAkB,CAAC,CAAD,CAAnB,EAAwBA,kBAAkB,CAAC,CAAD,CAA1C,CAAhC,CAAP,CAA7B,CAAxB,CAAlC,CAAjB;UACA;;QACF,KAAK,MAAL;QACA,KAAK,SAAL;UACE;UACA,IAAI7C,IAAI,CAACqB,IAAL,CAAU3B,MAAV,KAAqB,CAAzB,EAA4B;YAC1B0D,GAAG,GAAG,IAAN;YACAE,cAAc,GAAG,IAAIpE,YAAJ,CAAiB,GAAjB,EAAsB,UAAtB,EAAkC,CAAC2D,kBAAkB,CAAC,CAAD,CAAnB,EAAwB,IAAI5D,YAAJ,CAAiB,MAAjB,EAAyB,CAACiE,IAAD,CAAzB,CAAxB,CAAlC,CAAjB;UACD,CAHD,MAGO,IAAIlD,IAAI,CAACqB,IAAL,CAAU3B,MAAV,KAAqB,CAAzB,EAA4B;YACjC;YACAyD,IAAI,GAAG,IAAIjE,YAAJ,CAAiB,GAAjB,EAAsB,QAAtB,EAAgC,CAAC2D,kBAAkB,CAAC,CAAD,CAAnB,EAAwB7C,IAAI,CAACqB,IAAL,CAAU,CAAV,CAAxB,CAAhC,CAAP;YACA,OAAOf,WAAW,CAAC,IAAIpB,YAAJ,CAAiB,GAAjB,EAAsB,KAAtB,EAA6B,CAACgE,IAAD,EAAOC,IAAP,CAA7B,CAAD,EAA6CjB,OAA7C,CAAlB;UACD;;UACD;;QACF,KAAK,OAAL;UACEiB,IAAI,GAAGN,kBAAkB,CAAC,EAAD,CAAzB;;QACF;;QACA,KAAK,KAAL;UACE,IAAI,CAACM,IAAD,IAASnD,IAAI,CAACqB,IAAL,CAAU3B,MAAV,KAAqB,CAAlC,EAAqC;YACnC;YACA4D,cAAc,GAAGJ,IAAI,CAACK,KAAL,EAAjB;YACAH,GAAG,GAAG,IAAN;UACD,CAJD,MAIO,IAAIpD,IAAI,CAACqB,IAAL,CAAU3B,MAAV,KAAqB,CAArB,IAA0ByD,IAA1B,IAAkCnD,IAAI,CAACqB,IAAL,CAAU3B,MAAV,KAAqB,CAArB,IAA0BwC,OAAO,CAAClC,IAAI,CAACqB,IAAL,CAAU,CAAV,CAAD,CAAvE,EAAuF;YAC5F;YACAiC,cAAc,GAAG,IAAIpE,YAAJ,CAAiB,GAAjB,EAAsB,UAAtB,EAAkC,CAACgE,IAAI,CAACK,KAAL,EAAD,EAAe,IAAItE,YAAJ,CAAiB,KAAjB,EAAwB,CAACkE,IAAI,IAAInD,IAAI,CAACqB,IAAL,CAAU,CAAV,CAAT,CAAxB,CAAf,CAAlC,CAAjB;YACA+B,GAAG,GAAG,IAAN;UACD,CAJM,MAIA,IAAIpD,IAAI,CAACqB,IAAL,CAAU3B,MAAV,KAAqB,CAAzB,EAA4B;YACjC;YACA,OAAOY,WAAW,CAAC,IAAIpB,YAAJ,CAAiB,GAAjB,EAAsB,QAAtB,EAAgC,CAAC,IAAID,YAAJ,CAAiB,KAAjB,EAAwB,CAACiE,IAAD,CAAxB,CAAD,EAAkC,IAAIjE,YAAJ,CAAiB,KAAjB,EAAwB,CAACe,IAAI,CAACqB,IAAL,CAAU,CAAV,CAAD,CAAxB,CAAlC,CAAhC,CAAD,EAA8Ga,OAA9G,CAAlB;UACD;;UACD;;QACF,KAAK,KAAL;UACE,IAAIlC,IAAI,CAACqB,IAAL,CAAU3B,MAAV,KAAqB,CAAzB,EAA4B;YAC1B;YACA,OAAOY,WAAW,CAAC,IAAIpB,YAAJ,CAAiB,GAAjB,EAAsB,KAAtB,EAA6B,CAACgE,IAAD,EAAOlD,IAAI,CAACqB,IAAL,CAAU,CAAV,CAAP,CAA7B,CAAD,EAAqDa,OAArD,CAAlB;UACD;;UACD;;QACF,KAAK,KAAL;UACE;UACAoB,cAAc,GAAG,IAAIrE,YAAJ,CAAiB,KAAjB,EAAwB,CAACiE,IAAI,CAACK,KAAL,EAAD,CAAxB,CAAjB;UACA;;QACF,KAAK,KAAL;UACE;UACAD,cAAc,GAAG,IAAIrE,YAAJ,CAAiB,KAAjB,EAAwB,CAACiE,IAAI,CAACK,KAAL,EAAD,CAAxB,CAAjB;UACA;;QACF,KAAK,KAAL;UACE;UACAD,cAAc,GAAG,IAAIpE,YAAJ,CAAiB,GAAjB,EAAsB,YAAtB,EAAoC,CAAC,IAAID,YAAJ,CAAiB,KAAjB,EAAwB,CAACiE,IAAI,CAACK,KAAL,EAAD,CAAxB,CAAD,CAApC,CAAjB;UACA;;QACF,KAAK,KAAL;UACE;UACAD,cAAc,GAAG,IAAIpE,YAAJ,CAAiB,GAAjB,EAAsB,KAAtB,EAA6B,CAAC,IAAID,YAAJ,CAAiB,KAAjB,EAAwB,CAACiE,IAAI,CAACK,KAAL,EAAD,CAAxB,CAAD,EAA0CV,kBAAkB,CAAC,CAAD,CAA5D,CAA7B,CAAjB;UACA;;QACF,KAAK,KAAL;UACE;UACAS,cAAc,GAAG,IAAIpE,YAAJ,CAAiB,GAAjB,EAAsB,UAAtB,EAAkC,CAACc,IAAD,EAAO,IAAIf,YAAJ,CAAiB,KAAjB,EAAwB,CAACiE,IAAI,CAACK,KAAL,EAAD,CAAxB,CAAP,CAAlC,CAAjB;UACA;;QACF,KAAK,KAAL;UACE;UACAF,QAAQ,GAAG,IAAX;UACAC,cAAc,GAAG,IAAIpE,YAAJ,CAAiB,GAAjB,EAAsB,UAAtB,EAAkC,CAACc,IAAD,EAAO,IAAIf,YAAJ,CAAiB,KAAjB,EAAwB,CAACiE,IAAI,CAACK,KAAL,EAAD,CAAxB,CAAP,CAAlC,CAAjB;UACA;;QACF,KAAK,KAAL;UACE;UACAF,QAAQ,GAAG,IAAX;UACAC,cAAc,GAAG,IAAIpE,YAAJ,CAAiB,GAAjB,EAAsB,KAAtB,EAA6B,CAAC,IAAID,YAAJ,CAAiB,KAAjB,EAAwB,CAACiE,IAAI,CAACK,KAAL,EAAD,CAAxB,CAAD,EAA0CV,kBAAkB,CAAC,CAAD,CAA5D,CAA7B,CAAjB;UACA;;QACF,KAAK,MAAL;UACE;UACAO,GAAG,GAAG,IAAN;UACAE,cAAc,GAAG,IAAIrE,YAAJ,CAAiB,MAAjB,EAAyB,CAAC,IAAIC,YAAJ,CAAiB,GAAjB,EAAsB,UAAtB,EAAkC,CAAC2D,kBAAkB,CAAC,CAAD,CAAnB,EAAwB,IAAI3D,YAAJ,CAAiB,GAAjB,EAAsB,KAAtB,EAA6B,CAACgE,IAAI,CAACK,KAAL,EAAD,EAAeV,kBAAkB,CAAC,CAAD,CAAjC,CAA7B,CAAxB,CAAlC,CAAD,CAAzB,CAAjB;UACA;;QACF,KAAK,MAAL;UACE;UACAO,GAAG,GAAG,IAAN;UACAC,QAAQ,GAAG,IAAX;UACAC,cAAc,GAAG,IAAIrE,YAAJ,CAAiB,MAAjB,EAAyB,CAAC,IAAIC,YAAJ,CAAiB,GAAjB,EAAsB,UAAtB,EAAkC,CAAC2D,kBAAkB,CAAC,CAAD,CAAnB,EAAwB,IAAI3D,YAAJ,CAAiB,GAAjB,EAAsB,KAAtB,EAA6B,CAACgE,IAAI,CAACK,KAAL,EAAD,EAAeV,kBAAkB,CAAC,CAAD,CAAjC,CAA7B,CAAxB,CAAlC,CAAD,CAAzB,CAAjB;UACA;;QACF,KAAK,MAAL;UACE;UACAO,GAAG,GAAG,IAAN;UACAE,cAAc,GAAG,IAAIpE,YAAJ,CAAiB,GAAjB,EAAsB,KAAtB,EAA6B,CAAC,IAAIA,YAAJ,CAAiB,GAAjB,EAAsB,KAAtB,EAA6B,CAACgE,IAAI,CAACK,KAAL,EAAD,EAAeV,kBAAkB,CAAC,CAAD,CAAjC,CAA7B,CAAD,EAAsEA,kBAAkB,CAAC,CAAD,CAAxF,CAA7B,CAAjB;UACA;;QACF,KAAK,MAAL;UACE;UACAO,GAAG,GAAG,IAAN;UACAE,cAAc,GAAG,IAAIpE,YAAJ,CAAiB,GAAjB,EAAsB,UAAtB,EAAkC,CAAC,IAAID,YAAJ,CAAiB,KAAjB,EAAwB,CAACiE,IAAI,CAACK,KAAL,EAAD,CAAxB,CAAD,EAA0C,IAAItE,YAAJ,CAAiB,MAAjB,EAAyB,CAAC,IAAIC,YAAJ,CAAiB,GAAjB,EAAsB,UAAtB,EAAkC,CAAC,IAAIA,YAAJ,CAAiB,GAAjB,EAAsB,KAAtB,EAA6B,CAACgE,IAAI,CAACK,KAAL,EAAD,EAAeV,kBAAkB,CAAC,CAAD,CAAjC,CAA7B,CAAD,EAAsEA,kBAAkB,CAAC,CAAD,CAAxF,CAAlC,CAAD,CAAzB,CAA1C,CAAlC,CAAjB;UACA;;QACF,KAAK,MAAL;UACE;UACAO,GAAG,GAAG,IAAN;UACAC,QAAQ,GAAG,IAAX;UACAC,cAAc,GAAG,IAAIpE,YAAJ,CAAiB,GAAjB,EAAsB,UAAtB,EAAkC,CAAC,IAAID,YAAJ,CAAiB,KAAjB,EAAwB,CAACiE,IAAI,CAACK,KAAL,EAAD,CAAxB,CAAD,EAA0C,IAAItE,YAAJ,CAAiB,MAAjB,EAAyB,CAAC,IAAIC,YAAJ,CAAiB,GAAjB,EAAsB,UAAtB,EAAkC,CAAC,IAAIA,YAAJ,CAAiB,GAAjB,EAAsB,KAAtB,EAA6B,CAACgE,IAAI,CAACK,KAAL,EAAD,EAAeV,kBAAkB,CAAC,CAAD,CAAjC,CAA7B,CAAD,EAAsEA,kBAAkB,CAAC,CAAD,CAAxF,CAAlC,CAAD,CAAzB,CAA1C,CAAlC,CAAjB;UACA;;QACF,KAAK,MAAL;UACE;UACAO,GAAG,GAAG,IAAN;UACAC,QAAQ,GAAG,IAAX;UACAC,cAAc,GAAG,IAAIpE,YAAJ,CAAiB,GAAjB,EAAsB,KAAtB,EAA6B,CAAC,IAAIA,YAAJ,CAAiB,GAAjB,EAAsB,KAAtB,EAA6B,CAACgE,IAAI,CAACK,KAAL,EAAD,EAAeV,kBAAkB,CAAC,CAAD,CAAjC,CAA7B,CAAD,EAAsEA,kBAAkB,CAAC,CAAD,CAAxF,CAA7B,CAAjB;UACA;;QACF,KAAK,MAAL;UACE;UACAS,cAAc,GAAG,IAAIrE,YAAJ,CAAiB,MAAjB,EAAyB,CAACiE,IAAI,CAACK,KAAL,EAAD,CAAzB,CAAjB;UACA;;QACF,KAAK,MAAL;UACE;UACAD,cAAc,GAAG,IAAIrE,YAAJ,CAAiB,MAAjB,EAAyB,CAACiE,IAAI,CAACK,KAAL,EAAD,CAAzB,CAAjB;UACA;;QACF,KAAK,MAAL;UACE;UACAD,cAAc,GAAG,IAAIpE,YAAJ,CAAiB,GAAjB,EAAsB,KAAtB,EAA6B,CAAC,IAAID,YAAJ,CAAiB,MAAjB,EAAyB,CAACiE,IAAI,CAACK,KAAL,EAAD,CAAzB,CAAD,EAA2CV,kBAAkB,CAAC,CAAD,CAA7D,CAA7B,CAAjB;UACA;;QACF,KAAK,MAAL;UACE;UACAQ,QAAQ,GAAG,IAAX;UACAC,cAAc,GAAG,IAAIpE,YAAJ,CAAiB,GAAjB,EAAsB,UAAtB,EAAkC,CAACc,IAAD,EAAO,IAAIf,YAAJ,CAAiB,MAAjB,EAAyB,CAACiE,IAAI,CAACK,KAAL,EAAD,CAAzB,CAAP,CAAlC,CAAjB;UACA;;QACF,KAAK,MAAL;UACE;UACAF,QAAQ,GAAG,IAAX;UACAC,cAAc,GAAG,IAAIpE,YAAJ,CAAiB,GAAjB,EAAsB,UAAtB,EAAkC,CAACc,IAAD,EAAO,IAAIf,YAAJ,CAAiB,MAAjB,EAAyB,CAACiE,IAAI,CAACK,KAAL,EAAD,CAAzB,CAAP,CAAlC,CAAjB;UACA;;QACF,KAAK,MAAL;UACE;UACAF,QAAQ,GAAG,IAAX;UACAC,cAAc,GAAG,IAAIpE,YAAJ,CAAiB,GAAjB,EAAsB,KAAtB,EAA6B,CAAC,IAAID,YAAJ,CAAiB,MAAjB,EAAyB,CAACiE,IAAI,CAACK,KAAL,EAAD,CAAzB,CAAD,EAA2CV,kBAAkB,CAAC,CAAD,CAA7D,CAA7B,CAAjB;UACA;;QACF,KAAK,OAAL;UACE;UACAO,GAAG,GAAG,IAAN;UACAE,cAAc,GAAG,IAAIrE,YAAJ,CAAiB,MAAjB,EAAyB,CAAC,IAAIC,YAAJ,CAAiB,GAAjB,EAAsB,KAAtB,EAA6B,CAAC,IAAIA,YAAJ,CAAiB,GAAjB,EAAsB,KAAtB,EAA6B,CAACgE,IAAI,CAACK,KAAL,EAAD,EAAeV,kBAAkB,CAAC,CAAD,CAAjC,CAA7B,CAAD,EAAsEA,kBAAkB,CAAC,CAAD,CAAxF,CAA7B,CAAD,CAAzB,CAAjB;UACA;;QACF,KAAK,OAAL;UACE;UACAO,GAAG,GAAG,IAAN;UACAE,cAAc,GAAG,IAAIrE,YAAJ,CAAiB,MAAjB,EAAyB,CAAC,IAAIC,YAAJ,CAAiB,GAAjB,EAAsB,UAAtB,EAAkC,CAAC,IAAIA,YAAJ,CAAiB,GAAjB,EAAsB,KAAtB,EAA6B,CAACgE,IAAI,CAACK,KAAL,EAAD,EAAeV,kBAAkB,CAAC,CAAD,CAAjC,CAA7B,CAAD,EAAsEA,kBAAkB,CAAC,CAAD,CAAxF,CAAlC,CAAD,CAAzB,CAAjB;UACA;;QACF,KAAK,OAAL;UACE;UACAO,GAAG,GAAG,IAAN;UACAE,cAAc,GAAG,IAAIpE,YAAJ,CAAiB,GAAjB,EAAsB,UAAtB,EAAkC,CAAC2D,kBAAkB,CAAC,CAAD,CAAnB,EAAwB,IAAI3D,YAAJ,CAAiB,GAAjB,EAAsB,KAAtB,EAA6B,CAACgE,IAAI,CAACK,KAAL,EAAD,EAAeV,kBAAkB,CAAC,CAAD,CAAjC,CAA7B,CAAxB,CAAlC,CAAjB;UACA;;QACF,KAAK,OAAL;UACE;UACAO,GAAG,GAAG,IAAN;UACAC,QAAQ,GAAG,IAAX;UACAC,cAAc,GAAG,IAAIpE,YAAJ,CAAiB,GAAjB,EAAsB,UAAtB,EAAkC,CAACgE,IAAI,CAACK,KAAL,EAAD,EAAe,IAAItE,YAAJ,CAAiB,MAAjB,EAAyB,CAAC,IAAIC,YAAJ,CAAiB,GAAjB,EAAsB,UAAtB,EAAkC,CAAC2D,kBAAkB,CAAC,CAAD,CAAnB,EAAwB,IAAI3D,YAAJ,CAAiB,GAAjB,EAAsB,KAAtB,EAA6B,CAACgE,IAAI,CAACK,KAAL,EAAD,EAAeV,kBAAkB,CAAC,CAAD,CAAjC,CAA7B,CAAxB,CAAlC,CAAD,CAAzB,CAAf,CAAlC,CAAjB;UACA;;QACF,KAAK,OAAL;UACE;UACAO,GAAG,GAAG,IAAN;UACAC,QAAQ,GAAG,IAAX;UACAC,cAAc,GAAG,IAAIpE,YAAJ,CAAiB,GAAjB,EAAsB,UAAtB,EAAkC,CAAC,IAAID,YAAJ,CAAiB,KAAjB,EAAwB,CAACiE,IAAI,CAACK,KAAL,EAAD,CAAxB,CAAD,EAA0C,IAAItE,YAAJ,CAAiB,MAAjB,EAAyB,CAAC,IAAIC,YAAJ,CAAiB,GAAjB,EAAsB,KAAtB,EAA6B,CAAC,IAAIA,YAAJ,CAAiB,GAAjB,EAAsB,KAAtB,EAA6B,CAACgE,IAAI,CAACK,KAAL,EAAD,EAAeV,kBAAkB,CAAC,CAAD,CAAjC,CAA7B,CAAD,EAAsEA,kBAAkB,CAAC,CAAD,CAAxF,CAA7B,CAAD,CAAzB,CAA1C,CAAlC,CAAjB;UACA;;QACF,KAAK,OAAL;UACE;UACAO,GAAG,GAAG,IAAN;UACAC,QAAQ,GAAG,IAAX;UACAC,cAAc,GAAG,IAAIpE,YAAJ,CAAiB,GAAjB,EAAsB,UAAtB,EAAkC,CAAC2D,kBAAkB,CAAC,CAAD,CAAnB,EAAwB,IAAI3D,YAAJ,CAAiB,GAAjB,EAAsB,KAAtB,EAA6B,CAACgE,IAAI,CAACK,KAAL,EAAD,EAAeV,kBAAkB,CAAC,CAAD,CAAjC,CAA7B,CAAxB,CAAlC,CAAjB;UACA;;QACF,KAAK,KAAL;UACE;UACAS,cAAc,GAAG,IAAIpE,YAAJ,CAAiB,GAAjB,EAAsB,QAAtB,EAAgC,CAAC,IAAID,YAAJ,CAAiB,IAAIG,UAAJ,CAAe,KAAf,CAAjB,EAAwC,CAAC8D,IAAI,CAACK,KAAL,EAAD,CAAxC,CAAD,EAA0DL,IAAI,CAACK,KAAL,EAA1D,CAAhC,CAAjB;UACA;;QACF,KAAK,OAAL,CAxKF,CAwKgB;;QACd;UACE,MAAM,IAAI5B,KAAJ,CAAU,8BAA8B3B,IAAI,CAAC3B,IAAnC,GAA0C,mBAA1C,GAAgE,qGAA1E,CAAN;MA1KJ;;MA4KA,IAAImF,EAAJ,EAAQC,IAAR;;MACA,IAAIL,GAAJ,EAAS;QACPI,EAAE,GAAG,GAAL;QACAC,IAAI,GAAG,QAAP;MACD,CAHD,MAGO;QACLD,EAAE,GAAG,GAAL;QACAC,IAAI,GAAG,UAAP;MACD;MAED;AACN;AACA;;;MACM,IAAIC,eAAe,GAAGpD,WAAW,CAAC4C,IAAD,EAAOhB,OAAP,CAAjC;;MACA,IAAImB,QAAJ,EAAc;QACZK,eAAe,GAAG,IAAIxE,YAAJ,CAAiB,GAAjB,EAAsB,YAAtB,EAAoC,CAACwE,eAAD,CAApC,CAAlB;MACD;;MACD,OAAO,IAAIxE,YAAJ,CAAiBsE,EAAjB,EAAqBC,IAArB,EAA2B,CAACC,eAAD,EAAkBJ,cAAlB,CAA3B,CAAP;IACD,CA1NoC;IA2NrC,0BAA0B,SAASK,qBAAT,CAA+B3D,IAA/B,EAAqCkC,OAArC,EAA8C;MACtE,IAAIA,OAAO,CAAClC,IAAD,CAAX,EAAmB;QACjB,OAAO6C,kBAAkB,CAAC,CAAD,CAAzB;MACD;;MACD,IAAI7C,IAAI,CAACwD,EAAL,KAAY,GAAhB,EAAqB;QACnB;QACA,OAAO,IAAItE,YAAJ,CAAiBc,IAAI,CAACwD,EAAtB,EAA0BxD,IAAI,CAAC4D,EAA/B,EAAmC5D,IAAI,CAACqB,IAAL,CAAUwC,GAAV,CAAc,UAAUlB,GAAV,EAAe;UACrE,OAAOrC,WAAW,CAACqC,GAAD,EAAMT,OAAN,CAAlB;QACD,CAFyC,CAAnC,CAAP;MAGD;;MACD,IAAIlC,IAAI,CAACwD,EAAL,KAAY,GAAhB,EAAqB;QACnB;QACA,IAAIxD,IAAI,CAAC8D,OAAL,EAAJ,EAAoB;UAClB,OAAO,IAAI5E,YAAJ,CAAiBc,IAAI,CAACwD,EAAtB,EAA0BxD,IAAI,CAAC4D,EAA/B,EAAmC,CAACtD,WAAW,CAACN,IAAI,CAACqB,IAAL,CAAU,CAAV,CAAD,EAAea,OAAf,CAAZ,CAAnC,CAAP;QACD,CAJkB,CAMnB;;;QACA,IAAIlC,IAAI,CAAC+D,QAAL,EAAJ,EAAqB;UACnB,OAAO,IAAI7E,YAAJ,CAAiBc,IAAI,CAACwD,EAAtB,EAA0BxD,IAAI,CAAC4D,EAA/B,EAAmC,CAACtD,WAAW,CAACN,IAAI,CAACqB,IAAL,CAAU,CAAV,CAAD,EAAea,OAAf,CAAZ,EAAqC5B,WAAW,CAACN,IAAI,CAACqB,IAAL,CAAU,CAAV,CAAD,EAAea,OAAf,CAAhD,CAAnC,CAAP;QACD;MACF;;MACD,IAAIlC,IAAI,CAACwD,EAAL,KAAY,GAAhB,EAAqB;QACnB;QACA,IAAIQ,aAAa,GAAGhE,IAAI,CAACqB,IAAL,CAAU4C,MAAV,CAAiB,UAAUtB,GAAV,EAAe;UAClD,OAAOT,OAAO,CAACS,GAAD,CAAd;QACD,CAFmB,CAApB;;QAGA,IAAIqB,aAAa,CAACtE,MAAd,GAAuB,CAA3B,EAA8B;UAC5B,IAAIwE,gBAAgB,GAAGlE,IAAI,CAACqB,IAAL,CAAU4C,MAAV,CAAiB,UAAUtB,GAAV,EAAe;YACrD,OAAO,CAACT,OAAO,CAACS,GAAD,CAAf;UACD,CAFsB,CAAvB;UAGA,IAAIwB,eAAe,GAAGD,gBAAgB,CAACxE,MAAjB,KAA4B,CAA5B,GAAgCwE,gBAAgB,CAAC,CAAD,CAAhD,GAAsD,IAAIhF,YAAJ,CAAiB,GAAjB,EAAsB,UAAtB,EAAkCgF,gBAAlC,CAA5E;UACA,IAAIE,OAAO,GAAGJ,aAAa,CAACpD,MAAd,CAAqBN,WAAW,CAAC6D,eAAD,EAAkBjC,OAAlB,CAAhC,CAAd;UACA,OAAO,IAAIhD,YAAJ,CAAiB,GAAjB,EAAsB,UAAtB,EAAkCkF,OAAlC,CAAP;QACD,CAZkB,CAcnB;;;QACA,OAAO,IAAIlF,YAAJ,CAAiB,GAAjB,EAAsB,KAAtB,EAA6Bc,IAAI,CAACqB,IAAL,CAAUwC,GAAV,CAAc,UAAUQ,QAAV,EAAoB;UACpE,OAAO,IAAInF,YAAJ,CAAiB,GAAjB,EAAsB,UAAtB,EAAkCc,IAAI,CAACqB,IAAL,CAAUwC,GAAV,CAAc,UAAUS,QAAV,EAAoB;YACzE,OAAOA,QAAQ,KAAKD,QAAb,GAAwB/D,WAAW,CAACgE,QAAD,EAAWpC,OAAX,CAAnC,GAAyDoC,QAAQ,CAACf,KAAT,EAAhE;UACD,CAFwC,CAAlC,CAAP;QAGD,CAJmC,CAA7B,CAAP;MAKD;;MACD,IAAIvD,IAAI,CAACwD,EAAL,KAAY,GAAZ,IAAmBxD,IAAI,CAAC+D,QAAL,EAAvB,EAAwC;QACtC,IAAIb,IAAI,GAAGlD,IAAI,CAACqB,IAAL,CAAU,CAAV,CAAX;QACA,IAAI8B,IAAI,GAAGnD,IAAI,CAACqB,IAAL,CAAU,CAAV,CAAX,CAFsC,CAItC;;QACA,IAAIa,OAAO,CAACiB,IAAD,CAAX,EAAmB;UACjB,OAAO,IAAIjE,YAAJ,CAAiB,GAAjB,EAAsB,QAAtB,EAAgC,CAACoB,WAAW,CAAC4C,IAAD,EAAOhB,OAAP,CAAZ,EAA6BiB,IAA7B,CAAhC,CAAP;QACD,CAPqC,CAStC;;;QACA,IAAIjB,OAAO,CAACgB,IAAD,CAAX,EAAmB;UACjB,OAAO,IAAIhE,YAAJ,CAAiB,GAAjB,EAAsB,UAAtB,EAAkC,CAAC,IAAIA,YAAJ,CAAiB,GAAjB,EAAsB,YAAtB,EAAoC,CAACgE,IAAD,CAApC,CAAD,EAA8C,IAAIhE,YAAJ,CAAiB,GAAjB,EAAsB,QAAtB,EAAgC,CAACoB,WAAW,CAAC6C,IAAD,EAAOjB,OAAP,CAAZ,EAA6B,IAAIhD,YAAJ,CAAiB,GAAjB,EAAsB,KAAtB,EAA6B,CAACiE,IAAI,CAACI,KAAL,EAAD,EAAeV,kBAAkB,CAAC,CAAD,CAAjC,CAA7B,CAA7B,CAAhC,CAA9C,CAAlC,CAAP;QACD,CAZqC,CActC;;;QACA,OAAO,IAAI3D,YAAJ,CAAiB,GAAjB,EAAsB,QAAtB,EAAgC,CAAC,IAAIA,YAAJ,CAAiB,GAAjB,EAAsB,UAAtB,EAAkC,CAAC,IAAIA,YAAJ,CAAiB,GAAjB,EAAsB,UAAtB,EAAkC,CAACoB,WAAW,CAAC4C,IAAD,EAAOhB,OAAP,CAAZ,EAA6BiB,IAAI,CAACI,KAAL,EAA7B,CAAlC,CAAD,EAAgF,IAAIrE,YAAJ,CAAiB,GAAjB,EAAsB,UAAtB,EAAkC,CAACgE,IAAI,CAACK,KAAL,EAAD,EAAejD,WAAW,CAAC6C,IAAD,EAAOjB,OAAP,CAA1B,CAAlC,CAAhF,CAAlC,CAAD,EAAoM,IAAIhD,YAAJ,CAAiB,GAAjB,EAAsB,KAAtB,EAA6B,CAACiE,IAAI,CAACI,KAAL,EAAD,EAAeV,kBAAkB,CAAC,CAAD,CAAjC,CAA7B,CAApM,CAAhC,CAAP;MACD;;MACD,IAAI7C,IAAI,CAACwD,EAAL,KAAY,GAAZ,IAAmBxD,IAAI,CAAC+D,QAAL,EAAvB,EAAwC;QACtC,IAAIQ,IAAI,GAAGvE,IAAI,CAACqB,IAAL,CAAU,CAAV,CAAX;QACA,IAAImD,KAAK,GAAGxE,IAAI,CAACqB,IAAL,CAAU,CAAV,CAAZ;;QACA,IAAIa,OAAO,CAACqC,IAAD,CAAX,EAAmB;UACjB;UACA,IAAItG,cAAc,CAACsG,IAAD,CAAd,KAAyBzF,MAAM,CAACyF,IAAI,CAAC/C,KAAN,CAAN,IAAsB3C,KAAK,CAAC0F,IAAI,CAAC/C,KAAN,EAAa,CAAb,CAApD,CAAJ,EAA0E;YACxE,OAAOqB,kBAAkB,CAAC,CAAD,CAAzB;UACD,CAJgB,CAMjB;;;UACA,OAAO,IAAI3D,YAAJ,CAAiB,GAAjB,EAAsB,UAAtB,EAAkC,CAACc,IAAD,EAAO,IAAId,YAAJ,CAAiB,GAAjB,EAAsB,UAAtB,EAAkC,CAAC,IAAID,YAAJ,CAAiB,KAAjB,EAAwB,CAACsF,IAAI,CAAChB,KAAL,EAAD,CAAxB,CAAD,EAA0CjD,WAAW,CAACkE,KAAK,CAACjB,KAAN,EAAD,EAAgBrB,OAAhB,CAArD,CAAlC,CAAP,CAAlC,CAAP;QACD;;QACD,IAAIA,OAAO,CAACsC,KAAD,CAAX,EAAoB;UAClB,IAAIvG,cAAc,CAACuG,KAAD,CAAlB,EAA2B;YACzB;YACA,IAAI1F,MAAM,CAAC0F,KAAK,CAAChD,KAAP,CAAV,EAAyB;cACvB,OAAOqB,kBAAkB,CAAC,CAAD,CAAzB;YACD,CAJwB,CAKzB;;;YACA,IAAIhE,KAAK,CAAC2F,KAAK,CAAChD,KAAP,EAAc,CAAd,CAAT,EAA2B;cACzB,OAAOlB,WAAW,CAACiE,IAAD,EAAOrC,OAAP,CAAlB;YACD;UACF,CAViB,CAYlB;;;UACA,IAAIuC,WAAW,GAAG,IAAIvF,YAAJ,CAAiB,GAAjB,EAAsB,KAAtB,EAA6B,CAACqF,IAAI,CAAChB,KAAL,EAAD,EAAe,IAAIrE,YAAJ,CAAiB,GAAjB,EAAsB,UAAtB,EAAkC,CAACsF,KAAD,EAAQ3B,kBAAkB,CAAC,CAAD,CAA1B,CAAlC,CAAf,CAA7B,CAAlB;UACA,OAAO,IAAI3D,YAAJ,CAAiB,GAAjB,EAAsB,UAAtB,EAAkC,CAACsF,KAAK,CAACjB,KAAN,EAAD,EAAgB,IAAIrE,YAAJ,CAAiB,GAAjB,EAAsB,UAAtB,EAAkC,CAACoB,WAAW,CAACiE,IAAD,EAAOrC,OAAP,CAAZ,EAA6BuC,WAA7B,CAAlC,CAAhB,CAAlC,CAAP;QACD,CA3BqC,CA6BtC;;;QACA,OAAO,IAAIvF,YAAJ,CAAiB,GAAjB,EAAsB,UAAtB,EAAkC,CAAC,IAAIA,YAAJ,CAAiB,GAAjB,EAAsB,KAAtB,EAA6B,CAACqF,IAAI,CAAChB,KAAL,EAAD,EAAeiB,KAAK,CAACjB,KAAN,EAAf,CAA7B,CAAD,EAA8D,IAAIrE,YAAJ,CAAiB,GAAjB,EAAsB,KAAtB,EAA6B,CAAC,IAAIA,YAAJ,CAAiB,GAAjB,EAAsB,UAAtB,EAAkC,CAACoB,WAAW,CAACiE,IAAD,EAAOrC,OAAP,CAAZ,EAA6B,IAAIhD,YAAJ,CAAiB,GAAjB,EAAsB,QAAtB,EAAgC,CAACsF,KAAK,CAACjB,KAAN,EAAD,EAAgBgB,IAAI,CAAChB,KAAL,EAAhB,CAAhC,CAA7B,CAAlC,CAAD,EAAkI,IAAIrE,YAAJ,CAAiB,GAAjB,EAAsB,UAAtB,EAAkC,CAACoB,WAAW,CAACkE,KAAD,EAAQtC,OAAR,CAAZ,EAA8B,IAAIjD,YAAJ,CAAiB,KAAjB,EAAwB,CAACsF,IAAI,CAAChB,KAAL,EAAD,CAAxB,CAA9B,CAAlC,CAAlI,CAA7B,CAA9D,CAAlC,CAAP;MACD;;MACD,MAAM,IAAI5B,KAAJ,CAAU,8BAA8B3B,IAAI,CAACwD,EAAnC,GAAwC,mBAAxC,GAA8D,qGAAxE,CAAN;IACD;EAvToC,CAAhB,CAAvB;EA0TA;AACF;AACA;AACA;AACA;AACA;AACA;;;EACE,SAASX,kBAAT,CAA4BrB,KAA5B,EAAmCkD,SAAnC,EAA8C;IAC5C,OAAO,IAAI1F,YAAJ,CAAiBD,OAAO,CAACyC,KAAD,EAAQkD,SAAS,IAAItG,cAAc,CAACuG,MAAM,CAACnD,KAAD,CAAP,EAAgB9C,MAAhB,CAAnC,CAAxB,CAAP;EACD;;EACD,OAAOqC,UAAP;AACD,CApfmD,CAA7C"}, "metadata": {}, "sourceType": "module"}