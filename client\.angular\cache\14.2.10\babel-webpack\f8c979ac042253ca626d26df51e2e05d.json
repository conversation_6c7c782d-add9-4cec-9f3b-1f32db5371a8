{"ast": null, "code": "import { deepMap } from '../../utils/collection.js';\nimport { factory } from '../../utils/factory.js';\nimport { createEmptyMap } from '../../utils/map.js';\nvar name = 'evaluate';\nvar dependencies = ['typed', 'parse'];\nexport var createEvaluate = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed,\n    parse\n  } = _ref;\n  /**\n   * Evaluate an expression.\n   *\n   * The expression parser does not use JavaScript. Its syntax is close\n   * to JavaScript but more suited for mathematical expressions.\n   * See [https://mathjs.org/docs/expressions/syntax.html](https://mathjs.org/docs/expressions/syntax.html) to learn\n   * the syntax and get an overview of the exact differences from JavaScript.\n   *\n   * Note the evaluating arbitrary expressions may involve security risks,\n   * see [https://mathjs.org/docs/expressions/security.html](https://mathjs.org/docs/expressions/security.html) for more information.\n   *\n   * Syntax:\n   *\n   *     math.evaluate(expr)\n   *     math.evaluate(expr, scope)\n   *     math.evaluate([expr1, expr2, expr3, ...])\n   *     math.evaluate([expr1, expr2, expr3, ...], scope)\n   *\n   * Example:\n   *\n   *     math.evaluate('(2+3)/4')                // 1.25\n   *     math.evaluate('sqrt(3^2 + 4^2)')        // 5\n   *     math.evaluate('sqrt(-4)')               // 2i\n   *     math.evaluate(['a=3', 'b=4', 'a*b'])    // [3, 4, 12]\n   *\n   *     let scope = {a:3, b:4}\n   *     math.evaluate('a * b', scope)           // 12\n   *\n   * See also:\n   *\n   *    parse, compile\n   *\n   * @param {string | string[] | Matrix} expr   The expression to be evaluated\n   * @param {Object} [scope]                    Scope to read/write variables\n   * @return {*} The result of the expression\n   * @throws {Error}\n   */\n\n  return typed(name, {\n    string: function string(expr) {\n      var scope = createEmptyMap();\n      return parse(expr).compile().evaluate(scope);\n    },\n    'string, Map | Object': function string_Map__Object(expr, scope) {\n      return parse(expr).compile().evaluate(scope);\n    },\n    'Array | Matrix': function Array__Matrix(expr) {\n      var scope = createEmptyMap();\n      return deepMap(expr, function (entry) {\n        return parse(entry).compile().evaluate(scope);\n      });\n    },\n    'Array | Matrix, Map | Object': function Array__Matrix_Map__Object(expr, scope) {\n      return deepMap(expr, function (entry) {\n        return parse(entry).compile().evaluate(scope);\n      });\n    }\n  });\n});", "map": null, "metadata": {}, "sourceType": "module"}