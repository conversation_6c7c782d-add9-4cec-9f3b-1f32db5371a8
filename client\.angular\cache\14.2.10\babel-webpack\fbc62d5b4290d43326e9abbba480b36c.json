{"ast": null, "code": "ace.define(\"ace/theme/chaos\", [\"require\", \"exports\", \"module\", \"ace/lib/dom\"], function (acequire, exports, module) {\n  exports.isDark = true;\n  exports.cssClass = \"ace-chaos\";\n  exports.cssText = \".ace-chaos .ace_gutter {\\\nbackground: #141414;\\\ncolor: #595959;\\\nborder-right: 1px solid #282828;\\\n}\\\n.ace-chaos .ace_gutter-cell.ace_warning {\\\nbackground-image: none;\\\nbackground: #FC0;\\\nborder-left: none;\\\npadding-left: 0;\\\ncolor: #000;\\\n}\\\n.ace-chaos .ace_gutter-cell.ace_error {\\\nbackground-position: -6px center;\\\nbackground-image: none;\\\nbackground: #F10;\\\nborder-left: none;\\\npadding-left: 0;\\\ncolor: #000;\\\n}\\\n.ace-chaos .ace_print-margin {\\\nborder-left: 1px solid #555;\\\nright: 0;\\\nbackground: #1D1D1D;\\\n}\\\n.ace-chaos {\\\nbackground-color: #161616;\\\ncolor: #E6E1DC;\\\n}\\\n.ace-chaos .ace_cursor {\\\nborder-left: 2px solid #FFFFFF;\\\n}\\\n.ace-chaos .ace_cursor.ace_overwrite {\\\nborder-left: 0px;\\\nborder-bottom: 1px solid #FFFFFF;\\\n}\\\n.ace-chaos .ace_marker-layer .ace_selection {\\\nbackground: #494836;\\\n}\\\n.ace-chaos .ace_marker-layer .ace_step {\\\nbackground: rgb(198, 219, 174);\\\n}\\\n.ace-chaos .ace_marker-layer .ace_bracket {\\\nmargin: -1px 0 0 -1px;\\\nborder: 1px solid #FCE94F;\\\n}\\\n.ace-chaos .ace_marker-layer .ace_active-line {\\\nbackground: #333;\\\n}\\\n.ace-chaos .ace_gutter-active-line {\\\nbackground-color: #222;\\\n}\\\n.ace-chaos .ace_invisible {\\\ncolor: #404040;\\\n}\\\n.ace-chaos .ace_keyword {\\\ncolor:#00698F;\\\n}\\\n.ace-chaos .ace_keyword.ace_operator {\\\ncolor:#FF308F;\\\n}\\\n.ace-chaos .ace_constant {\\\ncolor:#1EDAFB;\\\n}\\\n.ace-chaos .ace_constant.ace_language {\\\ncolor:#FDC251;\\\n}\\\n.ace-chaos .ace_constant.ace_library {\\\ncolor:#8DFF0A;\\\n}\\\n.ace-chaos .ace_constant.ace_numeric {\\\ncolor:#58C554;\\\n}\\\n.ace-chaos .ace_invalid {\\\ncolor:#FFFFFF;\\\nbackground-color:#990000;\\\n}\\\n.ace-chaos .ace_invalid.ace_deprecated {\\\ncolor:#FFFFFF;\\\nbackground-color:#990000;\\\n}\\\n.ace-chaos .ace_support {\\\ncolor: #999;\\\n}\\\n.ace-chaos .ace_support.ace_function {\\\ncolor:#00AEEF;\\\n}\\\n.ace-chaos .ace_function {\\\ncolor:#00AEEF;\\\n}\\\n.ace-chaos .ace_string {\\\ncolor:#58C554;\\\n}\\\n.ace-chaos .ace_comment {\\\ncolor:#555;\\\nfont-style:italic;\\\npadding-bottom: 0px;\\\n}\\\n.ace-chaos .ace_variable {\\\ncolor:#997744;\\\n}\\\n.ace-chaos .ace_meta.ace_tag {\\\ncolor:#BE53E6;\\\n}\\\n.ace-chaos .ace_entity.ace_other.ace_attribute-name {\\\ncolor:#FFFF89;\\\n}\\\n.ace-chaos .ace_markup.ace_underline {\\\ntext-decoration: underline;\\\n}\\\n.ace-chaos .ace_fold-widget {\\\ntext-align: center;\\\n}\\\n.ace-chaos .ace_fold-widget:hover {\\\ncolor: #777;\\\n}\\\n.ace-chaos .ace_fold-widget.ace_start,\\\n.ace-chaos .ace_fold-widget.ace_end,\\\n.ace-chaos .ace_fold-widget.ace_closed{\\\nbackground: none;\\\nborder: none;\\\nbox-shadow: none;\\\n}\\\n.ace-chaos .ace_fold-widget.ace_start:after {\\\ncontent: '▾'\\\n}\\\n.ace-chaos .ace_fold-widget.ace_end:after {\\\ncontent: '▴'\\\n}\\\n.ace-chaos .ace_fold-widget.ace_closed:after {\\\ncontent: '‣'\\\n}\\\n.ace-chaos .ace_indent-guide {\\\nborder-right:1px dotted #333;\\\nmargin-right:-1px;\\\n}\\\n.ace-chaos .ace_fold { \\\nbackground: #222; \\\nborder-radius: 3px; \\\ncolor: #7AF; \\\nborder: none; \\\n}\\\n.ace-chaos .ace_fold:hover {\\\nbackground: #CCC; \\\ncolor: #000;\\\n}\\\n\";\n  var dom = acequire(\"../lib/dom\");\n  dom.importCssString(exports.cssText, exports.cssClass);\n});", "map": null, "metadata": {}, "sourceType": "script"}