{"ast": null, "code": "export var combinationsDocs = {\n  name: 'combinations',\n  category: 'Probability',\n  syntax: ['combinations(n, k)'],\n  description: 'Compute the number of combinations of n items taken k at a time',\n  examples: ['combinations(7, 5)'],\n  seealso: ['combinationsWithRep', 'permutations', 'factorial']\n};", "map": {"version": 3, "names": ["combinationsDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["D:/work/joyserver/client/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/probability/combinations.js"], "sourcesContent": ["export var combinationsDocs = {\n  name: 'combinations',\n  category: 'Probability',\n  syntax: ['combinations(n, k)'],\n  description: 'Compute the number of combinations of n items taken k at a time',\n  examples: ['combinations(7, 5)'],\n  seealso: ['combinationsWithRep', 'permutations', 'factorial']\n};"], "mappings": "AAAA,OAAO,IAAIA,gBAAgB,GAAG;EAC5BC,IAAI,EAAE,cADsB;EAE5BC,QAAQ,EAAE,aAFkB;EAG5BC,MAAM,EAAE,CAAC,oBAAD,CAHoB;EAI5BC,WAAW,EAAE,iEAJe;EAK5BC,QAAQ,EAAE,CAAC,oBAAD,CALkB;EAM5BC,OAAO,EAAE,CAAC,qBAAD,EAAwB,cAAxB,EAAwC,WAAxC;AANmB,CAAvB"}, "metadata": {}, "sourceType": "module"}