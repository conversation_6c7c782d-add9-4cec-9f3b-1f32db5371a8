{"ast": null, "code": "import { Observable } from '../Observable';\nimport { from } from './from';\nimport { isArray } from '../util/isArray';\nimport { EMPTY } from './empty';\nexport function onErrorResumeNext(...sources) {\n  if (sources.length === 0) {\n    return EMPTY;\n  }\n\n  const [first, ...remainder] = sources;\n\n  if (sources.length === 1 && isArray(first)) {\n    return onErrorResumeNext(...first);\n  }\n\n  return new Observable(subscriber => {\n    const subNext = () => subscriber.add(onErrorResumeNext(...remainder).subscribe(subscriber));\n\n    return from(first).subscribe({\n      next(value) {\n        subscriber.next(value);\n      },\n\n      error: subNext,\n      complete: subNext\n    });\n  });\n}", "map": {"version": 3, "names": ["Observable", "from", "isArray", "EMPTY", "onErrorResumeNext", "sources", "length", "first", "remainder", "subscriber", "subNext", "add", "subscribe", "next", "value", "error", "complete"], "sources": ["D:/work/joyserver/client/node_modules/@angular-slider/ngx-slider/node_modules/rxjs/_esm2015/internal/observable/onErrorResumeNext.js"], "sourcesContent": ["import { Observable } from '../Observable';\nimport { from } from './from';\nimport { isArray } from '../util/isArray';\nimport { EMPTY } from './empty';\nexport function onErrorResumeNext(...sources) {\n    if (sources.length === 0) {\n        return EMPTY;\n    }\n    const [first, ...remainder] = sources;\n    if (sources.length === 1 && isArray(first)) {\n        return onErrorResumeNext(...first);\n    }\n    return new Observable(subscriber => {\n        const subNext = () => subscriber.add(onErrorResumeNext(...remainder).subscribe(subscriber));\n        return from(first).subscribe({\n            next(value) { subscriber.next(value); },\n            error: subNext,\n            complete: subNext,\n        });\n    });\n}\n"], "mappings": "AAAA,SAASA,UAAT,QAA2B,eAA3B;AACA,SAASC,IAAT,QAAqB,QAArB;AACA,SAASC,OAAT,QAAwB,iBAAxB;AACA,SAASC,KAAT,QAAsB,SAAtB;AACA,OAAO,SAASC,iBAAT,CAA2B,GAAGC,OAA9B,EAAuC;EAC1C,IAAIA,OAAO,CAACC,MAAR,KAAmB,CAAvB,EAA0B;IACtB,OAAOH,KAAP;EACH;;EACD,MAAM,CAACI,KAAD,EAAQ,GAAGC,SAAX,IAAwBH,OAA9B;;EACA,IAAIA,OAAO,CAACC,MAAR,KAAmB,CAAnB,IAAwBJ,OAAO,CAACK,KAAD,CAAnC,EAA4C;IACxC,OAAOH,iBAAiB,CAAC,GAAGG,KAAJ,CAAxB;EACH;;EACD,OAAO,IAAIP,UAAJ,CAAeS,UAAU,IAAI;IAChC,MAAMC,OAAO,GAAG,MAAMD,UAAU,CAACE,GAAX,CAAeP,iBAAiB,CAAC,GAAGI,SAAJ,CAAjB,CAAgCI,SAAhC,CAA0CH,UAA1C,CAAf,CAAtB;;IACA,OAAOR,IAAI,CAACM,KAAD,CAAJ,CAAYK,SAAZ,CAAsB;MACzBC,IAAI,CAACC,KAAD,EAAQ;QAAEL,UAAU,CAACI,IAAX,CAAgBC,KAAhB;MAAyB,CADd;;MAEzBC,KAAK,EAAEL,OAFkB;MAGzBM,QAAQ,EAAEN;IAHe,CAAtB,CAAP;EAKH,CAPM,CAAP;AAQH"}, "metadata": {}, "sourceType": "module"}