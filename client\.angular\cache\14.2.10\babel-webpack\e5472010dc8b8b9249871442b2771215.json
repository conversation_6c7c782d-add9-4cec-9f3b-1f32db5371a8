{"ast": null, "code": "import { factory } from './utils/factory.js';\nimport { version } from './version.js';\nimport { createBigNumberE, createBigNumberPhi, createBigNumberPi, createBigNumberTau } from './utils/bignumber/constants.js';\nimport { pi, tau, e, phi } from './plain/number/index.js';\nexport var createTrue = /* #__PURE__ */factory('true', [], () => true);\nexport var createFalse = /* #__PURE__ */factory('false', [], () => false);\nexport var createNull = /* #__PURE__ */factory('null', [], () => null);\nexport var createInfinity = /* #__PURE__ */recreateFactory('Infinity', ['config', '?BigNumber'], _ref => {\n  var {\n    config,\n    BigNumber\n  } = _ref;\n  return config.number === 'BigNumber' ? new BigNumber(Infinity) : Infinity;\n});\nexport var createNaN = /* #__PURE__ */recreateFactory('NaN', ['config', '?BigNumber'], _ref2 => {\n  var {\n    config,\n    BigNumber\n  } = _ref2;\n  return config.number === 'BigNumber' ? new BigNumber(NaN) : NaN;\n});\nexport var createPi = /* #__PURE__ */recreateFactory('pi', ['config', '?BigNumber'], _ref3 => {\n  var {\n    config,\n    BigNumber\n  } = _ref3;\n  return config.number === 'BigNumber' ? createBigNumberPi(BigNumber) : pi;\n});\nexport var createTau = /* #__PURE__ */recreateFactory('tau', ['config', '?BigNumber'], _ref4 => {\n  var {\n    config,\n    BigNumber\n  } = _ref4;\n  return config.number === 'BigNumber' ? createBigNumberTau(BigNumber) : tau;\n});\nexport var createE = /* #__PURE__ */recreateFactory('e', ['config', '?BigNumber'], _ref5 => {\n  var {\n    config,\n    BigNumber\n  } = _ref5;\n  return config.number === 'BigNumber' ? createBigNumberE(BigNumber) : e;\n}); // golden ratio, (1+sqrt(5))/2\n\nexport var createPhi = /* #__PURE__ */recreateFactory('phi', ['config', '?BigNumber'], _ref6 => {\n  var {\n    config,\n    BigNumber\n  } = _ref6;\n  return config.number === 'BigNumber' ? createBigNumberPhi(BigNumber) : phi;\n});\nexport var createLN2 = /* #__PURE__ */recreateFactory('LN2', ['config', '?BigNumber'], _ref7 => {\n  var {\n    config,\n    BigNumber\n  } = _ref7;\n  return config.number === 'BigNumber' ? new BigNumber(2).ln() : Math.LN2;\n});\nexport var createLN10 = /* #__PURE__ */recreateFactory('LN10', ['config', '?BigNumber'], _ref8 => {\n  var {\n    config,\n    BigNumber\n  } = _ref8;\n  return config.number === 'BigNumber' ? new BigNumber(10).ln() : Math.LN10;\n});\nexport var createLOG2E = /* #__PURE__ */recreateFactory('LOG2E', ['config', '?BigNumber'], _ref9 => {\n  var {\n    config,\n    BigNumber\n  } = _ref9;\n  return config.number === 'BigNumber' ? new BigNumber(1).div(new BigNumber(2).ln()) : Math.LOG2E;\n});\nexport var createLOG10E = /* #__PURE__ */recreateFactory('LOG10E', ['config', '?BigNumber'], _ref10 => {\n  var {\n    config,\n    BigNumber\n  } = _ref10;\n  return config.number === 'BigNumber' ? new BigNumber(1).div(new BigNumber(10).ln()) : Math.LOG10E;\n});\nexport var createSQRT1_2 = /* #__PURE__ */recreateFactory( // eslint-disable-line camelcase\n'SQRT1_2', ['config', '?BigNumber'], _ref11 => {\n  var {\n    config,\n    BigNumber\n  } = _ref11;\n  return config.number === 'BigNumber' ? new BigNumber('0.5').sqrt() : Math.SQRT1_2;\n});\nexport var createSQRT2 = /* #__PURE__ */recreateFactory('SQRT2', ['config', '?BigNumber'], _ref12 => {\n  var {\n    config,\n    BigNumber\n  } = _ref12;\n  return config.number === 'BigNumber' ? new BigNumber(2).sqrt() : Math.SQRT2;\n});\nexport var createI = /* #__PURE__ */recreateFactory('i', ['Complex'], _ref13 => {\n  var {\n    Complex\n  } = _ref13;\n  return Complex.I;\n}); // for backward compatibility with v5\n\nexport var createUppercasePi = /* #__PURE__ */factory('PI', ['pi'], _ref14 => {\n  var {\n    pi\n  } = _ref14;\n  return pi;\n});\nexport var createUppercaseE = /* #__PURE__ */factory('E', ['e'], _ref15 => {\n  var {\n    e\n  } = _ref15;\n  return e;\n});\nexport var createVersion = /* #__PURE__ */factory('version', [], () => version); // helper function to create a factory with a flag recreateOnConfigChange\n// idea: allow passing optional properties to be attached to the factory function as 4th argument?\n\nfunction recreateFactory(name, dependencies, create) {\n  return factory(name, dependencies, create, {\n    recreateOnConfigChange: true\n  });\n}", "map": null, "metadata": {}, "sourceType": "module"}