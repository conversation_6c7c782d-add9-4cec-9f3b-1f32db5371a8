{"ast": null, "code": "/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { NodeDependencies } from './dependenciesNode.generated.js';\nimport { createConditionalNode } from '../../factoriesAny.js';\nexport var ConditionalNodeDependencies = {\n  NodeDependencies,\n  createConditionalNode\n};", "map": {"version": 3, "names": ["NodeDependencies", "createConditionalNode", "ConditionalNodeDependencies"], "sources": ["D:/work/joyserver/client/node_modules/mathjs/lib/esm/entry/dependenciesAny/dependenciesConditionalNode.generated.js"], "sourcesContent": ["/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { NodeDependencies } from './dependenciesNode.generated.js';\nimport { createConditionalNode } from '../../factoriesAny.js';\nexport var ConditionalNodeDependencies = {\n  NodeDependencies,\n  createConditionalNode\n};"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,gBAAT,QAAiC,iCAAjC;AACA,SAASC,qBAAT,QAAsC,uBAAtC;AACA,OAAO,IAAIC,2BAA2B,GAAG;EACvCF,gBADuC;EAEvCC;AAFuC,CAAlC"}, "metadata": {}, "sourceType": "module"}