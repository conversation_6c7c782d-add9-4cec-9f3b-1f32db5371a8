{"ast": null, "code": "import { memoize } from '../function.js';\n/**\n * Calculate BigNumber e\n * @param {function} BigNumber   BigNumber constructor\n * @returns {BigNumber} Returns e\n */\n\nexport var createBigNumberE = memoize(function (BigNumber) {\n  return new BigNumber(1).exp();\n}, {\n  hasher\n});\n/**\n * Calculate BigNumber golden ratio, phi = (1+sqrt(5))/2\n * @param {function} BigNumber   BigNumber constructor\n * @returns {BigNumber} Returns phi\n */\n\nexport var createBigNumberPhi = memoize(function (BigNumber) {\n  return new BigNumber(1).plus(new BigNumber(5).sqrt()).div(2);\n}, {\n  hasher\n});\n/**\n * Calculate BigNumber pi.\n * @param {function} BigNumber   BigNumber constructor\n * @returns {BigNumber} Returns pi\n */\n\nexport var createBigNumberPi = memoize(function (BigNumber) {\n  return BigNumber.acos(-1);\n}, {\n  hasher\n});\n/**\n * Calculate BigNumber tau, tau = 2 * pi\n * @param {function} BigNumber   BigNumber constructor\n * @returns {BigNumber} Returns tau\n */\n\nexport var createBigNumberTau = memoize(function (BigNumber) {\n  return createBigNumberPi(BigNumber).times(2);\n}, {\n  hasher\n});\n/**\n * Create a hash for a BigNumber constructor function. The created has is\n * the configured precision\n * @param {Array} args         Supposed to contain a single entry with\n *                             a BigNumber constructor\n * @return {number} precision\n * @private\n */\n\nfunction hasher(args) {\n  return args[0].precision;\n}", "map": null, "metadata": {}, "sourceType": "module"}