{"ast": null, "code": "import { popperGenerator, detectOverflow } from \"./createPopper.js\";\nimport eventListeners from \"./modifiers/eventListeners.js\";\nimport popperOffsets from \"./modifiers/popperOffsets.js\";\nimport computeStyles from \"./modifiers/computeStyles.js\";\nimport applyStyles from \"./modifiers/applyStyles.js\";\nvar defaultModifiers = [eventListeners, popperOffsets, computeStyles, applyStyles];\nvar createPopper = /*#__PURE__*/popperGenerator({\n  defaultModifiers: defaultModifiers\n}); // eslint-disable-next-line import/no-unused-modules\n\nexport { createPopper, popperGenerator, defaultModifiers, detectOverflow };", "map": {"version": 3, "names": ["popperGenerator", "detectOverflow", "eventListeners", "popperOffsets", "computeStyles", "applyStyles", "defaultModifiers", "createPopper"], "sources": ["D:/work/joyserver/client/node_modules/@popperjs/core/lib/popper-lite.js"], "sourcesContent": ["import { popperGenerator, detectOverflow } from \"./createPopper.js\";\nimport eventListeners from \"./modifiers/eventListeners.js\";\nimport popperOffsets from \"./modifiers/popperOffsets.js\";\nimport computeStyles from \"./modifiers/computeStyles.js\";\nimport applyStyles from \"./modifiers/applyStyles.js\";\nvar defaultModifiers = [eventListeners, popperOffsets, computeStyles, applyStyles];\nvar createPopper = /*#__PURE__*/popperGenerator({\n  defaultModifiers: defaultModifiers\n}); // eslint-disable-next-line import/no-unused-modules\n\nexport { createPopper, popperGenerator, defaultModifiers, detectOverflow };"], "mappings": "AAAA,SAASA,eAAT,EAA0BC,cAA1B,QAAgD,mBAAhD;AACA,OAAOC,cAAP,MAA2B,+BAA3B;AACA,OAAOC,aAAP,MAA0B,8BAA1B;AACA,OAAOC,aAAP,MAA0B,8BAA1B;AACA,OAAOC,WAAP,MAAwB,4BAAxB;AACA,IAAIC,gBAAgB,GAAG,CAACJ,cAAD,EAAiBC,aAAjB,EAAgCC,aAAhC,EAA+CC,WAA/C,CAAvB;AACA,IAAIE,YAAY,GAAG,aAAaP,eAAe,CAAC;EAC9CM,gBAAgB,EAAEA;AAD4B,CAAD,CAA/C,C,CAEI;;AAEJ,SAASC,YAAT,EAAuBP,eAAvB,EAAwCM,gBAAxC,EAA0DL,cAA1D"}, "metadata": {}, "sourceType": "module"}