{"ast": null, "code": "/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { NodeDependencies } from './dependenciesNode.generated.js';\nimport { subsetDependencies } from './dependenciesSubset.generated.js';\nimport { createAccessorNode } from '../../factoriesAny.js';\nexport var AccessorNodeDependencies = {\n  NodeDependencies,\n  subsetDependencies,\n  createAccessorNode\n};", "map": {"version": 3, "names": ["NodeDependencies", "subsetDependencies", "createAccessorNode", "AccessorNodeDependencies"], "sources": ["D:/work/joyserver/client/node_modules/mathjs/lib/esm/entry/dependenciesAny/dependenciesAccessorNode.generated.js"], "sourcesContent": ["/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { NodeDependencies } from './dependenciesNode.generated.js';\nimport { subsetDependencies } from './dependenciesSubset.generated.js';\nimport { createAccessorNode } from '../../factoriesAny.js';\nexport var AccessorNodeDependencies = {\n  NodeDependencies,\n  subsetDependencies,\n  createAccessorNode\n};"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,gBAAT,QAAiC,iCAAjC;AACA,SAASC,kBAAT,QAAmC,mCAAnC;AACA,SAASC,kBAAT,QAAmC,uBAAnC;AACA,OAAO,IAAIC,wBAAwB,GAAG;EACpCH,gBADoC;EAEpCC,kBAFoC;EAGpCC;AAHoC,CAA/B"}, "metadata": {}, "sourceType": "module"}