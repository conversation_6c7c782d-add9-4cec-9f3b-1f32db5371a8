{"ast": null, "code": "var Utils = require('./utils');\n\nfunction getColorAttrib(color, attrib) {\n  var alpha = color.a / 255;\n  var str = attrib + '=\"' + color.hex + '\"';\n  return alpha < 1 ? str + ' ' + attrib + '-opacity=\"' + alpha.toFixed(2).slice(1) + '\"' : str;\n}\n\nfunction svgCmd(cmd, x, y) {\n  var str = cmd + x;\n  if (typeof y !== 'undefined') str += ' ' + y;\n  return str;\n}\n\nfunction qrToPath(data, size, margin) {\n  var path = '';\n  var moveBy = 0;\n  var newRow = false;\n  var lineLength = 0;\n\n  for (var i = 0; i < data.length; i++) {\n    var col = Math.floor(i % size);\n    var row = Math.floor(i / size);\n    if (!col && !newRow) newRow = true;\n\n    if (data[i]) {\n      lineLength++;\n\n      if (!(i > 0 && col > 0 && data[i - 1])) {\n        path += newRow ? svgCmd('M', col + margin, 0.5 + row + margin) : svgCmd('m', moveBy, 0);\n        moveBy = 0;\n        newRow = false;\n      }\n\n      if (!(col + 1 < size && data[i + 1])) {\n        path += svgCmd('h', lineLength);\n        lineLength = 0;\n      }\n    } else {\n      moveBy++;\n    }\n  }\n\n  return path;\n}\n\nexports.render = function render(qrData, options, cb) {\n  var opts = Utils.getOptions(options);\n  var size = qrData.modules.size;\n  var data = qrData.modules.data;\n  var qrcodesize = size + opts.margin * 2;\n  var bg = !opts.color.light.a ? '' : '<path ' + getColorAttrib(opts.color.light, 'fill') + ' d=\"M0 0h' + qrcodesize + 'v' + qrcodesize + 'H0z\"/>';\n  var path = '<path ' + getColorAttrib(opts.color.dark, 'stroke') + ' d=\"' + qrToPath(data, size, opts.margin) + '\"/>';\n  var viewBox = 'viewBox=\"' + '0 0 ' + qrcodesize + ' ' + qrcodesize + '\"';\n  var width = !opts.width ? '' : 'width=\"' + opts.width + '\" height=\"' + opts.width + '\" ';\n  var svgTag = '<svg xmlns=\"http://www.w3.org/2000/svg\" ' + width + viewBox + ' shape-rendering=\"crispEdges\">' + bg + path + '</svg>\\n';\n\n  if (typeof cb === 'function') {\n    cb(null, svgTag);\n  }\n\n  return svgTag;\n};", "map": null, "metadata": {}, "sourceType": "script"}