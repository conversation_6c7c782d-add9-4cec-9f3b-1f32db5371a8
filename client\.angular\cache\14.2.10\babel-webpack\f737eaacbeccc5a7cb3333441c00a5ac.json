{"ast": null, "code": "import { factory } from '../../utils/factory.js';\nimport { sechNumber } from '../../plain/number/index.js';\nvar name = 'sech';\nvar dependencies = ['typed', 'BigNumber'];\nexport var createSech = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed,\n    BigNumber: _BigNumber\n  } = _ref;\n  /**\n   * Calculate the hyperbolic secant of a value,\n   * defined as `sech(x) = 1 / cosh(x)`.\n   *\n   * To avoid confusion with the matrix hyperbolic secant, this function does\n   * not apply to matrices.\n   *\n   * Syntax:\n   *\n   *    math.sech(x)\n   *\n   * Examples:\n   *\n   *    // sech(x) = 1/ cosh(x)\n   *    math.sech(0.5)       // returns 0.886818883970074\n   *    1 / math.cosh(0.5)   // returns 0.886818883970074\n   *\n   * See also:\n   *\n   *    cosh, csch, coth\n   *\n   * @param {number | BigNumber | Complex} x  Function input\n   * @return {number | BigNumber | Complex} Hyperbolic secant of x\n   */\n\n  return typed(name, {\n    number: sechNumber,\n    Complex: x => x.sech(),\n    BigNumber: x => new _BigNumber(1).div(x.cosh())\n  });\n});", "map": {"version": 3, "names": ["factory", "sechNumber", "name", "dependencies", "createSech", "_ref", "typed", "BigNumber", "_BigNumber", "number", "Complex", "x", "sech", "div", "cosh"], "sources": ["D:/work/joyserver/client/node_modules/mathjs/lib/esm/function/trigonometry/sech.js"], "sourcesContent": ["import { factory } from '../../utils/factory.js';\nimport { sechNumber } from '../../plain/number/index.js';\nvar name = 'sech';\nvar dependencies = ['typed', 'BigNumber'];\nexport var createSech = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed,\n    BigNumber: _BigNumber\n  } = _ref;\n  /**\n   * Calculate the hyperbolic secant of a value,\n   * defined as `sech(x) = 1 / cosh(x)`.\n   *\n   * To avoid confusion with the matrix hyperbolic secant, this function does\n   * not apply to matrices.\n   *\n   * Syntax:\n   *\n   *    math.sech(x)\n   *\n   * Examples:\n   *\n   *    // sech(x) = 1/ cosh(x)\n   *    math.sech(0.5)       // returns 0.886818883970074\n   *    1 / math.cosh(0.5)   // returns 0.886818883970074\n   *\n   * See also:\n   *\n   *    cosh, csch, coth\n   *\n   * @param {number | BigNumber | Complex} x  Function input\n   * @return {number | BigNumber | Complex} Hyperbolic secant of x\n   */\n  return typed(name, {\n    number: sechNumber,\n    Complex: x => x.sech(),\n    BigNumber: x => new _BigNumber(1).div(x.cosh())\n  });\n});"], "mappings": "AAAA,SAASA,OAAT,QAAwB,wBAAxB;AACA,SAASC,UAAT,QAA2B,6BAA3B;AACA,IAAIC,IAAI,GAAG,MAAX;AACA,IAAIC,YAAY,GAAG,CAAC,OAAD,EAAU,WAAV,CAAnB;AACA,OAAO,IAAIC,UAAU,GAAG,eAAeJ,OAAO,CAACE,IAAD,EAAOC,YAAP,EAAqBE,IAAI,IAAI;EACzE,IAAI;IACFC,KADE;IAEFC,SAAS,EAAEC;EAFT,IAGAH,IAHJ;EAIA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EACE,OAAOC,KAAK,CAACJ,IAAD,EAAO;IACjBO,MAAM,EAAER,UADS;IAEjBS,OAAO,EAAEC,CAAC,IAAIA,CAAC,CAACC,IAAF,EAFG;IAGjBL,SAAS,EAAEI,CAAC,IAAI,IAAIH,UAAJ,CAAe,CAAf,EAAkBK,GAAlB,CAAsBF,CAAC,CAACG,IAAF,EAAtB;EAHC,CAAP,CAAZ;AAKD,CAlC6C,CAAvC"}, "metadata": {}, "sourceType": "module"}