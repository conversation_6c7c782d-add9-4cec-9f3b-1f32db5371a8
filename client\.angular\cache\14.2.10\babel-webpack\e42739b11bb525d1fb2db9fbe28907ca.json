{"ast": null, "code": "/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { BigNumberDependencies } from './dependenciesBigNumberClass.generated.js';\nimport { UnitDependencies } from './dependenciesUnitClass.generated.js';\nimport { createLoschmidt } from '../../factoriesAny.js';\nexport var loschmidtDependencies = {\n  BigNumberDependencies,\n  UnitDependencies,\n  createLoschmidt\n};", "map": {"version": 3, "names": ["BigNumberDependencies", "UnitDependencies", "create<PERSON><PERSON><PERSON><PERSON>dt", "loschmidtDependencies"], "sources": ["D:/work/joyserver/client/node_modules/mathjs/lib/esm/entry/dependenciesAny/dependenciesLoschmidt.generated.js"], "sourcesContent": ["/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { BigNumberDependencies } from './dependenciesBigNumberClass.generated.js';\nimport { UnitDependencies } from './dependenciesUnitClass.generated.js';\nimport { createLoschmidt } from '../../factoriesAny.js';\nexport var loschmidtDependencies = {\n  BigNumberDependencies,\n  UnitDependencies,\n  createLoschmidt\n};"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,qBAAT,QAAsC,2CAAtC;AACA,SAASC,gBAAT,QAAiC,sCAAjC;AACA,SAASC,eAAT,QAAgC,uBAAhC;AACA,OAAO,IAAIC,qBAAqB,GAAG;EACjCH,qBADiC;EAEjCC,gBAFiC;EAGjCC;AAHiC,CAA5B"}, "metadata": {}, "sourceType": "module"}