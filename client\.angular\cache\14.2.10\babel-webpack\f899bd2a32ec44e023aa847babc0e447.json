{"ast": null, "code": "import { factory } from '../../utils/factory.js';\nimport { isInteger } from '../../utils/number.js';\nimport { arraySize as size } from '../../utils/array.js';\nimport { powNumber } from '../../plain/number/index.js';\nvar name = 'pow';\nvar dependencies = ['typed', 'config', 'identity', 'multiply', 'matrix', 'inv', 'fraction', 'number', 'Complex'];\nexport var createPow = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed,\n    config,\n    identity,\n    multiply,\n    matrix,\n    inv,\n    number,\n    fraction,\n    Complex\n  } = _ref;\n  /**\n   * Calculates the power of x to y, `x ^ y`.\n   *\n   * Matrix exponentiation is supported for square matrices `x` and integers `y`:\n   * when `y` is nonnegative, `x` may be any square matrix; and when `y` is\n   * negative, `x` must be invertible, and then this function returns\n   * inv(x)^(-y).\n   *\n   * For cubic roots of negative numbers, the function returns the principal\n   * root by default. In order to let the function return the real root,\n   * math.js can be configured with `math.config({predictable: true})`.\n   * To retrieve all cubic roots of a value, use `math.cbrt(x, true)`.\n   *\n   * Syntax:\n   *\n   *    math.pow(x, y)\n   *\n   * Examples:\n   *\n   *    math.pow(2, 3)               // returns number 8\n   *\n   *    const a = math.complex(2, 3)\n   *    math.pow(a, 2)                // returns Complex -5 + 12i\n   *\n   *    const b = [[1, 2], [4, 3]]\n   *    math.pow(b, 2)               // returns Array [[9, 8], [16, 17]]\n   *\n   *    const c = [[1, 2], [4, 3]]\n   *    math.pow(c, -1)               // returns Array [[-0.6, 0.4], [0.8, -0.2]]\n   *\n   * See also:\n   *\n   *    multiply, sqrt, cbrt, nthRoot\n   *\n   * @param  {number | BigNumber | bigint | Complex | Unit | Array | Matrix} x  The base\n   * @param  {number | BigNumber | bigint | Complex} y                          The exponent\n   * @return {number | BigNumber | bigint | Complex | Array | Matrix} The value of `x` to the power `y`\n   */\n\n  return typed(name, {\n    'number, number': _pow,\n    'Complex, Complex': function Complex_Complex(x, y) {\n      return x.pow(y);\n    },\n    'BigNumber, BigNumber': function BigNumber_BigNumber(x, y) {\n      if (y.isInteger() || x >= 0 || config.predictable) {\n        return x.pow(y);\n      } else {\n        return new Complex(x.toNumber(), 0).pow(y.toNumber(), 0);\n      }\n    },\n    'bigint, bigint': (x, y) => x ** y,\n    'Fraction, Fraction': function Fraction_Fraction(x, y) {\n      var result = x.pow(y);\n\n      if (result != null) {\n        return result;\n      }\n\n      if (config.predictable) {\n        throw new Error('Result of pow is non-rational and cannot be expressed as a fraction');\n      } else {\n        return _pow(x.valueOf(), y.valueOf());\n      }\n    },\n    'Array, number': _powArray,\n    'Array, BigNumber': function Array_BigNumber(x, y) {\n      return _powArray(x, y.toNumber());\n    },\n    'Matrix, number': _powMatrix,\n    'Matrix, BigNumber': function Matrix_BigNumber(x, y) {\n      return _powMatrix(x, y.toNumber());\n    },\n    'Unit, number | BigNumber': function Unit_number__BigNumber(x, y) {\n      return x.pow(y);\n    }\n  });\n  /**\n   * Calculates the power of x to y, x^y, for two numbers.\n   * @param {number} x\n   * @param {number} y\n   * @return {number | Complex} res\n   * @private\n   */\n\n  function _pow(x, y) {\n    // Alternatively could define a 'realmode' config option or something, but\n    // 'predictable' will work for now\n    if (config.predictable && !isInteger(y) && x < 0) {\n      // Check to see if y can be represented as a fraction\n      try {\n        var yFrac = fraction(y);\n        var yNum = number(yFrac);\n\n        if (y === yNum || Math.abs((y - yNum) / y) < 1e-14) {\n          if (yFrac.d % 2n === 1n) {\n            return (yFrac.n % 2n === 0n ? 1 : -1) * Math.pow(-x, y);\n          }\n        }\n      } catch (ex) {// fraction() throws an error if y is Infinity, etc.\n      } // Unable to express y as a fraction, so continue on\n\n    } // **for predictable mode** x^Infinity === NaN if x < -1\n    // N.B. this behavour is different from `Math.pow` which gives\n    // (-2)^Infinity === Infinity\n\n\n    if (config.predictable && (x < -1 && y === Infinity || x > -1 && x < 0 && y === -Infinity)) {\n      return NaN;\n    }\n\n    if (isInteger(y) || x >= 0 || config.predictable) {\n      return powNumber(x, y);\n    } else {\n      // TODO: the following infinity checks are duplicated from powNumber. Deduplicate this somehow\n      // x^Infinity === 0 if -1 < x < 1\n      // A real number 0 is returned instead of complex(0)\n      if (x * x < 1 && y === Infinity || x * x > 1 && y === -Infinity) {\n        return 0;\n      }\n\n      return new Complex(x, 0).pow(y, 0);\n    }\n  }\n  /**\n   * Calculate the power of a 2d array\n   * @param {Array} x     must be a 2 dimensional, square matrix\n   * @param {number} y    a integer value (positive if `x` is not invertible)\n   * @returns {Array}\n   * @private\n   */\n\n\n  function _powArray(x, y) {\n    if (!isInteger(y)) {\n      throw new TypeError('For A^b, b must be an integer (value is ' + y + ')');\n    } // verify that A is a 2 dimensional square matrix\n\n\n    var s = size(x);\n\n    if (s.length !== 2) {\n      throw new Error('For A^b, A must be 2 dimensional (A has ' + s.length + ' dimensions)');\n    }\n\n    if (s[0] !== s[1]) {\n      throw new Error('For A^b, A must be square (size is ' + s[0] + 'x' + s[1] + ')');\n    }\n\n    if (y < 0) {\n      try {\n        return _powArray(inv(x), -y);\n      } catch (error) {\n        if (error.message === 'Cannot calculate inverse, determinant is zero') {\n          throw new TypeError('For A^b, when A is not invertible, b must be a positive integer (value is ' + y + ')');\n        }\n\n        throw error;\n      }\n    }\n\n    var res = identity(s[0]).valueOf();\n    var px = x;\n\n    while (y >= 1) {\n      if ((y & 1) === 1) {\n        res = multiply(px, res);\n      }\n\n      y >>= 1;\n      px = multiply(px, px);\n    }\n\n    return res;\n  }\n  /**\n   * Calculate the power of a 2d matrix\n   * @param {Matrix} x     must be a 2 dimensional, square matrix\n   * @param {number} y    a positive, integer value\n   * @returns {Matrix}\n   * @private\n   */\n\n\n  function _powMatrix(x, y) {\n    return matrix(_powArray(x.valueOf(), y));\n  }\n});", "map": null, "metadata": {}, "sourceType": "module"}