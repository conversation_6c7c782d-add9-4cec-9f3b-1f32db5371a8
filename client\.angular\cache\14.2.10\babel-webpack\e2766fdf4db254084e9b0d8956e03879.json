{"ast": null, "code": "import { isScheduler } from '../util/isScheduler';\nimport { isArray } from '../util/isArray';\nimport { OuterSubscriber } from '../OuterSubscriber';\nimport { subscribeToResult } from '../util/subscribeToResult';\nimport { fromArray } from './fromArray';\nconst NONE = {};\nexport function combineLatest(...observables) {\n  let resultSelector = undefined;\n  let scheduler = undefined;\n\n  if (isScheduler(observables[observables.length - 1])) {\n    scheduler = observables.pop();\n  }\n\n  if (typeof observables[observables.length - 1] === 'function') {\n    resultSelector = observables.pop();\n  }\n\n  if (observables.length === 1 && isArray(observables[0])) {\n    observables = observables[0];\n  }\n\n  return fromArray(observables, scheduler).lift(new CombineLatestOperator(resultSelector));\n}\nexport class CombineLatestOperator {\n  constructor(resultSelector) {\n    this.resultSelector = resultSelector;\n  }\n\n  call(subscriber, source) {\n    return source.subscribe(new CombineLatestSubscriber(subscriber, this.resultSelector));\n  }\n\n}\nexport class CombineLatestSubscriber extends OuterSubscriber {\n  constructor(destination, resultSelector) {\n    super(destination);\n    this.resultSelector = resultSelector;\n    this.active = 0;\n    this.values = [];\n    this.observables = [];\n  }\n\n  _next(observable) {\n    this.values.push(NONE);\n    this.observables.push(observable);\n  }\n\n  _complete() {\n    const observables = this.observables;\n    const len = observables.length;\n\n    if (len === 0) {\n      this.destination.complete();\n    } else {\n      this.active = len;\n      this.toRespond = len;\n\n      for (let i = 0; i < len; i++) {\n        const observable = observables[i];\n        this.add(subscribeToResult(this, observable, undefined, i));\n      }\n    }\n  }\n\n  notifyComplete(unused) {\n    if ((this.active -= 1) === 0) {\n      this.destination.complete();\n    }\n  }\n\n  notifyNext(_outerValue, innerValue, outerIndex) {\n    const values = this.values;\n    const oldVal = values[outerIndex];\n    const toRespond = !this.toRespond ? 0 : oldVal === NONE ? --this.toRespond : this.toRespond;\n    values[outerIndex] = innerValue;\n\n    if (toRespond === 0) {\n      if (this.resultSelector) {\n        this._tryResultSelector(values);\n      } else {\n        this.destination.next(values.slice());\n      }\n    }\n  }\n\n  _tryResultSelector(values) {\n    let result;\n\n    try {\n      result = this.resultSelector.apply(this, values);\n    } catch (err) {\n      this.destination.error(err);\n      return;\n    }\n\n    this.destination.next(result);\n  }\n\n} //# sourceMappingURL=combineLatest.js.map", "map": null, "metadata": {}, "sourceType": "module"}