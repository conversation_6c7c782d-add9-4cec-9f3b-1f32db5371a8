{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Injectable, Directive, EventEmitter, Optional, Inject, Input, Output, NgModule } from '@angular/core';\nimport { Subject, Observable, ReplaySubject, merge, combineLatest, fromEvent } from 'rxjs';\nimport { filter, mergeMap, startWith, map, share, takeUntil, take, takeLast, count, pairwise, distinctUntilChanged } from 'rxjs/operators';\nimport { DOCUMENT } from '@angular/common';\nimport autoScroll from '@mattlewis92/dom-autoscroller';\n\nfunction addClass(renderer, element, classToAdd) {\n  if (classToAdd) {\n    classToAdd.split(' ').forEach(className => renderer.addClass(element.nativeElement, className));\n  }\n}\n\nfunction removeClass(renderer, element, classToRemove) {\n  if (classToRemove) {\n    classToRemove.split(' ').forEach(className => renderer.removeClass(element.nativeElement, className));\n  }\n}\n\nclass DraggableHelper {\n  constructor() {\n    this.currentDrag = new Subject();\n  }\n\n}\n\nDraggableHelper.ɵfac = function DraggableHelper_Factory(t) {\n  return new (t || DraggableHelper)();\n};\n\nDraggableHelper.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: DraggableHelper,\n  factory: DraggableHelper.ɵfac,\n  providedIn: 'root'\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DraggableHelper, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\n/**\n * If the window isn't scrollable, then place this on the scrollable container that draggable elements are inside. e.g.\n * ```html\n  <div style=\"overflow: scroll\" mwlDraggableScrollContainer>\n    <div mwlDraggable>Drag me!</div>\n  </div>\n  ```\n */\n\n\nclass DraggableScrollContainerDirective {\n  /**\n   * @hidden\n   */\n  constructor(elementRef) {\n    this.elementRef = elementRef;\n  }\n\n}\n\nDraggableScrollContainerDirective.ɵfac = function DraggableScrollContainerDirective_Factory(t) {\n  return new (t || DraggableScrollContainerDirective)(i0.ɵɵdirectiveInject(i0.ElementRef));\n};\n\nDraggableScrollContainerDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: DraggableScrollContainerDirective,\n  selectors: [[\"\", \"mwlDraggableScrollContainer\", \"\"]]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DraggableScrollContainerDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[mwlDraggableScrollContainer]'\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }];\n  }, null);\n})();\n\nclass DraggableDirective {\n  /**\n   * @hidden\n   */\n  constructor(element, renderer, draggableHelper, zone, vcr, scrollContainer, document) {\n    this.element = element;\n    this.renderer = renderer;\n    this.draggableHelper = draggableHelper;\n    this.zone = zone;\n    this.vcr = vcr;\n    this.scrollContainer = scrollContainer;\n    this.document = document;\n    /**\n     * The axis along which the element is draggable\n     */\n\n    this.dragAxis = {\n      x: true,\n      y: true\n    };\n    /**\n     * Snap all drags to an x / y grid\n     */\n\n    this.dragSnapGrid = {};\n    /**\n     * Show a ghost element that shows the drag when dragging\n     */\n\n    this.ghostDragEnabled = true;\n    /**\n     * Show the original element when ghostDragEnabled is true\n     */\n\n    this.showOriginalElementWhileDragging = false;\n    /**\n     * The cursor to use when hovering over a draggable element\n     */\n\n    this.dragCursor = '';\n    /*\n     * Options used to control the behaviour of auto scrolling: https://www.npmjs.com/package/dom-autoscroller\n     */\n\n    this.autoScroll = {\n      margin: 20\n    };\n    /**\n     * Called when the element can be dragged along one axis and has the mouse or pointer device pressed on it\n     */\n\n    this.dragPointerDown = new EventEmitter();\n    /**\n     * Called when the element has started to be dragged.\n     * Only called after at least one mouse or touch move event.\n     * If you call $event.cancelDrag$.emit() it will cancel the current drag\n     */\n\n    this.dragStart = new EventEmitter();\n    /**\n     * Called after the ghost element has been created\n     */\n\n    this.ghostElementCreated = new EventEmitter();\n    /**\n     * Called when the element is being dragged\n     */\n\n    this.dragging = new EventEmitter();\n    /**\n     * Called after the element is dragged\n     */\n\n    this.dragEnd = new EventEmitter();\n    /**\n     * @hidden\n     */\n\n    this.pointerDown$ = new Subject();\n    /**\n     * @hidden\n     */\n\n    this.pointerMove$ = new Subject();\n    /**\n     * @hidden\n     */\n\n    this.pointerUp$ = new Subject();\n    this.eventListenerSubscriptions = {};\n    this.destroy$ = new Subject();\n    this.timeLongPress = {\n      timerBegin: 0,\n      timerEnd: 0\n    };\n  }\n\n  ngOnInit() {\n    this.checkEventListeners();\n    const pointerDragged$ = this.pointerDown$.pipe(filter(() => this.canDrag()), mergeMap(pointerDownEvent => {\n      // fix for https://github.com/mattlewis92/angular-draggable-droppable/issues/61\n      // stop mouse events propagating up the chain\n      if (pointerDownEvent.event.stopPropagation && !this.scrollContainer) {\n        pointerDownEvent.event.stopPropagation();\n      } // hack to prevent text getting selected in safari while dragging\n\n\n      const globalDragStyle = this.renderer.createElement('style');\n      this.renderer.setAttribute(globalDragStyle, 'type', 'text/css');\n      this.renderer.appendChild(globalDragStyle, this.renderer.createText(`\n          body * {\n           -moz-user-select: none;\n           -ms-user-select: none;\n           -webkit-user-select: none;\n           user-select: none;\n          }\n        `));\n      requestAnimationFrame(() => {\n        this.document.head.appendChild(globalDragStyle);\n      });\n      const startScrollPosition = this.getScrollPosition();\n      const scrollContainerScroll$ = new Observable(observer => {\n        const scrollContainer = this.scrollContainer ? this.scrollContainer.elementRef.nativeElement : 'window';\n        return this.renderer.listen(scrollContainer, 'scroll', e => observer.next(e));\n      }).pipe(startWith(startScrollPosition), map(() => this.getScrollPosition()));\n      const currentDrag$ = new Subject();\n      const cancelDrag$ = new ReplaySubject();\n\n      if (this.dragPointerDown.observers.length > 0) {\n        this.zone.run(() => {\n          this.dragPointerDown.next({\n            x: 0,\n            y: 0\n          });\n        });\n      }\n\n      const dragComplete$ = merge(this.pointerUp$, this.pointerDown$, cancelDrag$, this.destroy$).pipe(share());\n      const pointerMove = combineLatest([this.pointerMove$, scrollContainerScroll$]).pipe(map(([pointerMoveEvent, scroll]) => {\n        return {\n          currentDrag$,\n          transformX: pointerMoveEvent.clientX - pointerDownEvent.clientX,\n          transformY: pointerMoveEvent.clientY - pointerDownEvent.clientY,\n          clientX: pointerMoveEvent.clientX,\n          clientY: pointerMoveEvent.clientY,\n          scrollLeft: scroll.left,\n          scrollTop: scroll.top,\n          target: pointerMoveEvent.event.target\n        };\n      }), map(moveData => {\n        if (this.dragSnapGrid.x) {\n          moveData.transformX = Math.round(moveData.transformX / this.dragSnapGrid.x) * this.dragSnapGrid.x;\n        }\n\n        if (this.dragSnapGrid.y) {\n          moveData.transformY = Math.round(moveData.transformY / this.dragSnapGrid.y) * this.dragSnapGrid.y;\n        }\n\n        return moveData;\n      }), map(moveData => {\n        if (!this.dragAxis.x) {\n          moveData.transformX = 0;\n        }\n\n        if (!this.dragAxis.y) {\n          moveData.transformY = 0;\n        }\n\n        return moveData;\n      }), map(moveData => {\n        const scrollX = moveData.scrollLeft - startScrollPosition.left;\n        const scrollY = moveData.scrollTop - startScrollPosition.top;\n        return { ...moveData,\n          x: moveData.transformX + scrollX,\n          y: moveData.transformY + scrollY\n        };\n      }), filter(({\n        x,\n        y,\n        transformX,\n        transformY\n      }) => !this.validateDrag || this.validateDrag({\n        x,\n        y,\n        transform: {\n          x: transformX,\n          y: transformY\n        }\n      })), takeUntil(dragComplete$), share());\n      const dragStarted$ = pointerMove.pipe(take(1), share());\n      const dragEnded$ = pointerMove.pipe(takeLast(1), share());\n      dragStarted$.subscribe(({\n        clientX,\n        clientY,\n        x,\n        y\n      }) => {\n        if (this.dragStart.observers.length > 0) {\n          this.zone.run(() => {\n            this.dragStart.next({\n              cancelDrag$\n            });\n          });\n        }\n\n        this.scroller = autoScroll([this.scrollContainer ? this.scrollContainer.elementRef.nativeElement : this.document.defaultView], { ...this.autoScroll,\n\n          autoScroll() {\n            return true;\n          }\n\n        });\n        addClass(this.renderer, this.element, this.dragActiveClass);\n\n        if (this.ghostDragEnabled) {\n          const rect = this.element.nativeElement.getBoundingClientRect();\n          const clone = this.element.nativeElement.cloneNode(true);\n\n          if (!this.showOriginalElementWhileDragging) {\n            this.renderer.setStyle(this.element.nativeElement, 'visibility', 'hidden');\n          }\n\n          if (this.ghostElementAppendTo) {\n            this.ghostElementAppendTo.appendChild(clone);\n          } else {\n            this.element.nativeElement.parentNode.insertBefore(clone, this.element.nativeElement.nextSibling);\n          }\n\n          this.ghostElement = clone;\n          this.document.body.style.cursor = this.dragCursor;\n          this.setElementStyles(clone, {\n            position: 'fixed',\n            top: `${rect.top}px`,\n            left: `${rect.left}px`,\n            width: `${rect.width}px`,\n            height: `${rect.height}px`,\n            cursor: this.dragCursor,\n            margin: '0',\n            willChange: 'transform',\n            pointerEvents: 'none'\n          });\n\n          if (this.ghostElementTemplate) {\n            const viewRef = this.vcr.createEmbeddedView(this.ghostElementTemplate);\n            clone.innerHTML = '';\n            viewRef.rootNodes.filter(node => node instanceof Node).forEach(node => {\n              clone.appendChild(node);\n            });\n            dragEnded$.subscribe(() => {\n              this.vcr.remove(this.vcr.indexOf(viewRef));\n            });\n          }\n\n          if (this.ghostElementCreated.observers.length > 0) {\n            this.zone.run(() => {\n              this.ghostElementCreated.emit({\n                clientX: clientX - x,\n                clientY: clientY - y,\n                element: clone\n              });\n            });\n          }\n\n          dragEnded$.subscribe(() => {\n            clone.parentElement.removeChild(clone);\n            this.ghostElement = null;\n            this.renderer.setStyle(this.element.nativeElement, 'visibility', '');\n          });\n        }\n\n        this.draggableHelper.currentDrag.next(currentDrag$);\n      });\n      dragEnded$.pipe(mergeMap(dragEndData => {\n        const dragEndData$ = cancelDrag$.pipe(count(), take(1), map(calledCount => ({ ...dragEndData,\n          dragCancelled: calledCount > 0\n        })));\n        cancelDrag$.complete();\n        return dragEndData$;\n      })).subscribe(({\n        x,\n        y,\n        dragCancelled\n      }) => {\n        this.scroller.destroy();\n\n        if (this.dragEnd.observers.length > 0) {\n          this.zone.run(() => {\n            this.dragEnd.next({\n              x,\n              y,\n              dragCancelled\n            });\n          });\n        }\n\n        removeClass(this.renderer, this.element, this.dragActiveClass);\n        currentDrag$.complete();\n      });\n      merge(dragComplete$, dragEnded$).pipe(take(1)).subscribe(() => {\n        requestAnimationFrame(() => {\n          this.document.head.removeChild(globalDragStyle);\n        });\n      });\n      return pointerMove;\n    }), share());\n    merge(pointerDragged$.pipe(take(1), map(value => [, value])), pointerDragged$.pipe(pairwise())).pipe(filter(([previous, next]) => {\n      if (!previous) {\n        return true;\n      }\n\n      return previous.x !== next.x || previous.y !== next.y;\n    }), map(([previous, next]) => next)).subscribe(({\n      x,\n      y,\n      currentDrag$,\n      clientX,\n      clientY,\n      transformX,\n      transformY,\n      target\n    }) => {\n      if (this.dragging.observers.length > 0) {\n        this.zone.run(() => {\n          this.dragging.next({\n            x,\n            y\n          });\n        });\n      }\n\n      requestAnimationFrame(() => {\n        if (this.ghostElement) {\n          const transform = `translate3d(${transformX}px, ${transformY}px, 0px)`;\n          this.setElementStyles(this.ghostElement, {\n            transform,\n            '-webkit-transform': transform,\n            '-ms-transform': transform,\n            '-moz-transform': transform,\n            '-o-transform': transform\n          });\n        }\n      });\n      currentDrag$.next({\n        clientX,\n        clientY,\n        dropData: this.dropData,\n        target\n      });\n    });\n  }\n\n  ngOnChanges(changes) {\n    if (changes.dragAxis) {\n      this.checkEventListeners();\n    }\n  }\n\n  ngOnDestroy() {\n    this.unsubscribeEventListeners();\n    this.pointerDown$.complete();\n    this.pointerMove$.complete();\n    this.pointerUp$.complete();\n    this.destroy$.next();\n  }\n\n  checkEventListeners() {\n    const canDrag = this.canDrag();\n    const hasEventListeners = Object.keys(this.eventListenerSubscriptions).length > 0;\n\n    if (canDrag && !hasEventListeners) {\n      this.zone.runOutsideAngular(() => {\n        this.eventListenerSubscriptions.mousedown = this.renderer.listen(this.element.nativeElement, 'mousedown', event => {\n          this.onMouseDown(event);\n        });\n        this.eventListenerSubscriptions.mouseup = this.renderer.listen('document', 'mouseup', event => {\n          this.onMouseUp(event);\n        });\n        this.eventListenerSubscriptions.touchstart = this.renderer.listen(this.element.nativeElement, 'touchstart', event => {\n          this.onTouchStart(event);\n        });\n        this.eventListenerSubscriptions.touchend = this.renderer.listen('document', 'touchend', event => {\n          this.onTouchEnd(event);\n        });\n        this.eventListenerSubscriptions.touchcancel = this.renderer.listen('document', 'touchcancel', event => {\n          this.onTouchEnd(event);\n        });\n        this.eventListenerSubscriptions.mouseenter = this.renderer.listen(this.element.nativeElement, 'mouseenter', () => {\n          this.onMouseEnter();\n        });\n        this.eventListenerSubscriptions.mouseleave = this.renderer.listen(this.element.nativeElement, 'mouseleave', () => {\n          this.onMouseLeave();\n        });\n      });\n    } else if (!canDrag && hasEventListeners) {\n      this.unsubscribeEventListeners();\n    }\n  }\n\n  onMouseDown(event) {\n    if (event.button === 0) {\n      if (!this.eventListenerSubscriptions.mousemove) {\n        this.eventListenerSubscriptions.mousemove = this.renderer.listen('document', 'mousemove', mouseMoveEvent => {\n          this.pointerMove$.next({\n            event: mouseMoveEvent,\n            clientX: mouseMoveEvent.clientX,\n            clientY: mouseMoveEvent.clientY\n          });\n        });\n      }\n\n      this.pointerDown$.next({\n        event,\n        clientX: event.clientX,\n        clientY: event.clientY\n      });\n    }\n  }\n\n  onMouseUp(event) {\n    if (event.button === 0) {\n      if (this.eventListenerSubscriptions.mousemove) {\n        this.eventListenerSubscriptions.mousemove();\n        delete this.eventListenerSubscriptions.mousemove;\n      }\n\n      this.pointerUp$.next({\n        event,\n        clientX: event.clientX,\n        clientY: event.clientY\n      });\n    }\n  }\n\n  onTouchStart(event) {\n    let startScrollPosition;\n    let isDragActivated;\n    let hasContainerScrollbar;\n\n    if (this.touchStartLongPress) {\n      this.timeLongPress.timerBegin = Date.now();\n      isDragActivated = false;\n      hasContainerScrollbar = this.hasScrollbar();\n      startScrollPosition = this.getScrollPosition();\n    }\n\n    if (!this.eventListenerSubscriptions.touchmove) {\n      const contextMenuListener = fromEvent(this.document, 'contextmenu').subscribe(e => {\n        e.preventDefault();\n      });\n      const touchMoveListener = fromEvent(this.document, 'touchmove', {\n        passive: false\n      }).subscribe(touchMoveEvent => {\n        if (this.touchStartLongPress && !isDragActivated && hasContainerScrollbar) {\n          isDragActivated = this.shouldBeginDrag(event, touchMoveEvent, startScrollPosition);\n        }\n\n        if (!this.touchStartLongPress || !hasContainerScrollbar || isDragActivated) {\n          touchMoveEvent.preventDefault();\n          this.pointerMove$.next({\n            event: touchMoveEvent,\n            clientX: touchMoveEvent.targetTouches[0].clientX,\n            clientY: touchMoveEvent.targetTouches[0].clientY\n          });\n        }\n      });\n\n      this.eventListenerSubscriptions.touchmove = () => {\n        contextMenuListener.unsubscribe();\n        touchMoveListener.unsubscribe();\n      };\n    }\n\n    this.pointerDown$.next({\n      event,\n      clientX: event.touches[0].clientX,\n      clientY: event.touches[0].clientY\n    });\n  }\n\n  onTouchEnd(event) {\n    if (this.eventListenerSubscriptions.touchmove) {\n      this.eventListenerSubscriptions.touchmove();\n      delete this.eventListenerSubscriptions.touchmove;\n\n      if (this.touchStartLongPress) {\n        this.enableScroll();\n      }\n    }\n\n    this.pointerUp$.next({\n      event,\n      clientX: event.changedTouches[0].clientX,\n      clientY: event.changedTouches[0].clientY\n    });\n  }\n\n  onMouseEnter() {\n    this.setCursor(this.dragCursor);\n  }\n\n  onMouseLeave() {\n    this.setCursor('');\n  }\n\n  canDrag() {\n    return this.dragAxis.x || this.dragAxis.y;\n  }\n\n  setCursor(value) {\n    if (!this.eventListenerSubscriptions.mousemove) {\n      this.renderer.setStyle(this.element.nativeElement, 'cursor', value);\n    }\n  }\n\n  unsubscribeEventListeners() {\n    Object.keys(this.eventListenerSubscriptions).forEach(type => {\n      this.eventListenerSubscriptions[type]();\n      delete this.eventListenerSubscriptions[type];\n    });\n  }\n\n  setElementStyles(element, styles) {\n    Object.keys(styles).forEach(key => {\n      this.renderer.setStyle(element, key, styles[key]);\n    });\n  }\n\n  getScrollElement() {\n    if (this.scrollContainer) {\n      return this.scrollContainer.elementRef.nativeElement;\n    } else {\n      return this.document.body;\n    }\n  }\n\n  getScrollPosition() {\n    if (this.scrollContainer) {\n      return {\n        top: this.scrollContainer.elementRef.nativeElement.scrollTop,\n        left: this.scrollContainer.elementRef.nativeElement.scrollLeft\n      };\n    } else {\n      return {\n        top: window.pageYOffset || this.document.documentElement.scrollTop,\n        left: window.pageXOffset || this.document.documentElement.scrollLeft\n      };\n    }\n  }\n\n  shouldBeginDrag(event, touchMoveEvent, startScrollPosition) {\n    const moveScrollPosition = this.getScrollPosition();\n    const deltaScroll = {\n      top: Math.abs(moveScrollPosition.top - startScrollPosition.top),\n      left: Math.abs(moveScrollPosition.left - startScrollPosition.left)\n    };\n    const deltaX = Math.abs(touchMoveEvent.targetTouches[0].clientX - event.touches[0].clientX) - deltaScroll.left;\n    const deltaY = Math.abs(touchMoveEvent.targetTouches[0].clientY - event.touches[0].clientY) - deltaScroll.top;\n    const deltaTotal = deltaX + deltaY;\n    const longPressConfig = this.touchStartLongPress;\n\n    if (deltaTotal > longPressConfig.delta || deltaScroll.top > 0 || deltaScroll.left > 0) {\n      this.timeLongPress.timerBegin = Date.now();\n    }\n\n    this.timeLongPress.timerEnd = Date.now();\n    const duration = this.timeLongPress.timerEnd - this.timeLongPress.timerBegin;\n\n    if (duration >= longPressConfig.delay) {\n      this.disableScroll();\n      return true;\n    }\n\n    return false;\n  }\n\n  enableScroll() {\n    if (this.scrollContainer) {\n      this.renderer.setStyle(this.scrollContainer.elementRef.nativeElement, 'overflow', '');\n    }\n\n    this.renderer.setStyle(this.document.body, 'overflow', '');\n  }\n\n  disableScroll() {\n    /* istanbul ignore next */\n    if (this.scrollContainer) {\n      this.renderer.setStyle(this.scrollContainer.elementRef.nativeElement, 'overflow', 'hidden');\n    }\n\n    this.renderer.setStyle(this.document.body, 'overflow', 'hidden');\n  }\n\n  hasScrollbar() {\n    const scrollContainer = this.getScrollElement();\n    const containerHasHorizontalScroll = scrollContainer.scrollWidth > scrollContainer.clientWidth;\n    const containerHasVerticalScroll = scrollContainer.scrollHeight > scrollContainer.clientHeight;\n    return containerHasHorizontalScroll || containerHasVerticalScroll;\n  }\n\n}\n\nDraggableDirective.ɵfac = function DraggableDirective_Factory(t) {\n  return new (t || DraggableDirective)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(DraggableHelper), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(DraggableScrollContainerDirective, 8), i0.ɵɵdirectiveInject(DOCUMENT));\n};\n\nDraggableDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: DraggableDirective,\n  selectors: [[\"\", \"mwlDraggable\", \"\"]],\n  inputs: {\n    dropData: \"dropData\",\n    dragAxis: \"dragAxis\",\n    dragSnapGrid: \"dragSnapGrid\",\n    ghostDragEnabled: \"ghostDragEnabled\",\n    showOriginalElementWhileDragging: \"showOriginalElementWhileDragging\",\n    validateDrag: \"validateDrag\",\n    dragCursor: \"dragCursor\",\n    dragActiveClass: \"dragActiveClass\",\n    ghostElementAppendTo: \"ghostElementAppendTo\",\n    ghostElementTemplate: \"ghostElementTemplate\",\n    touchStartLongPress: \"touchStartLongPress\",\n    autoScroll: \"autoScroll\"\n  },\n  outputs: {\n    dragPointerDown: \"dragPointerDown\",\n    dragStart: \"dragStart\",\n    ghostElementCreated: \"ghostElementCreated\",\n    dragging: \"dragging\",\n    dragEnd: \"dragEnd\"\n  },\n  features: [i0.ɵɵNgOnChangesFeature]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DraggableDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[mwlDraggable]'\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: i0.Renderer2\n    }, {\n      type: DraggableHelper\n    }, {\n      type: i0.NgZone\n    }, {\n      type: i0.ViewContainerRef\n    }, {\n      type: DraggableScrollContainerDirective,\n      decorators: [{\n        type: Optional\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }];\n  }, {\n    dropData: [{\n      type: Input\n    }],\n    dragAxis: [{\n      type: Input\n    }],\n    dragSnapGrid: [{\n      type: Input\n    }],\n    ghostDragEnabled: [{\n      type: Input\n    }],\n    showOriginalElementWhileDragging: [{\n      type: Input\n    }],\n    validateDrag: [{\n      type: Input\n    }],\n    dragCursor: [{\n      type: Input\n    }],\n    dragActiveClass: [{\n      type: Input\n    }],\n    ghostElementAppendTo: [{\n      type: Input\n    }],\n    ghostElementTemplate: [{\n      type: Input\n    }],\n    touchStartLongPress: [{\n      type: Input\n    }],\n    autoScroll: [{\n      type: Input\n    }],\n    dragPointerDown: [{\n      type: Output\n    }],\n    dragStart: [{\n      type: Output\n    }],\n    ghostElementCreated: [{\n      type: Output\n    }],\n    dragging: [{\n      type: Output\n    }],\n    dragEnd: [{\n      type: Output\n    }]\n  });\n})();\n\nfunction isCoordinateWithinRectangle(clientX, clientY, rect) {\n  return clientX >= rect.left && clientX <= rect.right && clientY >= rect.top && clientY <= rect.bottom;\n}\n\nclass DroppableDirective {\n  constructor(element, draggableHelper, zone, renderer, scrollContainer) {\n    this.element = element;\n    this.draggableHelper = draggableHelper;\n    this.zone = zone;\n    this.renderer = renderer;\n    this.scrollContainer = scrollContainer;\n    /**\n     * Called when a draggable element starts overlapping the element\n     */\n\n    this.dragEnter = new EventEmitter();\n    /**\n     * Called when a draggable element stops overlapping the element\n     */\n\n    this.dragLeave = new EventEmitter();\n    /**\n     * Called when a draggable element is moved over the element\n     */\n\n    this.dragOver = new EventEmitter();\n    /**\n     * Called when a draggable element is dropped on this element\n     */\n\n    this.drop = new EventEmitter(); // eslint-disable-line  @angular-eslint/no-output-native\n  }\n\n  ngOnInit() {\n    this.currentDragSubscription = this.draggableHelper.currentDrag.subscribe(drag$ => {\n      addClass(this.renderer, this.element, this.dragActiveClass);\n      const droppableElement = {\n        updateCache: true\n      };\n      const deregisterScrollListener = this.renderer.listen(this.scrollContainer ? this.scrollContainer.elementRef.nativeElement : 'window', 'scroll', () => {\n        droppableElement.updateCache = true;\n      });\n      let currentDragEvent;\n      const overlaps$ = drag$.pipe(map(({\n        clientX,\n        clientY,\n        dropData,\n        target\n      }) => {\n        currentDragEvent = {\n          clientX,\n          clientY,\n          dropData,\n          target\n        };\n\n        if (droppableElement.updateCache) {\n          droppableElement.rect = this.element.nativeElement.getBoundingClientRect();\n\n          if (this.scrollContainer) {\n            droppableElement.scrollContainerRect = this.scrollContainer.elementRef.nativeElement.getBoundingClientRect();\n          }\n\n          droppableElement.updateCache = false;\n        }\n\n        const isWithinElement = isCoordinateWithinRectangle(clientX, clientY, droppableElement.rect);\n        const isDropAllowed = !this.validateDrop || this.validateDrop({\n          clientX,\n          clientY,\n          target,\n          dropData\n        });\n\n        if (droppableElement.scrollContainerRect) {\n          return isWithinElement && isDropAllowed && isCoordinateWithinRectangle(clientX, clientY, droppableElement.scrollContainerRect);\n        } else {\n          return isWithinElement && isDropAllowed;\n        }\n      }));\n      const overlapsChanged$ = overlaps$.pipe(distinctUntilChanged());\n      let dragOverActive; // TODO - see if there's a way of doing this via rxjs\n\n      overlapsChanged$.pipe(filter(overlapsNow => overlapsNow)).subscribe(() => {\n        dragOverActive = true;\n        addClass(this.renderer, this.element, this.dragOverClass);\n\n        if (this.dragEnter.observers.length > 0) {\n          this.zone.run(() => {\n            this.dragEnter.next(currentDragEvent);\n          });\n        }\n      });\n      overlaps$.pipe(filter(overlapsNow => overlapsNow)).subscribe(() => {\n        if (this.dragOver.observers.length > 0) {\n          this.zone.run(() => {\n            this.dragOver.next(currentDragEvent);\n          });\n        }\n      });\n      overlapsChanged$.pipe(pairwise(), filter(([didOverlap, overlapsNow]) => didOverlap && !overlapsNow)).subscribe(() => {\n        dragOverActive = false;\n        removeClass(this.renderer, this.element, this.dragOverClass);\n\n        if (this.dragLeave.observers.length > 0) {\n          this.zone.run(() => {\n            this.dragLeave.next(currentDragEvent);\n          });\n        }\n      });\n      drag$.subscribe({\n        complete: () => {\n          deregisterScrollListener();\n          removeClass(this.renderer, this.element, this.dragActiveClass);\n\n          if (dragOverActive) {\n            removeClass(this.renderer, this.element, this.dragOverClass);\n\n            if (this.drop.observers.length > 0) {\n              this.zone.run(() => {\n                this.drop.next(currentDragEvent);\n              });\n            }\n          }\n        }\n      });\n    });\n  }\n\n  ngOnDestroy() {\n    if (this.currentDragSubscription) {\n      this.currentDragSubscription.unsubscribe();\n    }\n  }\n\n}\n\nDroppableDirective.ɵfac = function DroppableDirective_Factory(t) {\n  return new (t || DroppableDirective)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(DraggableHelper), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(DraggableScrollContainerDirective, 8));\n};\n\nDroppableDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: DroppableDirective,\n  selectors: [[\"\", \"mwlDroppable\", \"\"]],\n  inputs: {\n    dragOverClass: \"dragOverClass\",\n    dragActiveClass: \"dragActiveClass\",\n    validateDrop: \"validateDrop\"\n  },\n  outputs: {\n    dragEnter: \"dragEnter\",\n    dragLeave: \"dragLeave\",\n    dragOver: \"dragOver\",\n    drop: \"drop\"\n  }\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DroppableDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[mwlDroppable]'\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: DraggableHelper\n    }, {\n      type: i0.NgZone\n    }, {\n      type: i0.Renderer2\n    }, {\n      type: DraggableScrollContainerDirective,\n      decorators: [{\n        type: Optional\n      }]\n    }];\n  }, {\n    dragOverClass: [{\n      type: Input\n    }],\n    dragActiveClass: [{\n      type: Input\n    }],\n    validateDrop: [{\n      type: Input\n    }],\n    dragEnter: [{\n      type: Output\n    }],\n    dragLeave: [{\n      type: Output\n    }],\n    dragOver: [{\n      type: Output\n    }],\n    drop: [{\n      type: Output\n    }]\n  });\n})();\n\nclass DragAndDropModule {}\n\nDragAndDropModule.ɵfac = function DragAndDropModule_Factory(t) {\n  return new (t || DragAndDropModule)();\n};\n\nDragAndDropModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: DragAndDropModule\n});\nDragAndDropModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DragAndDropModule, [{\n    type: NgModule,\n    args: [{\n      declarations: [DraggableDirective, DroppableDirective, DraggableScrollContainerDirective],\n      exports: [DraggableDirective, DroppableDirective, DraggableScrollContainerDirective]\n    }]\n  }], null, null);\n})();\n/*\n * Public API Surface of angular-draggable-droppable\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\n\nexport { DragAndDropModule, DraggableDirective, DraggableScrollContainerDirective, DroppableDirective }; //# sourceMappingURL=angular-draggable-droppable.mjs.map", "map": null, "metadata": {}, "sourceType": "module"}