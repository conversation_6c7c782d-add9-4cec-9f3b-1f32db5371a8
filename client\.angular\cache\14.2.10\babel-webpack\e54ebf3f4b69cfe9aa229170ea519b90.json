{"ast": null, "code": "import { factory } from '../../utils/factory.js';\nvar name = 'sylvester';\nvar dependencies = ['typed', 'schur', 'matrixFromColumns', 'matrix', 'multiply', 'range', 'concat', 'transpose', 'index', 'subset', 'add', 'subtract', 'identity', 'lusolve', 'abs'];\nexport var createSylvester = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed,\n    schur,\n    matrixFromColumns,\n    matrix,\n    multiply,\n    range,\n    concat,\n    transpose,\n    index,\n    subset,\n    add,\n    subtract,\n    identity,\n    lusolve,\n    abs\n  } = _ref;\n  /**\n   *\n   * Solves the real-valued Sylvester equation AX+XB=C for X, where A, B and C are\n   * matrices of appropriate dimensions, being A and B squared. Notice that other\n   * equivalent definitions for the Sylvester equation exist and this function\n   * assumes the one presented in the original publication of the the Bartels-\n   * Stewart algorithm, which is implemented by this function.\n   * https://en.wikipedia.org/wiki/Sylvester_equation\n   *\n   * Syntax:\n   *\n   *     math.sylvester(A, B, C)\n   *\n   * Examples:\n   *\n   *     const A = [[-1, -2], [1, 1]]\n   *     const B = [[2, -1], [1, -2]]\n   *     const C = [[-3, 2], [3, 0]]\n   *     math.sylvester(A, B, C)      // returns DenseMatrix [[-0.25, 0.25], [1.5, -1.25]]\n   *\n   * See also:\n   *\n   *     schur, lyap\n   *\n   * @param {Matrix | Array} A  Matrix A\n   * @param {Matrix | Array} B  Matrix B\n   * @param {Matrix | Array} C  Matrix C\n   * @return {Matrix | Array}   Matrix X, solving the Sylvester equation\n   */\n\n  return typed(name, {\n    'Matrix, Matrix, Matrix': _sylvester,\n    'Array, Matrix, Matrix': function Array_Matrix_Matrix(A, B, C) {\n      return _sylvester(matrix(A), B, C);\n    },\n    'Array, Array, Matrix': function Array_Array_Matrix(A, B, C) {\n      return _sylvester(matrix(A), matrix(B), C);\n    },\n    'Array, Matrix, Array': function Array_Matrix_Array(A, B, C) {\n      return _sylvester(matrix(A), B, matrix(C));\n    },\n    'Matrix, Array, Matrix': function Matrix_Array_Matrix(A, B, C) {\n      return _sylvester(A, matrix(B), C);\n    },\n    'Matrix, Array, Array': function Matrix_Array_Array(A, B, C) {\n      return _sylvester(A, matrix(B), matrix(C));\n    },\n    'Matrix, Matrix, Array': function Matrix_Matrix_Array(A, B, C) {\n      return _sylvester(A, B, matrix(C));\n    },\n    'Array, Array, Array': function Array_Array_Array(A, B, C) {\n      return _sylvester(matrix(A), matrix(B), matrix(C)).toArray();\n    }\n  });\n\n  function _sylvester(A, B, C) {\n    var n = B.size()[0];\n    var m = A.size()[0];\n    var sA = schur(A);\n    var F = sA.T;\n    var U = sA.U;\n    var sB = schur(multiply(-1, B));\n    var G = sB.T;\n    var V = sB.U;\n    var D = multiply(multiply(transpose(U), C), V);\n    var all = range(0, m);\n    var y = [];\n\n    var hc = (a, b) => concat(a, b, 1);\n\n    var vc = (a, b) => concat(a, b, 0);\n\n    for (var k = 0; k < n; k++) {\n      if (k < n - 1 && abs(subset(G, index(k + 1, k))) > 1e-5) {\n        var RHS = vc(subset(D, index(all, k)), subset(D, index(all, k + 1)));\n\n        for (var j = 0; j < k; j++) {\n          RHS = add(RHS, vc(multiply(y[j], subset(G, index(j, k))), multiply(y[j], subset(G, index(j, k + 1)))));\n        }\n\n        var gkk = multiply(identity(m), multiply(-1, subset(G, index(k, k))));\n        var gmk = multiply(identity(m), multiply(-1, subset(G, index(k + 1, k))));\n        var gkm = multiply(identity(m), multiply(-1, subset(G, index(k, k + 1))));\n        var gmm = multiply(identity(m), multiply(-1, subset(G, index(k + 1, k + 1))));\n        var LHS = vc(hc(add(F, gkk), gmk), hc(gkm, add(F, gmm)));\n        var yAux = lusolve(LHS, RHS);\n        y[k] = yAux.subset(index(range(0, m), 0));\n        y[k + 1] = yAux.subset(index(range(m, 2 * m), 0));\n        k++;\n      } else {\n        var _RHS = subset(D, index(all, k));\n\n        for (var _j = 0; _j < k; _j++) {\n          _RHS = add(_RHS, multiply(y[_j], subset(G, index(_j, k))));\n        }\n\n        var _gkk = subset(G, index(k, k));\n\n        var _LHS = subtract(F, multiply(_gkk, identity(m)));\n\n        y[k] = lusolve(_LHS, _RHS);\n      }\n    }\n\n    var Y = matrix(matrixFromColumns(...y));\n    var X = multiply(U, multiply(Y, transpose(V)));\n    return X;\n  }\n});", "map": {"version": 3, "names": ["factory", "name", "dependencies", "createSylvester", "_ref", "typed", "schur", "matrixFromColumns", "matrix", "multiply", "range", "concat", "transpose", "index", "subset", "add", "subtract", "identity", "lusolve", "abs", "_sylvester", "Array_Matrix_Matrix", "A", "B", "C", "Array_Array_Matrix", "Array_Matrix_Array", "Matrix_Array_Matrix", "Matrix_Array_Array", "Matrix_Matrix_Array", "Array_Array_Array", "toArray", "n", "size", "m", "sA", "F", "T", "U", "sB", "G", "V", "D", "all", "y", "hc", "a", "b", "vc", "k", "RHS", "j", "gkk", "gmk", "gkm", "gmm", "LHS", "yAux", "_RHS", "_j", "_gkk", "_LHS", "Y", "X"], "sources": ["D:/work/joyserver/client/node_modules/mathjs/lib/esm/function/algebra/sylvester.js"], "sourcesContent": ["import { factory } from '../../utils/factory.js';\nvar name = 'sylvester';\nvar dependencies = ['typed', 'schur', 'matrixFromColumns', 'matrix', 'multiply', 'range', 'concat', 'transpose', 'index', 'subset', 'add', 'subtract', 'identity', 'lusolve', 'abs'];\nexport var createSylvester = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed,\n    schur,\n    matrixFromColumns,\n    matrix,\n    multiply,\n    range,\n    concat,\n    transpose,\n    index,\n    subset,\n    add,\n    subtract,\n    identity,\n    lusolve,\n    abs\n  } = _ref;\n  /**\n   *\n   * Solves the real-valued Sylvester equation AX+XB=C for X, where A, B and C are\n   * matrices of appropriate dimensions, being A and B squared. Notice that other\n   * equivalent definitions for the Sylvester equation exist and this function\n   * assumes the one presented in the original publication of the the Bartels-\n   * Stewart algorithm, which is implemented by this function.\n   * https://en.wikipedia.org/wiki/Sylvester_equation\n   *\n   * Syntax:\n   *\n   *     math.sylvester(A, B, C)\n   *\n   * Examples:\n   *\n   *     const A = [[-1, -2], [1, 1]]\n   *     const B = [[2, -1], [1, -2]]\n   *     const C = [[-3, 2], [3, 0]]\n   *     math.sylvester(A, B, C)      // returns DenseMatrix [[-0.25, 0.25], [1.5, -1.25]]\n   *\n   * See also:\n   *\n   *     schur, lyap\n   *\n   * @param {Matrix | Array} A  Matrix A\n   * @param {Matrix | Array} B  Matrix B\n   * @param {Matrix | Array} C  Matrix C\n   * @return {Matrix | Array}   Matrix X, solving the Sylvester equation\n   */\n  return typed(name, {\n    'Matrix, Matrix, Matrix': _sylvester,\n    'Array, Matrix, Matrix': function Array_Matrix_Matrix(A, B, C) {\n      return _sylvester(matrix(A), B, C);\n    },\n    'Array, Array, Matrix': function Array_Array_Matrix(A, B, C) {\n      return _sylvester(matrix(A), matrix(B), C);\n    },\n    'Array, Matrix, Array': function Array_Matrix_Array(A, B, C) {\n      return _sylvester(matrix(A), B, matrix(C));\n    },\n    'Matrix, Array, Matrix': function Matrix_Array_Matrix(A, B, C) {\n      return _sylvester(A, matrix(B), C);\n    },\n    'Matrix, Array, Array': function Matrix_Array_Array(A, B, C) {\n      return _sylvester(A, matrix(B), matrix(C));\n    },\n    'Matrix, Matrix, Array': function Matrix_Matrix_Array(A, B, C) {\n      return _sylvester(A, B, matrix(C));\n    },\n    'Array, Array, Array': function Array_Array_Array(A, B, C) {\n      return _sylvester(matrix(A), matrix(B), matrix(C)).toArray();\n    }\n  });\n  function _sylvester(A, B, C) {\n    var n = B.size()[0];\n    var m = A.size()[0];\n    var sA = schur(A);\n    var F = sA.T;\n    var U = sA.U;\n    var sB = schur(multiply(-1, B));\n    var G = sB.T;\n    var V = sB.U;\n    var D = multiply(multiply(transpose(U), C), V);\n    var all = range(0, m);\n    var y = [];\n    var hc = (a, b) => concat(a, b, 1);\n    var vc = (a, b) => concat(a, b, 0);\n    for (var k = 0; k < n; k++) {\n      if (k < n - 1 && abs(subset(G, index(k + 1, k))) > 1e-5) {\n        var RHS = vc(subset(D, index(all, k)), subset(D, index(all, k + 1)));\n        for (var j = 0; j < k; j++) {\n          RHS = add(RHS, vc(multiply(y[j], subset(G, index(j, k))), multiply(y[j], subset(G, index(j, k + 1)))));\n        }\n        var gkk = multiply(identity(m), multiply(-1, subset(G, index(k, k))));\n        var gmk = multiply(identity(m), multiply(-1, subset(G, index(k + 1, k))));\n        var gkm = multiply(identity(m), multiply(-1, subset(G, index(k, k + 1))));\n        var gmm = multiply(identity(m), multiply(-1, subset(G, index(k + 1, k + 1))));\n        var LHS = vc(hc(add(F, gkk), gmk), hc(gkm, add(F, gmm)));\n        var yAux = lusolve(LHS, RHS);\n        y[k] = yAux.subset(index(range(0, m), 0));\n        y[k + 1] = yAux.subset(index(range(m, 2 * m), 0));\n        k++;\n      } else {\n        var _RHS = subset(D, index(all, k));\n        for (var _j = 0; _j < k; _j++) {\n          _RHS = add(_RHS, multiply(y[_j], subset(G, index(_j, k))));\n        }\n        var _gkk = subset(G, index(k, k));\n        var _LHS = subtract(F, multiply(_gkk, identity(m)));\n        y[k] = lusolve(_LHS, _RHS);\n      }\n    }\n    var Y = matrix(matrixFromColumns(...y));\n    var X = multiply(U, multiply(Y, transpose(V)));\n    return X;\n  }\n});"], "mappings": "AAAA,SAASA,OAAT,QAAwB,wBAAxB;AACA,IAAIC,IAAI,GAAG,WAAX;AACA,IAAIC,YAAY,GAAG,CAAC,OAAD,EAAU,OAAV,EAAmB,mBAAnB,EAAwC,QAAxC,EAAkD,UAAlD,EAA8D,OAA9D,EAAuE,QAAvE,EAAiF,WAAjF,EAA8F,OAA9F,EAAuG,QAAvG,EAAiH,KAAjH,EAAwH,UAAxH,EAAoI,UAApI,EAAgJ,SAAhJ,EAA2J,KAA3J,CAAnB;AACA,OAAO,IAAIC,eAAe,GAAG,eAAeH,OAAO,CAACC,IAAD,EAAOC,YAAP,EAAqBE,IAAI,IAAI;EAC9E,IAAI;IACFC,KADE;IAEFC,KAFE;IAGFC,iBAHE;IAIFC,MAJE;IAKFC,QALE;IAMFC,KANE;IAOFC,MAPE;IAQFC,SARE;IASFC,KATE;IAUFC,MAVE;IAWFC,GAXE;IAYFC,QAZE;IAaFC,QAbE;IAcFC,OAdE;IAeFC;EAfE,IAgBAf,IAhBJ;EAiBA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EACE,OAAOC,KAAK,CAACJ,IAAD,EAAO;IACjB,0BAA0BmB,UADT;IAEjB,yBAAyB,SAASC,mBAAT,CAA6BC,CAA7B,EAAgCC,CAAhC,EAAmCC,CAAnC,EAAsC;MAC7D,OAAOJ,UAAU,CAACZ,MAAM,CAACc,CAAD,CAAP,EAAYC,CAAZ,EAAeC,CAAf,CAAjB;IACD,CAJgB;IAKjB,wBAAwB,SAASC,kBAAT,CAA4BH,CAA5B,EAA+BC,CAA/B,EAAkCC,CAAlC,EAAqC;MAC3D,OAAOJ,UAAU,CAACZ,MAAM,CAACc,CAAD,CAAP,EAAYd,MAAM,CAACe,CAAD,CAAlB,EAAuBC,CAAvB,CAAjB;IACD,CAPgB;IAQjB,wBAAwB,SAASE,kBAAT,CAA4BJ,CAA5B,EAA+BC,CAA/B,EAAkCC,CAAlC,EAAqC;MAC3D,OAAOJ,UAAU,CAACZ,MAAM,CAACc,CAAD,CAAP,EAAYC,CAAZ,EAAef,MAAM,CAACgB,CAAD,CAArB,CAAjB;IACD,CAVgB;IAWjB,yBAAyB,SAASG,mBAAT,CAA6BL,CAA7B,EAAgCC,CAAhC,EAAmCC,CAAnC,EAAsC;MAC7D,OAAOJ,UAAU,CAACE,CAAD,EAAId,MAAM,CAACe,CAAD,CAAV,EAAeC,CAAf,CAAjB;IACD,CAbgB;IAcjB,wBAAwB,SAASI,kBAAT,CAA4BN,CAA5B,EAA+BC,CAA/B,EAAkCC,CAAlC,EAAqC;MAC3D,OAAOJ,UAAU,CAACE,CAAD,EAAId,MAAM,CAACe,CAAD,CAAV,EAAef,MAAM,CAACgB,CAAD,CAArB,CAAjB;IACD,CAhBgB;IAiBjB,yBAAyB,SAASK,mBAAT,CAA6BP,CAA7B,EAAgCC,CAAhC,EAAmCC,CAAnC,EAAsC;MAC7D,OAAOJ,UAAU,CAACE,CAAD,EAAIC,CAAJ,EAAOf,MAAM,CAACgB,CAAD,CAAb,CAAjB;IACD,CAnBgB;IAoBjB,uBAAuB,SAASM,iBAAT,CAA2BR,CAA3B,EAA8BC,CAA9B,EAAiCC,CAAjC,EAAoC;MACzD,OAAOJ,UAAU,CAACZ,MAAM,CAACc,CAAD,CAAP,EAAYd,MAAM,CAACe,CAAD,CAAlB,EAAuBf,MAAM,CAACgB,CAAD,CAA7B,CAAV,CAA4CO,OAA5C,EAAP;IACD;EAtBgB,CAAP,CAAZ;;EAwBA,SAASX,UAAT,CAAoBE,CAApB,EAAuBC,CAAvB,EAA0BC,CAA1B,EAA6B;IAC3B,IAAIQ,CAAC,GAAGT,CAAC,CAACU,IAAF,GAAS,CAAT,CAAR;IACA,IAAIC,CAAC,GAAGZ,CAAC,CAACW,IAAF,GAAS,CAAT,CAAR;IACA,IAAIE,EAAE,GAAG7B,KAAK,CAACgB,CAAD,CAAd;IACA,IAAIc,CAAC,GAAGD,EAAE,CAACE,CAAX;IACA,IAAIC,CAAC,GAAGH,EAAE,CAACG,CAAX;IACA,IAAIC,EAAE,GAAGjC,KAAK,CAACG,QAAQ,CAAC,CAAC,CAAF,EAAKc,CAAL,CAAT,CAAd;IACA,IAAIiB,CAAC,GAAGD,EAAE,CAACF,CAAX;IACA,IAAII,CAAC,GAAGF,EAAE,CAACD,CAAX;IACA,IAAII,CAAC,GAAGjC,QAAQ,CAACA,QAAQ,CAACG,SAAS,CAAC0B,CAAD,CAAV,EAAed,CAAf,CAAT,EAA4BiB,CAA5B,CAAhB;IACA,IAAIE,GAAG,GAAGjC,KAAK,CAAC,CAAD,EAAIwB,CAAJ,CAAf;IACA,IAAIU,CAAC,GAAG,EAAR;;IACA,IAAIC,EAAE,GAAG,CAACC,CAAD,EAAIC,CAAJ,KAAUpC,MAAM,CAACmC,CAAD,EAAIC,CAAJ,EAAO,CAAP,CAAzB;;IACA,IAAIC,EAAE,GAAG,CAACF,CAAD,EAAIC,CAAJ,KAAUpC,MAAM,CAACmC,CAAD,EAAIC,CAAJ,EAAO,CAAP,CAAzB;;IACA,KAAK,IAAIE,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGjB,CAApB,EAAuBiB,CAAC,EAAxB,EAA4B;MAC1B,IAAIA,CAAC,GAAGjB,CAAC,GAAG,CAAR,IAAab,GAAG,CAACL,MAAM,CAAC0B,CAAD,EAAI3B,KAAK,CAACoC,CAAC,GAAG,CAAL,EAAQA,CAAR,CAAT,CAAP,CAAH,GAAkC,IAAnD,EAAyD;QACvD,IAAIC,GAAG,GAAGF,EAAE,CAAClC,MAAM,CAAC4B,CAAD,EAAI7B,KAAK,CAAC8B,GAAD,EAAMM,CAAN,CAAT,CAAP,EAA2BnC,MAAM,CAAC4B,CAAD,EAAI7B,KAAK,CAAC8B,GAAD,EAAMM,CAAC,GAAG,CAAV,CAAT,CAAjC,CAAZ;;QACA,KAAK,IAAIE,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGF,CAApB,EAAuBE,CAAC,EAAxB,EAA4B;UAC1BD,GAAG,GAAGnC,GAAG,CAACmC,GAAD,EAAMF,EAAE,CAACvC,QAAQ,CAACmC,CAAC,CAACO,CAAD,CAAF,EAAOrC,MAAM,CAAC0B,CAAD,EAAI3B,KAAK,CAACsC,CAAD,EAAIF,CAAJ,CAAT,CAAb,CAAT,EAAyCxC,QAAQ,CAACmC,CAAC,CAACO,CAAD,CAAF,EAAOrC,MAAM,CAAC0B,CAAD,EAAI3B,KAAK,CAACsC,CAAD,EAAIF,CAAC,GAAG,CAAR,CAAT,CAAb,CAAjD,CAAR,CAAT;QACD;;QACD,IAAIG,GAAG,GAAG3C,QAAQ,CAACQ,QAAQ,CAACiB,CAAD,CAAT,EAAczB,QAAQ,CAAC,CAAC,CAAF,EAAKK,MAAM,CAAC0B,CAAD,EAAI3B,KAAK,CAACoC,CAAD,EAAIA,CAAJ,CAAT,CAAX,CAAtB,CAAlB;QACA,IAAII,GAAG,GAAG5C,QAAQ,CAACQ,QAAQ,CAACiB,CAAD,CAAT,EAAczB,QAAQ,CAAC,CAAC,CAAF,EAAKK,MAAM,CAAC0B,CAAD,EAAI3B,KAAK,CAACoC,CAAC,GAAG,CAAL,EAAQA,CAAR,CAAT,CAAX,CAAtB,CAAlB;QACA,IAAIK,GAAG,GAAG7C,QAAQ,CAACQ,QAAQ,CAACiB,CAAD,CAAT,EAAczB,QAAQ,CAAC,CAAC,CAAF,EAAKK,MAAM,CAAC0B,CAAD,EAAI3B,KAAK,CAACoC,CAAD,EAAIA,CAAC,GAAG,CAAR,CAAT,CAAX,CAAtB,CAAlB;QACA,IAAIM,GAAG,GAAG9C,QAAQ,CAACQ,QAAQ,CAACiB,CAAD,CAAT,EAAczB,QAAQ,CAAC,CAAC,CAAF,EAAKK,MAAM,CAAC0B,CAAD,EAAI3B,KAAK,CAACoC,CAAC,GAAG,CAAL,EAAQA,CAAC,GAAG,CAAZ,CAAT,CAAX,CAAtB,CAAlB;QACA,IAAIO,GAAG,GAAGR,EAAE,CAACH,EAAE,CAAC9B,GAAG,CAACqB,CAAD,EAAIgB,GAAJ,CAAJ,EAAcC,GAAd,CAAH,EAAuBR,EAAE,CAACS,GAAD,EAAMvC,GAAG,CAACqB,CAAD,EAAImB,GAAJ,CAAT,CAAzB,CAAZ;QACA,IAAIE,IAAI,GAAGvC,OAAO,CAACsC,GAAD,EAAMN,GAAN,CAAlB;QACAN,CAAC,CAACK,CAAD,CAAD,GAAOQ,IAAI,CAAC3C,MAAL,CAAYD,KAAK,CAACH,KAAK,CAAC,CAAD,EAAIwB,CAAJ,CAAN,EAAc,CAAd,CAAjB,CAAP;QACAU,CAAC,CAACK,CAAC,GAAG,CAAL,CAAD,GAAWQ,IAAI,CAAC3C,MAAL,CAAYD,KAAK,CAACH,KAAK,CAACwB,CAAD,EAAI,IAAIA,CAAR,CAAN,EAAkB,CAAlB,CAAjB,CAAX;QACAe,CAAC;MACF,CAdD,MAcO;QACL,IAAIS,IAAI,GAAG5C,MAAM,CAAC4B,CAAD,EAAI7B,KAAK,CAAC8B,GAAD,EAAMM,CAAN,CAAT,CAAjB;;QACA,KAAK,IAAIU,EAAE,GAAG,CAAd,EAAiBA,EAAE,GAAGV,CAAtB,EAAyBU,EAAE,EAA3B,EAA+B;UAC7BD,IAAI,GAAG3C,GAAG,CAAC2C,IAAD,EAAOjD,QAAQ,CAACmC,CAAC,CAACe,EAAD,CAAF,EAAQ7C,MAAM,CAAC0B,CAAD,EAAI3B,KAAK,CAAC8C,EAAD,EAAKV,CAAL,CAAT,CAAd,CAAf,CAAV;QACD;;QACD,IAAIW,IAAI,GAAG9C,MAAM,CAAC0B,CAAD,EAAI3B,KAAK,CAACoC,CAAD,EAAIA,CAAJ,CAAT,CAAjB;;QACA,IAAIY,IAAI,GAAG7C,QAAQ,CAACoB,CAAD,EAAI3B,QAAQ,CAACmD,IAAD,EAAO3C,QAAQ,CAACiB,CAAD,CAAf,CAAZ,CAAnB;;QACAU,CAAC,CAACK,CAAD,CAAD,GAAO/B,OAAO,CAAC2C,IAAD,EAAOH,IAAP,CAAd;MACD;IACF;;IACD,IAAII,CAAC,GAAGtD,MAAM,CAACD,iBAAiB,CAAC,GAAGqC,CAAJ,CAAlB,CAAd;IACA,IAAImB,CAAC,GAAGtD,QAAQ,CAAC6B,CAAD,EAAI7B,QAAQ,CAACqD,CAAD,EAAIlD,SAAS,CAAC6B,CAAD,CAAb,CAAZ,CAAhB;IACA,OAAOsB,CAAP;EACD;AACF,CAlHkD,CAA5C"}, "metadata": {}, "sourceType": "module"}