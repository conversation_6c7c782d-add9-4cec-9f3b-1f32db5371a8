{"ast": null, "code": "/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { BigNumberDependencies } from './dependenciesBigNumberClass.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createBignumber } from '../../factoriesAny.js';\nexport var bignumberDependencies = {\n  BigNumberDependencies,\n  typedDependencies,\n  createBignumber\n};", "map": {"version": 3, "names": ["BigNumberDependencies", "typedDependencies", "createBignumber", "bignumberDependencies"], "sources": ["D:/work/joyserver/client/node_modules/mathjs/lib/esm/entry/dependenciesAny/dependenciesBignumber.generated.js"], "sourcesContent": ["/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { BigNumberDependencies } from './dependenciesBigNumberClass.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createBignumber } from '../../factoriesAny.js';\nexport var bignumberDependencies = {\n  BigNumberDependencies,\n  typedDependencies,\n  createBignumber\n};"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,qBAAT,QAAsC,2CAAtC;AACA,SAASC,iBAAT,QAAkC,kCAAlC;AACA,SAASC,eAAT,QAAgC,uBAAhC;AACA,OAAO,IAAIC,qBAAqB,GAAG;EACjCH,qBADiC;EAEjCC,iBAFiC;EAGjCC;AAHiC,CAA5B"}, "metadata": {}, "sourceType": "module"}