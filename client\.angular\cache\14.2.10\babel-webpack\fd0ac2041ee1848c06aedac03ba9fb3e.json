{"ast": null, "code": "export var concatDocs = {\n  name: 'concat',\n  category: 'Matrix',\n  syntax: ['concat(A, B, C, ...)', 'concat(A, B, C, ..., dim)'],\n  description: 'Concatenate matrices. By default, the matrices are concatenated by the last dimension. The dimension on which to concatenate can be provided as last argument.',\n  examples: ['A = [1, 2; 5, 6]', 'B = [3, 4; 7, 8]', 'concat(A, B)', 'concat(A, B, 1)', 'concat(A, B, 2)'],\n  seealso: ['det', 'diag', 'identity', 'inv', 'ones', 'range', 'size', 'squeeze', 'subset', 'trace', 'transpose', 'zeros']\n};", "map": {"version": 3, "names": ["concatDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["D:/work/joyserver/client/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/matrix/concat.js"], "sourcesContent": ["export var concatDocs = {\n  name: 'concat',\n  category: 'Matrix',\n  syntax: ['concat(A, B, C, ...)', 'concat(A, B, C, ..., dim)'],\n  description: 'Concatenate matrices. By default, the matrices are concatenated by the last dimension. The dimension on which to concatenate can be provided as last argument.',\n  examples: ['A = [1, 2; 5, 6]', 'B = [3, 4; 7, 8]', 'concat(A, B)', 'concat(A, B, 1)', 'concat(A, B, 2)'],\n  seealso: ['det', 'diag', 'identity', 'inv', 'ones', 'range', 'size', 'squeeze', 'subset', 'trace', 'transpose', 'zeros']\n};"], "mappings": "AAAA,OAAO,IAAIA,UAAU,GAAG;EACtBC,IAAI,EAAE,QADgB;EAEtBC,QAAQ,EAAE,QAFY;EAGtBC,MAAM,EAAE,CAAC,sBAAD,EAAyB,2BAAzB,CAHc;EAItBC,WAAW,EAAE,gKAJS;EAKtBC,QAAQ,EAAE,CAAC,kBAAD,EAAqB,kBAArB,EAAyC,cAAzC,EAAyD,iBAAzD,EAA4E,iBAA5E,CALY;EAMtBC,OAAO,EAAE,CAAC,KAAD,EAAQ,MAAR,EAAgB,UAAhB,EAA4B,KAA5B,EAAmC,MAAnC,EAA2C,OAA3C,EAAoD,MAApD,EAA4D,SAA5D,EAAuE,QAAvE,EAAiF,OAAjF,EAA0F,WAA1F,EAAuG,OAAvG;AANa,CAAjB"}, "metadata": {}, "sourceType": "module"}