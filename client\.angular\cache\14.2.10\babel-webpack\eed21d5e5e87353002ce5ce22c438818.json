{"ast": null, "code": "import { deepForEach, reduce, containsCollections } from '../../utils/collection.js';\nimport { factory } from '../../utils/factory.js';\nimport { safeNumberType } from '../../utils/number.js';\nimport { improveErrorMessage } from './utils/improveErrorMessage.js';\nvar name = 'max';\nvar dependencies = ['typed', 'config', 'numeric', 'larger'];\nexport var createMax = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed,\n    config,\n    numeric,\n    larger\n  } = _ref;\n  /**\n   * Compute the maximum value of a matrix or a  list with values.\n   * In case of a multidimensional array, the maximum of the flattened array\n   * will be calculated. When `dim` is provided, the maximum over the selected\n   * dimension will be calculated. Parameter `dim` is zero-based.\n   *\n   * Syntax:\n   *\n   *     math.max(a, b, c, ...)\n   *     math.max(A)\n   *     math.max(A, dimension)\n   *\n   * Examples:\n   *\n   *     math.max(2, 1, 4, 3)                  // returns 4\n   *     math.max([2, 1, 4, 3])                // returns 4\n   *\n   *     // maximum over a specified dimension (zero-based)\n   *     math.max([[2, 5], [4, 3], [1, 7]], 0) // returns [4, 7]\n   *     math.max([[2, 5], [4, 3], [1, 7]], 1) // returns [5, 4, 7]\n   *\n   *     math.max(2.7, 7.1, -4.5, 2.0, 4.1)    // returns 7.1\n   *     math.min(2.7, 7.1, -4.5, 2.0, 4.1)    // returns -4.5\n   *\n   * See also:\n   *\n   *    mean, median, min, prod, std, sum, variance\n   *\n   * @param {... *} args  A single matrix or or multiple scalar values\n   * @return {*} The maximum value\n   */\n\n  return typed(name, {\n    // max([a, b, c, d, ...])\n    'Array | Matrix': _max,\n    // max([a, b, c, d, ...], dim)\n    'Array | Matrix, number | BigNumber': function Array__Matrix_number__BigNumber(array, dim) {\n      return reduce(array, dim.valueOf(), _largest);\n    },\n    // max(a, b, c, d, ...)\n    '...': function _(args) {\n      if (containsCollections(args)) {\n        throw new TypeError('Scalar values expected in function max');\n      }\n\n      return _max(args);\n    }\n  });\n  /**\n   * Return the largest of two values\n   * @param {*} x\n   * @param {*} y\n   * @returns {*} Returns x when x is largest, or y when y is largest\n   * @private\n   */\n\n  function _largest(x, y) {\n    try {\n      return larger(x, y) ? x : y;\n    } catch (err) {\n      throw improveErrorMessage(err, 'max', y);\n    }\n  }\n  /**\n   * Recursively calculate the maximum value in an n-dimensional array\n   * @param {Array} array\n   * @return {number} max\n   * @private\n   */\n\n\n  function _max(array) {\n    var res;\n    deepForEach(array, function (value) {\n      try {\n        if (typeof value === 'number' && isNaN(value)) {\n          res = NaN;\n        } else if (res === undefined || larger(value, res)) {\n          res = value;\n        }\n      } catch (err) {\n        throw improveErrorMessage(err, 'max', value);\n      }\n    });\n\n    if (res === undefined) {\n      throw new Error('Cannot calculate max of an empty array');\n    } // make sure returning numeric value: parse a string into a numeric value\n\n\n    if (typeof res === 'string') {\n      res = numeric(res, safeNumberType(res, config));\n    }\n\n    return res;\n  }\n});", "map": {"version": 3, "names": ["deepForEach", "reduce", "containsCollections", "factory", "safeNumberType", "improveErrorMessage", "name", "dependencies", "createMax", "_ref", "typed", "config", "numeric", "larger", "_max", "Array__Matrix_number__BigNumber", "array", "dim", "valueOf", "_largest", "_", "args", "TypeError", "x", "y", "err", "res", "value", "isNaN", "NaN", "undefined", "Error"], "sources": ["D:/work/joyserver/client/node_modules/mathjs/lib/esm/function/statistics/max.js"], "sourcesContent": ["import { deepForEach, reduce, containsCollections } from '../../utils/collection.js';\nimport { factory } from '../../utils/factory.js';\nimport { safeNumberType } from '../../utils/number.js';\nimport { improveErrorMessage } from './utils/improveErrorMessage.js';\nvar name = 'max';\nvar dependencies = ['typed', 'config', 'numeric', 'larger'];\nexport var createMax = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed,\n    config,\n    numeric,\n    larger\n  } = _ref;\n  /**\n   * Compute the maximum value of a matrix or a  list with values.\n   * In case of a multidimensional array, the maximum of the flattened array\n   * will be calculated. When `dim` is provided, the maximum over the selected\n   * dimension will be calculated. Parameter `dim` is zero-based.\n   *\n   * Syntax:\n   *\n   *     math.max(a, b, c, ...)\n   *     math.max(A)\n   *     math.max(A, dimension)\n   *\n   * Examples:\n   *\n   *     math.max(2, 1, 4, 3)                  // returns 4\n   *     math.max([2, 1, 4, 3])                // returns 4\n   *\n   *     // maximum over a specified dimension (zero-based)\n   *     math.max([[2, 5], [4, 3], [1, 7]], 0) // returns [4, 7]\n   *     math.max([[2, 5], [4, 3], [1, 7]], 1) // returns [5, 4, 7]\n   *\n   *     math.max(2.7, 7.1, -4.5, 2.0, 4.1)    // returns 7.1\n   *     math.min(2.7, 7.1, -4.5, 2.0, 4.1)    // returns -4.5\n   *\n   * See also:\n   *\n   *    mean, median, min, prod, std, sum, variance\n   *\n   * @param {... *} args  A single matrix or or multiple scalar values\n   * @return {*} The maximum value\n   */\n  return typed(name, {\n    // max([a, b, c, d, ...])\n    'Array | Matrix': _max,\n    // max([a, b, c, d, ...], dim)\n    'Array | Matrix, number | BigNumber': function Array__Matrix_number__BigNumber(array, dim) {\n      return reduce(array, dim.valueOf(), _largest);\n    },\n    // max(a, b, c, d, ...)\n    '...': function _(args) {\n      if (containsCollections(args)) {\n        throw new TypeError('Scalar values expected in function max');\n      }\n      return _max(args);\n    }\n  });\n\n  /**\n   * Return the largest of two values\n   * @param {*} x\n   * @param {*} y\n   * @returns {*} Returns x when x is largest, or y when y is largest\n   * @private\n   */\n  function _largest(x, y) {\n    try {\n      return larger(x, y) ? x : y;\n    } catch (err) {\n      throw improveErrorMessage(err, 'max', y);\n    }\n  }\n\n  /**\n   * Recursively calculate the maximum value in an n-dimensional array\n   * @param {Array} array\n   * @return {number} max\n   * @private\n   */\n  function _max(array) {\n    var res;\n    deepForEach(array, function (value) {\n      try {\n        if (typeof value === 'number' && isNaN(value)) {\n          res = NaN;\n        } else if (res === undefined || larger(value, res)) {\n          res = value;\n        }\n      } catch (err) {\n        throw improveErrorMessage(err, 'max', value);\n      }\n    });\n    if (res === undefined) {\n      throw new Error('Cannot calculate max of an empty array');\n    }\n\n    // make sure returning numeric value: parse a string into a numeric value\n    if (typeof res === 'string') {\n      res = numeric(res, safeNumberType(res, config));\n    }\n    return res;\n  }\n});"], "mappings": "AAAA,SAASA,WAAT,EAAsBC,MAAtB,EAA8BC,mBAA9B,QAAyD,2BAAzD;AACA,SAASC,OAAT,QAAwB,wBAAxB;AACA,SAASC,cAAT,QAA+B,uBAA/B;AACA,SAASC,mBAAT,QAAoC,gCAApC;AACA,IAAIC,IAAI,GAAG,KAAX;AACA,IAAIC,YAAY,GAAG,CAAC,OAAD,EAAU,QAAV,EAAoB,SAApB,EAA+B,QAA/B,CAAnB;AACA,OAAO,IAAIC,SAAS,GAAG,eAAeL,OAAO,CAACG,IAAD,EAAOC,YAAP,EAAqBE,IAAI,IAAI;EACxE,IAAI;IACFC,KADE;IAEFC,MAFE;IAGFC,OAHE;IAIFC;EAJE,IAKAJ,IALJ;EAMA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EACE,OAAOC,KAAK,CAACJ,IAAD,EAAO;IACjB;IACA,kBAAkBQ,IAFD;IAGjB;IACA,sCAAsC,SAASC,+BAAT,CAAyCC,KAAzC,EAAgDC,GAAhD,EAAqD;MACzF,OAAOhB,MAAM,CAACe,KAAD,EAAQC,GAAG,CAACC,OAAJ,EAAR,EAAuBC,QAAvB,CAAb;IACD,CANgB;IAOjB;IACA,OAAO,SAASC,CAAT,CAAWC,IAAX,EAAiB;MACtB,IAAInB,mBAAmB,CAACmB,IAAD,CAAvB,EAA+B;QAC7B,MAAM,IAAIC,SAAJ,CAAc,wCAAd,CAAN;MACD;;MACD,OAAOR,IAAI,CAACO,IAAD,CAAX;IACD;EAbgB,CAAP,CAAZ;EAgBA;AACF;AACA;AACA;AACA;AACA;AACA;;EACE,SAASF,QAAT,CAAkBI,CAAlB,EAAqBC,CAArB,EAAwB;IACtB,IAAI;MACF,OAAOX,MAAM,CAACU,CAAD,EAAIC,CAAJ,CAAN,GAAeD,CAAf,GAAmBC,CAA1B;IACD,CAFD,CAEE,OAAOC,GAAP,EAAY;MACZ,MAAMpB,mBAAmB,CAACoB,GAAD,EAAM,KAAN,EAAaD,CAAb,CAAzB;IACD;EACF;EAED;AACF;AACA;AACA;AACA;AACA;;;EACE,SAASV,IAAT,CAAcE,KAAd,EAAqB;IACnB,IAAIU,GAAJ;IACA1B,WAAW,CAACgB,KAAD,EAAQ,UAAUW,KAAV,EAAiB;MAClC,IAAI;QACF,IAAI,OAAOA,KAAP,KAAiB,QAAjB,IAA6BC,KAAK,CAACD,KAAD,CAAtC,EAA+C;UAC7CD,GAAG,GAAGG,GAAN;QACD,CAFD,MAEO,IAAIH,GAAG,KAAKI,SAAR,IAAqBjB,MAAM,CAACc,KAAD,EAAQD,GAAR,CAA/B,EAA6C;UAClDA,GAAG,GAAGC,KAAN;QACD;MACF,CAND,CAME,OAAOF,GAAP,EAAY;QACZ,MAAMpB,mBAAmB,CAACoB,GAAD,EAAM,KAAN,EAAaE,KAAb,CAAzB;MACD;IACF,CAVU,CAAX;;IAWA,IAAID,GAAG,KAAKI,SAAZ,EAAuB;MACrB,MAAM,IAAIC,KAAJ,CAAU,wCAAV,CAAN;IACD,CAfkB,CAiBnB;;;IACA,IAAI,OAAOL,GAAP,KAAe,QAAnB,EAA6B;MAC3BA,GAAG,GAAGd,OAAO,CAACc,GAAD,EAAMtB,cAAc,CAACsB,GAAD,EAAMf,MAAN,CAApB,CAAb;IACD;;IACD,OAAOe,GAAP;EACD;AACF,CAlG4C,CAAtC"}, "metadata": {}, "sourceType": "module"}