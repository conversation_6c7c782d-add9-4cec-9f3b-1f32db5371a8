{"ast": null, "code": "import { deepForEach, reduce, containsCollections } from '../../utils/collection.js';\nimport { factory } from '../../utils/factory.js';\nimport { safeNumberType } from '../../utils/number.js';\nimport { improveErrorMessage } from './utils/improveErrorMessage.js';\nvar name = 'max';\nvar dependencies = ['typed', 'config', 'numeric', 'larger'];\nexport var createMax = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed,\n    config,\n    numeric,\n    larger\n  } = _ref;\n  /**\n   * Compute the maximum value of a matrix or a  list with values.\n   * In case of a multidimensional array, the maximum of the flattened array\n   * will be calculated. When `dim` is provided, the maximum over the selected\n   * dimension will be calculated. Parameter `dim` is zero-based.\n   *\n   * Syntax:\n   *\n   *     math.max(a, b, c, ...)\n   *     math.max(A)\n   *     math.max(A, dimension)\n   *\n   * Examples:\n   *\n   *     math.max(2, 1, 4, 3)                  // returns 4\n   *     math.max([2, 1, 4, 3])                // returns 4\n   *\n   *     // maximum over a specified dimension (zero-based)\n   *     math.max([[2, 5], [4, 3], [1, 7]], 0) // returns [4, 7]\n   *     math.max([[2, 5], [4, 3], [1, 7]], 1) // returns [5, 4, 7]\n   *\n   *     math.max(2.7, 7.1, -4.5, 2.0, 4.1)    // returns 7.1\n   *     math.min(2.7, 7.1, -4.5, 2.0, 4.1)    // returns -4.5\n   *\n   * See also:\n   *\n   *    mean, median, min, prod, std, sum, variance\n   *\n   * @param {... *} args  A single matrix or or multiple scalar values\n   * @return {*} The maximum value\n   */\n\n  return typed(name, {\n    // max([a, b, c, d, ...])\n    'Array | Matrix': _max,\n    // max([a, b, c, d, ...], dim)\n    'Array | Matrix, number | BigNumber': function Array__Matrix_number__BigNumber(array, dim) {\n      return reduce(array, dim.valueOf(), _largest);\n    },\n    // max(a, b, c, d, ...)\n    '...': function _(args) {\n      if (containsCollections(args)) {\n        throw new TypeError('Scalar values expected in function max');\n      }\n\n      return _max(args);\n    }\n  });\n  /**\n   * Return the largest of two values\n   * @param {*} x\n   * @param {*} y\n   * @returns {*} Returns x when x is largest, or y when y is largest\n   * @private\n   */\n\n  function _largest(x, y) {\n    try {\n      return larger(x, y) ? x : y;\n    } catch (err) {\n      throw improveErrorMessage(err, 'max', y);\n    }\n  }\n  /**\n   * Recursively calculate the maximum value in an n-dimensional array\n   * @param {Array} array\n   * @return {number} max\n   * @private\n   */\n\n\n  function _max(array) {\n    var res;\n    deepForEach(array, function (value) {\n      try {\n        if (typeof value === 'number' && isNaN(value)) {\n          res = NaN;\n        } else if (res === undefined || larger(value, res)) {\n          res = value;\n        }\n      } catch (err) {\n        throw improveErrorMessage(err, 'max', value);\n      }\n    });\n\n    if (res === undefined) {\n      throw new Error('Cannot calculate max of an empty array');\n    } // make sure returning numeric value: parse a string into a numeric value\n\n\n    if (typeof res === 'string') {\n      res = numeric(res, safeNumberType(res, config));\n    }\n\n    return res;\n  }\n});", "map": null, "metadata": {}, "sourceType": "module"}