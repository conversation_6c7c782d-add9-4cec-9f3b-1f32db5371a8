{"ast": null, "code": "import { Observable } from '../Observable';\nimport { AsyncSubject } from '../AsyncSubject';\nimport { map } from '../operators/map';\nimport { canReportError } from '../util/canReportError';\nimport { isArray } from '../util/isArray';\nimport { isScheduler } from '../util/isScheduler';\nexport function bindCallback(callbackFunc, resultSelector, scheduler) {\n  if (resultSelector) {\n    if (isScheduler(resultSelector)) {\n      scheduler = resultSelector;\n    } else {\n      return (...args) => bindCallback(callbackFunc, scheduler)(...args).pipe(map(args => isArray(args) ? resultSelector(...args) : resultSelector(args)));\n    }\n  }\n\n  return function (...args) {\n    const context = this;\n    let subject;\n    const params = {\n      context,\n      subject,\n      callbackFunc,\n      scheduler\n    };\n    return new Observable(subscriber => {\n      if (!scheduler) {\n        if (!subject) {\n          subject = new AsyncSubject();\n\n          const handler = (...innerArgs) => {\n            subject.next(innerArgs.length <= 1 ? innerArgs[0] : innerArgs);\n            subject.complete();\n          };\n\n          try {\n            callbackFunc.apply(context, [...args, handler]);\n          } catch (err) {\n            if (canReportError(subject)) {\n              subject.error(err);\n            } else {\n              console.warn(err);\n            }\n          }\n        }\n\n        return subject.subscribe(subscriber);\n      } else {\n        const state = {\n          args,\n          subscriber,\n          params\n        };\n        return scheduler.schedule(dispatch, 0, state);\n      }\n    });\n  };\n}\n\nfunction dispatch(state) {\n  const self = this;\n  const {\n    args,\n    subscriber,\n    params\n  } = state;\n  const {\n    callbackFunc,\n    context,\n    scheduler\n  } = params;\n  let {\n    subject\n  } = params;\n\n  if (!subject) {\n    subject = params.subject = new AsyncSubject();\n\n    const handler = (...innerArgs) => {\n      const value = innerArgs.length <= 1 ? innerArgs[0] : innerArgs;\n      this.add(scheduler.schedule(dispatchNext, 0, {\n        value,\n        subject\n      }));\n    };\n\n    try {\n      callbackFunc.apply(context, [...args, handler]);\n    } catch (err) {\n      subject.error(err);\n    }\n  }\n\n  this.add(subject.subscribe(subscriber));\n}\n\nfunction dispatchNext(state) {\n  const {\n    value,\n    subject\n  } = state;\n  subject.next(value);\n  subject.complete();\n}\n\nfunction dispatchError(state) {\n  const {\n    err,\n    subject\n  } = state;\n  subject.error(err);\n} //# sourceMappingURL=bindCallback.js.map", "map": null, "metadata": {}, "sourceType": "module"}