{"ast": null, "code": "/**\n * Create a range error with the message:\n *     'Dimension mismatch (<actual size> != <expected size>)'\n * @param {number | number[]} actual        The actual size\n * @param {number | number[]} expected      The expected size\n * @param {string} [relation='!=']          Optional relation between actual\n *                                          and expected size: '!=', '<', etc.\n * @extends RangeError\n */\nexport function DimensionError(actual, expected, relation) {\n  if (!(this instanceof DimensionError)) {\n    throw new SyntaxError('Constructor must be called with the new operator');\n  }\n\n  this.actual = actual;\n  this.expected = expected;\n  this.relation = relation;\n  this.message = 'Dimension mismatch (' + (Array.isArray(actual) ? '[' + actual.join(', ') + ']' : actual) + ' ' + (this.relation || '!=') + ' ' + (Array.isArray(expected) ? '[' + expected.join(', ') + ']' : expected) + ')';\n  this.stack = new Error().stack;\n}\nDimensionError.prototype = new RangeError();\nDimensionError.prototype.constructor = RangeError;\nDimensionError.prototype.name = 'DimensionError';\nDimensionError.prototype.isDimensionError = true;", "map": {"version": 3, "names": ["DimensionError", "actual", "expected", "relation", "SyntaxError", "message", "Array", "isArray", "join", "stack", "Error", "prototype", "RangeError", "constructor", "name", "isDimensionError"], "sources": ["D:/work/joyserver/client/node_modules/mathjs/lib/esm/error/DimensionError.js"], "sourcesContent": ["/**\n * Create a range error with the message:\n *     'Dimension mismatch (<actual size> != <expected size>)'\n * @param {number | number[]} actual        The actual size\n * @param {number | number[]} expected      The expected size\n * @param {string} [relation='!=']          Optional relation between actual\n *                                          and expected size: '!=', '<', etc.\n * @extends RangeError\n */\nexport function DimensionError(actual, expected, relation) {\n  if (!(this instanceof DimensionError)) {\n    throw new SyntaxError('Constructor must be called with the new operator');\n  }\n  this.actual = actual;\n  this.expected = expected;\n  this.relation = relation;\n  this.message = 'Dimension mismatch (' + (Array.isArray(actual) ? '[' + actual.join(', ') + ']' : actual) + ' ' + (this.relation || '!=') + ' ' + (Array.isArray(expected) ? '[' + expected.join(', ') + ']' : expected) + ')';\n  this.stack = new Error().stack;\n}\nDimensionError.prototype = new RangeError();\nDimensionError.prototype.constructor = RangeError;\nDimensionError.prototype.name = 'DimensionError';\nDimensionError.prototype.isDimensionError = true;"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASA,cAAT,CAAwBC,MAAxB,EAAgCC,QAAhC,EAA0CC,QAA1C,EAAoD;EACzD,IAAI,EAAE,gBAAgBH,cAAlB,CAAJ,EAAuC;IACrC,MAAM,IAAII,WAAJ,CAAgB,kDAAhB,CAAN;EACD;;EACD,KAAKH,MAAL,GAAcA,MAAd;EACA,KAAKC,QAAL,GAAgBA,QAAhB;EACA,KAAKC,QAAL,GAAgBA,QAAhB;EACA,KAAKE,OAAL,GAAe,0BAA0BC,KAAK,CAACC,OAAN,CAAcN,MAAd,IAAwB,MAAMA,MAAM,CAACO,IAAP,CAAY,IAAZ,CAAN,GAA0B,GAAlD,GAAwDP,MAAlF,IAA4F,GAA5F,IAAmG,KAAKE,QAAL,IAAiB,IAApH,IAA4H,GAA5H,IAAmIG,KAAK,CAACC,OAAN,CAAcL,QAAd,IAA0B,MAAMA,QAAQ,CAACM,IAAT,CAAc,IAAd,CAAN,GAA4B,GAAtD,GAA4DN,QAA/L,IAA2M,GAA1N;EACA,KAAKO,KAAL,GAAa,IAAIC,KAAJ,GAAYD,KAAzB;AACD;AACDT,cAAc,CAACW,SAAf,GAA2B,IAAIC,UAAJ,EAA3B;AACAZ,cAAc,CAACW,SAAf,CAAyBE,WAAzB,GAAuCD,UAAvC;AACAZ,cAAc,CAACW,SAAf,CAAyBG,IAAzB,GAAgC,gBAAhC;AACAd,cAAc,CAACW,SAAf,CAAyBI,gBAAzB,GAA4C,IAA5C"}, "metadata": {}, "sourceType": "module"}