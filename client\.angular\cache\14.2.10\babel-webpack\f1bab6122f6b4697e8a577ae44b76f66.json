{"ast": null, "code": "/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { ComplexDependencies } from './dependenciesComplexClass.generated.js';\nimport { divideScalarDependencies } from './dependenciesDivideScalar.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createNthRoots } from '../../factoriesAny.js';\nexport var nthRootsDependencies = {\n  ComplexDependencies,\n  divideScalarDependencies,\n  typedDependencies,\n  createNthRoots\n};", "map": {"version": 3, "names": ["ComplexDependencies", "divideScalarDependencies", "typedDependencies", "createNthRoots", "nthRootsDependencies"], "sources": ["D:/work/joyserver/client/node_modules/mathjs/lib/esm/entry/dependenciesAny/dependenciesNthRoots.generated.js"], "sourcesContent": ["/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { ComplexDependencies } from './dependenciesComplexClass.generated.js';\nimport { divideScalarDependencies } from './dependenciesDivideScalar.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createNthRoots } from '../../factoriesAny.js';\nexport var nthRootsDependencies = {\n  ComplexDependencies,\n  divideScalarDependencies,\n  typedDependencies,\n  createNthRoots\n};"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,mBAAT,QAAoC,yCAApC;AACA,SAASC,wBAAT,QAAyC,yCAAzC;AACA,SAASC,iBAAT,QAAkC,kCAAlC;AACA,SAASC,cAAT,QAA+B,uBAA/B;AACA,OAAO,IAAIC,oBAAoB,GAAG;EAChCJ,mBADgC;EAEhCC,wBAFgC;EAGhCC,iBAHgC;EAIhCC;AAJgC,CAA3B"}, "metadata": {}, "sourceType": "module"}