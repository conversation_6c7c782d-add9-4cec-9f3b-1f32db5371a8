{"ast": null, "code": "export var setDifferenceDocs = {\n  name: 'setDifference',\n  category: 'Set',\n  syntax: ['setDifference(set1, set2)'],\n  description: 'Create the difference of two (multi)sets: every element of set1, that is not the element of set2. Multi-dimension arrays will be converted to single-dimension arrays before the operation.',\n  examples: ['setDifference([1, 2, 3, 4], [3, 4, 5, 6])', 'setDifference([[1, 2], [3, 4]], [[3, 4], [5, 6]])'],\n  seealso: ['setUnion', 'setIntersect', 'setSymDifference']\n};", "map": {"version": 3, "names": ["setDifferenceDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["D:/work/joyserver/client/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/set/setDifference.js"], "sourcesContent": ["export var setDifferenceDocs = {\n  name: 'setDifference',\n  category: 'Set',\n  syntax: ['setDifference(set1, set2)'],\n  description: 'Create the difference of two (multi)sets: every element of set1, that is not the element of set2. Multi-dimension arrays will be converted to single-dimension arrays before the operation.',\n  examples: ['setDifference([1, 2, 3, 4], [3, 4, 5, 6])', 'setDifference([[1, 2], [3, 4]], [[3, 4], [5, 6]])'],\n  seealso: ['setUnion', 'setIntersect', 'setSymDifference']\n};"], "mappings": "AAAA,OAAO,IAAIA,iBAAiB,GAAG;EAC7BC,IAAI,EAAE,eADuB;EAE7BC,QAAQ,EAAE,KAFmB;EAG7BC,MAAM,EAAE,CAAC,2BAAD,CAHqB;EAI7BC,WAAW,EAAE,6LAJgB;EAK7BC,QAAQ,EAAE,CAAC,2CAAD,EAA8C,mDAA9C,CALmB;EAM7BC,OAAO,EAAE,CAAC,UAAD,EAAa,cAAb,EAA6B,kBAA7B;AANoB,CAAxB"}, "metadata": {}, "sourceType": "module"}