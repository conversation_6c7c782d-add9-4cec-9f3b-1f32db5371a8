{"ast": null, "code": "export var isNumericDocs = {\n  name: 'isNumeric',\n  category: 'Utils',\n  syntax: ['isNumeric(x)'],\n  description: 'Test whether a value is a numeric value. ' + 'Returns true when the input is a number, BigNumber, Fraction, or boolean.',\n  examples: ['isNumeric(2)', 'isNumeric(\"2\")', 'hasNumericValue(\"2\")', 'isNumeric(0)', 'isNumeric(bignumber(500))', 'isNumeric(fraction(0.125))', 'isNumeric(2 + 3i)', 'isNumeric([2.3, \"foo\", false])'],\n  seealso: ['isInteger', 'isZero', 'isNegative', 'isPositive', 'isNaN', 'hasNumericValue']\n};", "map": {"version": 3, "names": ["isNumericDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["D:/work/joyserver/client/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/utils/isNumeric.js"], "sourcesContent": ["export var isNumericDocs = {\n  name: 'isNumeric',\n  category: 'Utils',\n  syntax: ['isNumeric(x)'],\n  description: 'Test whether a value is a numeric value. ' + 'Returns true when the input is a number, BigNumber, Fraction, or boolean.',\n  examples: ['isNumeric(2)', 'isNumeric(\"2\")', 'hasNumericValue(\"2\")', 'isNumeric(0)', 'isNumeric(bignumber(500))', 'isNumeric(fraction(0.125))', 'isNumeric(2 + 3i)', 'isNumeric([2.3, \"foo\", false])'],\n  seealso: ['isInteger', 'isZero', 'isNegative', 'isPositive', 'isNaN', 'hasNumericValue']\n};"], "mappings": "AAAA,OAAO,IAAIA,aAAa,GAAG;EACzBC,IAAI,EAAE,WADmB;EAEzBC,QAAQ,EAAE,OAFe;EAGzBC,MAAM,EAAE,CAAC,cAAD,CAHiB;EAIzBC,WAAW,EAAE,8CAA8C,2EAJlC;EAKzBC,QAAQ,EAAE,CAAC,cAAD,EAAiB,gBAAjB,EAAmC,sBAAnC,EAA2D,cAA3D,EAA2E,2BAA3E,EAAwG,4BAAxG,EAAsI,mBAAtI,EAA2J,gCAA3J,CALe;EAMzBC,OAAO,EAAE,CAAC,WAAD,EAAc,QAAd,EAAwB,YAAxB,EAAsC,YAAtC,EAAoD,OAApD,EAA6D,iBAA7D;AANgB,CAApB"}, "metadata": {}, "sourceType": "module"}