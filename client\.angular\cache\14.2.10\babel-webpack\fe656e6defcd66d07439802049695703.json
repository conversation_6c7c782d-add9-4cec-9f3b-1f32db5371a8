{"ast": null, "code": "export var isPositiveDocs = {\n  name: 'isPositive',\n  category: 'Utils',\n  syntax: ['isPositive(x)'],\n  description: 'Test whether a value is positive: larger than zero.',\n  examples: ['isPositive(2)', 'isPositive(0)', 'isPositive(-4)', 'isPositive([3, 0.5, -2])'],\n  seealso: ['isInteger', 'isNumeric', 'isNegative', 'isZero']\n};", "map": null, "metadata": {}, "sourceType": "module"}