{"ast": null, "code": "import { Subscriber } from '../Subscriber';\nexport function map(project, thisArg) {\n  return function mapOperation(source) {\n    if (typeof project !== 'function') {\n      throw new TypeError('argument is not a function. Are you looking for `mapTo()`?');\n    }\n\n    return source.lift(new MapOperator(project, thisArg));\n  };\n}\nexport class MapOperator {\n  constructor(project, thisArg) {\n    this.project = project;\n    this.thisArg = thisArg;\n  }\n\n  call(subscriber, source) {\n    return source.subscribe(new MapSubscriber(subscriber, this.project, this.thisArg));\n  }\n\n}\n\nclass MapSubscriber extends Subscriber {\n  constructor(destination, project, thisArg) {\n    super(destination);\n    this.project = project;\n    this.count = 0;\n    this.thisArg = thisArg || this;\n  }\n\n  _next(value) {\n    let result;\n\n    try {\n      result = this.project.call(this.thisArg, value, this.count++);\n    } catch (err) {\n      this.destination.error(err);\n      return;\n    }\n\n    this.destination.next(result);\n  }\n\n} //# sourceMappingURL=map.js.map", "map": null, "metadata": {}, "sourceType": "module"}