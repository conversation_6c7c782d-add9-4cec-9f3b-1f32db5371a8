{"ast": null, "code": "import { Subject } from './Subject';\nexport class AsyncSubject extends Subject {\n  constructor() {\n    super(...arguments);\n    this._value = null;\n    this._hasValue = false;\n    this._isComplete = false;\n  }\n\n  _checkFinalizedStatuses(subscriber) {\n    const {\n      hasError,\n      _hasValue,\n      _value,\n      thrownError,\n      isStopped,\n      _isComplete\n    } = this;\n\n    if (hasError) {\n      subscriber.error(thrownError);\n    } else if (isStopped || _isComplete) {\n      _hasValue && subscriber.next(_value);\n      subscriber.complete();\n    }\n  }\n\n  next(value) {\n    if (!this.isStopped) {\n      this._value = value;\n      this._hasValue = true;\n    }\n  }\n\n  complete() {\n    const {\n      _hasValue,\n      _value,\n      _isComplete\n    } = this;\n\n    if (!_isComplete) {\n      this._isComplete = true;\n      _hasValue && super.next(_value);\n      super.complete();\n    }\n  }\n\n}", "map": {"version": 3, "names": ["Subject", "AsyncSubject", "constructor", "arguments", "_value", "_hasValue", "_isComplete", "_checkFinalizedStatuses", "subscriber", "<PERSON><PERSON><PERSON><PERSON>", "thrownError", "isStopped", "error", "next", "complete", "value"], "sources": ["D:/work/joyserver/client/node_modules/rxjs/dist/esm/internal/AsyncSubject.js"], "sourcesContent": ["import { Subject } from './Subject';\nexport class AsyncSubject extends Subject {\n    constructor() {\n        super(...arguments);\n        this._value = null;\n        this._hasValue = false;\n        this._isComplete = false;\n    }\n    _checkFinalizedStatuses(subscriber) {\n        const { hasError, _hasValue, _value, thrownError, isStopped, _isComplete } = this;\n        if (hasError) {\n            subscriber.error(thrownError);\n        }\n        else if (isStopped || _isComplete) {\n            _hasValue && subscriber.next(_value);\n            subscriber.complete();\n        }\n    }\n    next(value) {\n        if (!this.isStopped) {\n            this._value = value;\n            this._hasValue = true;\n        }\n    }\n    complete() {\n        const { _hasValue, _value, _isComplete } = this;\n        if (!_isComplete) {\n            this._isComplete = true;\n            _hasValue && super.next(_value);\n            super.complete();\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,OAAT,QAAwB,WAAxB;AACA,OAAO,MAAMC,YAAN,SAA2BD,OAA3B,CAAmC;EACtCE,WAAW,GAAG;IACV,MAAM,GAAGC,SAAT;IACA,KAAKC,MAAL,GAAc,IAAd;IACA,KAAKC,SAAL,GAAiB,KAAjB;IACA,KAAKC,WAAL,GAAmB,KAAnB;EACH;;EACDC,uBAAuB,CAACC,UAAD,EAAa;IAChC,MAAM;MAAEC,QAAF;MAAYJ,SAAZ;MAAuBD,MAAvB;MAA+BM,WAA/B;MAA4CC,SAA5C;MAAuDL;IAAvD,IAAuE,IAA7E;;IACA,IAAIG,QAAJ,EAAc;MACVD,UAAU,CAACI,KAAX,CAAiBF,WAAjB;IACH,CAFD,MAGK,IAAIC,SAAS,IAAIL,WAAjB,EAA8B;MAC/BD,SAAS,IAAIG,UAAU,CAACK,IAAX,CAAgBT,MAAhB,CAAb;MACAI,UAAU,CAACM,QAAX;IACH;EACJ;;EACDD,IAAI,CAACE,KAAD,EAAQ;IACR,IAAI,CAAC,KAAKJ,SAAV,EAAqB;MACjB,KAAKP,MAAL,GAAcW,KAAd;MACA,KAAKV,SAAL,GAAiB,IAAjB;IACH;EACJ;;EACDS,QAAQ,GAAG;IACP,MAAM;MAAET,SAAF;MAAaD,MAAb;MAAqBE;IAArB,IAAqC,IAA3C;;IACA,IAAI,CAACA,WAAL,EAAkB;MACd,KAAKA,WAAL,GAAmB,IAAnB;MACAD,SAAS,IAAI,MAAMQ,IAAN,CAAWT,MAAX,CAAb;MACA,MAAMU,QAAN;IACH;EACJ;;AA9BqC"}, "metadata": {}, "sourceType": "module"}