{"ast": null, "code": "import _asyncToGenerator from \"D:/work/joyserver/client/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Location, LocationStrategy, PathLocationStrategy } from \"@angular/common\";\nimport { RegInfoState } from \"@core-types/config.types\";\nimport { lastValueFrom } from \"rxjs\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@core-service/localStorage.service\";\nimport * as i3 from \"@core-service/socket/socket.service\";\nimport * as i4 from \"@core-service/seat.service\";\nimport * as i5 from \"@core-service/detector.service\";\nimport * as i6 from \"@core/service/api.service\";\nimport * as i7 from \"@core/modal/custom-loading/custom-loading.service\";\nimport * as i8 from \"@ngx-translate/core\";\nimport * as i9 from \"@core/service/exam.service\";\nimport * as i10 from \"@angular/common\";\nimport * as i11 from \"@core/service/utils/util.service\";\nimport * as i12 from \"@angular/forms\";\nimport * as i13 from \"../../core/modal/custom-confirm/custom-confirm.component\";\n\nfunction RegisterFormComponent_div_1_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const IP_r5 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\"\", IP_r5, \"\\u00A0\\u00A0\");\n  }\n}\n\nfunction RegisterFormComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 12);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵtemplate(3, RegisterFormComponent_div_1_span_3_Template, 2, 1, \"span\", 13);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 2, \"register.localIp\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.win.joyshell.IPAddresses);\n  }\n}\n\nfunction RegisterFormComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 5)(1, \"label\", 14);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"input\", 15);\n    i0.ɵɵlistener(\"ngModelChange\", function RegisterFormComponent_div_12_Template_input_ngModelChange_4_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r6 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r6.seatNumber = $event);\n    });\n    i0.ɵɵelementEnd()();\n  }\n\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(3, 2, \"register.apply\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngModel\", ctx_r1.seatNumber);\n  }\n}\n\nfunction RegisterFormComponent_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 5)(1, \"label\", 16);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"input\", 17);\n    i0.ɵɵlistener(\"ngModelChange\", function RegisterFormComponent_div_13_Template_input_ngModelChange_4_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r8 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r8.passcode = $event);\n    });\n    i0.ɵɵelementEnd()();\n  }\n\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(3, 2, \"register.passcode\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngModel\", ctx_r2.passcode);\n  }\n}\n\nfunction RegisterFormComponent_custom_confirm_19_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"custom-confirm\", 18);\n    i0.ɵɵlistener(\"onConfirm\", function RegisterFormComponent_custom_confirm_19_Template_custom_confirm_onConfirm_0_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.confirmForceOccupy());\n    })(\"onCancel\", function RegisterFormComponent_custom_confirm_19_Template_custom_confirm_onCancel_0_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r12 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r12.cancelForceOccupy());\n    });\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"bodyText\", ctx_r3.lan.ipConflict);\n  }\n}\n\nexport class RegisterFormComponent {\n  constructor(router, localStorageService, socketService, seatService, detectorService, apiService, loadingUI, translate, examSer, location, utilSer) {\n    this.router = router;\n    this.localStorageService = localStorageService;\n    this.socketService = socketService;\n    this.seatService = seatService;\n    this.detectorService = detectorService;\n    this.apiService = apiService;\n    this.loadingUI = loadingUI;\n    this.translate = translate;\n    this.examSer = examSer;\n    this.utilSer = utilSer;\n    this.force = false;\n    this.state = \"ready\";\n    this.dsMode = false;\n    this.hostEditable = false;\n    this.location = location;\n    this.dsMode = this.localStorageService.isDynamicSeatEnabled();\n    this.lan = {\n      ipConflict: this.translate.instant(\"register.ipConflict1\")\n    };\n  }\n\n  ngOnInit() {\n    this.win = window;\n\n    if (window.joyshell) {\n      // electron\n      this.hostEditable = true;\n      this.setHost(this.localStorageService.serverHost);\n    } else {\n      // browser\n      if (this.localStorageService.mode === \"localweb\") {\n        this.hostEditable = true;\n\n        if (this.localStorageService.serverHost) {\n          this.setHost(this.localStorageService.serverHost);\n        }\n      } else {\n        // web\n        this.setHost(location.host);\n      }\n    } // 检测注册信息变化\n\n\n    if (!window.is_local && this.localStorageService.mode === \"localweb\") {\n      this.sub = this.localStorageService.pollRegInfo().subscribe(diff => {\n        if (diff && this.state !== \"registering\") {\n          this.localStorageService.updateRegInfo(diff);\n          window.location.reload();\n        }\n      });\n    } // 重新注册，重置本地变量\n\n\n    this.examSer.resetLocalVariable();\n  }\n\n  setHost(s) {\n    if (s) {\n      const p = s.split(\":\");\n\n      if (p.length > 1) {\n        this.host = p[0] + (p[1] !== \"\" + this.localStorageService.getDefaultServerPort() ? \":\" + p[1] : \"\");\n      } else {\n        this.host = s;\n      }\n    } else {\n      console.log(\"**** host is undefined\");\n    }\n  }\n\n  ngOnDestroy() {\n    if (this.sub) {\n      this.sub.unsubscribe();\n    }\n  }\n\n  register() {\n    var _this = this;\n\n    if (this.dsMode) {\n      if (!this.passcode) {\n        this.warn = this.translate.instant(\"register.passcodeRequired\");\n        return;\n      }\n    } else {\n      if (typeof this.seatNumber === \"undefined\" || this.seatNumber === null) {\n        this.warn = this.translate.instant(\"register.seatNumberInvalid\");\n        return;\n      }\n\n      if (Number(this.seatNumber) <= 0) {\n        this.warn = this.translate.instant(\"register.seatNumberLt0\");\n        return;\n      }\n\n      if (Number(this.seatNumber) > 9999) {\n        this.warn = this.translate.instant(\"register.seatNumberSt9999\");\n        return;\n      }\n    } // 保存管理机地址\n\n\n    this.state = \"registering\";\n    this.warn = \"\";\n    this.localStorageService.setServerHost(this.host).then( /*#__PURE__*/_asyncToGenerator(function* () {\n      // 注册考试机\n      const registerInfo = {\n        seat: _this.seatNumber,\n        force: _this.force,\n        device: yield _this.getDeviceInfo(),\n        server: _this.host\n      };\n\n      if (_this.dsMode) {\n        registerInfo.seat = \"0\";\n        registerInfo.code = _this.passcode.trim();\n      }\n\n      _this.loadingUI.setValue(true);\n\n      return _this.apiService.register(registerInfo).toPromise();\n    })).then(this.handleRegister.bind(this)).catch(resp => {\n      // handle error\n      console.error(\"register\", resp.status, resp.statusText);\n\n      if (resp.status === 0 || resp.status === 502) {\n        // CONNECTION_REFUSED\n        this.warn = this.translate.instant(\"register.linkFail\");\n      }\n    }).then(() => {\n      this.state = \"ready\";\n      this.loadingUI.setValue(false);\n    });\n  }\n\n  handleRegister(result) {\n    var _this2 = this;\n\n    return _asyncToGenerator(function* () {\n      // 注册失败\n      if (result.status === \"failed\") {\n        console.log(`* Register client failed:  ${result.err}`);\n        _this2.warn = _this2.translate.instant(result.code);\n        return;\n      }\n\n      if (result.status === \"conflict\") {\n        _this2.conflict_host = result.conflict_host; //this.warn = `机位号“${this.seatNumber}”已被占用`;\n\n        _this2.warn = yield lastValueFrom(_this2.translate.get(\"register.seatExisted\", {\n          seatNumber: _this2.seatNumber\n        }));\n        _this2.lan.ipConflict = yield lastValueFrom(_this2.translate.get(\"register.ipConflict\", {\n          seatNum: _this2.seatNumber\n        }));\n        return;\n      }\n\n      if (!result.seat) {\n        result.seat = +_this2.seatNumber;\n      } else {\n        _this2.seatNumber = result.seat;\n      }\n\n      const regInfo = {\n        code: result.code,\n        seat: result.seat,\n        room: result.room,\n        room_code: result.room_code,\n        center_name: result.center_name,\n        center_address: result.center_address,\n        state: RegInfoState.Valid\n      };\n\n      _this2.localStorageService.updateRegInfo(regInfo);\n\n      _this2.seatService.setSeatNumber(result.seat); // 连接管理机\n\n\n      try {\n        yield _this2.socketService.connect(); // 登录成功\n\n        _this2.router.navigate([\"register/info\"]);\n      } catch (err) {\n        console.error(err);\n        _this2.warn = _this2.translate.instant(\"register.linkDisabled\");\n      }\n    })();\n  }\n\n  confirmForceOccupy() {\n    this.force = true;\n    this.register();\n  }\n\n  cancelForceOccupy() {\n    this.conflict_host = null;\n  } // 设备信息\n\n\n  getDeviceInfo() {\n    var _this3 = this;\n\n    return _asyncToGenerator(function* () {\n      const screen = window.screen.width + \"*\" + window.screen.height;\n\n      const ua = _this3.detectorService.getDeviceInfo()[\"parse\"](navigator.userAgent);\n\n      const physAddresses = yield _this3.utilSer.getPhysAddr();\n      const hostname = yield _this3.utilSer.getHostName();\n      const device = {\n        screen_size: screen,\n        OS: ua[\"os\"].name,\n        mac_addr: physAddresses,\n        hostname\n      };\n      return device;\n    })();\n  }\n\n}\n\nRegisterFormComponent.ɵfac = function RegisterFormComponent_Factory(t) {\n  return new (t || RegisterFormComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.LocalStorageService), i0.ɵɵdirectiveInject(i3.SocketService), i0.ɵɵdirectiveInject(i4.SeatService), i0.ɵɵdirectiveInject(i5.DetectorService), i0.ɵɵdirectiveInject(i6.APIService), i0.ɵɵdirectiveInject(i7.CustomLoadingService), i0.ɵɵdirectiveInject(i8.TranslateService), i0.ɵɵdirectiveInject(i9.ExamService), i0.ɵɵdirectiveInject(i10.Location), i0.ɵɵdirectiveInject(i11.UtilService));\n};\n\nRegisterFormComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: RegisterFormComponent,\n  selectors: [[\"ng-component\"]],\n  features: [i0.ɵɵProvidersFeature([Location, {\n    provide: LocationStrategy,\n    useClass: PathLocationStrategy\n  }])],\n  decls: 20,\n  vars: 16,\n  consts: [[1, \"register-form-wrap\"], [\"class\", \"local-ip font-weight-bold\", 4, \"ngIf\"], [1, \"form-wrap\", \"row\"], [1, \"form-content\", \"col-4\", \"offset-1\"], [1, \"title\"], [1, \"form-group\"], [\"for\", \"managerIp\"], [\"type\", \"text\", \"id\", \"managerIp\", 1, \"form-control\", 3, \"disabled\", \"ngModel\", \"ngModelChange\"], [\"class\", \"form-group\", 4, \"ngIf\"], [1, \"error\"], [1, \"btn\", \"mt-1\", \"w-100\", \"btn-register\", 3, \"click\"], [3, \"bodyText\", \"onConfirm\", \"onCancel\", 4, \"ngIf\"], [1, \"local-ip\", \"font-weight-bold\"], [4, \"ngFor\", \"ngForOf\"], [\"for\", \"seatNumer\"], [\"type\", \"number\", \"min\", \"1\", \"max\", \"9999\", \"id\", \"seatNumer\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"passCode\"], [\"type\", \"text\", \"id\", \"passCode\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [3, \"bodyText\", \"onConfirm\", \"onCancel\"]],\n  template: function RegisterFormComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"div\", 0);\n      i0.ɵɵtemplate(1, RegisterFormComponent_div_1_Template, 4, 4, \"div\", 1);\n      i0.ɵɵelementStart(2, \"div\", 2)(3, \"div\", 3)(4, \"div\", 4);\n      i0.ɵɵtext(5);\n      i0.ɵɵpipe(6, \"translate\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(7, \"div\", 5)(8, \"label\", 6);\n      i0.ɵɵtext(9);\n      i0.ɵɵpipe(10, \"translate\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(11, \"input\", 7);\n      i0.ɵɵlistener(\"ngModelChange\", function RegisterFormComponent_Template_input_ngModelChange_11_listener($event) {\n        return ctx.host = $event;\n      });\n      i0.ɵɵelementEnd()();\n      i0.ɵɵtemplate(12, RegisterFormComponent_div_12_Template, 5, 4, \"div\", 8);\n      i0.ɵɵtemplate(13, RegisterFormComponent_div_13_Template, 5, 4, \"div\", 8);\n      i0.ɵɵelementStart(14, \"div\", 9);\n      i0.ɵɵtext(15);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(16, \"button\", 10);\n      i0.ɵɵlistener(\"click\", function RegisterFormComponent_Template_button_click_16_listener() {\n        return ctx.register();\n      });\n      i0.ɵɵtext(17);\n      i0.ɵɵpipe(18, \"translate\");\n      i0.ɵɵelementEnd()()()();\n      i0.ɵɵtemplate(19, RegisterFormComponent_custom_confirm_19_Template, 1, 1, \"custom-confirm\", 11);\n    }\n\n    if (rf & 2) {\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.win.joyshell && ctx.win.joyshell.IPAddresses);\n      i0.ɵɵadvance(4);\n      i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(6, 10, \"register.reg\"));\n      i0.ɵɵadvance(4);\n      i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(10, 12, \"register.ip\"));\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"disabled\", !ctx.hostEditable)(\"ngModel\", ctx.host);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", !ctx.dsMode);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.dsMode);\n      i0.ɵɵadvance(2);\n      i0.ɵɵtextInterpolate(ctx.warn);\n      i0.ɵɵadvance(2);\n      i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(18, 14, \"register.register\"));\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngIf\", !!ctx.conflict_host);\n    }\n  },\n  dependencies: [i12.DefaultValueAccessor, i12.NumberValueAccessor, i12.NgControlStatus, i12.MinValidator, i12.MaxValidator, i10.NgForOf, i10.NgIf, i12.NgModel, i13.CustomConfirmComponent, i8.TranslatePipe],\n  styles: [\".error[_ngcontent-%COMP%]{min-height:30px;color:#f55}.local-ip[_ngcontent-%COMP%]{position:absolute;right:10px;top:10px;color:#383e50}.register-form-wrap[_ngcontent-%COMP%]{height:100%;background-color:#edf0ff;padding-top:120px}.register-form-wrap[_ngcontent-%COMP%]   .form-wrap[_ngcontent-%COMP%]{width:880px;height:470px;margin:0 auto;background:url(register_form.caba2c34ac4c4a48.png) no-repeat center center;background-size:cover;box-shadow:0 0 23px 5px #8690b157;border-radius:10px}.register-form-wrap[_ngcontent-%COMP%]   .form-wrap[_ngcontent-%COMP%]   .form-content[_ngcontent-%COMP%]{padding-top:60px}.register-form-wrap[_ngcontent-%COMP%]   .form-wrap[_ngcontent-%COMP%]   .form-content[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-size:24px;color:#373d4f;font-weight:700;margin-bottom:40px}.register-form-wrap[_ngcontent-%COMP%]   .form-wrap[_ngcontent-%COMP%]   .form-content[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]{color:#828aa0;font-size:14px}.register-form-wrap[_ngcontent-%COMP%]   .form-wrap[_ngcontent-%COMP%]   .form-content[_ngcontent-%COMP%]   .btn-register[_ngcontent-%COMP%]{background-color:#5d83ff;color:#fff}\"]\n});", "map": null, "metadata": {}, "sourceType": "module"}