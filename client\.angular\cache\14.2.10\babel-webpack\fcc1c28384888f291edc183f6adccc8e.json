{"ast": null, "code": "export var isNaNDocs = {\n  name: 'isNaN',\n  category: 'Utils',\n  syntax: ['isNaN(x)'],\n  description: 'Test whether a value is NaN (not a number)',\n  examples: ['isNaN(2)', 'isNaN(0 / 0)', 'isNaN(NaN)', 'isNaN(Infinity)'],\n  seealso: ['isNegative', 'isNumeric', 'isPositive', 'isZero']\n};", "map": {"version": 3, "names": ["isNaNDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["D:/work/joyserver/client/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/utils/isNaN.js"], "sourcesContent": ["export var isNaNDocs = {\n  name: 'isNaN',\n  category: 'Utils',\n  syntax: ['isNaN(x)'],\n  description: 'Test whether a value is NaN (not a number)',\n  examples: ['isNaN(2)', 'isNaN(0 / 0)', 'isNaN(NaN)', 'isNaN(Infinity)'],\n  seealso: ['isNegative', 'isNumeric', 'isPositive', 'isZero']\n};"], "mappings": "AAAA,OAAO,IAAIA,SAAS,GAAG;EACrBC,IAAI,EAAE,OADe;EAErBC,QAAQ,EAAE,OAFW;EAGrBC,MAAM,EAAE,CAAC,UAAD,CAHa;EAIrBC,WAAW,EAAE,4CAJQ;EAKrBC,QAAQ,EAAE,CAAC,UAAD,EAAa,cAAb,EAA6B,YAA7B,EAA2C,iBAA3C,CALW;EAMrBC,OAAO,EAAE,CAAC,YAAD,EAAe,WAAf,EAA4B,YAA5B,EAA0C,QAA1C;AANY,CAAhB"}, "metadata": {}, "sourceType": "module"}