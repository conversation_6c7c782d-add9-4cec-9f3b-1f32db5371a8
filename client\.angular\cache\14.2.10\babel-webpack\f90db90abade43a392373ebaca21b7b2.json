{"ast": null, "code": "import { isArray, isMatrix, isString, typeOf } from '../../utils/is.js';\nimport { clone } from '../../utils/object.js';\nimport { factory } from '../../utils/factory.js';\nvar name = 'ImmutableDenseMatrix';\nvar dependencies = ['smaller', 'DenseMatrix'];\nexport var createImmutableDenseMatrixClass = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    smaller,\n    DenseMatrix\n  } = _ref;\n\n  function ImmutableDenseMatrix(data, datatype) {\n    if (!(this instanceof ImmutableDenseMatrix)) {\n      throw new SyntaxError('Constructor must be called with the new operator');\n    }\n\n    if (datatype && !isString(datatype)) {\n      throw new Error('Invalid datatype: ' + datatype);\n    }\n\n    if (isMatrix(data) || isArray(data)) {\n      // use DenseMatrix implementation\n      var matrix = new DenseMatrix(data, datatype); // internal structures\n\n      this._data = matrix._data;\n      this._size = matrix._size;\n      this._datatype = matrix._datatype;\n      this._min = null;\n      this._max = null;\n    } else if (data && isArray(data.data) && isArray(data.size)) {\n      // initialize fields from JSON representation\n      this._data = data.data;\n      this._size = data.size;\n      this._datatype = data.datatype;\n      this._min = typeof data.min !== 'undefined' ? data.min : null;\n      this._max = typeof data.max !== 'undefined' ? data.max : null;\n    } else if (data) {\n      // unsupported type\n      throw new TypeError('Unsupported type of data (' + typeOf(data) + ')');\n    } else {\n      // nothing provided\n      this._data = [];\n      this._size = [0];\n      this._datatype = datatype;\n      this._min = null;\n      this._max = null;\n    }\n  }\n\n  ImmutableDenseMatrix.prototype = new DenseMatrix();\n  /**\n   * Attach type information\n   */\n\n  ImmutableDenseMatrix.prototype.type = 'ImmutableDenseMatrix';\n  ImmutableDenseMatrix.prototype.isImmutableDenseMatrix = true;\n  /**\n   * Get a subset of the matrix, or replace a subset of the matrix.\n   *\n   * Usage:\n   *     const subset = matrix.subset(index)               // retrieve subset\n   *     const value = matrix.subset(index, replacement)   // replace subset\n   *\n   * @param {Index} index\n   * @param {Array | ImmutableDenseMatrix | *} [replacement]\n   * @param {*} [defaultValue=0]      Default value, filled in on new entries when\n   *                                  the matrix is resized. If not provided,\n   *                                  new matrix elements will be filled with zeros.\n   */\n\n  ImmutableDenseMatrix.prototype.subset = function (index) {\n    switch (arguments.length) {\n      case 1:\n        {\n          // use base implementation\n          var m = DenseMatrix.prototype.subset.call(this, index); // check result is a matrix\n\n          if (isMatrix(m)) {\n            // return immutable matrix\n            return new ImmutableDenseMatrix({\n              data: m._data,\n              size: m._size,\n              datatype: m._datatype\n            });\n          }\n\n          return m;\n        }\n      // intentional fall through\n\n      case 2:\n      case 3:\n        throw new Error('Cannot invoke set subset on an Immutable Matrix instance');\n\n      default:\n        throw new SyntaxError('Wrong number of arguments');\n    }\n  };\n  /**\n   * Replace a single element in the matrix.\n   * @param {Number[]} index   Zero-based index\n   * @param {*} value\n   * @param {*} [defaultValue]        Default value, filled in on new entries when\n   *                                  the matrix is resized. If not provided,\n   *                                  new matrix elements will be left undefined.\n   * @return {ImmutableDenseMatrix} self\n   */\n\n\n  ImmutableDenseMatrix.prototype.set = function () {\n    throw new Error('Cannot invoke set on an Immutable Matrix instance');\n  };\n  /**\n   * Resize the matrix to the given size. Returns a copy of the matrix when\n   * `copy=true`, otherwise return the matrix itself (resize in place).\n   *\n   * @param {Number[]} size           The new size the matrix should have.\n   * @param {*} [defaultValue=0]      Default value, filled in on new entries.\n   *                                  If not provided, the matrix elements will\n   *                                  be filled with zeros.\n   * @param {boolean} [copy]          Return a resized copy of the matrix\n   *\n   * @return {Matrix}                 The resized matrix\n   */\n\n\n  ImmutableDenseMatrix.prototype.resize = function () {\n    throw new Error('Cannot invoke resize on an Immutable Matrix instance');\n  };\n  /**\n   * Disallows reshaping in favor of immutability.\n   *\n   * @throws {Error} Operation not allowed\n   */\n\n\n  ImmutableDenseMatrix.prototype.reshape = function () {\n    throw new Error('Cannot invoke reshape on an Immutable Matrix instance');\n  };\n  /**\n   * Create a clone of the matrix\n   * @return {ImmutableDenseMatrix} clone\n   */\n\n\n  ImmutableDenseMatrix.prototype.clone = function () {\n    return new ImmutableDenseMatrix({\n      data: clone(this._data),\n      size: clone(this._size),\n      datatype: this._datatype\n    });\n  };\n  /**\n   * Get a JSON representation of the matrix\n   * @returns {Object}\n   */\n\n\n  ImmutableDenseMatrix.prototype.toJSON = function () {\n    return {\n      mathjs: 'ImmutableDenseMatrix',\n      data: this._data,\n      size: this._size,\n      datatype: this._datatype\n    };\n  };\n  /**\n   * Generate a matrix from a JSON object\n   * @param {Object} json  An object structured like\n   *                       `{\"mathjs\": \"ImmutableDenseMatrix\", data: [], size: []}`,\n   *                       where mathjs is optional\n   * @returns {ImmutableDenseMatrix}\n   */\n\n\n  ImmutableDenseMatrix.fromJSON = function (json) {\n    return new ImmutableDenseMatrix(json);\n  };\n  /**\n   * Swap rows i and j in Matrix.\n   *\n   * @param {Number} i       Matrix row index 1\n   * @param {Number} j       Matrix row index 2\n   *\n   * @return {Matrix}        The matrix reference\n   */\n\n\n  ImmutableDenseMatrix.prototype.swapRows = function () {\n    throw new Error('Cannot invoke swapRows on an Immutable Matrix instance');\n  };\n  /**\n   * Calculate the minimum value in the set\n   * @return {Number | undefined} min\n   */\n\n\n  ImmutableDenseMatrix.prototype.min = function () {\n    // check min has been calculated before\n    if (this._min === null) {\n      // minimum\n      var m = null; // compute min\n\n      this.forEach(function (v) {\n        if (m === null || smaller(v, m)) {\n          m = v;\n        }\n      });\n      this._min = m !== null ? m : undefined;\n    }\n\n    return this._min;\n  };\n  /**\n   * Calculate the maximum value in the set\n   * @return {Number | undefined} max\n   */\n\n\n  ImmutableDenseMatrix.prototype.max = function () {\n    // check max has been calculated before\n    if (this._max === null) {\n      // maximum\n      var m = null; // compute max\n\n      this.forEach(function (v) {\n        if (m === null || smaller(m, v)) {\n          m = v;\n        }\n      });\n      this._max = m !== null ? m : undefined;\n    }\n\n    return this._max;\n  };\n\n  return ImmutableDenseMatrix;\n}, {\n  isClass: true\n});", "map": null, "metadata": {}, "sourceType": "module"}