{"ast": null, "code": "export var setDistinctDocs = {\n  name: 'setDistinct',\n  category: 'Set',\n  syntax: ['setDistinct(set)'],\n  description: 'Collect the distinct elements of a multiset. A multi-dimension array will be converted to a single-dimension array before the operation.',\n  examples: ['setDistinct([1, 1, 1, 2, 2, 3])'],\n  seealso: ['setMultiplicity']\n};", "map": {"version": 3, "names": ["setDistinctDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["D:/work/joyserver/client/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/set/setDistinct.js"], "sourcesContent": ["export var setDistinctDocs = {\n  name: 'setDistinct',\n  category: 'Set',\n  syntax: ['setDistinct(set)'],\n  description: 'Collect the distinct elements of a multiset. A multi-dimension array will be converted to a single-dimension array before the operation.',\n  examples: ['setDistinct([1, 1, 1, 2, 2, 3])'],\n  seealso: ['setMultiplicity']\n};"], "mappings": "AAAA,OAAO,IAAIA,eAAe,GAAG;EAC3BC,IAAI,EAAE,aADqB;EAE3BC,QAAQ,EAAE,KAFiB;EAG3BC,MAAM,EAAE,CAAC,kBAAD,CAHmB;EAI3BC,WAAW,EAAE,0IAJc;EAK3BC,QAAQ,EAAE,CAAC,iCAAD,CALiB;EAM3BC,OAAO,EAAE,CAAC,iBAAD;AANkB,CAAtB"}, "metadata": {}, "sourceType": "module"}