{"ast": null, "code": "import { factory } from '../../../utils/factory.js';\nimport { clone } from '../../../utils/object.js';\nvar name = 'matAlgo14xDs';\nvar dependencies = ['typed'];\nexport var createMatAlgo14xDs = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed\n  } = _ref;\n  /**\n   * Iterates over DenseMatrix items and invokes the callback function f(Aij..z, b).\n   * Callback function invoked MxN times.\n   *\n   * C(i,j,...z) = f(Aij..z, b)\n   *\n   * @param {Matrix}   a                 The DenseMatrix instance (A)\n   * @param {Scalar}   b                 The Scalar value\n   * @param {Function} callback          The f(Aij..z,b) operation to invoke\n   * @param {boolean}  inverse           A true value indicates callback should be invoked f(b,Aij..z)\n   *\n   * @return {Matrix}                    DenseMatrix (C)\n   *\n   * https://github.com/josdejong/mathjs/pull/346#issuecomment-97659042\n   */\n\n  return function matAlgo14xDs(a, b, callback, inverse) {\n    // a arrays\n    var adata = a._data;\n    var asize = a._size;\n    var adt = a._datatype; // datatype\n\n    var dt; // callback signature to use\n\n    var cf = callback; // process data types\n\n    if (typeof adt === 'string') {\n      // datatype\n      dt = adt; // convert b to the same datatype\n\n      b = typed.convert(b, dt); // callback\n\n      cf = typed.find(callback, [dt, dt]);\n    } // populate cdata, iterate through dimensions\n\n\n    var cdata = asize.length > 0 ? _iterate(cf, 0, asize, asize[0], adata, b, inverse) : []; // c matrix\n\n    return a.createDenseMatrix({\n      data: cdata,\n      size: clone(asize),\n      datatype: dt\n    });\n  }; // recursive function\n\n  function _iterate(f, level, s, n, av, bv, inverse) {\n    // initialize array for this level\n    var cv = []; // check we reach the last level\n\n    if (level === s.length - 1) {\n      // loop arrays in last level\n      for (var i = 0; i < n; i++) {\n        // invoke callback and store value\n        cv[i] = inverse ? f(bv, av[i]) : f(av[i], bv);\n      }\n    } else {\n      // iterate current level\n      for (var j = 0; j < n; j++) {\n        // iterate next level\n        cv[j] = _iterate(f, level + 1, s, s[level + 1], av[j], bv, inverse);\n      }\n    }\n\n    return cv;\n  }\n});", "map": null, "metadata": {}, "sourceType": "module"}