{"ast": null, "code": "import _asyncToGenerator from \"D:/work/joyserver/client/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { ElementRef } from \"@angular/core\";\nimport { Exam } from \"@core-types/exam.types\";\nimport { timer, lastValueFrom } from \"rxjs\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@core/service/exam.service\";\nimport * as i2 from \"@core/service/response.service\";\nimport * as i3 from \"@core/service/utils/util.service\";\nimport * as i4 from \"@core/service/form.service\";\nimport * as i5 from \"@ngx-translate/core\";\nimport * as i6 from \"@core/service/jtcustom.service\";\nimport * as i7 from \"@core/service/auto-test.service\";\nimport * as i8 from \"@angular/common\";\nimport * as i9 from \"../../../core/modal/custom-confirm/custom-confirm.component\";\nimport * as i10 from \"./item-block/item-block.component\";\nimport * as i11 from \"../../../core/pipe/domSanitized.pipe\";\nconst _c0 = [\"templateIframe\"];\n\nfunction SelectItemsComponent_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"iframe\", 1, 2);\n    i0.ɵɵelementContainerEnd();\n  }\n}\n\nfunction SelectItemsComponent_ng_container_1_div_1_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 11);\n    i0.ɵɵelement(1, \"app-item-block\", 12);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const item_r6 = ctx.$implicit;\n    const i_r7 = ctx.index;\n    const ctx_r5 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"item\", item_r6)(\"index\", i_r7)(\"group\", ctx_r5.group)(\"itemsSelected\", ctx_r5.itemsSelected);\n  }\n}\n\nconst _c1 = function (a0) {\n  return {\n    disabled: a0\n  };\n};\n\nfunction SelectItemsComponent_ng_container_1_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 5)(1, \"div\", 6);\n    i0.ɵɵelement(2, \"div\", 7);\n    i0.ɵɵpipe(3, \"safeHtml\");\n    i0.ɵɵtemplate(4, SelectItemsComponent_ng_container_1_div_1_div_4_Template, 2, 4, \"div\", 8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 9)(6, \"button\", 10);\n    i0.ɵɵlistener(\"click\", function SelectItemsComponent_ng_container_1_div_1_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r8 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r8.btnConfirm());\n    });\n    i0.ɵɵtext(7);\n    i0.ɵɵpipe(8, \"translate\");\n    i0.ɵɵelementEnd()()();\n  }\n\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"innerHTML\", i0.ɵɵpipeBind1(3, 4, ctx_r3.group == null ? null : ctx_r3.group.selection == null ? null : ctx_r3.group.selection.title), i0.ɵɵsanitizeHtml);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.group == null ? null : ctx_r3.group.items);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(8, _c1, ctx_r3.itemsSelected.length < ctx_r3.group.selection.manual));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(8, 6, \"exam.groupSelection.confirm\"), \" \");\n  }\n}\n\nfunction SelectItemsComponent_ng_container_1_custom_confirm_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"custom-confirm\", 13);\n    i0.ɵɵlistener(\"onConfirm\", function SelectItemsComponent_ng_container_1_custom_confirm_2_Template_custom_confirm_onConfirm_0_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r10.modalConfirm());\n    })(\"onCancel\", function SelectItemsComponent_ng_container_1_custom_confirm_2_Template_custom_confirm_onCancel_0_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r12 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r12.modalCancel());\n    });\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"bodyText\", ctx_r4.tipCont);\n  }\n}\n\nfunction SelectItemsComponent_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, SelectItemsComponent_ng_container_1_div_1_Template, 9, 10, \"div\", 3);\n    i0.ɵɵtemplate(2, SelectItemsComponent_ng_container_1_custom_confirm_2_Template, 1, 1, \"custom-confirm\", 4);\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.responseSer.itemsHasSelected(ctx_r1.group.id));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.showTip);\n  }\n}\n\nexport class SelectItemsComponent {\n  constructor(examSer, responseSer, utilSer, formSer, translate, jtcostmeSer, autoTestSer) {\n    this.examSer = examSer;\n    this.responseSer = responseSer;\n    this.utilSer = utilSer;\n    this.formSer = formSer;\n    this.translate = translate;\n    this.jtcostmeSer = jtcostmeSer;\n    this.autoTestSer = autoTestSer;\n    this.showTip = false;\n    this.tipCont = \"\";\n    this.itemsSelected = [];\n    this.subscriptions = [];\n  }\n\n  ngOnInit() {\n    this.subscriptions.push(this.examSer.examEvents.subscribe(event => {\n      if (event.type === Exam.Event.SelectItemsFromGroup) {\n        if (typeof event.data !== \"string\") {\n          this.group = event.data;\n          const skin = this.examSer.session.config.skin;\n          const template = this.group.selection.template;\n          this.templateUrl = \"/seat/skin/\" + skin + \"/template/\" + template + \".html\";\n          this.itemsSelected = []; // set cur group id for template in skin\n\n          this.jtcostmeSer.groupForSelectItems = this.group;\n          this.reloadIframe();\n        }\n      }\n    })); // 如果已经开启自动试考\n\n    if (this.examSer.autoTestOn) {\n      this.autoTest();\n    } else {\n      // 如果进入当前页才开始开启自动试考\n      this.subscriptions.push(this.examSer.examEventsFromManager.subscribe(result => {\n        if (result.type === Exam.EventManager.AutoTest) {\n          if (!this.examSer.autoTestOn) {\n            this.autoTest();\n          }\n        }\n      }));\n    }\n  }\n\n  set content(content) {\n    if (content) {\n      this.iframeDom = content.nativeElement;\n      this.examSer.loadIframe(this.iframeDom, this.templateUrl, true);\n    }\n  }\n\n  ngOnDestroy() {\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n\n    if (this.iframeDom) {\n      window.focus();\n    }\n  }\n\n  autoTest() {\n    if (this.autoTestSub) {\n      this.autoTestSub.unsubscribe();\n    }\n\n    this.autoTestSub = timer(1000, 3000).subscribe(t => {\n      this.autoTestSer.test({\n        pageType: \"select-items\",\n        extraData: {\n          manual: this.group.selection.manual,\n          optionsLength: this.group.items.length\n        }\n      });\n    });\n    this.subscriptions.push(this.autoTestSub);\n  } // 从group select template 到另一个 template，reload iframe[skin里不同group 的 template可能同名]\n\n\n  reloadIframe() {\n    if (this.iframeDom) {\n      this.iframeDom.src = \"\";\n      this.examSer.loadIframe(this.iframeDom, this.templateUrl, true);\n    }\n  }\n\n  btnConfirm() {\n    var _this = this;\n\n    return _asyncToGenerator(function* () {\n      if (_this.itemsSelected.length === _this.group.selection.manual) {\n        _this.tipCont = yield _this.getTipCont();\n        _this.showTip = true;\n      }\n    })();\n  }\n\n  getTipCont() {\n    var _this2 = this;\n\n    return _asyncToGenerator(function* () {\n      let tip = _this2.translate.instant(\"exam.groupSelection.selectedItems\");\n\n      for (let i = 0, len = _this2.group.items.length; i < len; i++) {\n        const item = _this2.group.items[i];\n\n        if (_this2.itemsSelected.indexOf(item[\"id\"]) > -1) {\n          yield function () {\n            var _ref = _asyncToGenerator(function* (i) {\n              const index = _this2.getTitleIndex(i);\n\n              const tipContTrans = yield lastValueFrom(_this2.translate.get(\"exam.groupSelection.titleName\", {\n                titleNum: index\n              }));\n              tip += tipContTrans + \"、\";\n            });\n\n            return function (_x) {\n              return _ref.apply(this, arguments);\n            };\n          }()(i);\n        }\n      }\n\n      tip = tip.substr(0, tip.length - 1);\n      tip += \"<div class='font-min mt-3 text-center'><label><input type='checkbox' id='selection-check-confirm' class='mr-1 align-middle'>\" + _this2.translate.instant(\"exam.groupSelection.checkConfirm\") + \"</label></div>\";\n      return tip;\n    })();\n  }\n\n  getTitleIndex(i) {\n    const chineseLan = [\"zh\", \"zh-hk\"];\n    return chineseLan.indexOf(this.utilSer.langSet) > -1 ? this.utilSer.numberConvertToUppercase(i + 1) : i + 1;\n  }\n\n  modalConfirm() {\n    // 检查是否勾选确认\n    const btnCheckConfirm = document.getElementById(\"selection-check-confirm\");\n\n    if (!btnCheckConfirm.checked) {\n      return;\n    }\n\n    this.showTip = false;\n    const group = this.group;\n    this.examSer.setItemSelected(group, this.itemsSelected);\n  }\n\n  modalCancel() {\n    this.showTip = false;\n  }\n\n}\n\nSelectItemsComponent.ɵfac = function SelectItemsComponent_Factory(t) {\n  return new (t || SelectItemsComponent)(i0.ɵɵdirectiveInject(i1.ExamService), i0.ɵɵdirectiveInject(i2.ResponseService), i0.ɵɵdirectiveInject(i3.UtilService), i0.ɵɵdirectiveInject(i4.FormService), i0.ɵɵdirectiveInject(i5.TranslateService), i0.ɵɵdirectiveInject(i6.JTCustomService), i0.ɵɵdirectiveInject(i7.AutoTestService));\n};\n\nSelectItemsComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: SelectItemsComponent,\n  selectors: [[\"app-select-items\"]],\n  viewQuery: function SelectItemsComponent_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(_c0, 5);\n    }\n\n    if (rf & 2) {\n      let _t;\n\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.content = _t.first);\n    }\n  },\n  decls: 2,\n  vars: 2,\n  consts: [[4, \"ngIf\"], [1, \"template-iframe\"], [\"templateIframe\", \"\"], [\"class\", \"select-items\", 4, \"ngIf\"], [\"class\", \"confirmSelectionModal\", 3, \"bodyText\", \"onConfirm\", \"onCancel\", 4, \"ngIf\"], [1, \"select-items\"], [1, \"sel-items-cont\"], [1, \"select-tip\", \"p-3\", 3, \"innerHTML\"], [\"class\", \"sel-item-block selection-name-group\", 4, \"ngFor\", \"ngForOf\"], [1, \"btn-confirm\", \"text-center\"], [1, \"yk-btn-primary\", \"btn-confirm-selection\", 3, \"ngClass\", \"click\"], [1, \"sel-item-block\", \"selection-name-group\"], [3, \"item\", \"index\", \"group\", \"itemsSelected\"], [1, \"confirmSelectionModal\", 3, \"bodyText\", \"onConfirm\", \"onCancel\"]],\n  template: function SelectItemsComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵtemplate(0, SelectItemsComponent_ng_container_0_Template, 3, 0, \"ng-container\", 0);\n      i0.ɵɵtemplate(1, SelectItemsComponent_ng_container_1_Template, 3, 2, \"ng-container\", 0);\n    }\n\n    if (rf & 2) {\n      i0.ɵɵproperty(\"ngIf\", ctx.group == null ? null : ctx.group.selection == null ? null : ctx.group.selection.template);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", !(ctx.group == null ? null : ctx.group.selection == null ? null : ctx.group.selection.template));\n    }\n  },\n  dependencies: [i8.NgClass, i8.NgForOf, i8.NgIf, i9.CustomConfirmComponent, i10.ItemBlockComponent, i11.SafeHtmlPipe, i5.TranslatePipe],\n  styles: [\".select-items[_ngcontent-%COMP%]{position:absolute;width:100%;bottom:0;top:0;z-index:3}.select-items[_ngcontent-%COMP%]   .sel-items-cont[_ngcontent-%COMP%]{position:absolute;width:100%;bottom:50px;top:0;overflow:auto}.select-items[_ngcontent-%COMP%]   .btn-confirm[_ngcontent-%COMP%]{position:absolute;width:100%;bottom:10px}.select-items[_ngcontent-%COMP%]   .btn-confirm[_ngcontent-%COMP%] > button.disabled[_ngcontent-%COMP%]{background-color:#447dffb3;border:1px solid rgba(68,125,255,.7)}.select-items[_ngcontent-%COMP%]   .sel-item-block[_ngcontent-%COMP%]:last-of-type     .title{border-bottom:1px solid #ccc!important}iframe.template-iframe[_ngcontent-%COMP%]{position:absolute;inset:0;height:100%;width:100%;border:none}\"]\n});", "map": null, "metadata": {}, "sourceType": "module"}