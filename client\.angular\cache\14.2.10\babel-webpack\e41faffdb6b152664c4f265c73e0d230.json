{"ast": null, "code": "import { concatAll } from '../operators/concatAll';\nimport { popScheduler } from '../util/args';\nimport { from } from './from';\nexport function concat(...args) {\n  return concatAll()(from(args, popScheduler(args)));\n}", "map": {"version": 3, "names": ["concatAll", "popScheduler", "from", "concat", "args"], "sources": ["D:/work/joyserver/client/node_modules/rxjs/dist/esm/internal/observable/concat.js"], "sourcesContent": ["import { concatAll } from '../operators/concatAll';\nimport { popScheduler } from '../util/args';\nimport { from } from './from';\nexport function concat(...args) {\n    return concatAll()(from(args, popScheduler(args)));\n}\n"], "mappings": "AAAA,SAASA,SAAT,QAA0B,wBAA1B;AACA,SAASC,YAAT,QAA6B,cAA7B;AACA,SAASC,IAAT,QAAqB,QAArB;AACA,OAAO,SAASC,MAAT,CAAgB,GAAGC,IAAnB,EAAyB;EAC5B,OAAOJ,SAAS,GAAGE,IAAI,CAACE,IAAD,EAAOH,YAAY,CAACG,IAAD,CAAnB,CAAP,CAAhB;AACH"}, "metadata": {}, "sourceType": "module"}