{"ast": null, "code": "/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { UnitDependencies } from './dependenciesUnitClass.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createUnitFunction } from '../../factoriesAny.js';\nexport var unitDependencies = {\n  UnitDependencies,\n  typedDependencies,\n  createUnitFunction\n};", "map": {"version": 3, "names": ["UnitDependencies", "typedDependencies", "createUnitFunction", "unitDependencies"], "sources": ["D:/work/joyserver/client/node_modules/mathjs/lib/esm/entry/dependenciesAny/dependenciesUnitFunction.generated.js"], "sourcesContent": ["/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { UnitDependencies } from './dependenciesUnitClass.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createUnitFunction } from '../../factoriesAny.js';\nexport var unitDependencies = {\n  UnitDependencies,\n  typedDependencies,\n  createUnitFunction\n};"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,gBAAT,QAAiC,sCAAjC;AACA,SAASC,iBAAT,QAAkC,kCAAlC;AACA,SAASC,kBAAT,QAAmC,uBAAnC;AACA,OAAO,IAAIC,gBAAgB,GAAG;EAC5BH,gBAD4B;EAE5BC,iBAF4B;EAG5BC;AAH4B,CAAvB"}, "metadata": {}, "sourceType": "module"}