{"ast": null, "code": "export var detDocs = {\n  name: 'det',\n  category: 'Matrix',\n  syntax: ['det(x)'],\n  description: 'Calculate the determinant of a matrix',\n  examples: ['det([1, 2; 3, 4])', 'det([-2, 2, 3; -1, 1, 3; 2, 0, -1])'],\n  seealso: ['concat', 'diag', 'identity', 'inv', 'ones', 'range', 'size', 'squeeze', 'subset', 'trace', 'transpose', 'zeros']\n};", "map": {"version": 3, "names": ["detDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["D:/work/joyserver/client/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/matrix/det.js"], "sourcesContent": ["export var detDocs = {\n  name: 'det',\n  category: 'Matrix',\n  syntax: ['det(x)'],\n  description: 'Calculate the determinant of a matrix',\n  examples: ['det([1, 2; 3, 4])', 'det([-2, 2, 3; -1, 1, 3; 2, 0, -1])'],\n  seealso: ['concat', 'diag', 'identity', 'inv', 'ones', 'range', 'size', 'squeeze', 'subset', 'trace', 'transpose', 'zeros']\n};"], "mappings": "AAAA,OAAO,IAAIA,OAAO,GAAG;EACnBC,IAAI,EAAE,KADa;EAEnBC,QAAQ,EAAE,QAFS;EAGnBC,MAAM,EAAE,CAAC,QAAD,CAHW;EAInBC,WAAW,EAAE,uCAJM;EAKnBC,QAAQ,EAAE,CAAC,mBAAD,EAAsB,qCAAtB,CALS;EAMnBC,OAAO,EAAE,CAAC,QAAD,EAAW,MAAX,EAAmB,UAAnB,EAA+B,KAA/B,EAAsC,MAAtC,EAA8C,OAA9C,EAAuD,MAAvD,EAA+D,SAA/D,EAA0E,QAA1E,EAAoF,OAApF,EAA6F,WAA7F,EAA0G,OAA1G;AANU,CAAd"}, "metadata": {}, "sourceType": "module"}