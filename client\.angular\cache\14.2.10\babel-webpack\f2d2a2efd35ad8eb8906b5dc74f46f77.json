{"ast": null, "code": "import { Subscriber } from '../Subscriber';\nexport function dematerialize() {\n  return function dematerializeOperatorFunction(source) {\n    return source.lift(new DeMaterializeOperator());\n  };\n}\n\nclass DeMaterializeOperator {\n  call(subscriber, source) {\n    return source.subscribe(new DeMaterializeSubscriber(subscriber));\n  }\n\n}\n\nclass DeMaterializeSubscriber extends Subscriber {\n  constructor(destination) {\n    super(destination);\n  }\n\n  _next(value) {\n    value.observe(this.destination);\n  }\n\n}", "map": {"version": 3, "names": ["Subscriber", "dematerialize", "dematerializeOperatorFunction", "source", "lift", "DeMaterializeOperator", "call", "subscriber", "subscribe", "DeMaterializeSubscriber", "constructor", "destination", "_next", "value", "observe"], "sources": ["D:/work/joyserver/client/node_modules/@angular-slider/ngx-slider/node_modules/rxjs/_esm2015/internal/operators/dematerialize.js"], "sourcesContent": ["import { Subscriber } from '../Subscriber';\nexport function dematerialize() {\n    return function dematerializeOperatorFunction(source) {\n        return source.lift(new DeMaterializeOperator());\n    };\n}\nclass DeMaterializeOperator {\n    call(subscriber, source) {\n        return source.subscribe(new DeMaterializeSubscriber(subscriber));\n    }\n}\nclass DeMaterializeSubscriber extends Subscriber {\n    constructor(destination) {\n        super(destination);\n    }\n    _next(value) {\n        value.observe(this.destination);\n    }\n}\n"], "mappings": "AAAA,SAASA,UAAT,QAA2B,eAA3B;AACA,OAAO,SAASC,aAAT,GAAyB;EAC5B,OAAO,SAASC,6BAAT,CAAuCC,MAAvC,EAA+C;IAClD,OAAOA,MAAM,CAACC,IAAP,CAAY,IAAIC,qBAAJ,EAAZ,CAAP;EACH,CAFD;AAGH;;AACD,MAAMA,qBAAN,CAA4B;EACxBC,IAAI,CAACC,UAAD,EAAaJ,MAAb,EAAqB;IACrB,OAAOA,MAAM,CAACK,SAAP,CAAiB,IAAIC,uBAAJ,CAA4BF,UAA5B,CAAjB,CAAP;EACH;;AAHuB;;AAK5B,MAAME,uBAAN,SAAsCT,UAAtC,CAAiD;EAC7CU,WAAW,CAACC,WAAD,EAAc;IACrB,MAAMA,WAAN;EACH;;EACDC,KAAK,CAACC,KAAD,EAAQ;IACTA,KAAK,CAACC,OAAN,CAAc,KAAKH,WAAnB;EACH;;AAN4C"}, "metadata": {}, "sourceType": "module"}