{"ast": null, "code": "/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { NodeDependencies } from './dependenciesNode.generated.js';\nimport { createObjectNode } from '../../factoriesAny.js';\nexport var ObjectNodeDependencies = {\n  NodeDependencies,\n  createObjectNode\n};", "map": {"version": 3, "names": ["NodeDependencies", "createObjectNode", "ObjectNodeDependencies"], "sources": ["D:/work/joyserver/client/node_modules/mathjs/lib/esm/entry/dependenciesAny/dependenciesObjectNode.generated.js"], "sourcesContent": ["/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { NodeDependencies } from './dependenciesNode.generated.js';\nimport { createObjectNode } from '../../factoriesAny.js';\nexport var ObjectNodeDependencies = {\n  NodeDependencies,\n  createObjectNode\n};"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,gBAAT,QAAiC,iCAAjC;AACA,SAASC,gBAAT,QAAiC,uBAAjC;AACA,OAAO,IAAIC,sBAAsB,GAAG;EAClCF,gBADkC;EAElCC;AAFkC,CAA7B"}, "metadata": {}, "sourceType": "module"}