{"ast": null, "code": "import { createFilter } from '../../function/matrix/filter.js';\nimport { factory } from '../../utils/factory.js';\nimport { isFunctionAssignmentNode, isSymbolNode } from '../../utils/is.js';\nimport { compileInlineExpression } from './utils/compileInlineExpression.js';\nimport { createTransformCallback } from './utils/transformCallback.js';\nvar name = 'filter';\nvar dependencies = ['typed'];\nexport var createFilterTransform = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed\n  } = _ref;\n  /**\n   * Attach a transform function to math.filter\n   * Adds a property transform containing the transform function.\n   *\n   * This transform adds support for equations as test function for math.filter,\n   * so you can do something like 'filter([3, -2, 5], x > 0)'.\n   */\n\n  function filterTransform(args, math, scope) {\n    var filter = createFilter({\n      typed\n    });\n    var transformCallback = createTransformCallback({\n      typed\n    });\n\n    if (args.length === 0) {\n      return filter();\n    }\n\n    var x = args[0];\n\n    if (args.length === 1) {\n      return filter(x);\n    }\n\n    var N = args.length - 1;\n    var callback = args[N];\n\n    if (x) {\n      x = _compileAndEvaluate(x, scope);\n    }\n\n    if (callback) {\n      if (isSymbolNode(callback) || isFunctionAssignmentNode(callback)) {\n        // a function pointer, like filter([3, -2, 5], myTestFunction)\n        callback = _compileAndEvaluate(callback, scope);\n      } else {\n        // an expression like filter([3, -2, 5], x > 0)\n        callback = compileInlineExpression(callback, math, scope);\n      }\n    }\n\n    return filter(x, transformCallback(callback, N));\n  }\n\n  filterTransform.rawArgs = true;\n\n  function _compileAndEvaluate(arg, scope) {\n    return arg.compile().evaluate(scope);\n  }\n\n  return filterTransform;\n}, {\n  isTransformFunction: true\n});", "map": {"version": 3, "names": ["createFilter", "factory", "isFunctionAssignmentNode", "isSymbolNode", "compileInlineExpression", "createTransformCallback", "name", "dependencies", "createFilterTransform", "_ref", "typed", "filterTransform", "args", "math", "scope", "filter", "transformCallback", "length", "x", "N", "callback", "_compileAndEvaluate", "rawArgs", "arg", "compile", "evaluate", "isTransformFunction"], "sources": ["D:/work/joyserver/client/node_modules/mathjs/lib/esm/expression/transform/filter.transform.js"], "sourcesContent": ["import { createFilter } from '../../function/matrix/filter.js';\nimport { factory } from '../../utils/factory.js';\nimport { isFunctionAssignmentNode, isSymbolNode } from '../../utils/is.js';\nimport { compileInlineExpression } from './utils/compileInlineExpression.js';\nimport { createTransformCallback } from './utils/transformCallback.js';\nvar name = 'filter';\nvar dependencies = ['typed'];\nexport var createFilterTransform = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed\n  } = _ref;\n  /**\n   * Attach a transform function to math.filter\n   * Adds a property transform containing the transform function.\n   *\n   * This transform adds support for equations as test function for math.filter,\n   * so you can do something like 'filter([3, -2, 5], x > 0)'.\n   */\n  function filterTransform(args, math, scope) {\n    var filter = createFilter({\n      typed\n    });\n    var transformCallback = createTransformCallback({\n      typed\n    });\n    if (args.length === 0) {\n      return filter();\n    }\n    var x = args[0];\n    if (args.length === 1) {\n      return filter(x);\n    }\n    var N = args.length - 1;\n    var callback = args[N];\n    if (x) {\n      x = _compileAndEvaluate(x, scope);\n    }\n    if (callback) {\n      if (isSymbolNode(callback) || isFunctionAssignmentNode(callback)) {\n        // a function pointer, like filter([3, -2, 5], myTestFunction)\n        callback = _compileAndEvaluate(callback, scope);\n      } else {\n        // an expression like filter([3, -2, 5], x > 0)\n        callback = compileInlineExpression(callback, math, scope);\n      }\n    }\n    return filter(x, transformCallback(callback, N));\n  }\n  filterTransform.rawArgs = true;\n  function _compileAndEvaluate(arg, scope) {\n    return arg.compile().evaluate(scope);\n  }\n  return filterTransform;\n}, {\n  isTransformFunction: true\n});"], "mappings": "AAAA,SAASA,YAAT,QAA6B,iCAA7B;AACA,SAASC,OAAT,QAAwB,wBAAxB;AACA,SAASC,wBAAT,EAAmCC,YAAnC,QAAuD,mBAAvD;AACA,SAASC,uBAAT,QAAwC,oCAAxC;AACA,SAASC,uBAAT,QAAwC,8BAAxC;AACA,IAAIC,IAAI,GAAG,QAAX;AACA,IAAIC,YAAY,GAAG,CAAC,OAAD,CAAnB;AACA,OAAO,IAAIC,qBAAqB,GAAG,eAAeP,OAAO,CAACK,IAAD,EAAOC,YAAP,EAAqBE,IAAI,IAAI;EACpF,IAAI;IACFC;EADE,IAEAD,IAFJ;EAGA;AACF;AACA;AACA;AACA;AACA;AACA;;EACE,SAASE,eAAT,CAAyBC,IAAzB,EAA+BC,IAA/B,EAAqCC,KAArC,EAA4C;IAC1C,IAAIC,MAAM,GAAGf,YAAY,CAAC;MACxBU;IADwB,CAAD,CAAzB;IAGA,IAAIM,iBAAiB,GAAGX,uBAAuB,CAAC;MAC9CK;IAD8C,CAAD,CAA/C;;IAGA,IAAIE,IAAI,CAACK,MAAL,KAAgB,CAApB,EAAuB;MACrB,OAAOF,MAAM,EAAb;IACD;;IACD,IAAIG,CAAC,GAAGN,IAAI,CAAC,CAAD,CAAZ;;IACA,IAAIA,IAAI,CAACK,MAAL,KAAgB,CAApB,EAAuB;MACrB,OAAOF,MAAM,CAACG,CAAD,CAAb;IACD;;IACD,IAAIC,CAAC,GAAGP,IAAI,CAACK,MAAL,GAAc,CAAtB;IACA,IAAIG,QAAQ,GAAGR,IAAI,CAACO,CAAD,CAAnB;;IACA,IAAID,CAAJ,EAAO;MACLA,CAAC,GAAGG,mBAAmB,CAACH,CAAD,EAAIJ,KAAJ,CAAvB;IACD;;IACD,IAAIM,QAAJ,EAAc;MACZ,IAAIjB,YAAY,CAACiB,QAAD,CAAZ,IAA0BlB,wBAAwB,CAACkB,QAAD,CAAtD,EAAkE;QAChE;QACAA,QAAQ,GAAGC,mBAAmB,CAACD,QAAD,EAAWN,KAAX,CAA9B;MACD,CAHD,MAGO;QACL;QACAM,QAAQ,GAAGhB,uBAAuB,CAACgB,QAAD,EAAWP,IAAX,EAAiBC,KAAjB,CAAlC;MACD;IACF;;IACD,OAAOC,MAAM,CAACG,CAAD,EAAIF,iBAAiB,CAACI,QAAD,EAAWD,CAAX,CAArB,CAAb;EACD;;EACDR,eAAe,CAACW,OAAhB,GAA0B,IAA1B;;EACA,SAASD,mBAAT,CAA6BE,GAA7B,EAAkCT,KAAlC,EAAyC;IACvC,OAAOS,GAAG,CAACC,OAAJ,GAAcC,QAAd,CAAuBX,KAAvB,CAAP;EACD;;EACD,OAAOH,eAAP;AACD,CA9CwD,EA8CtD;EACDe,mBAAmB,EAAE;AADpB,CA9CsD,CAAlD"}, "metadata": {}, "sourceType": "module"}