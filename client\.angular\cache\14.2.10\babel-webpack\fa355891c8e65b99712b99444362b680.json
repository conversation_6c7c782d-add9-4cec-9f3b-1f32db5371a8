{"ast": null, "code": "/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { NodeDependencies } from './dependenciesNode.generated.js';\nimport { SymbolNodeDependencies } from './dependenciesSymbolNode.generated.js';\nimport { createFunctionNode } from '../../factoriesAny.js';\nexport var FunctionNodeDependencies = {\n  NodeDependencies,\n  SymbolNodeDependencies,\n  createFunctionNode\n};", "map": {"version": 3, "names": ["NodeDependencies", "SymbolNodeDependencies", "createFunctionNode", "FunctionNodeDependencies"], "sources": ["D:/work/joyserver/client/node_modules/mathjs/lib/esm/entry/dependenciesAny/dependenciesFunctionNode.generated.js"], "sourcesContent": ["/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { NodeDependencies } from './dependenciesNode.generated.js';\nimport { SymbolNodeDependencies } from './dependenciesSymbolNode.generated.js';\nimport { createFunctionNode } from '../../factoriesAny.js';\nexport var FunctionNodeDependencies = {\n  NodeDependencies,\n  SymbolNodeDependencies,\n  createFunctionNode\n};"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,gBAAT,QAAiC,iCAAjC;AACA,SAASC,sBAAT,QAAuC,uCAAvC;AACA,SAASC,kBAAT,QAAmC,uBAAnC;AACA,OAAO,IAAIC,wBAAwB,GAAG;EACpCH,gBADoC;EAEpCC,sBAFoC;EAGpCC;AAHoC,CAA/B"}, "metadata": {}, "sourceType": "module"}