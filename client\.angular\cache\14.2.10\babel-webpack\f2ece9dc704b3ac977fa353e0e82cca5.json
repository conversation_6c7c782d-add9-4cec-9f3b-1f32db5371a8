{"ast": null, "code": "/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { BigNumberDependencies } from './dependenciesBigNumberClass.generated.js';\nimport { UnitDependencies } from './dependenciesUnitClass.generated.js';\nimport { createMagneticConstant } from '../../factoriesAny.js';\nexport var magneticConstantDependencies = {\n  BigNumberDependencies,\n  UnitDependencies,\n  createMagneticConstant\n};", "map": {"version": 3, "names": ["BigNumberDependencies", "UnitDependencies", "createMagneticConstant", "magneticConstantDependencies"], "sources": ["D:/work/joyserver/client/node_modules/mathjs/lib/esm/entry/dependenciesAny/dependenciesMagneticConstant.generated.js"], "sourcesContent": ["/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { BigNumberDependencies } from './dependenciesBigNumberClass.generated.js';\nimport { UnitDependencies } from './dependenciesUnitClass.generated.js';\nimport { createMagneticConstant } from '../../factoriesAny.js';\nexport var magneticConstantDependencies = {\n  BigNumberDependencies,\n  UnitDependencies,\n  createMagneticConstant\n};"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,qBAAT,QAAsC,2CAAtC;AACA,SAASC,gBAAT,QAAiC,sCAAjC;AACA,SAASC,sBAAT,QAAuC,uBAAvC;AACA,OAAO,IAAIC,4BAA4B,GAAG;EACxCH,qBADwC;EAExCC,gBAFwC;EAGxCC;AAHwC,CAAnC"}, "metadata": {}, "sourceType": "module"}