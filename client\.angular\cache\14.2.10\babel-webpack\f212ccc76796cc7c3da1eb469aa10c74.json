{"ast": null, "code": "ace.define(\"ace/mode/doc_comment_highlight_rules\", [\"require\", \"exports\", \"module\", \"ace/lib/oop\", \"ace/mode/text_highlight_rules\"], function (acequire, exports, module) {\n  \"use strict\";\n\n  var oop = acequire(\"../lib/oop\");\n  var TextHighlightRules = acequire(\"./text_highlight_rules\").TextHighlightRules;\n\n  var DocCommentHighlightRules = function () {\n    this.$rules = {\n      \"start\": [{\n        token: \"comment.doc.tag\",\n        regex: \"@[\\\\w\\\\d_]+\" // TODO: fix email addresses\n\n      }, DocCommentHighlightRules.getTagRule(), {\n        defaultToken: \"comment.doc\",\n        caseInsensitive: true\n      }]\n    };\n  };\n\n  oop.inherits(DocCommentHighlightRules, TextHighlightRules);\n\n  DocCommentHighlightRules.getTagRule = function (start) {\n    return {\n      token: \"comment.doc.tag.storage.type\",\n      regex: \"\\\\b(?:TODO|FIXME|XXX|HACK)\\\\b\"\n    };\n  };\n\n  DocCommentHighlightRules.getStartRule = function (start) {\n    return {\n      token: \"comment.doc\",\n      // doc comment\n      regex: \"\\\\/\\\\*(?=\\\\*)\",\n      next: start\n    };\n  };\n\n  DocCommentHighlightRules.getEndRule = function (start) {\n    return {\n      token: \"comment.doc\",\n      // closing comment\n      regex: \"\\\\*\\\\/\",\n      next: start\n    };\n  };\n\n  exports.DocCommentHighlightRules = DocCommentHighlightRules;\n});\nace.define(\"ace/mode/swift_highlight_rules\", [\"require\", \"exports\", \"module\", \"ace/lib/oop\", \"ace/lib/lang\", \"ace/mode/doc_comment_highlight_rules\", \"ace/mode/text_highlight_rules\"], function (acequire, exports, module) {\n  \"use strict\";\n\n  var oop = acequire(\"../lib/oop\");\n  var lang = acequire(\"../lib/lang\");\n  var DocCommentHighlightRules = acequire(\"./doc_comment_highlight_rules\").DocCommentHighlightRules;\n  var TextHighlightRules = acequire(\"./text_highlight_rules\").TextHighlightRules;\n\n  var SwiftHighlightRules = function () {\n    var keywordMapper = this.createKeywordMapper({\n      \"variable.language\": \"\",\n      \"keyword\": \"__COLUMN__|__FILE__|__FUNCTION__|__LINE__\" + \"|as|associativity|break|case|class|continue|default|deinit|didSet\" + \"|do|dynamicType|else|enum|extension|fallthrough|for|func|get|if|import\" + \"|in|infix|init|inout|is|left|let|let|mutating|new|none|nonmutating\" + \"|operator|override|postfix|precedence|prefix|protocol|return|right\" + \"|safe|Self|self|set|struct|subscript|switch|Type|typealias\" + \"|unowned|unsafe|var|weak|where|while|willSet\" + \"|convenience|dynamic|final|infix|lazy|mutating|nonmutating|optional|override|postfix\" + \"|prefix|acequired|static|guard|defer\",\n      \"storage.type\": \"bool|double|Double\" + \"|extension|float|Float|int|Int|private|public|string|String\",\n      \"constant.language\": \"false|Infinity|NaN|nil|no|null|null|off|on|super|this|true|undefined|yes\",\n      \"support.function\": \"\"\n    }, \"identifier\");\n\n    function string(start, options) {\n      var nestable = options.nestable || options.interpolation;\n      var interpStart = options.interpolation && options.interpolation.nextState || \"start\";\n      var mainRule = {\n        regex: start + (options.multiline ? \"\" : \"(?=.)\"),\n        token: \"string.start\"\n      };\n      var nextState = [options.escape && {\n        regex: options.escape,\n        token: \"character.escape\"\n      }, options.interpolation && {\n        token: \"paren.quasi.start\",\n        regex: lang.escapeRegExp(options.interpolation.lead + options.interpolation.open),\n        push: interpStart\n      }, options.error && {\n        regex: options.error,\n        token: \"error.invalid\"\n      }, {\n        regex: start + (options.multiline ? \"\" : \"|$\"),\n        token: \"string.end\",\n        next: nestable ? \"pop\" : \"start\"\n      }, {\n        defaultToken: \"string\"\n      }].filter(Boolean);\n      if (nestable) mainRule.push = nextState;else mainRule.next = nextState;\n      if (!options.interpolation) return mainRule;\n      var open = options.interpolation.open;\n      var close = options.interpolation.close;\n      var counter = {\n        regex: \"[\" + lang.escapeRegExp(open + close) + \"]\",\n        onMatch: function (val, state, stack) {\n          this.next = val == open ? this.nextState : \"\";\n\n          if (val == open && stack.length) {\n            stack.unshift(\"start\", state);\n            return \"paren\";\n          }\n\n          if (val == close && stack.length) {\n            stack.shift();\n            this.next = stack.shift();\n            if (this.next.indexOf(\"string\") != -1) return \"paren.quasi.end\";\n          }\n\n          return val == open ? \"paren.lparen\" : \"paren.rparen\";\n        },\n        nextState: interpStart\n      };\n      return [counter, mainRule];\n    }\n\n    function comments() {\n      return [{\n        token: \"comment\",\n        regex: \"\\\\/\\\\/(?=.)\",\n        next: [DocCommentHighlightRules.getTagRule(), {\n          token: \"comment\",\n          regex: \"$|^\",\n          next: \"start\"\n        }, {\n          defaultToken: \"comment\",\n          caseInsensitive: true\n        }]\n      }, DocCommentHighlightRules.getStartRule(\"doc-start\"), {\n        token: \"comment.start\",\n        regex: /\\/\\*/,\n        stateName: \"nested_comment\",\n        push: [DocCommentHighlightRules.getTagRule(), {\n          token: \"comment.start\",\n          regex: /\\/\\*/,\n          push: \"nested_comment\"\n        }, {\n          token: \"comment.end\",\n          regex: \"\\\\*\\\\/\",\n          next: \"pop\"\n        }, {\n          defaultToken: \"comment\",\n          caseInsensitive: true\n        }]\n      }];\n    }\n\n    this.$rules = {\n      start: [string('\"', {\n        escape: /\\\\(?:[0\\\\tnr\"']|u{[a-fA-F1-9]{0,8}})/,\n        interpolation: {\n          lead: \"\\\\\",\n          open: \"(\",\n          close: \")\"\n        },\n        error: /\\\\./,\n        multiline: false\n      }), comments({\n        type: \"c\",\n        nestable: true\n      }), {\n        regex: /@[a-zA-Z_$][a-zA-Z_$\\d\\u0080-\\ufffe]*/,\n        token: \"variable.parameter\"\n      }, {\n        regex: /[a-zA-Z_$][a-zA-Z_$\\d\\u0080-\\ufffe]*/,\n        token: keywordMapper\n      }, {\n        token: \"constant.numeric\",\n        regex: /[+-]?(?:0(?:b[01]+|o[0-7]+|x[\\da-fA-F])|\\d+(?:(?:\\.\\d*)?(?:[PpEe][+-]?\\d+)?)\\b)/\n      }, {\n        token: \"keyword.operator\",\n        regex: /--|\\+\\+|===|==|=|!=|!==|<=|>=|<<=|>>=|>>>=|<>|<|>|!|&&|\\|\\||\\?:|[!$%&*+\\-~\\/^]=?/,\n        next: \"start\"\n      }, {\n        token: \"punctuation.operator\",\n        regex: /[?:,;.]/,\n        next: \"start\"\n      }, {\n        token: \"paren.lparen\",\n        regex: /[\\[({]/,\n        next: \"start\"\n      }, {\n        token: \"paren.rparen\",\n        regex: /[\\])}]/\n      }]\n    };\n    this.embedRules(DocCommentHighlightRules, \"doc-\", [DocCommentHighlightRules.getEndRule(\"start\")]);\n    this.normalizeRules();\n  };\n\n  oop.inherits(SwiftHighlightRules, TextHighlightRules);\n  exports.HighlightRules = SwiftHighlightRules;\n});\nace.define(\"ace/mode/folding/cstyle\", [\"require\", \"exports\", \"module\", \"ace/lib/oop\", \"ace/range\", \"ace/mode/folding/fold_mode\"], function (acequire, exports, module) {\n  \"use strict\";\n\n  var oop = acequire(\"../../lib/oop\");\n  var Range = acequire(\"../../range\").Range;\n  var BaseFoldMode = acequire(\"./fold_mode\").FoldMode;\n\n  var FoldMode = exports.FoldMode = function (commentRegex) {\n    if (commentRegex) {\n      this.foldingStartMarker = new RegExp(this.foldingStartMarker.source.replace(/\\|[^|]*?$/, \"|\" + commentRegex.start));\n      this.foldingStopMarker = new RegExp(this.foldingStopMarker.source.replace(/\\|[^|]*?$/, \"|\" + commentRegex.end));\n    }\n  };\n\n  oop.inherits(FoldMode, BaseFoldMode);\n  (function () {\n    this.foldingStartMarker = /([\\{\\[\\(])[^\\}\\]\\)]*$|^\\s*(\\/\\*)/;\n    this.foldingStopMarker = /^[^\\[\\{\\(]*([\\}\\]\\)])|^[\\s\\*]*(\\*\\/)/;\n    this.singleLineBlockCommentRe = /^\\s*(\\/\\*).*\\*\\/\\s*$/;\n    this.tripleStarBlockCommentRe = /^\\s*(\\/\\*\\*\\*).*\\*\\/\\s*$/;\n    this.startRegionRe = /^\\s*(\\/\\*|\\/\\/)#?region\\b/;\n    this._getFoldWidgetBase = this.getFoldWidget;\n\n    this.getFoldWidget = function (session, foldStyle, row) {\n      var line = session.getLine(row);\n\n      if (this.singleLineBlockCommentRe.test(line)) {\n        if (!this.startRegionRe.test(line) && !this.tripleStarBlockCommentRe.test(line)) return \"\";\n      }\n\n      var fw = this._getFoldWidgetBase(session, foldStyle, row);\n\n      if (!fw && this.startRegionRe.test(line)) return \"start\"; // lineCommentRegionStart\n\n      return fw;\n    };\n\n    this.getFoldWidgetRange = function (session, foldStyle, row, forceMultiline) {\n      var line = session.getLine(row);\n      if (this.startRegionRe.test(line)) return this.getCommentRegionBlock(session, line, row);\n      var match = line.match(this.foldingStartMarker);\n\n      if (match) {\n        var i = match.index;\n        if (match[1]) return this.openingBracketBlock(session, match[1], row, i);\n        var range = session.getCommentFoldRange(row, i + match[0].length, 1);\n\n        if (range && !range.isMultiLine()) {\n          if (forceMultiline) {\n            range = this.getSectionRange(session, row);\n          } else if (foldStyle != \"all\") range = null;\n        }\n\n        return range;\n      }\n\n      if (foldStyle === \"markbegin\") return;\n      var match = line.match(this.foldingStopMarker);\n\n      if (match) {\n        var i = match.index + match[0].length;\n        if (match[1]) return this.closingBracketBlock(session, match[1], row, i);\n        return session.getCommentFoldRange(row, i, -1);\n      }\n    };\n\n    this.getSectionRange = function (session, row) {\n      var line = session.getLine(row);\n      var startIndent = line.search(/\\S/);\n      var startRow = row;\n      var startColumn = line.length;\n      row = row + 1;\n      var endRow = row;\n      var maxRow = session.getLength();\n\n      while (++row < maxRow) {\n        line = session.getLine(row);\n        var indent = line.search(/\\S/);\n        if (indent === -1) continue;\n        if (startIndent > indent) break;\n        var subRange = this.getFoldWidgetRange(session, \"all\", row);\n\n        if (subRange) {\n          if (subRange.start.row <= startRow) {\n            break;\n          } else if (subRange.isMultiLine()) {\n            row = subRange.end.row;\n          } else if (startIndent == indent) {\n            break;\n          }\n        }\n\n        endRow = row;\n      }\n\n      return new Range(startRow, startColumn, endRow, session.getLine(endRow).length);\n    };\n\n    this.getCommentRegionBlock = function (session, line, row) {\n      var startColumn = line.search(/\\s*$/);\n      var maxRow = session.getLength();\n      var startRow = row;\n      var re = /^\\s*(?:\\/\\*|\\/\\/|--)#?(end)?region\\b/;\n      var depth = 1;\n\n      while (++row < maxRow) {\n        line = session.getLine(row);\n        var m = re.exec(line);\n        if (!m) continue;\n        if (m[1]) depth--;else depth++;\n        if (!depth) break;\n      }\n\n      var endRow = row;\n\n      if (endRow > startRow) {\n        return new Range(startRow, startColumn, endRow, line.length);\n      }\n    };\n  }).call(FoldMode.prototype);\n});\nace.define(\"ace/mode/swift\", [\"require\", \"exports\", \"module\", \"ace/lib/oop\", \"ace/mode/text\", \"ace/mode/swift_highlight_rules\", \"ace/mode/behaviour/cstyle\", \"ace/mode/folding/cstyle\"], function (acequire, exports, module) {\n  \"use strict\";\n\n  var oop = acequire(\"../lib/oop\");\n  var TextMode = acequire(\"./text\").Mode;\n  var HighlightRules = acequire(\"./swift_highlight_rules\").HighlightRules;\n  var CstyleBehaviour = acequire(\"./behaviour/cstyle\").CstyleBehaviour;\n  var FoldMode = acequire(\"./folding/cstyle\").FoldMode;\n\n  var Mode = function () {\n    this.HighlightRules = HighlightRules;\n    this.foldingRules = new FoldMode();\n    this.$behaviour = new CstyleBehaviour();\n    this.$behaviour = this.$defaultBehaviour;\n  };\n\n  oop.inherits(Mode, TextMode);\n  (function () {\n    this.lineCommentStart = \"//\";\n    this.blockComment = {\n      start: \"/*\",\n      end: \"*/\",\n      nestable: true\n    };\n    this.$id = \"ace/mode/swift\";\n  }).call(Mode.prototype);\n  exports.Mode = Mode;\n});", "map": {"version": 3, "names": ["ace", "define", "acequire", "exports", "module", "oop", "TextHighlightRules", "DocCommentHighlightRules", "$rules", "token", "regex", "getTagRule", "defaultToken", "caseInsensitive", "inherits", "start", "getStartRule", "next", "getEndRule", "lang", "SwiftHighlightRules", "keywordMapper", "createKeywordMapper", "string", "options", "nestable", "interpolation", "interpStart", "nextState", "mainRule", "multiline", "escape", "escapeRegExp", "lead", "open", "push", "error", "filter", "Boolean", "close", "counter", "onMatch", "val", "state", "stack", "length", "unshift", "shift", "indexOf", "comments", "stateName", "type", "embedRules", "normalizeRules", "HighlightRules", "Range", "BaseFoldMode", "FoldMode", "commentRegex", "foldingStartMarker", "RegExp", "source", "replace", "foldingStopMarker", "end", "singleLineBlockCommentRe", "tripleStarBlockCommentRe", "startRegionRe", "_getFoldWidgetBase", "getFoldWidget", "session", "foldStyle", "row", "line", "getLine", "test", "fw", "getFoldWidgetRange", "forceMultiline", "getCommentRegionBlock", "match", "i", "index", "openingBracketBlock", "range", "getCommentFoldRange", "isMultiLine", "getSectionRange", "closingBracketBlock", "startIndent", "search", "startRow", "startColumn", "endRow", "maxRow", "<PERSON><PERSON><PERSON><PERSON>", "indent", "subRange", "re", "depth", "m", "exec", "call", "prototype", "TextMode", "Mode", "CstyleBehaviour", "foldingRules", "$behaviour", "$defaultBehaviour", "lineCommentStart", "blockComment", "$id"], "sources": ["D:/work/joyserver/client/node_modules/brace/mode/swift.js"], "sourcesContent": ["ace.define(\"ace/mode/doc_comment_highlight_rules\",[\"require\",\"exports\",\"module\",\"ace/lib/oop\",\"ace/mode/text_highlight_rules\"], function(acequire, exports, module) {\n\"use strict\";\n\nvar oop = acequire(\"../lib/oop\");\nvar TextHighlightRules = acequire(\"./text_highlight_rules\").TextHighlightRules;\n\nvar DocCommentHighlightRules = function() {\n    this.$rules = {\n        \"start\" : [ {\n            token : \"comment.doc.tag\",\n            regex : \"@[\\\\w\\\\d_]+\" // TODO: fix email addresses\n        }, \n        DocCommentHighlightRules.getTagRule(),\n        {\n            defaultToken : \"comment.doc\",\n            caseInsensitive: true\n        }]\n    };\n};\n\noop.inherits(DocCommentHighlightRules, TextHighlightRules);\n\nDocCommentHighlightRules.getTagRule = function(start) {\n    return {\n        token : \"comment.doc.tag.storage.type\",\n        regex : \"\\\\b(?:TODO|FIXME|XXX|HACK)\\\\b\"\n    };\n};\n\nDocCommentHighlightRules.getStartRule = function(start) {\n    return {\n        token : \"comment.doc\", // doc comment\n        regex : \"\\\\/\\\\*(?=\\\\*)\",\n        next  : start\n    };\n};\n\nDocCommentHighlightRules.getEndRule = function (start) {\n    return {\n        token : \"comment.doc\", // closing comment\n        regex : \"\\\\*\\\\/\",\n        next  : start\n    };\n};\n\n\nexports.DocCommentHighlightRules = DocCommentHighlightRules;\n\n});\n\nace.define(\"ace/mode/swift_highlight_rules\",[\"require\",\"exports\",\"module\",\"ace/lib/oop\",\"ace/lib/lang\",\"ace/mode/doc_comment_highlight_rules\",\"ace/mode/text_highlight_rules\"], function(acequire, exports, module) {\n\"use strict\";\n\nvar oop = acequire(\"../lib/oop\");\nvar lang = acequire(\"../lib/lang\");\nvar DocCommentHighlightRules = acequire(\"./doc_comment_highlight_rules\").DocCommentHighlightRules;\nvar TextHighlightRules = acequire(\"./text_highlight_rules\").TextHighlightRules;\n\nvar SwiftHighlightRules = function() {\n   var keywordMapper = this.createKeywordMapper({\n        \"variable.language\": \"\",\n        \"keyword\": \"__COLUMN__|__FILE__|__FUNCTION__|__LINE__\"\n            + \"|as|associativity|break|case|class|continue|default|deinit|didSet\"\n            + \"|do|dynamicType|else|enum|extension|fallthrough|for|func|get|if|import\"\n            + \"|in|infix|init|inout|is|left|let|let|mutating|new|none|nonmutating\"\n            + \"|operator|override|postfix|precedence|prefix|protocol|return|right\"\n            + \"|safe|Self|self|set|struct|subscript|switch|Type|typealias\"\n            + \"|unowned|unsafe|var|weak|where|while|willSet\"\n            + \"|convenience|dynamic|final|infix|lazy|mutating|nonmutating|optional|override|postfix\"\n            + \"|prefix|acequired|static|guard|defer\",\n        \"storage.type\": \"bool|double|Double\"\n            + \"|extension|float|Float|int|Int|private|public|string|String\",\n        \"constant.language\":\n            \"false|Infinity|NaN|nil|no|null|null|off|on|super|this|true|undefined|yes\",\n        \"support.function\":\n            \"\"\n    }, \"identifier\");\n    \n    function string(start, options) {\n        var nestable = options.nestable || options.interpolation;\n        var interpStart = options.interpolation && options.interpolation.nextState || \"start\";\n        var mainRule = {\n            regex: start + (options.multiline ? \"\" : \"(?=.)\"),\n            token: \"string.start\"\n        };\n        var nextState = [\n            options.escape && {\n                regex: options.escape,\n                token: \"character.escape\"\n            },\n            options.interpolation && {\n                token : \"paren.quasi.start\",\n                regex : lang.escapeRegExp(options.interpolation.lead + options.interpolation.open),\n                push  : interpStart\n            },\n            options.error && {\n                regex: options.error,\n                token: \"error.invalid\"\n            }, \n            {\n                regex: start + (options.multiline ? \"\" : \"|$\"),\n                token: \"string.end\",\n                next: nestable ? \"pop\" : \"start\"\n            }, {\n                defaultToken: \"string\"\n            }\n        ].filter(Boolean);\n        \n        if (nestable)\n            mainRule.push = nextState;\n        else\n            mainRule.next = nextState;\n        \n        if (!options.interpolation)\n            return mainRule;\n        \n        var open = options.interpolation.open;\n        var close = options.interpolation.close;\n        var counter = {\n            regex: \"[\" + lang.escapeRegExp(open + close) + \"]\",\n            onMatch: function(val, state, stack) {\n                this.next = val == open ? this.nextState : \"\";\n                if (val == open && stack.length) {\n                    stack.unshift(\"start\", state);\n                    return \"paren\";\n                }\n                if (val == close && stack.length) {\n                    stack.shift();\n                    this.next = stack.shift();\n                    if (this.next.indexOf(\"string\") != -1)\n                        return \"paren.quasi.end\";\n                }\n                return val == open ? \"paren.lparen\" : \"paren.rparen\";\n            },\n            nextState: interpStart\n        };\n        return [counter, mainRule];\n    }\n    \n    function comments() {\n        return [{\n                token : \"comment\",\n                regex : \"\\\\/\\\\/(?=.)\",\n                next : [\n                    DocCommentHighlightRules.getTagRule(),\n                    {token : \"comment\", regex : \"$|^\", next: \"start\"},\n                    {defaultToken : \"comment\", caseInsensitive: true}\n                ]\n            },\n            DocCommentHighlightRules.getStartRule(\"doc-start\"),\n            {\n                token : \"comment.start\",\n                regex : /\\/\\*/,\n                stateName: \"nested_comment\",\n                push : [\n                    DocCommentHighlightRules.getTagRule(),\n                    {token : \"comment.start\", regex : /\\/\\*/, push: \"nested_comment\"},\n                    {token : \"comment.end\", regex : \"\\\\*\\\\/\", next : \"pop\"},\n                    {defaultToken : \"comment\", caseInsensitive: true}\n                ]\n            }\n        ];\n    }\n    \n\n    this.$rules = {\n        start: [\n            string('\"', {\n                escape: /\\\\(?:[0\\\\tnr\"']|u{[a-fA-F1-9]{0,8}})/,\n                interpolation: {lead: \"\\\\\", open: \"(\", close: \")\"},\n                error: /\\\\./,\n                multiline: false\n            }),\n            comments({type: \"c\", nestable: true}),\n            {\n                 regex: /@[a-zA-Z_$][a-zA-Z_$\\d\\u0080-\\ufffe]*/,\n                 token: \"variable.parameter\"\n            },\n            {\n                regex: /[a-zA-Z_$][a-zA-Z_$\\d\\u0080-\\ufffe]*/,\n                token: keywordMapper\n            },  \n            {\n                token : \"constant.numeric\", \n                regex : /[+-]?(?:0(?:b[01]+|o[0-7]+|x[\\da-fA-F])|\\d+(?:(?:\\.\\d*)?(?:[PpEe][+-]?\\d+)?)\\b)/\n            }, {\n                token : \"keyword.operator\",\n                regex : /--|\\+\\+|===|==|=|!=|!==|<=|>=|<<=|>>=|>>>=|<>|<|>|!|&&|\\|\\||\\?:|[!$%&*+\\-~\\/^]=?/,\n                next  : \"start\"\n            }, {\n                token : \"punctuation.operator\",\n                regex : /[?:,;.]/,\n                next  : \"start\"\n            }, {\n                token : \"paren.lparen\",\n                regex : /[\\[({]/,\n                next  : \"start\"\n            }, {\n                token : \"paren.rparen\",\n                regex : /[\\])}]/\n            } \n            \n        ]\n    };\n    this.embedRules(DocCommentHighlightRules, \"doc-\",\n        [ DocCommentHighlightRules.getEndRule(\"start\") ]);\n    \n    this.normalizeRules();\n};\n\n\noop.inherits(SwiftHighlightRules, TextHighlightRules);\n\nexports.HighlightRules = SwiftHighlightRules;\n});\n\nace.define(\"ace/mode/folding/cstyle\",[\"require\",\"exports\",\"module\",\"ace/lib/oop\",\"ace/range\",\"ace/mode/folding/fold_mode\"], function(acequire, exports, module) {\n\"use strict\";\n\nvar oop = acequire(\"../../lib/oop\");\nvar Range = acequire(\"../../range\").Range;\nvar BaseFoldMode = acequire(\"./fold_mode\").FoldMode;\n\nvar FoldMode = exports.FoldMode = function(commentRegex) {\n    if (commentRegex) {\n        this.foldingStartMarker = new RegExp(\n            this.foldingStartMarker.source.replace(/\\|[^|]*?$/, \"|\" + commentRegex.start)\n        );\n        this.foldingStopMarker = new RegExp(\n            this.foldingStopMarker.source.replace(/\\|[^|]*?$/, \"|\" + commentRegex.end)\n        );\n    }\n};\noop.inherits(FoldMode, BaseFoldMode);\n\n(function() {\n    \n    this.foldingStartMarker = /([\\{\\[\\(])[^\\}\\]\\)]*$|^\\s*(\\/\\*)/;\n    this.foldingStopMarker = /^[^\\[\\{\\(]*([\\}\\]\\)])|^[\\s\\*]*(\\*\\/)/;\n    this.singleLineBlockCommentRe= /^\\s*(\\/\\*).*\\*\\/\\s*$/;\n    this.tripleStarBlockCommentRe = /^\\s*(\\/\\*\\*\\*).*\\*\\/\\s*$/;\n    this.startRegionRe = /^\\s*(\\/\\*|\\/\\/)#?region\\b/;\n    this._getFoldWidgetBase = this.getFoldWidget;\n    this.getFoldWidget = function(session, foldStyle, row) {\n        var line = session.getLine(row);\n    \n        if (this.singleLineBlockCommentRe.test(line)) {\n            if (!this.startRegionRe.test(line) && !this.tripleStarBlockCommentRe.test(line))\n                return \"\";\n        }\n    \n        var fw = this._getFoldWidgetBase(session, foldStyle, row);\n    \n        if (!fw && this.startRegionRe.test(line))\n            return \"start\"; // lineCommentRegionStart\n    \n        return fw;\n    };\n\n    this.getFoldWidgetRange = function(session, foldStyle, row, forceMultiline) {\n        var line = session.getLine(row);\n        \n        if (this.startRegionRe.test(line))\n            return this.getCommentRegionBlock(session, line, row);\n        \n        var match = line.match(this.foldingStartMarker);\n        if (match) {\n            var i = match.index;\n\n            if (match[1])\n                return this.openingBracketBlock(session, match[1], row, i);\n                \n            var range = session.getCommentFoldRange(row, i + match[0].length, 1);\n            \n            if (range && !range.isMultiLine()) {\n                if (forceMultiline) {\n                    range = this.getSectionRange(session, row);\n                } else if (foldStyle != \"all\")\n                    range = null;\n            }\n            \n            return range;\n        }\n\n        if (foldStyle === \"markbegin\")\n            return;\n\n        var match = line.match(this.foldingStopMarker);\n        if (match) {\n            var i = match.index + match[0].length;\n\n            if (match[1])\n                return this.closingBracketBlock(session, match[1], row, i);\n\n            return session.getCommentFoldRange(row, i, -1);\n        }\n    };\n    \n    this.getSectionRange = function(session, row) {\n        var line = session.getLine(row);\n        var startIndent = line.search(/\\S/);\n        var startRow = row;\n        var startColumn = line.length;\n        row = row + 1;\n        var endRow = row;\n        var maxRow = session.getLength();\n        while (++row < maxRow) {\n            line = session.getLine(row);\n            var indent = line.search(/\\S/);\n            if (indent === -1)\n                continue;\n            if  (startIndent > indent)\n                break;\n            var subRange = this.getFoldWidgetRange(session, \"all\", row);\n            \n            if (subRange) {\n                if (subRange.start.row <= startRow) {\n                    break;\n                } else if (subRange.isMultiLine()) {\n                    row = subRange.end.row;\n                } else if (startIndent == indent) {\n                    break;\n                }\n            }\n            endRow = row;\n        }\n        \n        return new Range(startRow, startColumn, endRow, session.getLine(endRow).length);\n    };\n    this.getCommentRegionBlock = function(session, line, row) {\n        var startColumn = line.search(/\\s*$/);\n        var maxRow = session.getLength();\n        var startRow = row;\n        \n        var re = /^\\s*(?:\\/\\*|\\/\\/|--)#?(end)?region\\b/;\n        var depth = 1;\n        while (++row < maxRow) {\n            line = session.getLine(row);\n            var m = re.exec(line);\n            if (!m) continue;\n            if (m[1]) depth--;\n            else depth++;\n\n            if (!depth) break;\n        }\n\n        var endRow = row;\n        if (endRow > startRow) {\n            return new Range(startRow, startColumn, endRow, line.length);\n        }\n    };\n\n}).call(FoldMode.prototype);\n\n});\n\nace.define(\"ace/mode/swift\",[\"require\",\"exports\",\"module\",\"ace/lib/oop\",\"ace/mode/text\",\"ace/mode/swift_highlight_rules\",\"ace/mode/behaviour/cstyle\",\"ace/mode/folding/cstyle\"], function(acequire, exports, module) {\n\"use strict\";\n\nvar oop = acequire(\"../lib/oop\");\nvar TextMode = acequire(\"./text\").Mode;\nvar HighlightRules = acequire(\"./swift_highlight_rules\").HighlightRules;\nvar CstyleBehaviour = acequire(\"./behaviour/cstyle\").CstyleBehaviour;\nvar FoldMode = acequire(\"./folding/cstyle\").FoldMode;\n\nvar Mode = function() {\n    this.HighlightRules = HighlightRules;\n    this.foldingRules = new FoldMode();\n    this.$behaviour = new CstyleBehaviour();\n    this.$behaviour = this.$defaultBehaviour;\n};\noop.inherits(Mode, TextMode);\n\n(function() {\n    this.lineCommentStart = \"//\";\n    this.blockComment = {start: \"/*\", end: \"*/\", nestable: true};\n    \n    this.$id = \"ace/mode/swift\";\n}).call(Mode.prototype);\n\nexports.Mode = Mode;\n});\n"], "mappings": "AAAAA,GAAG,CAACC,MAAJ,CAAW,sCAAX,EAAkD,CAAC,SAAD,EAAW,SAAX,EAAqB,QAArB,EAA8B,aAA9B,EAA4C,+BAA5C,CAAlD,EAAgI,UAASC,QAAT,EAAmBC,OAAnB,EAA4BC,MAA5B,EAAoC;EACpK;;EAEA,IAAIC,GAAG,GAAGH,QAAQ,CAAC,YAAD,CAAlB;EACA,IAAII,kBAAkB,GAAGJ,QAAQ,CAAC,wBAAD,CAAR,CAAmCI,kBAA5D;;EAEA,IAAIC,wBAAwB,GAAG,YAAW;IACtC,KAAKC,MAAL,GAAc;MACV,SAAU,CAAE;QACRC,KAAK,EAAG,iBADA;QAERC,KAAK,EAAG,aAFA,CAEc;;MAFd,CAAF,EAIVH,wBAAwB,CAACI,UAAzB,EAJU,EAKV;QACIC,YAAY,EAAG,aADnB;QAEIC,eAAe,EAAE;MAFrB,CALU;IADA,CAAd;EAWH,CAZD;;EAcAR,GAAG,CAACS,QAAJ,CAAaP,wBAAb,EAAuCD,kBAAvC;;EAEAC,wBAAwB,CAACI,UAAzB,GAAsC,UAASI,KAAT,EAAgB;IAClD,OAAO;MACHN,KAAK,EAAG,8BADL;MAEHC,KAAK,EAAG;IAFL,CAAP;EAIH,CALD;;EAOAH,wBAAwB,CAACS,YAAzB,GAAwC,UAASD,KAAT,EAAgB;IACpD,OAAO;MACHN,KAAK,EAAG,aADL;MACoB;MACvBC,KAAK,EAAG,eAFL;MAGHO,IAAI,EAAIF;IAHL,CAAP;EAKH,CAND;;EAQAR,wBAAwB,CAACW,UAAzB,GAAsC,UAAUH,KAAV,EAAiB;IACnD,OAAO;MACHN,KAAK,EAAG,aADL;MACoB;MACvBC,KAAK,EAAG,QAFL;MAGHO,IAAI,EAAIF;IAHL,CAAP;EAKH,CAND;;EASAZ,OAAO,CAACI,wBAAR,GAAmCA,wBAAnC;AAEC,CAhDD;AAkDAP,GAAG,CAACC,MAAJ,CAAW,gCAAX,EAA4C,CAAC,SAAD,EAAW,SAAX,EAAqB,QAArB,EAA8B,aAA9B,EAA4C,cAA5C,EAA2D,sCAA3D,EAAkG,+BAAlG,CAA5C,EAAgL,UAASC,QAAT,EAAmBC,OAAnB,EAA4BC,MAA5B,EAAoC;EACpN;;EAEA,IAAIC,GAAG,GAAGH,QAAQ,CAAC,YAAD,CAAlB;EACA,IAAIiB,IAAI,GAAGjB,QAAQ,CAAC,aAAD,CAAnB;EACA,IAAIK,wBAAwB,GAAGL,QAAQ,CAAC,+BAAD,CAAR,CAA0CK,wBAAzE;EACA,IAAID,kBAAkB,GAAGJ,QAAQ,CAAC,wBAAD,CAAR,CAAmCI,kBAA5D;;EAEA,IAAIc,mBAAmB,GAAG,YAAW;IAClC,IAAIC,aAAa,GAAG,KAAKC,mBAAL,CAAyB;MACxC,qBAAqB,EADmB;MAExC,WAAW,8CACL,mEADK,GAEL,wEAFK,GAGL,oEAHK,GAIL,oEAJK,GAKL,4DALK,GAML,8CANK,GAOL,sFAPK,GAQL,sCAVkC;MAWxC,gBAAgB,uBACV,6DAZkC;MAaxC,qBACI,0EAdoC;MAexC,oBACI;IAhBoC,CAAzB,EAiBhB,YAjBgB,CAApB;;IAmBC,SAASC,MAAT,CAAgBR,KAAhB,EAAuBS,OAAvB,EAAgC;MAC5B,IAAIC,QAAQ,GAAGD,OAAO,CAACC,QAAR,IAAoBD,OAAO,CAACE,aAA3C;MACA,IAAIC,WAAW,GAAGH,OAAO,CAACE,aAAR,IAAyBF,OAAO,CAACE,aAAR,CAAsBE,SAA/C,IAA4D,OAA9E;MACA,IAAIC,QAAQ,GAAG;QACXnB,KAAK,EAAEK,KAAK,IAAIS,OAAO,CAACM,SAAR,GAAoB,EAApB,GAAyB,OAA7B,CADD;QAEXrB,KAAK,EAAE;MAFI,CAAf;MAIA,IAAImB,SAAS,GAAG,CACZJ,OAAO,CAACO,MAAR,IAAkB;QACdrB,KAAK,EAAEc,OAAO,CAACO,MADD;QAEdtB,KAAK,EAAE;MAFO,CADN,EAKZe,OAAO,CAACE,aAAR,IAAyB;QACrBjB,KAAK,EAAG,mBADa;QAErBC,KAAK,EAAGS,IAAI,CAACa,YAAL,CAAkBR,OAAO,CAACE,aAAR,CAAsBO,IAAtB,GAA6BT,OAAO,CAACE,aAAR,CAAsBQ,IAArE,CAFa;QAGrBC,IAAI,EAAIR;MAHa,CALb,EAUZH,OAAO,CAACY,KAAR,IAAiB;QACb1B,KAAK,EAAEc,OAAO,CAACY,KADF;QAEb3B,KAAK,EAAE;MAFM,CAVL,EAcZ;QACIC,KAAK,EAAEK,KAAK,IAAIS,OAAO,CAACM,SAAR,GAAoB,EAApB,GAAyB,IAA7B,CADhB;QAEIrB,KAAK,EAAE,YAFX;QAGIQ,IAAI,EAAEQ,QAAQ,GAAG,KAAH,GAAW;MAH7B,CAdY,EAkBT;QACCb,YAAY,EAAE;MADf,CAlBS,EAqBdyB,MArBc,CAqBPC,OArBO,CAAhB;MAuBA,IAAIb,QAAJ,EACII,QAAQ,CAACM,IAAT,GAAgBP,SAAhB,CADJ,KAGIC,QAAQ,CAACZ,IAAT,GAAgBW,SAAhB;MAEJ,IAAI,CAACJ,OAAO,CAACE,aAAb,EACI,OAAOG,QAAP;MAEJ,IAAIK,IAAI,GAAGV,OAAO,CAACE,aAAR,CAAsBQ,IAAjC;MACA,IAAIK,KAAK,GAAGf,OAAO,CAACE,aAAR,CAAsBa,KAAlC;MACA,IAAIC,OAAO,GAAG;QACV9B,KAAK,EAAE,MAAMS,IAAI,CAACa,YAAL,CAAkBE,IAAI,GAAGK,KAAzB,CAAN,GAAwC,GADrC;QAEVE,OAAO,EAAE,UAASC,GAAT,EAAcC,KAAd,EAAqBC,KAArB,EAA4B;UACjC,KAAK3B,IAAL,GAAYyB,GAAG,IAAIR,IAAP,GAAc,KAAKN,SAAnB,GAA+B,EAA3C;;UACA,IAAIc,GAAG,IAAIR,IAAP,IAAeU,KAAK,CAACC,MAAzB,EAAiC;YAC7BD,KAAK,CAACE,OAAN,CAAc,OAAd,EAAuBH,KAAvB;YACA,OAAO,OAAP;UACH;;UACD,IAAID,GAAG,IAAIH,KAAP,IAAgBK,KAAK,CAACC,MAA1B,EAAkC;YAC9BD,KAAK,CAACG,KAAN;YACA,KAAK9B,IAAL,GAAY2B,KAAK,CAACG,KAAN,EAAZ;YACA,IAAI,KAAK9B,IAAL,CAAU+B,OAAV,CAAkB,QAAlB,KAA+B,CAAC,CAApC,EACI,OAAO,iBAAP;UACP;;UACD,OAAON,GAAG,IAAIR,IAAP,GAAc,cAAd,GAA+B,cAAtC;QACH,CAfS;QAgBVN,SAAS,EAAED;MAhBD,CAAd;MAkBA,OAAO,CAACa,OAAD,EAAUX,QAAV,CAAP;IACH;;IAED,SAASoB,QAAT,GAAoB;MAChB,OAAO,CAAC;QACAxC,KAAK,EAAG,SADR;QAEAC,KAAK,EAAG,aAFR;QAGAO,IAAI,EAAG,CACHV,wBAAwB,CAACI,UAAzB,EADG,EAEH;UAACF,KAAK,EAAG,SAAT;UAAoBC,KAAK,EAAG,KAA5B;UAAmCO,IAAI,EAAE;QAAzC,CAFG,EAGH;UAACL,YAAY,EAAG,SAAhB;UAA2BC,eAAe,EAAE;QAA5C,CAHG;MAHP,CAAD,EASHN,wBAAwB,CAACS,YAAzB,CAAsC,WAAtC,CATG,EAUH;QACIP,KAAK,EAAG,eADZ;QAEIC,KAAK,EAAG,MAFZ;QAGIwC,SAAS,EAAE,gBAHf;QAIIf,IAAI,EAAG,CACH5B,wBAAwB,CAACI,UAAzB,EADG,EAEH;UAACF,KAAK,EAAG,eAAT;UAA0BC,KAAK,EAAG,MAAlC;UAA0CyB,IAAI,EAAE;QAAhD,CAFG,EAGH;UAAC1B,KAAK,EAAG,aAAT;UAAwBC,KAAK,EAAG,QAAhC;UAA0CO,IAAI,EAAG;QAAjD,CAHG,EAIH;UAACL,YAAY,EAAG,SAAhB;UAA2BC,eAAe,EAAE;QAA5C,CAJG;MAJX,CAVG,CAAP;IAsBH;;IAGD,KAAKL,MAAL,GAAc;MACVO,KAAK,EAAE,CACHQ,MAAM,CAAC,GAAD,EAAM;QACRQ,MAAM,EAAE,sCADA;QAERL,aAAa,EAAE;UAACO,IAAI,EAAE,IAAP;UAAaC,IAAI,EAAE,GAAnB;UAAwBK,KAAK,EAAE;QAA/B,CAFP;QAGRH,KAAK,EAAE,KAHC;QAIRN,SAAS,EAAE;MAJH,CAAN,CADH,EAOHmB,QAAQ,CAAC;QAACE,IAAI,EAAE,GAAP;QAAY1B,QAAQ,EAAE;MAAtB,CAAD,CAPL,EAQH;QACKf,KAAK,EAAE,uCADZ;QAEKD,KAAK,EAAE;MAFZ,CARG,EAYH;QACIC,KAAK,EAAE,sCADX;QAEID,KAAK,EAAEY;MAFX,CAZG,EAgBH;QACIZ,KAAK,EAAG,kBADZ;QAEIC,KAAK,EAAG;MAFZ,CAhBG,EAmBA;QACCD,KAAK,EAAG,kBADT;QAECC,KAAK,EAAG,kFAFT;QAGCO,IAAI,EAAI;MAHT,CAnBA,EAuBA;QACCR,KAAK,EAAG,sBADT;QAECC,KAAK,EAAG,SAFT;QAGCO,IAAI,EAAI;MAHT,CAvBA,EA2BA;QACCR,KAAK,EAAG,cADT;QAECC,KAAK,EAAG,QAFT;QAGCO,IAAI,EAAI;MAHT,CA3BA,EA+BA;QACCR,KAAK,EAAG,cADT;QAECC,KAAK,EAAG;MAFT,CA/BA;IADG,CAAd;IAuCA,KAAK0C,UAAL,CAAgB7C,wBAAhB,EAA0C,MAA1C,EACI,CAAEA,wBAAwB,CAACW,UAAzB,CAAoC,OAApC,CAAF,CADJ;IAGA,KAAKmC,cAAL;EACH,CAtJD;;EAyJAhD,GAAG,CAACS,QAAJ,CAAaM,mBAAb,EAAkCd,kBAAlC;EAEAH,OAAO,CAACmD,cAAR,GAAyBlC,mBAAzB;AACC,CApKD;AAsKApB,GAAG,CAACC,MAAJ,CAAW,yBAAX,EAAqC,CAAC,SAAD,EAAW,SAAX,EAAqB,QAArB,EAA8B,aAA9B,EAA4C,WAA5C,EAAwD,4BAAxD,CAArC,EAA4H,UAASC,QAAT,EAAmBC,OAAnB,EAA4BC,MAA5B,EAAoC;EAChK;;EAEA,IAAIC,GAAG,GAAGH,QAAQ,CAAC,eAAD,CAAlB;EACA,IAAIqD,KAAK,GAAGrD,QAAQ,CAAC,aAAD,CAAR,CAAwBqD,KAApC;EACA,IAAIC,YAAY,GAAGtD,QAAQ,CAAC,aAAD,CAAR,CAAwBuD,QAA3C;;EAEA,IAAIA,QAAQ,GAAGtD,OAAO,CAACsD,QAAR,GAAmB,UAASC,YAAT,EAAuB;IACrD,IAAIA,YAAJ,EAAkB;MACd,KAAKC,kBAAL,GAA0B,IAAIC,MAAJ,CACtB,KAAKD,kBAAL,CAAwBE,MAAxB,CAA+BC,OAA/B,CAAuC,WAAvC,EAAoD,MAAMJ,YAAY,CAAC3C,KAAvE,CADsB,CAA1B;MAGA,KAAKgD,iBAAL,GAAyB,IAAIH,MAAJ,CACrB,KAAKG,iBAAL,CAAuBF,MAAvB,CAA8BC,OAA9B,CAAsC,WAAtC,EAAmD,MAAMJ,YAAY,CAACM,GAAtE,CADqB,CAAzB;IAGH;EACJ,CATD;;EAUA3D,GAAG,CAACS,QAAJ,CAAa2C,QAAb,EAAuBD,YAAvB;EAEA,CAAC,YAAW;IAER,KAAKG,kBAAL,GAA0B,kCAA1B;IACA,KAAKI,iBAAL,GAAyB,sCAAzB;IACA,KAAKE,wBAAL,GAA+B,sBAA/B;IACA,KAAKC,wBAAL,GAAgC,0BAAhC;IACA,KAAKC,aAAL,GAAqB,2BAArB;IACA,KAAKC,kBAAL,GAA0B,KAAKC,aAA/B;;IACA,KAAKA,aAAL,GAAqB,UAASC,OAAT,EAAkBC,SAAlB,EAA6BC,GAA7B,EAAkC;MACnD,IAAIC,IAAI,GAAGH,OAAO,CAACI,OAAR,CAAgBF,GAAhB,CAAX;;MAEA,IAAI,KAAKP,wBAAL,CAA8BU,IAA9B,CAAmCF,IAAnC,CAAJ,EAA8C;QAC1C,IAAI,CAAC,KAAKN,aAAL,CAAmBQ,IAAnB,CAAwBF,IAAxB,CAAD,IAAkC,CAAC,KAAKP,wBAAL,CAA8BS,IAA9B,CAAmCF,IAAnC,CAAvC,EACI,OAAO,EAAP;MACP;;MAED,IAAIG,EAAE,GAAG,KAAKR,kBAAL,CAAwBE,OAAxB,EAAiCC,SAAjC,EAA4CC,GAA5C,CAAT;;MAEA,IAAI,CAACI,EAAD,IAAO,KAAKT,aAAL,CAAmBQ,IAAnB,CAAwBF,IAAxB,CAAX,EACI,OAAO,OAAP,CAX+C,CAW/B;;MAEpB,OAAOG,EAAP;IACH,CAdD;;IAgBA,KAAKC,kBAAL,GAA0B,UAASP,OAAT,EAAkBC,SAAlB,EAA6BC,GAA7B,EAAkCM,cAAlC,EAAkD;MACxE,IAAIL,IAAI,GAAGH,OAAO,CAACI,OAAR,CAAgBF,GAAhB,CAAX;MAEA,IAAI,KAAKL,aAAL,CAAmBQ,IAAnB,CAAwBF,IAAxB,CAAJ,EACI,OAAO,KAAKM,qBAAL,CAA2BT,OAA3B,EAAoCG,IAApC,EAA0CD,GAA1C,CAAP;MAEJ,IAAIQ,KAAK,GAAGP,IAAI,CAACO,KAAL,CAAW,KAAKrB,kBAAhB,CAAZ;;MACA,IAAIqB,KAAJ,EAAW;QACP,IAAIC,CAAC,GAAGD,KAAK,CAACE,KAAd;QAEA,IAAIF,KAAK,CAAC,CAAD,CAAT,EACI,OAAO,KAAKG,mBAAL,CAAyBb,OAAzB,EAAkCU,KAAK,CAAC,CAAD,CAAvC,EAA4CR,GAA5C,EAAiDS,CAAjD,CAAP;QAEJ,IAAIG,KAAK,GAAGd,OAAO,CAACe,mBAAR,CAA4Bb,GAA5B,EAAiCS,CAAC,GAAGD,KAAK,CAAC,CAAD,CAAL,CAASnC,MAA9C,EAAsD,CAAtD,CAAZ;;QAEA,IAAIuC,KAAK,IAAI,CAACA,KAAK,CAACE,WAAN,EAAd,EAAmC;UAC/B,IAAIR,cAAJ,EAAoB;YAChBM,KAAK,GAAG,KAAKG,eAAL,CAAqBjB,OAArB,EAA8BE,GAA9B,CAAR;UACH,CAFD,MAEO,IAAID,SAAS,IAAI,KAAjB,EACHa,KAAK,GAAG,IAAR;QACP;;QAED,OAAOA,KAAP;MACH;;MAED,IAAIb,SAAS,KAAK,WAAlB,EACI;MAEJ,IAAIS,KAAK,GAAGP,IAAI,CAACO,KAAL,CAAW,KAAKjB,iBAAhB,CAAZ;;MACA,IAAIiB,KAAJ,EAAW;QACP,IAAIC,CAAC,GAAGD,KAAK,CAACE,KAAN,GAAcF,KAAK,CAAC,CAAD,CAAL,CAASnC,MAA/B;QAEA,IAAImC,KAAK,CAAC,CAAD,CAAT,EACI,OAAO,KAAKQ,mBAAL,CAAyBlB,OAAzB,EAAkCU,KAAK,CAAC,CAAD,CAAvC,EAA4CR,GAA5C,EAAiDS,CAAjD,CAAP;QAEJ,OAAOX,OAAO,CAACe,mBAAR,CAA4Bb,GAA5B,EAAiCS,CAAjC,EAAoC,CAAC,CAArC,CAAP;MACH;IACJ,CArCD;;IAuCA,KAAKM,eAAL,GAAuB,UAASjB,OAAT,EAAkBE,GAAlB,EAAuB;MAC1C,IAAIC,IAAI,GAAGH,OAAO,CAACI,OAAR,CAAgBF,GAAhB,CAAX;MACA,IAAIiB,WAAW,GAAGhB,IAAI,CAACiB,MAAL,CAAY,IAAZ,CAAlB;MACA,IAAIC,QAAQ,GAAGnB,GAAf;MACA,IAAIoB,WAAW,GAAGnB,IAAI,CAAC5B,MAAvB;MACA2B,GAAG,GAAGA,GAAG,GAAG,CAAZ;MACA,IAAIqB,MAAM,GAAGrB,GAAb;MACA,IAAIsB,MAAM,GAAGxB,OAAO,CAACyB,SAAR,EAAb;;MACA,OAAO,EAAEvB,GAAF,GAAQsB,MAAf,EAAuB;QACnBrB,IAAI,GAAGH,OAAO,CAACI,OAAR,CAAgBF,GAAhB,CAAP;QACA,IAAIwB,MAAM,GAAGvB,IAAI,CAACiB,MAAL,CAAY,IAAZ,CAAb;QACA,IAAIM,MAAM,KAAK,CAAC,CAAhB,EACI;QACJ,IAAKP,WAAW,GAAGO,MAAnB,EACI;QACJ,IAAIC,QAAQ,GAAG,KAAKpB,kBAAL,CAAwBP,OAAxB,EAAiC,KAAjC,EAAwCE,GAAxC,CAAf;;QAEA,IAAIyB,QAAJ,EAAc;UACV,IAAIA,QAAQ,CAAClF,KAAT,CAAeyD,GAAf,IAAsBmB,QAA1B,EAAoC;YAChC;UACH,CAFD,MAEO,IAAIM,QAAQ,CAACX,WAAT,EAAJ,EAA4B;YAC/Bd,GAAG,GAAGyB,QAAQ,CAACjC,GAAT,CAAaQ,GAAnB;UACH,CAFM,MAEA,IAAIiB,WAAW,IAAIO,MAAnB,EAA2B;YAC9B;UACH;QACJ;;QACDH,MAAM,GAAGrB,GAAT;MACH;;MAED,OAAO,IAAIjB,KAAJ,CAAUoC,QAAV,EAAoBC,WAApB,EAAiCC,MAAjC,EAAyCvB,OAAO,CAACI,OAAR,CAAgBmB,MAAhB,EAAwBhD,MAAjE,CAAP;IACH,CA9BD;;IA+BA,KAAKkC,qBAAL,GAA6B,UAAST,OAAT,EAAkBG,IAAlB,EAAwBD,GAAxB,EAA6B;MACtD,IAAIoB,WAAW,GAAGnB,IAAI,CAACiB,MAAL,CAAY,MAAZ,CAAlB;MACA,IAAII,MAAM,GAAGxB,OAAO,CAACyB,SAAR,EAAb;MACA,IAAIJ,QAAQ,GAAGnB,GAAf;MAEA,IAAI0B,EAAE,GAAG,sCAAT;MACA,IAAIC,KAAK,GAAG,CAAZ;;MACA,OAAO,EAAE3B,GAAF,GAAQsB,MAAf,EAAuB;QACnBrB,IAAI,GAAGH,OAAO,CAACI,OAAR,CAAgBF,GAAhB,CAAP;QACA,IAAI4B,CAAC,GAAGF,EAAE,CAACG,IAAH,CAAQ5B,IAAR,CAAR;QACA,IAAI,CAAC2B,CAAL,EAAQ;QACR,IAAIA,CAAC,CAAC,CAAD,CAAL,EAAUD,KAAK,GAAf,KACKA,KAAK;QAEV,IAAI,CAACA,KAAL,EAAY;MACf;;MAED,IAAIN,MAAM,GAAGrB,GAAb;;MACA,IAAIqB,MAAM,GAAGF,QAAb,EAAuB;QACnB,OAAO,IAAIpC,KAAJ,CAAUoC,QAAV,EAAoBC,WAApB,EAAiCC,MAAjC,EAAyCpB,IAAI,CAAC5B,MAA9C,CAAP;MACH;IACJ,CArBD;EAuBH,CArHD,EAqHGyD,IArHH,CAqHQ7C,QAAQ,CAAC8C,SArHjB;AAuHC,CA1ID;AA4IAvG,GAAG,CAACC,MAAJ,CAAW,gBAAX,EAA4B,CAAC,SAAD,EAAW,SAAX,EAAqB,QAArB,EAA8B,aAA9B,EAA4C,eAA5C,EAA4D,gCAA5D,EAA6F,2BAA7F,EAAyH,yBAAzH,CAA5B,EAAiL,UAASC,QAAT,EAAmBC,OAAnB,EAA4BC,MAA5B,EAAoC;EACrN;;EAEA,IAAIC,GAAG,GAAGH,QAAQ,CAAC,YAAD,CAAlB;EACA,IAAIsG,QAAQ,GAAGtG,QAAQ,CAAC,QAAD,CAAR,CAAmBuG,IAAlC;EACA,IAAInD,cAAc,GAAGpD,QAAQ,CAAC,yBAAD,CAAR,CAAoCoD,cAAzD;EACA,IAAIoD,eAAe,GAAGxG,QAAQ,CAAC,oBAAD,CAAR,CAA+BwG,eAArD;EACA,IAAIjD,QAAQ,GAAGvD,QAAQ,CAAC,kBAAD,CAAR,CAA6BuD,QAA5C;;EAEA,IAAIgD,IAAI,GAAG,YAAW;IAClB,KAAKnD,cAAL,GAAsBA,cAAtB;IACA,KAAKqD,YAAL,GAAoB,IAAIlD,QAAJ,EAApB;IACA,KAAKmD,UAAL,GAAkB,IAAIF,eAAJ,EAAlB;IACA,KAAKE,UAAL,GAAkB,KAAKC,iBAAvB;EACH,CALD;;EAMAxG,GAAG,CAACS,QAAJ,CAAa2F,IAAb,EAAmBD,QAAnB;EAEA,CAAC,YAAW;IACR,KAAKM,gBAAL,GAAwB,IAAxB;IACA,KAAKC,YAAL,GAAoB;MAAChG,KAAK,EAAE,IAAR;MAAciD,GAAG,EAAE,IAAnB;MAAyBvC,QAAQ,EAAE;IAAnC,CAApB;IAEA,KAAKuF,GAAL,GAAW,gBAAX;EACH,CALD,EAKGV,IALH,CAKQG,IAAI,CAACF,SALb;EAOApG,OAAO,CAACsG,IAAR,GAAeA,IAAf;AACC,CAzBD"}, "metadata": {}, "sourceType": "script"}