{"ast": null, "code": "/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { BigNumberDependencies } from './dependenciesBigNumberClass.generated.js';\nimport { UnitDependencies } from './dependenciesUnitClass.generated.js';\nimport { createMolarMass } from '../../factoriesAny.js';\nexport var molarMassDependencies = {\n  BigNumberDependencies,\n  UnitDependencies,\n  createMolarMass\n};", "map": {"version": 3, "names": ["BigNumberDependencies", "UnitDependencies", "createMolarMass", "molarMassDependencies"], "sources": ["D:/work/joyserver/client/node_modules/mathjs/lib/esm/entry/dependenciesAny/dependenciesMolarMass.generated.js"], "sourcesContent": ["/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { BigNumberDependencies } from './dependenciesBigNumberClass.generated.js';\nimport { UnitDependencies } from './dependenciesUnitClass.generated.js';\nimport { createMolarMass } from '../../factoriesAny.js';\nexport var molarMassDependencies = {\n  BigNumberDependencies,\n  UnitDependencies,\n  createMolarMass\n};"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,qBAAT,QAAsC,2CAAtC;AACA,SAASC,gBAAT,QAAiC,sCAAjC;AACA,SAASC,eAAT,QAAgC,uBAAhC;AACA,OAAO,IAAIC,qBAAqB,GAAG;EACjCH,qBADiC;EAEjCC,gBAFiC;EAGjCC;AAHiC,CAA5B"}, "metadata": {}, "sourceType": "module"}