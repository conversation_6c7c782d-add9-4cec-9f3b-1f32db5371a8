{"ast": null, "code": "/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { addDependencies } from './dependenciesAdd.generated.js';\nimport { matrixDependencies } from './dependenciesMatrix.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createTrace } from '../../factoriesAny.js';\nexport var traceDependencies = {\n  addDependencies,\n  matrixDependencies,\n  typedDependencies,\n  createTrace\n};", "map": {"version": 3, "names": ["addDependencies", "matrixDependencies", "typedDependencies", "createTrace", "traceDependencies"], "sources": ["D:/work/joyserver/client/node_modules/mathjs/lib/esm/entry/dependenciesAny/dependenciesTrace.generated.js"], "sourcesContent": ["/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { addDependencies } from './dependenciesAdd.generated.js';\nimport { matrixDependencies } from './dependenciesMatrix.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createTrace } from '../../factoriesAny.js';\nexport var traceDependencies = {\n  addDependencies,\n  matrixDependencies,\n  typedDependencies,\n  createTrace\n};"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,eAAT,QAAgC,gCAAhC;AACA,SAASC,kBAAT,QAAmC,mCAAnC;AACA,SAASC,iBAAT,QAAkC,kCAAlC;AACA,SAASC,WAAT,QAA4B,uBAA5B;AACA,OAAO,IAAIC,iBAAiB,GAAG;EAC7BJ,eAD6B;EAE7BC,kBAF6B;EAG7BC,iBAH6B;EAI7BC;AAJ6B,CAAxB"}, "metadata": {}, "sourceType": "module"}