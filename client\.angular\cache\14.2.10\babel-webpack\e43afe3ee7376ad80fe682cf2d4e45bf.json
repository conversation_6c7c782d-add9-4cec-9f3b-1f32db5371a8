{"ast": null, "code": "import _asyncToGenerator from \"D:/work/joyserver/client/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { EventEmitter } from \"@angular/core\";\nimport { Subscription } from \"rxjs\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@core-service/exam.service\";\nimport * as i2 from \"@core/service/form.service\";\nimport * as i3 from \"@core/modal/custom-alert/custom-alert.service\";\nimport * as i4 from \"@ngx-translate/core\";\nimport * as i5 from \"@core/service/utils/util.service\";\nimport * as i6 from \"@core/service/annotator.service\";\nimport * as i7 from \"@angular/common\";\nimport * as i8 from \"../../components/item-audio/item-audio.component\";\nimport * as i9 from \"../../components/privacy-strip/privacy-strip.component\";\nimport * as i10 from \"../../components/item-timer/item-timer.component\";\nimport * as i11 from \"../../../../core/pipe/domSanitized.pipe\";\n\nfunction McComponent_div_1_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"app-item-audio\", 15);\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"audioUrl\", ctx_r4.item.audio.url)(\"audioConfig\", ctx_r4.item.audio)(\"type\", \"item\");\n  }\n}\n\nfunction McComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 12);\n    i0.ɵɵelement(1, \"app-item-timer\", 13);\n    i0.ɵɵtemplate(2, McComponent_div_1_ng_container_2_Template, 2, 3, \"ng-container\", 14);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"resCurItem\", ctx_r0.resCurItem)(\"item\", ctx_r0.item);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.item.audio == null ? null : ctx_r0.item.audio.url);\n  }\n}\n\nfunction McComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 16);\n    i0.ɵɵlistener(\"click\", function McComponent_div_10_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.showRef());\n    });\n    i0.ɵɵelementStart(1, \"i\", 17);\n    i0.ɵɵtext(2, \"\\uE62B\");\n    i0.ɵɵelementEnd()();\n  }\n}\n\nfunction McComponent_div_12_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 23);\n    i0.ɵɵelement(1, \"b\", 24);\n    i0.ɵɵtext(2, \".\");\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const i_r8 = i0.ɵɵnextContext().index;\n    const ctx_r9 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"innerHTML\", ctx_r9.utilSer.initOptId(i_r8), i0.ɵɵsanitizeHtml);\n  }\n}\n\nfunction McComponent_div_12_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"input\", 25);\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    const opt_r7 = i0.ɵɵnextContext().$implicit;\n    const ctx_r10 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵpropertyInterpolate2(\"id\", \"\", ctx_r10.item.id, \"-\", opt_r7.id, \"\");\n    i0.ɵɵpropertyInterpolate(\"name\", ctx_r10.item.id);\n  }\n}\n\nfunction McComponent_div_12_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"input\", 26);\n    i0.ɵɵlistener(\"change\", function McComponent_div_12_ng_container_3_Template_input_change_1_listener($event) {\n      i0.ɵɵrestoreView(_r15);\n      const ctx_r16 = i0.ɵɵnextContext();\n      const opt_r7 = ctx_r16.$implicit;\n      const i_r8 = ctx_r16.index;\n      const ctx_r14 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r14.changeOpt({\n        item: ctx_r14.item,\n        answer: {\n          optId: opt_r7.id,\n          index: i_r8.toString(),\n          isChecked: $event.target[\"checked\"]\n        },\n        evt: $event\n      }));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    const opt_r7 = i0.ɵɵnextContext().$implicit;\n    const ctx_r11 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵpropertyInterpolate2(\"id\", \"\", ctx_r11.item.id, \"-\", opt_r7.id, \"\");\n    i0.ɵɵpropertyInterpolate(\"name\", ctx_r11.item.id);\n    i0.ɵɵproperty(\"checked\", ctx_r11.initOptChecked(opt_r7.id));\n  }\n}\n\nconst _c0 = function (a0) {\n  return {\n    \"option-checked\": a0\n  };\n};\n\nfunction McComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 18);\n    i0.ɵɵtemplate(1, McComponent_div_12_span_1_Template, 3, 1, \"span\", 19);\n    i0.ɵɵtemplate(2, McComponent_div_12_ng_container_2_Template, 2, 3, \"ng-container\", 14);\n    i0.ɵɵtemplate(3, McComponent_div_12_ng_container_3_Template, 2, 4, \"ng-container\", 14);\n    i0.ɵɵelementStart(4, \"label\", 20)(5, \"span\", 21);\n    i0.ɵɵelement(6, \"i\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(7, \"span\", 22);\n    i0.ɵɵpipe(8, \"safeHtml\");\n    i0.ɵɵelementEnd()();\n  }\n\n  if (rf & 2) {\n    const opt_r7 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(9, _c0, ctx_r2.initOptChecked(opt_r7.id)));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.examService.privacy_strip);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.examService.privacy_strip);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.examService.privacy_strip);\n    i0.ɵɵadvance(1);\n    i0.ɵɵpropertyInterpolate2(\"for\", \"\", ctx_r2.item.id, \"-\", opt_r7.id, \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"innerHTML\", i0.ɵɵpipeBind1(8, 7, opt_r7.description), i0.ɵɵsanitizeHtml);\n  }\n}\n\nfunction McComponent_app_privacy_strip_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r19 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"app-privacy-strip\", 27);\n    i0.ɵɵlistener(\"changeOptEvt\", function McComponent_app_privacy_strip_13_Template_app_privacy_strip_changeOptEvt_0_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r18 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r18.changeOpt($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"index\", ctx_r3.item.index)(\"item\", ctx_r3.item)(\"showGroupInstr\", ctx_r3.showGroupInstr);\n  }\n}\n\nconst _c1 = function (a0, a1) {\n  return {\n    \"has-audio\": a0,\n    \"has-annotator\": a1\n  };\n};\n\nconst _c2 = function (a0, a1) {\n  return {\n    \"only-highlightable\": a0,\n    \"only-annotatable\": a1\n  };\n};\n\nexport class McComponent {\n  constructor(examService, formSer, _alertSvc, translate, utilSer, annotatorSer) {\n    this.examService = examService;\n    this.formSer = formSer;\n    this._alertSvc = _alertSvc;\n    this.translate = translate;\n    this.utilSer = utilSer;\n    this.annotatorSer = annotatorSer;\n    this.section = null;\n    this.item = null;\n    this.resCurItem = null;\n    this.subscriptions = [];\n    this.updatingRes = false;\n    this.showRefEvt = new EventEmitter();\n  }\n\n  ngOnInit() {}\n\n  ngOnChanges() {\n    this.initData();\n    setTimeout(() => {\n      this.destroyAnnotator();\n\n      if (this.item && (this.item.annotatable || this.item.highlightable)) {\n        this.initAnnotator();\n      }\n    }, 0);\n  }\n\n  ngOnDestroy() {\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n    this.destroyAnnotator();\n  }\n\n  ngAfterViewInit() {\n    if (this.item && (this.item.annotatable || this.item.highlightable)) {\n      this.initAnnotator();\n    }\n  }\n\n  changeOpt(responseInfo) {\n    var _this = this;\n\n    return _asyncToGenerator(function* () {\n      _this.enterNewItem();\n\n      if (!_this.updatingRes) {\n        _this.updatingRes = true;\n        const jt = window[\"JTCustom\"];\n        const evt = responseInfo.evt;\n\n        if (responseInfo.answer.isChecked) {\n          if (jt.checkItemOptionsWhenChangeOpts) {\n            const pass = yield jt.checkItemOptionsWhenChangeOpts();\n\n            if (!pass) {\n              evt.target.checked = false;\n              _this.updatingRes = false;\n              return;\n            }\n          } else if (_this.formSer.moreThanMaxChoices(_this.item, _this.resCurItem.answer.value)) {\n            evt.target.checked = false;\n            _this.updatingRes = false;\n\n            _this.examService.alertTip(\"exam.mc.maxChoicesTip\", {\n              max: _this.item.content.max_choices\n            });\n\n            return;\n          }\n        }\n\n        _this.examService.updateItemResponse(responseInfo);\n\n        _this.updatingRes = false;\n      }\n    })();\n  }\n\n  initData() {\n    this.section = this.examService.currentSection();\n    this.item = this.formSer.getItemById(this.section, this.itemId);\n    this.resCurItem = this.examService.getItemResById(this.itemId);\n  }\n\n  initOptChecked(optId) {\n    const resCurItem = this.examService.getItemResById(this.itemId);\n    return this.examService.initOptChecked(resCurItem, optId);\n  }\n\n  enterNewItem() {\n    // 按组展示模式，如果作答题非当前题\n    if (this.section.display == \"bygroup\" && this.itemId != this.examService.response.current_item_id) {\n      this.examService.enterItem({\n        itemId: this.itemId\n      });\n    }\n  }\n\n  showRef() {\n    this.enterNewItem();\n    this.showRefEvt.emit();\n  }\n\n  initAnnotator() {\n    this.annotatorEle = window[\"$\"](\".wrap-item-cont .joy-item-material\");\n    this.annotatorSer.annotatorBgColor = \"yellow\";\n    window[\"annotatorBgColor\"] = \"yellow\";\n    const annotatorEle = this.annotatorEle;\n    const itemRes = this.examService.getResCurItem();\n    const item = this.examService.currentItem();\n    this.annotatorSer.initAnnotator(itemRes, item, annotatorEle, this.examService);\n  }\n\n  destroyAnnotator() {\n    if (this.annotatorEle) {\n      this.annotatorSer.destroyAnnotator(this.annotatorEle);\n    }\n  }\n\n}\n\nMcComponent.ɵfac = function McComponent_Factory(t) {\n  return new (t || McComponent)(i0.ɵɵdirectiveInject(i1.ExamService), i0.ɵɵdirectiveInject(i2.FormService), i0.ɵɵdirectiveInject(i3.CustomAlertService), i0.ɵɵdirectiveInject(i4.TranslateService), i0.ɵɵdirectiveInject(i5.UtilService), i0.ɵɵdirectiveInject(i6.AnnotatorService));\n};\n\nMcComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: McComponent,\n  selectors: [[\"mc\"]],\n  inputs: {\n    itemId: \"itemId\",\n    showGroupInstr: \"showGroupInstr\"\n  },\n  outputs: {\n    showRefEvt: \"showRefEvt\"\n  },\n  features: [i0.ɵɵNgOnChangesFeature],\n  decls: 14,\n  vars: 17,\n  consts: [[1, \"item\", \"item-mc\"], [\"class\", \"wrap-audio-timer\", 4, \"ngIf\"], [1, \"wrap-item-cont\", \"zoom-cont\", \"pl-3\", \"pr-3\", \"pt-2\", \"pb-2\", 3, \"ngClass\", \"id\"], [1, \"item-head\", \"pt-2\", \"pb-3\", \"clearfix\"], [1, \"text-right\", \"time-count-down\"], [1, \"item-prop\"], [1, \"item-index\"], [1, \"item-stem\", \"joy-item-material\", 3, \"innerHTML\", \"ngClass\"], [\"class\", \"ref-btn\", 3, \"click\", 4, \"ngIf\"], [1, \"item-cont\"], [\"class\", \"option\", 3, \"ngClass\", 4, \"ngFor\", \"ngForOf\"], [3, \"index\", \"item\", \"showGroupInstr\", \"changeOptEvt\", 4, \"ngIf\"], [1, \"wrap-audio-timer\"], [3, \"resCurItem\", \"item\"], [4, \"ngIf\"], [3, \"audioUrl\", \"audioConfig\", \"type\"], [1, \"ref-btn\", 3, \"click\"], [1, \"iconfont\", \"text-primary\"], [1, \"option\", 3, \"ngClass\"], [\"class\", \"optId font-weight-bold\", 4, \"ngIf\"], [1, \"option-cont\", 3, \"for\"], [1, \"custome-checkbox\"], [1, \"option-txt\", 3, \"innerHTML\"], [1, \"optId\", \"font-weight-bold\"], [3, \"innerHTML\"], [\"type\", \"checkbox\", \"order\", \"1\", 1, \"origin-input\", 3, \"id\", \"name\"], [\"type\", \"checkbox\", \"order\", \"1\", 1, \"origin-input\", 3, \"id\", \"checked\", \"name\", \"change\"], [3, \"index\", \"item\", \"showGroupInstr\", \"changeOptEvt\"]],\n  template: function McComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"div\", 0);\n      i0.ɵɵtemplate(1, McComponent_div_1_Template, 3, 3, \"div\", 1);\n      i0.ɵɵelementStart(2, \"div\", 2)(3, \"div\", 3);\n      i0.ɵɵelement(4, \"div\", 4);\n      i0.ɵɵelementStart(5, \"span\", 5)(6, \"i\", 6);\n      i0.ɵɵtext(7);\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelement(8, \"div\", 7);\n      i0.ɵɵpipe(9, \"safeHtml\");\n      i0.ɵɵtemplate(10, McComponent_div_10_Template, 3, 0, \"div\", 8);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(11, \"div\", 9);\n      i0.ɵɵtemplate(12, McComponent_div_12_Template, 9, 11, \"div\", 10);\n      i0.ɵɵelementEnd()();\n      i0.ɵɵtemplate(13, McComponent_app_privacy_strip_13_Template, 1, 3, \"app-privacy-strip\", 11);\n      i0.ɵɵelementEnd();\n    }\n\n    if (rf & 2) {\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.item.audio == null ? null : ctx.item.audio.url);\n      i0.ɵɵadvance(1);\n      i0.ɵɵpropertyInterpolate1(\"id\", \"item-\", ctx.item.id, \"\");\n      i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(11, _c1, !!(ctx.item.audio == null ? null : ctx.item.audio.url), ctx.item.annotatable || ctx.item.highlightable));\n      i0.ɵɵadvance(5);\n      i0.ɵɵtextInterpolate(ctx.item.index);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"innerHTML\", i0.ɵɵpipeBind1(9, 9, ctx.item.content.stem), i0.ɵɵsanitizeHtml)(\"ngClass\", i0.ɵɵpureFunction2(14, _c2, !ctx.item.annotatable, !ctx.item.highlightable));\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngIf\", ctx.formSer.hasRef(ctx.item));\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngForOf\", ctx.item.content.options);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.examService.privacy_strip);\n    }\n  },\n  dependencies: [i7.NgClass, i7.NgForOf, i7.NgIf, i8.ItemAudioComponent, i9.PrivacyStripComponent, i10.ItemTimerComponent, i11.SafeHtmlPipe],\n  styles: [\"\\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsImZpbGUiOiJtYy5jb21wb25lbnQuc2NzcyJ9 */\"]\n});", "map": {"version": 3, "mappings": ";AAAA,SAAiEA,YAAjE,QAAoG,eAApG;AAGA,SAASC,YAAT,QAAoE,MAApE;;;;;;;;;;;;;;;;ICGIC;IACEA;IACFA;;;;;IADkBA;IAAAA,iDAA2B,aAA3B,EAA2BC,iBAA3B,EAA2B,MAA3B,EAA2B,MAA3B;;;;;;IANpBD;IAEEA;IAGAA;IAIFA;;;;;IAPkBA;IAAAA,+CAAyB,MAAzB,EAAyBE,WAAzB;IAGDF;IAAAA;;;;;;;;IAoBbA;IAAkDA;MAAAA;MAAA;MAAA,OAASA,gCAAT;IAAkB,CAAlB;IAChDA;IAAiCA;IAAQA;;;;;;IASzCA;IACGA;IAA0CA;IAACA;;;;;;IAAxCA;IAAAA;;;;;;IAGNA;IACEA;IACFA;;;;;;IADyBA;IAAAA;IAAgCA;;;;;;;;IAEzDA;IACEA;IAIEA;MAAAA;MAAA;MAAA;MAAA;MAAA;MAAA,OAETA;QAAAG;QAAAC;UAAAC;UAAAC,OAC4DC,eAD5D;UACwEC,yBAC1E,SAD0E;QADxE;QAEOC;MAFP,GAFS;IAOZ,CAPY;IAJFT;IAeFA;;;;;;IAbIA;IAAAA;IASAA;IARAA;;;;;;;;;;;;IAhBNA;IAKEA;IAIAA;IAGAA;IAkBAA,kCAA4D,CAA5D,EAA4D,MAA5D,EAA4D,EAA5D;IACiCA;IAAOA;IACtCA;;IACFA;;;;;;IA9BAA;IAEsCA;IAAAA;IAIvBA;IAAAA;IAGAA;IAAAA;IAkBYA;IAAAA;IAEAA;IAAAA;;;;;;;;IAMjCA;IAIEA;MAAAA;MAAA;MAAA,OAAgBA,yCAAhB;IAAiC,CAAjC;IAEDA;;;;;IAJCA,0CAAoB,MAApB,EAAoBU,WAApB,EAAoB,gBAApB,EAAoBA,qBAApB;;;;;;;;;;;;;;;;;;ADxDJ,OAAM,MAAOC,WAAP,CAAkB;EAYtBC,YACSC,WADT,EAESC,OAFT,EAGUC,SAHV,EAIUC,SAJV,EAKSC,OALT,EAMSC,YANT,EAMuC;IAL9B;IACA;IACC;IACA;IACD;IACA;IAjBF,eAAwB,IAAxB;IACA,YAAkB,IAAlB;IACA,kBAAa,IAAb;IACC,qBAAgC,EAAhC;IAED,mBAAc,KAAd;IAKP,kBAAa,IAAIpB,YAAJ,EAAb;EAQI;;EAEJqB,QAAQ,IAAK;;EAEbC,WAAW;IACT,KAAKC,QAAL;IACAC,UAAU,CAAC,MAAK;MACd,KAAKC,gBAAL;;MACA,IAAI,KAAKpB,IAAL,KAAc,KAAKA,IAAL,CAAUqB,WAAV,IAAyB,KAAKrB,IAAL,CAAUsB,aAAjD,CAAJ,EAAqE;QACnE,KAAKC,aAAL;MACD;IACF,CALS,EAKP,CALO,CAAV;EAMD;;EAEDC,WAAW;IACT,KAAKC,aAAL,CAAmBC,OAAnB,CAA4BC,GAAD,IAASA,GAAG,CAACC,WAAJ,EAApC;IACA,KAAKR,gBAAL;EACD;;EAEDS,eAAe;IACb,IAAI,KAAK7B,IAAL,KAAc,KAAKA,IAAL,CAAUqB,WAAV,IAAyB,KAAKrB,IAAL,CAAUsB,aAAjD,CAAJ,EAAqE;MACnE,KAAKC,aAAL;IACD;EACF;;EAEKO,SAAS,CAACC,YAAD,EAAa;IAAA;;IAAA;MAC1B,KAAI,CAACC,YAAL;;MAEA,IAAI,CAAC,KAAI,CAACC,WAAV,EAAuB;QACrB,KAAI,CAACA,WAAL,GAAmB,IAAnB;QAEA,MAAMC,EAAE,GAAGC,MAAM,CAAC,UAAD,CAAjB;QACA,MAAM7B,GAAG,GAAGyB,YAAY,CAACzB,GAAzB;;QACA,IAAIyB,YAAY,CAAC9B,MAAb,CAAoBI,SAAxB,EAAmC;UACjC,IAAI6B,EAAE,CAACE,8BAAP,EAAuC;YACrC,MAAMC,IAAI,SAASH,EAAE,CAACE,8BAAH,EAAnB;;YACA,IAAI,CAACC,IAAL,EAAW;cACT/B,GAAG,CAACgC,MAAJ,CAAWC,OAAX,GAAqB,KAArB;cACA,KAAI,CAACN,WAAL,GAAmB,KAAnB;cACA;YACD;UACF,CAPD,MAOO,IAAI,KAAI,CAACtB,OAAL,CAAa6B,kBAAb,CAAgC,KAAI,CAACxC,IAArC,EAA2C,KAAI,CAACyC,UAAL,CAAgBxC,MAAhB,CAAuByC,KAAlE,CAAJ,EAA8E;YACnFpC,GAAG,CAACgC,MAAJ,CAAWC,OAAX,GAAqB,KAArB;YACA,KAAI,CAACN,WAAL,GAAmB,KAAnB;;YACA,KAAI,CAACvB,WAAL,CAAiBiC,QAAjB,CAA0B,uBAA1B,EAAmD;cAAEC,GAAG,EAAE,KAAI,CAAC5C,IAAL,CAAU6C,OAAV,CAAkBC;YAAzB,CAAnD;;YACA;UACD;QACF;;QAED,KAAI,CAACpC,WAAL,CAAiBqC,kBAAjB,CAAoChB,YAApC;;QACA,KAAI,CAACE,WAAL,GAAmB,KAAnB;MACD;IA1ByB;EA2B3B;;EAEDf,QAAQ;IACN,KAAK8B,OAAL,GAAe,KAAKtC,WAAL,CAAiBuC,cAAjB,EAAf;IACA,KAAKjD,IAAL,GAAY,KAAKW,OAAL,CAAauC,WAAb,CAAyB,KAAKF,OAA9B,EAAuC,KAAKG,MAA5C,CAAZ;IACA,KAAKV,UAAL,GAAkB,KAAK/B,WAAL,CAAiB0C,cAAjB,CAAgC,KAAKD,MAArC,CAAlB;EACD;;EAEDE,cAAc,CAACnD,KAAD,EAAM;IAClB,MAAMuC,UAAU,GAAG,KAAK/B,WAAL,CAAiB0C,cAAjB,CAAgC,KAAKD,MAArC,CAAnB;IACA,OAAO,KAAKzC,WAAL,CAAiB2C,cAAjB,CAAgCZ,UAAhC,EAA4CvC,KAA5C,CAAP;EACD;;EAED8B,YAAY;IACV;IACA,IAAI,KAAKgB,OAAL,CAAaM,OAAb,IAAwB,SAAxB,IAAqC,KAAKH,MAAL,IAAe,KAAKzC,WAAL,CAAiB6C,QAAjB,CAA0BC,eAAlF,EAAmG;MACjG,KAAK9C,WAAL,CAAiB+C,SAAjB,CAA2B;QAAEN,MAAM,EAAE,KAAKA;MAAf,CAA3B;IACD;EACF;;EAEDO,OAAO;IACL,KAAK1B,YAAL;IACA,KAAK2B,UAAL,CAAgBC,IAAhB;EACD;;EAEDrC,aAAa;IACX,KAAKsC,YAAL,GAAoB1B,MAAM,CAAC,GAAD,CAAN,CAAY,oCAAZ,CAApB;IACA,KAAKpB,YAAL,CAAkB+C,gBAAlB,GAAqC,QAArC;IACA3B,MAAM,CAAC,kBAAD,CAAN,GAA6B,QAA7B;IACA,MAAM0B,YAAY,GAAG,KAAKA,YAA1B;IACA,MAAME,OAAO,GAAG,KAAKrD,WAAL,CAAiBsD,aAAjB,EAAhB;IACA,MAAMhE,IAAI,GAAG,KAAKU,WAAL,CAAiBuD,WAAjB,EAAb;IACA,KAAKlD,YAAL,CAAkBQ,aAAlB,CAAgCwC,OAAhC,EAAyC/D,IAAzC,EAA+C6D,YAA/C,EAA6D,KAAKnD,WAAlE;EACD;;EAEDU,gBAAgB;IACd,IAAI,KAAKyC,YAAT,EAAuB;MACrB,KAAK9C,YAAL,CAAkBK,gBAAlB,CAAmC,KAAKyC,YAAxC;IACD;EACF;;AA9GqB;;;mBAAXrD,aAAWX;AAAA;;;QAAXW;EAAW0D;EAAAC;IAAAhB;IAAAiB;EAAA;EAAAC;IAAAV;EAAA;EAAAW;EAAAC;EAAAC;EAAAC;EAAAC;IAAA;MCfxB7E;MACEA;MAUAA,+BAIC,CAJD,EAIC,KAJD,EAIC,CAJD;MAMIA;MACAA,gCAAwB,CAAxB,EAAwB,GAAxB,EAAwB,CAAxB;MACwBA;MAAgBA;MAExCA;;MAKAA;MAGFA;MACAA;MACEA;MAmCFA;MAGFA;MAOFA;;;;MA3EiCA;MAAAA;MAa7BA;MAAAA;MADAA;MAM0BA;MAAAA;MAItBA;MAAAA,2FAA0C,SAA1C,EAA0CA,2EAA1C;MAGoBA;MAAAA;MAOJA;MAAAA;MAqCnBA;MAAAA", "names": ["EventEmitter", "Subscription", "i0", "ctx_r4", "ctx_r0", "item", "answer", "optId", "index", "i_r8", "isChecked", "evt", "ctx_r3", "McComponent", "constructor", "examService", "formSer", "_alertSvc", "translate", "utilSer", "annotatorSer", "ngOnInit", "ngOnChanges", "initData", "setTimeout", "destroyAnnotator", "annotatable", "highlightable", "initAnnotator", "ngOnDestroy", "subscriptions", "for<PERSON>ach", "sub", "unsubscribe", "ngAfterViewInit", "changeOpt", "responseInfo", "enterNewItem", "updatingRes", "jt", "window", "checkItemOptionsWhenChangeOpts", "pass", "target", "checked", "moreThanMaxChoices", "resCurItem", "value", "alertTip", "max", "content", "max_choices", "updateItemResponse", "section", "currentSection", "getItemById", "itemId", "getItemResById", "initOptChecked", "display", "response", "current_item_id", "enterItem", "showRef", "showRefEvt", "emit", "annotatorEle", "annotatorBgColor", "itemRes", "getResCurItem", "currentItem", "selectors", "inputs", "showGroupInstr", "outputs", "features", "decls", "vars", "consts", "template"], "sourceRoot": "", "sources": ["D:\\work\\joyserver\\client\\src\\app\\exam\\exam-sailfish\\item\\mc\\mc.component.ts", "D:\\work\\joyserver\\client\\src\\app\\exam\\exam-sailfish\\item\\mc\\mc.component.html"], "sourcesContent": ["import { Component, Input, OnChang<PERSON>, On<PERSON>nit, On<PERSON><PERSON>roy, Output, EventEmitter, AfterViewInit } from \"@angular/core\";\nimport { ExamService } from \"@core-service/exam.service\";\nimport { Exam } from \"@core-types/exam.types\";\nimport { Subscription, Observable, timer, Subject, fromEvent } from \"rxjs\";\nimport { UtilService } from \"@core/service/utils/util.service\";\nimport { FormService } from \"@core/service/form.service\";\nimport { CustomAlertService } from \"@core/modal/custom-alert/custom-alert.service\";\nimport { TranslateService } from \"@ngx-translate/core\";\nimport { AnnotatorService } from \"@core/service/annotator.service\";\n\n@Component({\n  selector: \"mc\",\n  templateUrl: \"./mc.component.html\",\n  styleUrls: [\"./mc.component.scss\"],\n})\nexport class McComponent implements OnInit, OnChanges, OnDestroy, AfterViewInit {\n  public section: Exam.Section = null;\n  public item: Exam.Item = null;\n  public resCurItem = null;\n  private subscriptions: Subscription[] = [];\n  private annotatorEle: Element;\n  public updatingRes = false;\n\n  @Input() itemId;\n  @Input() showGroupInstr;\n  @Output()\n  showRefEvt = new EventEmitter();\n  constructor(\n    public examService: ExamService,\n    public formSer: FormService,\n    private _alertSvc: CustomAlertService,\n    private translate: TranslateService,\n    public utilSer: UtilService,\n    public annotatorSer: AnnotatorService\n  ) {}\n\n  ngOnInit() {}\n\n  ngOnChanges() {\n    this.initData();\n    setTimeout(() => {\n      this.destroyAnnotator();\n      if (this.item && (this.item.annotatable || this.item.highlightable)) {\n        this.initAnnotator();\n      }\n    }, 0);\n  }\n\n  ngOnDestroy() {\n    this.subscriptions.forEach((sub) => sub.unsubscribe());\n    this.destroyAnnotator();\n  }\n\n  ngAfterViewInit() {\n    if (this.item && (this.item.annotatable || this.item.highlightable)) {\n      this.initAnnotator();\n    }\n  }\n\n  async changeOpt(responseInfo) {\n    this.enterNewItem();\n\n    if (!this.updatingRes) {\n      this.updatingRes = true;\n\n      const jt = window[\"JTCustom\"];\n      const evt = responseInfo.evt;\n      if (responseInfo.answer.isChecked) {\n        if (jt.checkItemOptionsWhenChangeOpts) {\n          const pass = await jt.checkItemOptionsWhenChangeOpts();\n          if (!pass) {\n            evt.target.checked = false;\n            this.updatingRes = false;\n            return;\n          }\n        } else if (this.formSer.moreThanMaxChoices(this.item, this.resCurItem.answer.value)) {\n          evt.target.checked = false;\n          this.updatingRes = false;\n          this.examService.alertTip(\"exam.mc.maxChoicesTip\", { max: this.item.content.max_choices });\n          return;\n        }\n      }\n\n      this.examService.updateItemResponse(responseInfo);\n      this.updatingRes = false;\n    }\n  }\n\n  initData() {\n    this.section = this.examService.currentSection();\n    this.item = this.formSer.getItemById(this.section, this.itemId) as Exam.Item;\n    this.resCurItem = this.examService.getItemResById(this.itemId);\n  }\n\n  initOptChecked(optId) {\n    const resCurItem = this.examService.getItemResById(this.itemId);\n    return this.examService.initOptChecked(resCurItem, optId);\n  }\n\n  enterNewItem() {\n    // 按组展示模式，如果作答题非当前题\n    if (this.section.display == \"bygroup\" && this.itemId != this.examService.response.current_item_id) {\n      this.examService.enterItem({ itemId: this.itemId });\n    }\n  }\n\n  showRef() {\n    this.enterNewItem();\n    this.showRefEvt.emit();\n  }\n\n  initAnnotator() {\n    this.annotatorEle = window[\"$\"](\".wrap-item-cont .joy-item-material\");\n    this.annotatorSer.annotatorBgColor = \"yellow\";\n    window[\"annotatorBgColor\"] = \"yellow\";\n    const annotatorEle = this.annotatorEle;\n    const itemRes = this.examService.getResCurItem();\n    const item = this.examService.currentItem() as Exam.Item;\n    this.annotatorSer.initAnnotator(itemRes, item, annotatorEle, this.examService);\n  }\n\n  destroyAnnotator() {\n    if (this.annotatorEle) {\n      this.annotatorSer.destroyAnnotator(this.annotatorEle);\n    }\n  }\n}\n", "<div class=\"item item-mc\">\n  <div class=\"wrap-audio-timer\" *ngIf=\"item.audio?.url\">\n    <!--试题倒计时-->\n    <app-item-timer [resCurItem]=\"resCurItem\" [item]=\"item\"></app-item-timer>\n\n    <!--播放音频 S-->\n    <ng-container *ngIf=\"item.audio?.url\">\n      <app-item-audio [audioUrl]=\"item.audio.url\" [audioConfig]=\"item.audio\" [type]=\"'item'\"></app-item-audio>\n    </ng-container>\n    <!--播放音频 E-->\n  </div>\n  <div\n    class=\"wrap-item-cont zoom-cont pl-3 pr-3 pt-2 pb-2\"\n    [ngClass]=\"{ 'has-audio': !!item.audio?.url, 'has-annotator': item.annotatable || item.highlightable }\"\n    id=\"item-{{ item.id }}\"\n  >\n    <div class=\"item-head pt-2 pb-3 clearfix\">\n      <div class=\"text-right time-count-down\"></div>\n      <span class=\"item-prop\">\n        <i class=\"item-index\">{{ item.index }}</i>\n      </span>\n      <div\n        class=\"item-stem joy-item-material\"\n        [innerHTML]=\"item.content.stem | safeHtml\"\n        [ngClass]=\"{ 'only-highlightable': !item.annotatable, 'only-annotatable': !item.highlightable }\"\n      ></div>\n      <div class=\"ref-btn\" *ngIf=\"formSer.hasRef(item)\" (click)=\"showRef()\">\n        <i class=\"iconfont text-primary\">&#xe62b;</i>\n      </div>\n    </div>\n    <div class=\"item-cont\">\n      <div\n        class=\"option\"\n        *ngFor=\"let opt of item.content.options; let i = index\"\n        [ngClass]=\"{ 'option-checked': initOptChecked(opt.id) }\"\n      >\n        <span class=\"optId font-weight-bold\" *ngIf=\"examService.privacy_strip\"\n          ><b [innerHTML]=\"utilSer.initOptId(i)\"></b>.</span\n        >\n\n        <ng-container *ngIf=\"examService.privacy_strip\">\n          <input type=\"checkbox\" id=\"{{ item.id }}-{{ opt.id }}\" name=\"{{ item.id }}\" order=\"1\" class=\"origin-input\" />\n        </ng-container>\n        <ng-container *ngIf=\"!examService.privacy_strip\">\n          <input\n            type=\"checkbox\"\n            id=\"{{ item.id }}-{{ opt.id }}\"\n            [checked]=\"initOptChecked(opt.id)\"\n            (change)=\"\n              changeOpt({\n                item: item,\n                answer: { optId: opt.id, index: i.toString(), isChecked: $event.target['checked'] },\n                evt: $event\n              })\n            \"\n            name=\"{{ item.id }}\"\n            order=\"1\"\n            class=\"origin-input\"\n          />\n        </ng-container>\n\n        <label class=\"option-cont\" for=\"{{ item.id }}-{{ opt.id }}\">\n          <span class=\"custome-checkbox\"><i></i></span>\n          <span class=\"option-txt\" [innerHTML]=\"opt.description | safeHtml\"></span>\n        </label>\n      </div>\n    </div>\n  </div>\n  <!--防窥条答题区-->\n  <app-privacy-strip\n    *ngIf=\"examService.privacy_strip\"\n    [index]=\"item.index\"\n    [item]=\"item\"\n    (changeOptEvt)=\"changeOpt($event)\"\n    [showGroupInstr]=\"showGroupInstr\"\n  ></app-privacy-strip>\n</div>\n"]}, "metadata": {}, "sourceType": "module"}