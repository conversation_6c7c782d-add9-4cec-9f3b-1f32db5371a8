{"ast": null, "code": "import _asyncToGenerator from \"D:/work/joyserver/client/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { ElementRef } from \"@angular/core\";\nimport { lastValueFrom, timer } from \"rxjs\";\nimport { Exam } from \"@core-types/exam.types\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@core/service/exam.service\";\nimport * as i2 from \"@core/service/response.service\";\nimport * as i3 from \"@core/service/utils/util.service\";\nimport * as i4 from \"@core/service/form.service\";\nimport * as i5 from \"@core/service/jtcustom.service\";\nimport * as i6 from \"@core/service/auto-test.service\";\nimport * as i7 from \"@ngx-translate/core\";\nimport * as i8 from \"@angular/common\";\nimport * as i9 from \"../../../core/modal/custom-confirm/custom-confirm.component\";\nimport * as i10 from \"./item-block/item-block.component\";\nimport * as i11 from \"../../../core/pipe/domSanitized.pipe\";\nconst _c0 = [\"templateIframe\"];\n\nfunction MqSelectItemsComponent_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"iframe\", 1, 2);\n    i0.ɵɵelementContainerEnd();\n  }\n}\n\nfunction MqSelectItemsComponent_ng_container_1_div_1_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 11);\n    i0.ɵɵelement(1, \"app-item-block\", 12);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const item_r6 = ctx.$implicit;\n    const i_r7 = ctx.index;\n    const ctx_r5 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"item\", item_r6)(\"index\", i_r7)(\"group\", ctx_r5.mq)(\"itemsSelected\", ctx_r5.itemsSelected);\n  }\n}\n\nconst _c1 = function (a0) {\n  return {\n    disabled: a0\n  };\n};\n\nfunction MqSelectItemsComponent_ng_container_1_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 5)(1, \"div\", 6);\n    i0.ɵɵelement(2, \"div\", 7);\n    i0.ɵɵpipe(3, \"safeHtml\");\n    i0.ɵɵtemplate(4, MqSelectItemsComponent_ng_container_1_div_1_div_4_Template, 2, 4, \"div\", 8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 9)(6, \"button\", 10);\n    i0.ɵɵlistener(\"click\", function MqSelectItemsComponent_ng_container_1_div_1_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r8 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r8.btnConfirm());\n    });\n    i0.ɵɵtext(7);\n    i0.ɵɵpipe(8, \"translate\");\n    i0.ɵɵelementEnd()()();\n  }\n\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"innerHTML\", i0.ɵɵpipeBind1(3, 4, ctx_r3.mq == null ? null : ctx_r3.mq.selection == null ? null : ctx_r3.mq.selection.title), i0.ɵɵsanitizeHtml);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.mq == null ? null : ctx_r3.mq.content == null ? null : ctx_r3.mq.content.items);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(8, _c1, ctx_r3.itemsSelected.length < (ctx_r3.mq == null ? null : ctx_r3.mq.selection == null ? null : ctx_r3.mq.selection.manual)));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(8, 6, \"exam.groupSelection.confirm\"), \" \");\n  }\n}\n\nfunction MqSelectItemsComponent_ng_container_1_custom_confirm_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"custom-confirm\", 13);\n    i0.ɵɵlistener(\"onConfirm\", function MqSelectItemsComponent_ng_container_1_custom_confirm_2_Template_custom_confirm_onConfirm_0_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r10.modalConfirm());\n    })(\"onCancel\", function MqSelectItemsComponent_ng_container_1_custom_confirm_2_Template_custom_confirm_onCancel_0_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r12 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r12.modalCancel());\n    });\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"bodyText\", ctx_r4.tipCont);\n  }\n}\n\nfunction MqSelectItemsComponent_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, MqSelectItemsComponent_ng_container_1_div_1_Template, 9, 10, \"div\", 3);\n    i0.ɵɵtemplate(2, MqSelectItemsComponent_ng_container_1_custom_confirm_2_Template, 1, 1, \"custom-confirm\", 4);\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.responseSer.mqHasSelectedItems(ctx_r1.mq == null ? null : ctx_r1.mq.id));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.showTip);\n  }\n}\n\nexport class MqSelectItemsComponent {\n  constructor(examSer, responseSer, utilSer, formSer, jtcostmeSer, autoTestSer, translate) {\n    this.examSer = examSer;\n    this.responseSer = responseSer;\n    this.utilSer = utilSer;\n    this.formSer = formSer;\n    this.jtcostmeSer = jtcostmeSer;\n    this.autoTestSer = autoTestSer;\n    this.translate = translate;\n    this.showTip = false;\n    this.tipCont = \"\";\n    this.itemsSelected = [];\n    this.subscriptions = [];\n  }\n\n  ngOnInit() {\n    this.subscriptions.push(this.examSer.examEvents.subscribe(event => {\n      if (event.type === Exam.Event.SelectItemsFromMq) {\n        if (typeof event.data !== \"string\") {\n          this.mq = event.data;\n          const skin = this.examSer.session.config.skin;\n          const template = this.mq.selection.template;\n          this.templateUrl = \"/seat/skin/\" + skin + \"/template/\" + template + \".html\";\n          this.itemsSelected = []; // set cur mq id for template in skin\n\n          this.jtcostmeSer.mqForSelectItems = this.mq;\n          console.log(\"mqForSelectItems\", this.jtcostmeSer.mqForSelectItems);\n          this.reloadIframe();\n        }\n      }\n    })); // 如果已经开启自动试考\n\n    if (this.examSer.autoTestOn) {\n      this.autoTest();\n    } else {\n      // 如果进入当前页才开始开启自动试考\n      this.subscriptions.push(this.examSer.examEventsFromManager.subscribe(result => {\n        if (result.type === Exam.EventManager.AutoTest) {\n          if (!this.examSer.autoTestOn) {\n            this.autoTest();\n          }\n        }\n      }));\n    }\n  }\n\n  set content(content) {\n    if (content) {\n      this.iframeDom = content.nativeElement;\n      this.examSer.loadIframe(this.iframeDom, this.templateUrl, true);\n    }\n  }\n\n  ngOnDestroy() {\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n\n    if (this.iframeDom) {\n      window.focus();\n    }\n  }\n\n  autoTest() {\n    if (this.autoTestSub) {\n      this.autoTestSub.unsubscribe();\n    }\n\n    this.autoTestSub = timer(1000, 3000).subscribe(t => {\n      this.autoTestSer.test({\n        pageType: \"select-items\",\n        extraData: {\n          manual: this.mq.selection.manual,\n          optionsLength: this.mq.content.items.length\n        }\n      });\n    });\n    this.subscriptions.push(this.autoTestSub);\n  }\n\n  btnConfirm() {\n    var _this = this;\n\n    return _asyncToGenerator(function* () {\n      if (_this.itemsSelected.length === _this.mq.selection.manual) {\n        _this.tipCont = yield _this.getTipCont();\n        _this.showTip = true;\n      }\n    })();\n  }\n\n  getTipCont() {\n    var _this2 = this;\n\n    return _asyncToGenerator(function* () {\n      let tip = _this2.translate.instant(\"exam.groupSelection.selectedItems\");\n\n      for (let i = 0, len = _this2.mq.content.items.length; i < len; i++) {\n        const item = _this2.mq.content.items[i];\n\n        if (_this2.itemsSelected.indexOf(item[\"id\"]) > -1) {\n          yield function () {\n            var _ref = _asyncToGenerator(function* (i) {\n              const index = _this2.getTitleIndex(i);\n\n              const tipContTrans = yield lastValueFrom(_this2.translate.get(\"exam.groupSelection.titleName\", {\n                titleNum: index\n              }));\n              tip += tipContTrans + \"、\";\n            });\n\n            return function (_x) {\n              return _ref.apply(this, arguments);\n            };\n          }()(i);\n        }\n      }\n\n      tip = tip.substr(0, tip.length - 1);\n      tip += \"<div class='font-min mt-3 text-center'><label><input type='checkbox' id='selection-check-confirm' class='mr-1 align-middle'>\" + _this2.translate.instant(\"exam.groupSelection.checkConfirm\") + \"</label></div>\";\n      return tip;\n    })();\n  }\n\n  modalConfirm() {\n    // 检查是否勾选确认\n    const btnCheckConfirm = document.getElementById(\"selection-check-confirm\");\n\n    if (!btnCheckConfirm.checked) {\n      return;\n    }\n\n    this.showTip = false;\n    const mq = this.mq;\n    this.examSer.setMqItemSelected(mq, this.itemsSelected);\n  }\n\n  modalCancel() {\n    this.showTip = false;\n  }\n\n  reloadIframe() {\n    if (this.iframeDom) {\n      this.iframeDom.src = \"\";\n      this.examSer.loadIframe(this.iframeDom, this.templateUrl, true);\n    }\n  }\n\n  getTitleIndex(i) {\n    const chineseLan = [\"zh\", \"zh-hk\"];\n    return chineseLan.indexOf(this.utilSer.langSet) > -1 ? this.utilSer.numberConvertToUppercase(i + 1) : i + 1;\n  }\n\n}\n\nMqSelectItemsComponent.ɵfac = function MqSelectItemsComponent_Factory(t) {\n  return new (t || MqSelectItemsComponent)(i0.ɵɵdirectiveInject(i1.ExamService), i0.ɵɵdirectiveInject(i2.ResponseService), i0.ɵɵdirectiveInject(i3.UtilService), i0.ɵɵdirectiveInject(i4.FormService), i0.ɵɵdirectiveInject(i5.JTCustomService), i0.ɵɵdirectiveInject(i6.AutoTestService), i0.ɵɵdirectiveInject(i7.TranslateService));\n};\n\nMqSelectItemsComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: MqSelectItemsComponent,\n  selectors: [[\"app-mq-select-items\"]],\n  viewQuery: function MqSelectItemsComponent_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(_c0, 5);\n    }\n\n    if (rf & 2) {\n      let _t;\n\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.content = _t.first);\n    }\n  },\n  decls: 2,\n  vars: 2,\n  consts: [[4, \"ngIf\"], [1, \"template-iframe\"], [\"templateIframe\", \"\"], [\"class\", \"select-items\", 4, \"ngIf\"], [\"class\", \"confirmSelectionModal\", 3, \"bodyText\", \"onConfirm\", \"onCancel\", 4, \"ngIf\"], [1, \"select-items\"], [1, \"sel-items-cont\"], [1, \"select-tip\", \"p-3\", 3, \"innerHTML\"], [\"class\", \"sel-item-block selection-name-group\", 4, \"ngFor\", \"ngForOf\"], [1, \"btn-confirm\", \"text-center\"], [1, \"yk-btn-primary\", \"btn-confirm-selection\", 3, \"ngClass\", \"click\"], [1, \"sel-item-block\", \"selection-name-group\"], [3, \"item\", \"index\", \"group\", \"itemsSelected\"], [1, \"confirmSelectionModal\", 3, \"bodyText\", \"onConfirm\", \"onCancel\"]],\n  template: function MqSelectItemsComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵtemplate(0, MqSelectItemsComponent_ng_container_0_Template, 3, 0, \"ng-container\", 0);\n      i0.ɵɵtemplate(1, MqSelectItemsComponent_ng_container_1_Template, 3, 2, \"ng-container\", 0);\n    }\n\n    if (rf & 2) {\n      i0.ɵɵproperty(\"ngIf\", ctx.mq == null ? null : ctx.mq.selection == null ? null : ctx.mq.selection.template);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", !(ctx.mq == null ? null : ctx.mq.selection == null ? null : ctx.mq.selection.template));\n    }\n  },\n  dependencies: [i8.NgClass, i8.NgForOf, i8.NgIf, i9.CustomConfirmComponent, i10.ItemBlockComponent, i11.SafeHtmlPipe, i7.TranslatePipe],\n  styles: [\".select-items[_ngcontent-%COMP%] {\\n        background-color: #fff;\\n        position: absolute;\\n        width: 100%;\\n        bottom: 0;\\n        top: 0;\\n        z-index: 3;\\n        .sel-items-cont {\\n          position: absolute;\\n          width: 100%;\\n          bottom: 50px;\\n          top: 0;\\n          overflow: auto;\\n        }\\n\\n        .btn-confirm {\\n          position: absolute;\\n          width: 100%;\\n          bottom: 10px;\\n          & > button.disabled {\\n            background-color: rgba(68, 125, 255, 0.7);\\n            border: 1px solid rgba(68, 125, 255, 0.7);\\n          }\\n        }\\n        .sel-item-block:last-of-type {\\n          ::ng-deep {\\n            .title {\\n              border-bottom: 1px solid #ccc !important;\\n            }\\n          }\\n        }\\n      }\\n\\n      iframe.template-iframe[_ngcontent-%COMP%] {\\n        position: absolute;\\n        top: 0;\\n        left: 0;\\n        bottom: 0;\\n        right: 0;\\n        height: 100%;\\n        width: 100%;\\n        border: none;\\n      }\\n/*# sourceMappingURL=data:application/json;base64,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 */\"]\n});", "map": {"version": 3, "mappings": ";AAAA,SAAoBA,UAApB,QAAoE,eAApE;AAOA,SAASC,aAAT,EAAsCC,KAAtC,QAAmD,MAAnD;AACA,SAASC,IAAT,QAAqB,wBAArB;;;;;;;;;;;;;;;;;IAMIC;IACEA;IACFA;;;;;;IAOMA;IACEA;IACFA;;;;;;;IADkBA;IAAAA,+BAAa,OAAb,EAAaC,IAAb,EAAa,OAAb,EAAaC,SAAb,EAAa,eAAb,EAAaA,oBAAb;;;;;;;;;;;;;;IAJtBF,+BAA0E,CAA1E,EAA0E,KAA1E,EAA0E,CAA1E;IAEIA;;IACAA;IAGFA;IAEAA,+BAAqC,CAArC,EAAqC,QAArC,EAAqC,EAArC;IAIIA;MAAAA;MAAA;MAAA,OAASA,mCAAT;IAAqB,CAArB;IAEAA;;IACFA;;;;;IAb4BA;IAAAA;IACsCA;IAAAA;IAQhEA;IAAAA;IAGAA;IAAAA;;;;;;;;IAKNA;IAIEA;MAAAA;MAAA;MAAA,OAAaA,sCAAb;IAA2B,CAA3B,EAA4B,UAA5B,EAA4B;MAAAA;MAAA;MAAA,OAChBA,qCADgB;IACH,CADzB;IAGFA;;;;;IAJEA;;;;;;IAvBJA;IACEA;IAmBAA;IAQFA;;;;;IA3B6BA;IAAAA;IAqBxBA;IAAAA;;;;AAwDT,OAAM,MAAOG,sBAAP,CAA6B;EAUjCC,YACSC,OADT,EAESC,WAFT,EAGSC,OAHT,EAIUC,OAJV,EAKUC,WALV,EAMUC,WANV,EAOUC,SAPV,EAOqC;IAN5B;IACA;IACA;IACC;IACA;IACA;IACA;IAfH,eAAU,KAAV;IACA,eAAU,EAAV;IACA,qBAAgB,EAAhB;IAKC,qBAAgC,EAAhC;EASJ;;EAEJC,QAAQ;IACN,KAAKC,aAAL,CAAmBC,IAAnB,CACE,KAAKT,OAAL,CAAaU,UAAb,CAAwBC,SAAxB,CAAmCC,KAAD,IAAU;MAC1C,IAAIA,KAAK,CAACC,IAAN,KAAenB,IAAI,CAACoB,KAAL,CAAWC,iBAA9B,EAAiD;QAC/C,IAAI,OAAOH,KAAK,CAACI,IAAb,KAAsB,QAA1B,EAAoC;UAClC,KAAKC,EAAL,GAAUL,KAAK,CAACI,IAAhB;UACA,MAAME,IAAI,GAAG,KAAKlB,OAAL,CAAamB,OAAb,CAAqBC,MAArB,CAA4BF,IAAzC;UACA,MAAMG,QAAQ,GAAG,KAAKJ,EAAL,CAAQK,SAAR,CAAkBD,QAAnC;UACA,KAAKE,WAAL,GAAmB,gBAAgBL,IAAhB,GAAuB,YAAvB,GAAsCG,QAAtC,GAAiD,OAApE;UACA,KAAKG,aAAL,GAAqB,EAArB,CALkC,CAOlC;;UACA,KAAKpB,WAAL,CAAiBqB,gBAAjB,GAAoC,KAAKR,EAAzC;UACAS,OAAO,CAACC,GAAR,CAAY,kBAAZ,EAAgC,KAAKvB,WAAL,CAAiBqB,gBAAjD;UACA,KAAKG,YAAL;QACD;MACF;IACF,CAfD,CADF,EADM,CAoBN;;IACA,IAAI,KAAK5B,OAAL,CAAa6B,UAAjB,EAA6B;MAC3B,KAAKC,QAAL;IACD,CAFD,MAEO;MACL;MACA,KAAKtB,aAAL,CAAmBC,IAAnB,CACE,KAAKT,OAAL,CAAa+B,qBAAb,CAAmCpB,SAAnC,CAA8CqB,MAAD,IAAW;QACtD,IAAIA,MAAM,CAACnB,IAAP,KAAgBnB,IAAI,CAACuC,YAAL,CAAkBC,QAAtC,EAAgD;UAC9C,IAAI,CAAC,KAAKlC,OAAL,CAAa6B,UAAlB,EAA8B;YAC5B,KAAKC,QAAL;UACD;QACF;MACF,CAND,CADF;IASD;EACF;;EAEuC,IAAPK,OAAO,CAACA,OAAD,EAAoB;IAC1D,IAAIA,OAAJ,EAAa;MACX,KAAKC,SAAL,GAAiBD,OAAO,CAACE,aAAzB;MACA,KAAKrC,OAAL,CAAasC,UAAb,CAAwB,KAAKF,SAA7B,EAAwC,KAAKb,WAA7C,EAA0D,IAA1D;IACD;EACF;;EAEDgB,WAAW;IACT,KAAK/B,aAAL,CAAmBgC,OAAnB,CAA4BC,GAAD,IAASA,GAAG,CAACC,WAAJ,EAApC;;IACA,IAAI,KAAKN,SAAT,EAAoB;MAClBO,MAAM,CAACC,KAAP;IACD;EACF;;EAEDd,QAAQ;IACN,IAAI,KAAKe,WAAT,EAAsB;MACpB,KAAKA,WAAL,CAAiBH,WAAjB;IACD;;IACD,KAAKG,WAAL,GAAmBpD,KAAK,CAAC,IAAD,EAAO,IAAP,CAAL,CAAkBkB,SAAlB,CAA6BmC,CAAD,IAAM;MACnD,KAAKzC,WAAL,CAAiB0C,IAAjB,CAAsB;QACpBC,QAAQ,EAAE,cADU;QAEpBC,SAAS,EAAE;UAAEC,MAAM,EAAE,KAAKjC,EAAL,CAAQK,SAAR,CAAkB4B,MAA5B;UAAoCC,aAAa,EAAE,KAAKlC,EAAL,CAAQkB,OAAR,CAAgBiB,KAAhB,CAAsBC;QAAzE;MAFS,CAAtB;IAID,CALkB,CAAnB;IAMA,KAAK7C,aAAL,CAAmBC,IAAnB,CAAwB,KAAKoC,WAA7B;EACD;;EAEKS,UAAU;IAAA;;IAAA;MACd,IAAI,KAAI,CAAC9B,aAAL,CAAmB6B,MAAnB,KAA8B,KAAI,CAACpC,EAAL,CAAQK,SAAR,CAAkB4B,MAApD,EAA4D;QAC1D,KAAI,CAACK,OAAL,SAAqB,KAAI,CAACC,UAAL,EAArB;QACA,KAAI,CAACC,OAAL,GAAe,IAAf;MACD;IAJa;EAKf;;EAEKD,UAAU;IAAA;;IAAA;MACd,IAAIE,GAAG,GAAG,MAAI,CAACpD,SAAL,CAAeqD,OAAf,CAAuB,mCAAvB,CAAV;;MAEA,KAAK,IAAIC,CAAC,GAAG,CAAR,EAAWC,GAAG,GAAG,MAAI,CAAC5C,EAAL,CAAQkB,OAAR,CAAgBiB,KAAhB,CAAsBC,MAA5C,EAAoDO,CAAC,GAAGC,GAAxD,EAA6DD,CAAC,EAA9D,EAAkE;QAChE,MAAME,IAAI,GAAG,MAAI,CAAC7C,EAAL,CAAQkB,OAAR,CAAgBiB,KAAhB,CAAsBQ,CAAtB,CAAb;;QACA,IAAI,MAAI,CAACpC,aAAL,CAAmBuC,OAAnB,CAA2BD,IAAI,CAAC,IAAD,CAA/B,IAAyC,CAAC,CAA9C,EAAiD;UAC/C,MAAM;YAAA,6BAAC,WAAOF,CAAP,EAAY;cACjB,MAAMI,KAAK,GAAG,MAAI,CAACC,aAAL,CAAmBL,CAAnB,CAAd;;cACA,MAAMM,YAAY,SAAS1E,aAAa,CACtC,MAAI,CAACc,SAAL,CAAe6D,GAAf,CAAmB,+BAAnB,EAAoD;gBAAEC,QAAQ,EAAEJ;cAAZ,CAApD,CADsC,CAAxC;cAGAN,GAAG,IAAIQ,YAAY,GAAG,GAAtB;YACD,CANK;;YAAA;cAAA;YAAA;UAAA,IAMHN,CANG,CAAN;QAOD;MACF;;MACDF,GAAG,GAAGA,GAAG,CAACW,MAAJ,CAAW,CAAX,EAAcX,GAAG,CAACL,MAAJ,GAAa,CAA3B,CAAN;MACAK,GAAG,IACD,iIACA,MAAI,CAACpD,SAAL,CAAeqD,OAAf,CAAuB,kCAAvB,CADA,GAEA,gBAHF;MAIA,OAAOD,GAAP;IApBc;EAqBf;;EACDY,YAAY;IACV;IACA,MAAMC,eAAe,GAAGC,QAAQ,CAACC,cAAT,CAAwB,yBAAxB,CAAxB;;IACA,IAAI,CAACF,eAAe,CAACG,OAArB,EAA8B;MAC5B;IACD;;IAED,KAAKjB,OAAL,GAAe,KAAf;IAEA,MAAMxC,EAAE,GAAG,KAAKA,EAAhB;IACA,KAAKjB,OAAL,CAAa2E,iBAAb,CAA+B1D,EAA/B,EAAmC,KAAKO,aAAxC;EACD;;EAEDoD,WAAW;IACT,KAAKnB,OAAL,GAAe,KAAf;EACD;;EAED7B,YAAY;IACV,IAAI,KAAKQ,SAAT,EAAoB;MAClB,KAAKA,SAAL,CAAeyC,GAAf,GAAqB,EAArB;MACA,KAAK7E,OAAL,CAAasC,UAAb,CAAwB,KAAKF,SAA7B,EAAwC,KAAKb,WAA7C,EAA0D,IAA1D;IACD;EACF;;EAED0C,aAAa,CAACL,CAAD,EAAE;IACb,MAAMkB,UAAU,GAAG,CAAC,IAAD,EAAO,OAAP,CAAnB;IACA,OAAOA,UAAU,CAACf,OAAX,CAAmB,KAAK7D,OAAL,CAAa6E,OAAhC,IAA2C,CAAC,CAA5C,GAAgD,KAAK7E,OAAL,CAAa8E,wBAAb,CAAsCpB,CAAC,GAAG,CAA1C,CAAhD,GAA+FA,CAAC,GAAG,CAA1G;EACD;;AA5IgC;;;mBAAtB9D,wBAAsBH;AAAA;;;QAAtBG;EAAsBmF;EAAAC;IAAA;;;;;;;;;;;;;;;MAnF/BvF;MAKAA;;;;MALeA;MAKAA;MAAAA", "names": ["ElementRef", "lastValueFrom", "timer", "Exam", "i0", "i_r7", "ctx_r5", "MqSelectItemsComponent", "constructor", "examSer", "responseSer", "utilSer", "formSer", "jtcostmeSer", "autoTestSer", "translate", "ngOnInit", "subscriptions", "push", "examEvents", "subscribe", "event", "type", "Event", "SelectItemsFromMq", "data", "mq", "skin", "session", "config", "template", "selection", "templateUrl", "itemsSelected", "mqForSelectItems", "console", "log", "reloadIframe", "autoTestOn", "autoTest", "examEventsFromManager", "result", "EventManager", "AutoTest", "content", "iframeDom", "nativeElement", "loadIframe", "ngOnDestroy", "for<PERSON>ach", "sub", "unsubscribe", "window", "focus", "autoTestSub", "t", "test", "pageType", "extraData", "manual", "optionsLength", "items", "length", "btnConfirm", "tipCont", "getTipCont", "showTip", "tip", "instant", "i", "len", "item", "indexOf", "index", "getTitleIndex", "tipContTrans", "get", "titleNum", "substr", "modalConfirm", "btnCheckConfirm", "document", "getElementById", "checked", "setMqItemSelected", "modalCancel", "src", "chineseLan", "langSet", "numberConvertToUppercase", "selectors", "viewQuery"], "sourceRoot": "", "sources": ["D:\\work\\joyserver\\client\\src\\app\\exam\\exam-sailfish\\select-items\\mq-select-items.component.ts"], "sourcesContent": ["import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit, ViewChild } from \"@angular/core\";\nimport { ExamService } from \"@core/service/exam.service\";\nimport { ResponseService } from \"@core/service/response.service\";\nimport { UtilService } from \"@core/service/utils/util.service\";\nimport { FormService } from \"@core/service/form.service\";\nimport { JTCustomService } from \"@core/service/jtcustom.service\";\nimport { AutoTestService } from \"@core/service/auto-test.service\";\nimport { lastValueFrom, Subscription, timer } from \"rxjs\";\nimport { Exam } from \"@core-types/exam.types\";\nimport { TranslateService } from \"@ngx-translate/core\";\n@Component({\n  selector: \"app-mq-select-items\",\n  template: `\n    <!--selection 配置了template-->\n    <ng-container *ngIf=\"mq?.selection?.template\">\n      <iframe #templateIframe class=\"template-iframe\"></iframe>\n    </ng-container>\n\n    <!--selection 没配置了template-->\n    <ng-container *ngIf=\"!mq?.selection?.template\">\n      <div class=\"select-items\" *ngIf=\"!responseSer.mqHasSelectedItems(mq?.id)\">\n        <div class=\"sel-items-cont\">\n          <div class=\"select-tip p-3\" [innerHTML]=\"mq?.selection?.title | safeHtml\"></div>\n          <div class=\"sel-item-block selection-name-group\" *ngFor=\"let item of mq?.content?.items; let i = index\">\n            <app-item-block [item]=\"item\" [index]=\"i\" [group]=\"mq\" [itemsSelected]=\"itemsSelected\"></app-item-block>\n          </div>\n        </div>\n\n        <div class=\"btn-confirm text-center\">\n          <button\n            class=\"yk-btn-primary btn-confirm-selection\"\n            [ngClass]=\"{ disabled: itemsSelected.length < mq?.selection?.manual }\"\n            (click)=\"btnConfirm()\"\n          >\n            {{ \"exam.groupSelection.confirm\" | translate }}\n          </button>\n        </div>\n      </div>\n\n      <custom-confirm\n        class=\"confirmSelectionModal\"\n        *ngIf=\"showTip\"\n        [bodyText]=\"tipCont\"\n        (onConfirm)=\"modalConfirm()\"\n        (onCancel)=\"modalCancel()\"\n      >\n      </custom-confirm>\n    </ng-container>\n  `,\n  styles: [\n    `\n      .select-items {\n        background-color: #fff;\n        position: absolute;\n        width: 100%;\n        bottom: 0;\n        top: 0;\n        z-index: 3;\n        .sel-items-cont {\n          position: absolute;\n          width: 100%;\n          bottom: 50px;\n          top: 0;\n          overflow: auto;\n        }\n\n        .btn-confirm {\n          position: absolute;\n          width: 100%;\n          bottom: 10px;\n          & > button.disabled {\n            background-color: rgba(68, 125, 255, 0.7);\n            border: 1px solid rgba(68, 125, 255, 0.7);\n          }\n        }\n        .sel-item-block:last-of-type {\n          ::ng-deep {\n            .title {\n              border-bottom: 1px solid #ccc !important;\n            }\n          }\n        }\n      }\n\n      iframe.template-iframe {\n        position: absolute;\n        top: 0;\n        left: 0;\n        bottom: 0;\n        right: 0;\n        height: 100%;\n        width: 100%;\n        border: none;\n      }\n    `,\n  ],\n})\nexport class MqSelectItemsComponent implements OnInit, OnDestroy {\n  public mq;\n  public showTip = false;\n  public tipCont = \"\";\n  public itemsSelected = [];\n  private iframeDom;\n  private templateUrl;\n\n  private autoTestSub;\n  private subscriptions: Subscription[] = [];\n  constructor(\n    public examSer: ExamService,\n    public responseSer: ResponseService,\n    public utilSer: UtilService,\n    private formSer: FormService,\n    private jtcostmeSer: JTCustomService,\n    private autoTestSer: AutoTestService,\n    private translate: TranslateService\n  ) {}\n\n  ngOnInit(): void {\n    this.subscriptions.push(\n      this.examSer.examEvents.subscribe((event) => {\n        if (event.type === Exam.Event.SelectItemsFromMq) {\n          if (typeof event.data !== \"string\") {\n            this.mq = event.data;\n            const skin = this.examSer.session.config.skin;\n            const template = this.mq.selection.template;\n            this.templateUrl = \"/seat/skin/\" + skin + \"/template/\" + template + \".html\";\n            this.itemsSelected = [];\n\n            // set cur mq id for template in skin\n            this.jtcostmeSer.mqForSelectItems = this.mq;\n            console.log(\"mqForSelectItems\", this.jtcostmeSer.mqForSelectItems);\n            this.reloadIframe();\n          }\n        }\n      })\n    );\n\n    // 如果已经开启自动试考\n    if (this.examSer.autoTestOn) {\n      this.autoTest();\n    } else {\n      // 如果进入当前页才开始开启自动试考\n      this.subscriptions.push(\n        this.examSer.examEventsFromManager.subscribe((result) => {\n          if (result.type === Exam.EventManager.AutoTest) {\n            if (!this.examSer.autoTestOn) {\n              this.autoTest();\n            }\n          }\n        })\n      );\n    }\n  }\n\n  @ViewChild(\"templateIframe\") set content(content: ElementRef) {\n    if (content) {\n      this.iframeDom = content.nativeElement;\n      this.examSer.loadIframe(this.iframeDom, this.templateUrl, true);\n    }\n  }\n\n  ngOnDestroy(): void {\n    this.subscriptions.forEach((sub) => sub.unsubscribe());\n    if (this.iframeDom) {\n      window.focus();\n    }\n  }\n\n  autoTest() {\n    if (this.autoTestSub) {\n      this.autoTestSub.unsubscribe();\n    }\n    this.autoTestSub = timer(1000, 3000).subscribe((t) => {\n      this.autoTestSer.test({\n        pageType: \"select-items\",\n        extraData: { manual: this.mq.selection.manual, optionsLength: this.mq.content.items.length },\n      });\n    });\n    this.subscriptions.push(this.autoTestSub);\n  }\n\n  async btnConfirm() {\n    if (this.itemsSelected.length === this.mq.selection.manual) {\n      this.tipCont = await this.getTipCont();\n      this.showTip = true;\n    }\n  }\n\n  async getTipCont() {\n    let tip = this.translate.instant(\"exam.groupSelection.selectedItems\");\n\n    for (let i = 0, len = this.mq.content.items.length; i < len; i++) {\n      const item = this.mq.content.items[i];\n      if (this.itemsSelected.indexOf(item[\"id\"]) > -1) {\n        await (async (i) => {\n          const index = this.getTitleIndex(i);\n          const tipContTrans = await lastValueFrom(\n            this.translate.get(\"exam.groupSelection.titleName\", { titleNum: index })\n          );\n          tip += tipContTrans + \"、\";\n        })(i);\n      }\n    }\n    tip = tip.substr(0, tip.length - 1);\n    tip +=\n      \"<div class='font-min mt-3 text-center'><label><input type='checkbox' id='selection-check-confirm' class='mr-1 align-middle'>\" +\n      this.translate.instant(\"exam.groupSelection.checkConfirm\") +\n      \"</label></div>\";\n    return tip;\n  }\n  modalConfirm() {\n    // 检查是否勾选确认\n    const btnCheckConfirm = document.getElementById(\"selection-check-confirm\") as HTMLInputElement;\n    if (!btnCheckConfirm.checked) {\n      return;\n    }\n\n    this.showTip = false;\n\n    const mq = this.mq;\n    this.examSer.setMqItemSelected(mq, this.itemsSelected);\n  }\n\n  modalCancel() {\n    this.showTip = false;\n  }\n\n  reloadIframe() {\n    if (this.iframeDom) {\n      this.iframeDom.src = \"\";\n      this.examSer.loadIframe(this.iframeDom, this.templateUrl, true);\n    }\n  }\n\n  getTitleIndex(i) {\n    const chineseLan = [\"zh\", \"zh-hk\"];\n    return chineseLan.indexOf(this.utilSer.langSet) > -1 ? this.utilSer.numberConvertToUppercase(i + 1) : i + 1;\n  }\n}\n"]}, "metadata": {}, "sourceType": "module"}