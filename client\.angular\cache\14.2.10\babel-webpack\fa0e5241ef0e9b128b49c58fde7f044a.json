{"ast": null, "code": "export var equalTextDocs = {\n  name: 'equalText',\n  category: 'Relational',\n  syntax: ['equalText(x, y)'],\n  description: 'Check equality of two strings. Comparison is case sensitive. Returns true if the values are equal, and false if not.',\n  examples: ['equalText(\"Hello\", \"Hello\")', 'equalText(\"a\", \"A\")', 'equal(\"2e3\", \"2000\")', 'equalText(\"2e3\", \"2000\")', 'equalText(\"B\", [\"A\", \"B\", \"C\"])'],\n  seealso: ['compare', 'compareNatural', 'compareText', 'equal']\n};", "map": {"version": 3, "names": ["equalTextDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["D:/work/joyserver/client/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/relational/equalText.js"], "sourcesContent": ["export var equalTextDocs = {\n  name: 'equalText',\n  category: 'Relational',\n  syntax: ['equalText(x, y)'],\n  description: 'Check equality of two strings. Comparison is case sensitive. Returns true if the values are equal, and false if not.',\n  examples: ['equalText(\"Hello\", \"Hello\")', 'equalText(\"a\", \"A\")', 'equal(\"2e3\", \"2000\")', 'equalText(\"2e3\", \"2000\")', 'equalText(\"B\", [\"A\", \"B\", \"C\"])'],\n  seealso: ['compare', 'compareNatural', 'compareText', 'equal']\n};"], "mappings": "AAAA,OAAO,IAAIA,aAAa,GAAG;EACzBC,IAAI,EAAE,WADmB;EAEzBC,QAAQ,EAAE,YAFe;EAGzBC,MAAM,EAAE,CAAC,iBAAD,CAHiB;EAIzBC,WAAW,EAAE,sHAJY;EAKzBC,QAAQ,EAAE,CAAC,6BAAD,EAAgC,qBAAhC,EAAuD,sBAAvD,EAA+E,0BAA/E,EAA2G,iCAA3G,CALe;EAMzBC,OAAO,EAAE,CAAC,SAAD,EAAY,gBAAZ,EAA8B,aAA9B,EAA6C,OAA7C;AANgB,CAApB"}, "metadata": {}, "sourceType": "module"}