{"ast": null, "code": "import { Observable } from '../Observable';\nimport { from } from './from';\nimport { isArray } from '../util/isArray';\nimport { EMPTY } from './empty';\nexport function onErrorResumeNext(...sources) {\n  if (sources.length === 0) {\n    return EMPTY;\n  }\n\n  const [first, ...remainder] = sources;\n\n  if (sources.length === 1 && isArray(first)) {\n    return onErrorResumeNext(...first);\n  }\n\n  return new Observable(subscriber => {\n    const subNext = () => subscriber.add(onErrorResumeNext(...remainder).subscribe(subscriber));\n\n    return from(first).subscribe({\n      next(value) {\n        subscriber.next(value);\n      },\n\n      error: subNext,\n      complete: subNext\n    });\n  });\n} //# sourceMappingURL=onErrorResumeNext.js.map", "map": null, "metadata": {}, "sourceType": "module"}