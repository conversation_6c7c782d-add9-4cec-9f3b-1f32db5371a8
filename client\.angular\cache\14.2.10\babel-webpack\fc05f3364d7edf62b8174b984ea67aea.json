{"ast": null, "code": "/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { BigNumberDependencies } from './dependenciesBigNumberClass.generated.js';\nimport { UnitDependencies } from './dependenciesUnitClass.generated.js';\nimport { createNuclearMagneton } from '../../factoriesAny.js';\nexport var nuclearMagnetonDependencies = {\n  BigNumberDependencies,\n  UnitDependencies,\n  createNuclearMagneton\n};", "map": {"version": 3, "names": ["BigNumberDependencies", "UnitDependencies", "createNuclearMagneton", "nuclearMagnetonDependencies"], "sources": ["D:/work/joyserver/client/node_modules/mathjs/lib/esm/entry/dependenciesAny/dependenciesNuclearMagneton.generated.js"], "sourcesContent": ["/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { BigNumberDependencies } from './dependenciesBigNumberClass.generated.js';\nimport { UnitDependencies } from './dependenciesUnitClass.generated.js';\nimport { createNuclearMagneton } from '../../factoriesAny.js';\nexport var nuclearMagnetonDependencies = {\n  BigNumberDependencies,\n  UnitDependencies,\n  createNuclearMagneton\n};"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,qBAAT,QAAsC,2CAAtC;AACA,SAASC,gBAAT,QAAiC,sCAAjC;AACA,SAASC,qBAAT,QAAsC,uBAAtC;AACA,OAAO,IAAIC,2BAA2B,GAAG;EACvCH,qBADuC;EAEvCC,gBAFuC;EAGvCC;AAHuC,CAAlC"}, "metadata": {}, "sourceType": "module"}