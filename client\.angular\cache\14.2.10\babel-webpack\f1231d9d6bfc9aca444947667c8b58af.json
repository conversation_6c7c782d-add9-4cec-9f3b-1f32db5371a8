{"ast": null, "code": "import { factory } from '../../utils/factory.js';\nimport { createRow } from '../../function/matrix/row.js';\nimport { errorTransform } from './utils/errorTransform.js';\nimport { isNumber } from '../../utils/is.js';\nvar name = 'row';\nvar dependencies = ['typed', 'Index', 'matrix', 'range'];\n/**\n * Attach a transform function to matrix.column\n * Adds a property transform containing the transform function.\n *\n * This transform changed the last `index` parameter of function column\n * from zero-based to one-based\n */\n\nexport var createRowTransform = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed,\n    Index,\n    matrix,\n    range\n  } = _ref;\n  var row = createRow({\n    typed,\n    Index,\n    matrix,\n    range\n  }); // @see: comment of row itself\n\n  return typed('row', {\n    '...any': function any(args) {\n      // change last argument from zero-based to one-based\n      var lastIndex = args.length - 1;\n      var last = args[lastIndex];\n\n      if (isNumber(last)) {\n        args[lastIndex] = last - 1;\n      }\n\n      try {\n        return row.apply(null, args);\n      } catch (err) {\n        throw errorTransform(err);\n      }\n    }\n  });\n}, {\n  isTransformFunction: true\n});", "map": {"version": 3, "names": ["factory", "createRow", "errorTransform", "isNumber", "name", "dependencies", "createRowTransform", "_ref", "typed", "Index", "matrix", "range", "row", "any", "args", "lastIndex", "length", "last", "apply", "err", "isTransformFunction"], "sources": ["D:/work/joyserver/client/node_modules/mathjs/lib/esm/expression/transform/row.transform.js"], "sourcesContent": ["import { factory } from '../../utils/factory.js';\nimport { createRow } from '../../function/matrix/row.js';\nimport { errorTransform } from './utils/errorTransform.js';\nimport { isNumber } from '../../utils/is.js';\nvar name = 'row';\nvar dependencies = ['typed', 'Index', 'matrix', 'range'];\n\n/**\n * Attach a transform function to matrix.column\n * Adds a property transform containing the transform function.\n *\n * This transform changed the last `index` parameter of function column\n * from zero-based to one-based\n */\nexport var createRowTransform = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed,\n    Index,\n    matrix,\n    range\n  } = _ref;\n  var row = createRow({\n    typed,\n    Index,\n    matrix,\n    range\n  });\n\n  // @see: comment of row itself\n  return typed('row', {\n    '...any': function any(args) {\n      // change last argument from zero-based to one-based\n      var lastIndex = args.length - 1;\n      var last = args[lastIndex];\n      if (isNumber(last)) {\n        args[lastIndex] = last - 1;\n      }\n      try {\n        return row.apply(null, args);\n      } catch (err) {\n        throw errorTransform(err);\n      }\n    }\n  });\n}, {\n  isTransformFunction: true\n});"], "mappings": "AAAA,SAASA,OAAT,QAAwB,wBAAxB;AACA,SAASC,SAAT,QAA0B,8BAA1B;AACA,SAASC,cAAT,QAA+B,2BAA/B;AACA,SAASC,QAAT,QAAyB,mBAAzB;AACA,IAAIC,IAAI,GAAG,KAAX;AACA,IAAIC,YAAY,GAAG,CAAC,OAAD,EAAU,OAAV,EAAmB,QAAnB,EAA6B,OAA7B,CAAnB;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,OAAO,IAAIC,kBAAkB,GAAG,eAAeN,OAAO,CAACI,IAAD,EAAOC,YAAP,EAAqBE,IAAI,IAAI;EACjF,IAAI;IACFC,KADE;IAEFC,KAFE;IAGFC,MAHE;IAIFC;EAJE,IAKAJ,IALJ;EAMA,IAAIK,GAAG,GAAGX,SAAS,CAAC;IAClBO,KADkB;IAElBC,KAFkB;IAGlBC,MAHkB;IAIlBC;EAJkB,CAAD,CAAnB,CAPiF,CAcjF;;EACA,OAAOH,KAAK,CAAC,KAAD,EAAQ;IAClB,UAAU,SAASK,GAAT,CAAaC,IAAb,EAAmB;MAC3B;MACA,IAAIC,SAAS,GAAGD,IAAI,CAACE,MAAL,GAAc,CAA9B;MACA,IAAIC,IAAI,GAAGH,IAAI,CAACC,SAAD,CAAf;;MACA,IAAIZ,QAAQ,CAACc,IAAD,CAAZ,EAAoB;QAClBH,IAAI,CAACC,SAAD,CAAJ,GAAkBE,IAAI,GAAG,CAAzB;MACD;;MACD,IAAI;QACF,OAAOL,GAAG,CAACM,KAAJ,CAAU,IAAV,EAAgBJ,IAAhB,CAAP;MACD,CAFD,CAEE,OAAOK,GAAP,EAAY;QACZ,MAAMjB,cAAc,CAACiB,GAAD,CAApB;MACD;IACF;EAbiB,CAAR,CAAZ;AAeD,CA9BqD,EA8BnD;EACDC,mBAAmB,EAAE;AADpB,CA9BmD,CAA/C"}, "metadata": {}, "sourceType": "module"}