{"ast": null, "code": "import { Observable } from '../Observable';\nimport { isArray } from '../util/isArray';\nimport { isFunction } from '../util/isFunction';\nimport { map } from '../operators/map';\n\nconst toString = (() => Object.prototype.toString)();\n\nexport function fromEvent(target, eventName, options, resultSelector) {\n  if (isFunction(options)) {\n    resultSelector = options;\n    options = undefined;\n  }\n\n  if (resultSelector) {\n    return fromEvent(target, eventName, options).pipe(map(args => isArray(args) ? resultSelector(...args) : resultSelector(args)));\n  }\n\n  return new Observable(subscriber => {\n    function handler(e) {\n      if (arguments.length > 1) {\n        subscriber.next(Array.prototype.slice.call(arguments));\n      } else {\n        subscriber.next(e);\n      }\n    }\n\n    setupSubscription(target, eventName, handler, subscriber, options);\n  });\n}\n\nfunction setupSubscription(sourceObj, eventName, handler, subscriber, options) {\n  let unsubscribe;\n\n  if (isEventTarget(sourceObj)) {\n    const source = sourceObj;\n    sourceObj.addEventListener(eventName, handler, options);\n\n    unsubscribe = () => source.removeEventListener(eventName, handler, options);\n  } else if (isJQueryStyleEventEmitter(sourceObj)) {\n    const source = sourceObj;\n    sourceObj.on(eventName, handler);\n\n    unsubscribe = () => source.off(eventName, handler);\n  } else if (isNodeStyleEventEmitter(sourceObj)) {\n    const source = sourceObj;\n    sourceObj.addListener(eventName, handler);\n\n    unsubscribe = () => source.removeListener(eventName, handler);\n  } else if (sourceObj && sourceObj.length) {\n    for (let i = 0, len = sourceObj.length; i < len; i++) {\n      setupSubscription(sourceObj[i], eventName, handler, subscriber, options);\n    }\n  } else {\n    throw new TypeError('Invalid event target');\n  }\n\n  subscriber.add(unsubscribe);\n}\n\nfunction isNodeStyleEventEmitter(sourceObj) {\n  return sourceObj && typeof sourceObj.addListener === 'function' && typeof sourceObj.removeListener === 'function';\n}\n\nfunction isJQueryStyleEventEmitter(sourceObj) {\n  return sourceObj && typeof sourceObj.on === 'function' && typeof sourceObj.off === 'function';\n}\n\nfunction isEventTarget(sourceObj) {\n  return sourceObj && typeof sourceObj.addEventListener === 'function' && typeof sourceObj.removeEventListener === 'function';\n} //# sourceMappingURL=fromEvent.js.map", "map": null, "metadata": {}, "sourceType": "module"}