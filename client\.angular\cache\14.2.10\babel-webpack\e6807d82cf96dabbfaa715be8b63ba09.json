{"ast": null, "code": "import _asyncToGenerator from \"D:/work/joyserver/client/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ngx-translate/core\";\nexport class UserMediaService {\n  constructor(translate) {\n    this.translate = translate;\n  } // 连接设备\n\n\n  getUserMedia(opts, VIDEO_SOURCE) {\n    if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {\n      console.log(\"**** videoSource: \" + VIDEO_SOURCE);\n      const constraints = {\n        video: {\n          deviceId: VIDEO_SOURCE ? {\n            exact: VIDEO_SOURCE\n          } : undefined,\n          width: {\n            min: 320,\n            ideal: opts && opts.size ? opts.size.w : 320,\n            max: 1280\n          },\n          height: {\n            min: 240,\n            ideal: opts && opts.size ? opts.size.h : 240,\n            max: 720\n          }\n        },\n        audio: false\n      };\n      return navigator.mediaDevices.getUserMedia(constraints);\n    } else {\n      console.log(\"**** getUserMedia is undefined\");\n    }\n  } // 关闭媒体流\n\n\n  stopTrack(stream) {\n    if (stream) {\n      if (stream.getTracks().length) {\n        for (const track of stream.getTracks()) {\n          track.stop();\n        }\n      }\n    }\n  } // 获取视频流：失败\n\n\n  handleError(error) {\n    const mediaError = {\n      AbortError: this.translate.instant(\"mediaErrs.AbortError\"),\n      NotReadableError: this.translate.instant(\"mediaErrs.NotReadableError\"),\n      NotAllowedError: this.translate.instant(\"mediaErrs.NotAllowedError\"),\n      NotFoundError: this.translate.instant(\"mediaErrs.NotFoundError\"),\n      SecurityError: this.translate.instant(\"mediaErrs.SecurityError\"),\n      TypeError: this.translate.instant(\"mediaErrs.TypeError\"),\n      OverConstrainedError: this.translate.instant(\"mediaErrs.OverConstrainedError\")\n    };\n    const errMsg = mediaError[error.name] || this.translate.instant(\"mediaErrs.unknownError\");\n    console.log(`**** Get user media: fail - ${errMsg}`);\n    return this.translate.instant(\"mediaErrs.errtype\") + errMsg;\n  } // 通过本地视频流拍照\n\n\n  getPhotoCaptured(video, opts = {\n    sW: 320,\n    sH: 240,\n    cW: 320,\n    cH: 240,\n    sX: 0,\n    sY: 0,\n    dX: 0,\n    dY: 0\n  }, quality) {\n    const {\n      sX,\n      sY,\n      dX,\n      dY,\n      sW,\n      sH,\n      cW,\n      cH\n    } = opts;\n    const canvas = document.createElement(\"canvas\");\n    const q = quality || 1;\n    canvas.width = cW;\n    canvas.height = cH;\n\n    if (video) {\n      const ctx = canvas.getContext(\"2d\");\n      ctx.clearRect(0, 0, canvas.width, canvas.height);\n      ctx.drawImage(video, sX, sY, sW, sH, dX, dY, cW, cH);\n    }\n\n    return canvas.toDataURL(\"image/jpeg\", q);\n  } // deviceId、kind相同不一定是同一个设备，还要判断label\n\n\n  deviceAvailable(type, label) {\n    return _asyncToGenerator(function* () {\n      const deviceId = type === \"audioinput\" ? localStorage.getItem(\"AUDIO_SOURCE\") : localStorage.getItem(\"VIDEO_SOURCE\");\n      const devices = yield navigator.mediaDevices.enumerateDevices();\n      return devices.find(device => {\n        if (device.kind.includes(type) && device.deviceId === deviceId && device.label === label) {\n          return true;\n        }\n      });\n    })();\n  }\n\n  getDevicesListByKind(kind) {\n    return _asyncToGenerator(function* () {\n      const devices = yield navigator.mediaDevices.enumerateDevices();\n      return devices.filter(d => {\n        return d.kind === \"videoinput\";\n      });\n    })();\n  }\n\n  getDeviceByLabel(deviceLabel, type) {\n    return _asyncToGenerator(function* () {\n      const t = type ? type : \"videoinput\";\n      const devices = yield navigator.mediaDevices.enumerateDevices();\n      return devices.filter(d => {\n        return d.kind.includes(t) && deviceLabel === d.label;\n      });\n    })();\n  } // 获取电脑音频播放、麦克风录入设置音量\n\n\n  getPCVolume(type) {\n    let volume = 0;\n\n    if (joyshell) {\n      volume = !!type && type === \"mic\" ? joyshell.Audio.mic.get() : joyshell.Audio.speaker.get();\n    } else {\n      volume = 50;\n    }\n\n    return volume;\n  } // 设置电脑音量\n\n\n  setPCVolume(type, volume) {\n    if (!joyshell) {\n      return;\n    }\n\n    const Audio = joyshell.Audio;\n\n    if (type === \"audio\") {\n      if (Audio.speaker.isMuted()) {\n        Audio.speaker[\"unmute\"]();\n      }\n\n      Audio.speaker.set(volume);\n    } else {\n      if (Audio.mic.isMuted()) {\n        Audio.mic[\"unmute\"]();\n      }\n\n      Audio.mic.set(volume);\n    }\n  }\n\n}\n\nUserMediaService.ɵfac = function UserMediaService_Factory(t) {\n  return new (t || UserMediaService)(i0.ɵɵinject(i1.TranslateService));\n};\n\nUserMediaService.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n  token: UserMediaService,\n  factory: UserMediaService.ɵfac,\n  providedIn: \"root\"\n});", "map": {"version": 3, "mappings": ";;;AAMA,OAAM,MAAOA,gBAAP,CAAuB;EAC3BC,YAAoBC,SAApB,EAA+C;IAA3B;EAA+B,CADxB,CAE3B;;;EACAC,YAAY,CAACC,IAAD,EAA8CC,YAA9C,EAA2D;IACrE,IAAIC,SAAS,CAACC,YAAV,IAA0BD,SAAS,CAACC,YAAV,CAAuBJ,YAArD,EAAmE;MACjEK,OAAO,CAACC,GAAR,CAAY,uBAAuBJ,YAAnC;MAEA,MAAMK,WAAW,GAAG;QAClBC,KAAK,EAAE;UACLC,QAAQ,EAAEP,YAAY,GAAG;YAAEQ,KAAK,EAAER;UAAT,CAAH,GAA6BS,SAD9C;UAELC,KAAK,EAAE;YAAEC,GAAG,EAAE,GAAP;YAAYC,KAAK,EAAEb,IAAI,IAAIA,IAAI,CAACc,IAAb,GAAoBd,IAAI,CAACc,IAAL,CAAUC,CAA9B,GAAkC,GAArD;YAA0DC,GAAG,EAAE;UAA/D,CAFF;UAGLC,MAAM,EAAE;YAAEL,GAAG,EAAE,GAAP;YAAYC,KAAK,EAAEb,IAAI,IAAIA,IAAI,CAACc,IAAb,GAAoBd,IAAI,CAACc,IAAL,CAAUI,CAA9B,GAAkC,GAArD;YAA0DF,GAAG,EAAE;UAA/D;QAHH,CADW;QAMlBG,KAAK,EAAE;MANW,CAApB;MAQA,OAAOjB,SAAS,CAACC,YAAV,CAAuBJ,YAAvB,CAAoCO,WAApC,CAAP;IACD,CAZD,MAYO;MACLF,OAAO,CAACC,GAAR,CAAY,gCAAZ;IACD;EACF,CAnB0B,CAqB3B;;;EACAe,SAAS,CAACC,MAAD,EAAO;IACd,IAAIA,MAAJ,EAAY;MACV,IAAIA,MAAM,CAACC,SAAP,GAAmBC,MAAvB,EAA+B;QAC7B,KAAK,MAAMC,KAAX,IAAoBH,MAAM,CAACC,SAAP,EAApB,EAAwC;UACtCE,KAAK,CAACC,IAAN;QACD;MACF;IACF;EACF,CA9B0B,CAgC3B;;;EACAC,WAAW,CAACC,KAAD,EAAM;IACf,MAAMC,UAAU,GAAG;MACjBC,UAAU,EAAE,KAAK/B,SAAL,CAAegC,OAAf,CAAuB,sBAAvB,CADK;MAEjBC,gBAAgB,EAAE,KAAKjC,SAAL,CAAegC,OAAf,CAAuB,4BAAvB,CAFD;MAGjBE,eAAe,EAAE,KAAKlC,SAAL,CAAegC,OAAf,CAAuB,2BAAvB,CAHA;MAIjBG,aAAa,EAAE,KAAKnC,SAAL,CAAegC,OAAf,CAAuB,yBAAvB,CAJE;MAKjBI,aAAa,EAAE,KAAKpC,SAAL,CAAegC,OAAf,CAAuB,yBAAvB,CALE;MAMjBK,SAAS,EAAE,KAAKrC,SAAL,CAAegC,OAAf,CAAuB,qBAAvB,CANM;MAOjBM,oBAAoB,EAAE,KAAKtC,SAAL,CAAegC,OAAf,CAAuB,gCAAvB;IAPL,CAAnB;IASA,MAAMO,MAAM,GAAGT,UAAU,CAACD,KAAK,CAACW,IAAP,CAAV,IAA0B,KAAKxC,SAAL,CAAegC,OAAf,CAAuB,wBAAvB,CAAzC;IACA1B,OAAO,CAACC,GAAR,CAAY,+BAA+BgC,MAAM,EAAjD;IACA,OAAO,KAAKvC,SAAL,CAAegC,OAAf,CAAuB,mBAAvB,IAA8CO,MAArD;EACD,CA9C0B,CAgD3B;;;EACAE,gBAAgB,CACdhC,KADc,EAEdP,IAAI,GAAG;IAAEwC,EAAE,EAAE,GAAN;IAAWC,EAAE,EAAE,GAAf;IAAoBC,EAAE,EAAE,GAAxB;IAA6BC,EAAE,EAAE,GAAjC;IAAsCC,EAAE,EAAE,CAA1C;IAA6CC,EAAE,EAAE,CAAjD;IAAoDC,EAAE,EAAE,CAAxD;IAA2DC,EAAE,EAAE;EAA/D,CAFO,EAGdC,OAHc,EAGN;IAER,MAAM;MAAEJ,EAAF;MAAMC,EAAN;MAAUC,EAAV;MAAcC,EAAd;MAAkBP,EAAlB;MAAsBC,EAAtB;MAA0BC,EAA1B;MAA8BC;IAA9B,IAAqC3C,IAA3C;IACA,MAAMiD,MAAM,GAAGC,QAAQ,CAACC,aAAT,CAAuB,QAAvB,CAAf;IACA,MAAMC,CAAC,GAAGJ,OAAO,IAAI,CAArB;IACAC,MAAM,CAACtC,KAAP,GAAe+B,EAAf;IACAO,MAAM,CAAChC,MAAP,GAAgB0B,EAAhB;;IACA,IAAIpC,KAAJ,EAAW;MACT,MAAM8C,GAAG,GAAGJ,MAAM,CAACK,UAAP,CAAkB,IAAlB,CAAZ;MACAD,GAAG,CAACE,SAAJ,CAAc,CAAd,EAAiB,CAAjB,EAAoBN,MAAM,CAACtC,KAA3B,EAAkCsC,MAAM,CAAChC,MAAzC;MACAoC,GAAG,CAACG,SAAJ,CAAcjD,KAAd,EAAqBqC,EAArB,EAAyBC,EAAzB,EAA6BL,EAA7B,EAAiCC,EAAjC,EAAqCK,EAArC,EAAyCC,EAAzC,EAA6CL,EAA7C,EAAiDC,EAAjD;IACD;;IACD,OAAOM,MAAM,CAACQ,SAAP,CAAiB,YAAjB,EAA+BL,CAA/B,CAAP;EACD,CAjE0B,CAmE3B;;;EACMM,eAAe,CAACC,IAAD,EAAoCC,KAApC,EAAyC;IAAA;MAC5D,MAAMpD,QAAQ,GACZmD,IAAI,KAAK,YAAT,GAAwBE,YAAY,CAACC,OAAb,CAAqB,cAArB,CAAxB,GAA+DD,YAAY,CAACC,OAAb,CAAqB,cAArB,CADjE;MAEA,MAAMC,OAAO,SAAS7D,SAAS,CAACC,YAAV,CAAuB6D,gBAAvB,EAAtB;MACA,OAAOD,OAAO,CAACE,IAAR,CAAcC,MAAD,IAAW;QAC7B,IAAIA,MAAM,CAACC,IAAP,CAAYC,QAAZ,CAAqBT,IAArB,KAA8BO,MAAM,CAAC1D,QAAP,KAAoBA,QAAlD,IAA8D0D,MAAM,CAACN,KAAP,KAAiBA,KAAnF,EAA0F;UACxF,OAAO,IAAP;QACD;MACF,CAJM,CAAP;IAJ4D;EAS7D;;EAEKS,oBAAoB,CAACF,IAAD,EAAK;IAAA;MAC7B,MAAMJ,OAAO,SAAS7D,SAAS,CAACC,YAAV,CAAuB6D,gBAAvB,EAAtB;MACA,OAAOD,OAAO,CAACO,MAAR,CAAgBC,CAAD,IAAM;QAC1B,OAAOA,CAAC,CAACJ,IAAF,KAAW,YAAlB;MACD,CAFM,CAAP;IAF6B;EAK9B;;EAEKK,gBAAgB,CAACC,WAAD,EAAcd,IAAd,EAAmB;IAAA;MACvC,MAAMe,CAAC,GAAGf,IAAI,GAAGA,IAAH,GAAU,YAAxB;MACA,MAAMI,OAAO,SAAS7D,SAAS,CAACC,YAAV,CAAuB6D,gBAAvB,EAAtB;MACA,OAAOD,OAAO,CAACO,MAAR,CAAgBC,CAAD,IAAM;QAC1B,OAAOA,CAAC,CAACJ,IAAF,CAAOC,QAAP,CAAgBM,CAAhB,KAAsBD,WAAW,KAAKF,CAAC,CAACX,KAA/C;MACD,CAFM,CAAP;IAHuC;EAMxC,CA5F0B,CA8F3B;;;EACAe,WAAW,CAAChB,IAAD,EAAuB;IAChC,IAAIiB,MAAM,GAAG,CAAb;;IACA,IAAIC,QAAJ,EAAc;MACZD,MAAM,GAAG,CAAC,CAACjB,IAAF,IAAUA,IAAI,KAAK,KAAnB,GAA2BkB,QAAQ,CAACC,KAAT,CAAeC,GAAf,CAAmBC,GAAnB,EAA3B,GAAsDH,QAAQ,CAACC,KAAT,CAAeG,OAAf,CAAuBD,GAAvB,EAA/D;IACD,CAFD,MAEO;MACLJ,MAAM,GAAG,EAAT;IACD;;IACD,OAAOA,MAAP;EACD,CAvG0B,CAyG3B;;;EACAM,WAAW,CAACvB,IAAD,EAAwBiB,MAAxB,EAA8B;IACvC,IAAI,CAACC,QAAL,EAAe;MACb;IACD;;IACD,MAAMC,KAAK,GAAGD,QAAQ,CAACC,KAAvB;;IACA,IAAInB,IAAI,KAAK,OAAb,EAAsB;MACpB,IAAImB,KAAK,CAACG,OAAN,CAAcE,OAAd,EAAJ,EAA6B;QAC3BL,KAAK,CAACG,OAAN,CAAc,QAAd;MACD;;MACDH,KAAK,CAACG,OAAN,CAAcG,GAAd,CAAkBR,MAAlB;IACD,CALD,MAKO;MACL,IAAIE,KAAK,CAACC,GAAN,CAAUI,OAAV,EAAJ,EAAyB;QACvBL,KAAK,CAACC,GAAN,CAAU,QAAV;MACD;;MACDD,KAAK,CAACC,GAAN,CAAUK,GAAV,CAAcR,MAAd;IACD;EACF;;AA1H0B;;;mBAAhBhF,kBAAgByF;AAAA;;;SAAhBzF;EAAgB0F,SAAhB1F,gBAAgB;EAAA2F,YAFf", "names": ["UserMediaService", "constructor", "translate", "getUserMedia", "opts", "VIDEO_SOURCE", "navigator", "mediaDevices", "console", "log", "constraints", "video", "deviceId", "exact", "undefined", "width", "min", "ideal", "size", "w", "max", "height", "h", "audio", "stopTrack", "stream", "getTracks", "length", "track", "stop", "handleError", "error", "mediaError", "AbortError", "instant", "NotReadableError", "NotAllowedError", "NotFoundError", "SecurityError", "TypeError", "OverConstrainedError", "errMsg", "name", "getPhotoCaptured", "sW", "sH", "cW", "cH", "sX", "sY", "dX", "dY", "quality", "canvas", "document", "createElement", "q", "ctx", "getContext", "clearRect", "drawImage", "toDataURL", "deviceAvailable", "type", "label", "localStorage", "getItem", "devices", "enumerateDevices", "find", "device", "kind", "includes", "getDevicesListByKind", "filter", "d", "getDeviceByLabel", "deviceLabel", "t", "getPCVolume", "volume", "joyshell", "Audio", "mic", "get", "speaker", "setPCVolume", "isMuted", "set", "i0", "factory", "providedIn"], "sourceRoot": "", "sources": ["D:\\work\\joyserver\\client\\src\\app\\core\\service\\user-media.service.ts"], "sourcesContent": ["import { Injectable } from \"@angular/core\";\nimport { TranslateService } from \"@ngx-translate/core\";\n\n@Injectable({\n  providedIn: \"root\",\n})\nexport class UserMediaService {\n  constructor(private translate: TranslateService) {}\n  // 连接设备\n  getUserMedia(opts: { size?: { w?: number; h?: number } }, VIDEO_SOURCE?) {\n    if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {\n      console.log(\"**** videoSource: \" + VIDEO_SOURCE);\n\n      const constraints = {\n        video: {\n          deviceId: VIDEO_SOURCE ? { exact: VIDEO_SOURCE } : undefined,\n          width: { min: 320, ideal: opts && opts.size ? opts.size.w : 320, max: 1280 },\n          height: { min: 240, ideal: opts && opts.size ? opts.size.h : 240, max: 720 },\n        },\n        audio: false,\n      };\n      return navigator.mediaDevices.getUserMedia(constraints);\n    } else {\n      console.log(\"**** getUserMedia is undefined\");\n    }\n  }\n\n  // 关闭媒体流\n  stopTrack(stream) {\n    if (stream) {\n      if (stream.getTracks().length) {\n        for (const track of stream.getTracks()) {\n          track.stop();\n        }\n      }\n    }\n  }\n\n  // 获取视频流：失败\n  handleError(error) {\n    const mediaError = {\n      AbortError: this.translate.instant(\"mediaErrs.AbortError\"),\n      NotReadableError: this.translate.instant(\"mediaErrs.NotReadableError\"),\n      NotAllowedError: this.translate.instant(\"mediaErrs.NotAllowedError\"),\n      NotFoundError: this.translate.instant(\"mediaErrs.NotFoundError\"),\n      SecurityError: this.translate.instant(\"mediaErrs.SecurityError\"),\n      TypeError: this.translate.instant(\"mediaErrs.TypeError\"),\n      OverConstrainedError: this.translate.instant(\"mediaErrs.OverConstrainedError\"),\n    };\n    const errMsg = mediaError[error.name] || this.translate.instant(\"mediaErrs.unknownError\");\n    console.log(`**** Get user media: fail - ${errMsg}`);\n    return this.translate.instant(\"mediaErrs.errtype\") + errMsg;\n  }\n\n  // 通过本地视频流拍照\n  getPhotoCaptured(\n    video: HTMLVideoElement,\n    opts = { sW: 320, sH: 240, cW: 320, cH: 240, sX: 0, sY: 0, dX: 0, dY: 0 },\n    quality?\n  ) {\n    const { sX, sY, dX, dY, sW, sH, cW, cH } = opts;\n    const canvas = document.createElement(\"canvas\");\n    const q = quality || 1;\n    canvas.width = cW;\n    canvas.height = cH;\n    if (video) {\n      const ctx = canvas.getContext(\"2d\");\n      ctx.clearRect(0, 0, canvas.width, canvas.height);\n      ctx.drawImage(video, sX, sY, sW, sH, dX, dY, cW, cH);\n    }\n    return canvas.toDataURL(\"image/jpeg\", q);\n  }\n\n  // deviceId、kind相同不一定是同一个设备，还要判断label\n  async deviceAvailable(type: \"audioinput\" | \"videoinput\", label) {\n    const deviceId =\n      type === \"audioinput\" ? localStorage.getItem(\"AUDIO_SOURCE\") : localStorage.getItem(\"VIDEO_SOURCE\");\n    const devices = await navigator.mediaDevices.enumerateDevices();\n    return devices.find((device) => {\n      if (device.kind.includes(type) && device.deviceId === deviceId && device.label === label) {\n        return true;\n      }\n    });\n  }\n\n  async getDevicesListByKind(kind) {\n    const devices = await navigator.mediaDevices.enumerateDevices();\n    return devices.filter((d) => {\n      return d.kind === \"videoinput\";\n    });\n  }\n\n  async getDeviceByLabel(deviceLabel, type?) {\n    const t = type ? type : \"videoinput\";\n    const devices = await navigator.mediaDevices.enumerateDevices();\n    return devices.filter((d) => {\n      return d.kind.includes(t) && deviceLabel === d.label;\n    });\n  }\n\n  // 获取电脑音频播放、麦克风录入设置音量\n  getPCVolume(type?: \"audio\" | \"mic\") {\n    let volume = 0;\n    if (joyshell) {\n      volume = !!type && type === \"mic\" ? joyshell.Audio.mic.get() : joyshell.Audio.speaker.get();\n    } else {\n      volume = 50;\n    }\n    return volume;\n  }\n\n  // 设置电脑音量\n  setPCVolume(type: \"audio\" | \"mic\", volume) {\n    if (!joyshell) {\n      return;\n    }\n    const Audio = joyshell.Audio;\n    if (type === \"audio\") {\n      if (Audio.speaker.isMuted()) {\n        Audio.speaker[\"unmute\"]();\n      }\n      Audio.speaker.set(volume);\n    } else {\n      if (Audio.mic.isMuted()) {\n        Audio.mic[\"unmute\"]();\n      }\n      Audio.mic.set(volume);\n    }\n  }\n}\n"]}, "metadata": {}, "sourceType": "module"}