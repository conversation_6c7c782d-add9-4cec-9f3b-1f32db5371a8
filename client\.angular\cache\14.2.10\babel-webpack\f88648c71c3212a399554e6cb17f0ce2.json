{"ast": null, "code": "import getParentNode from \"./getParentNode.js\";\nimport isScrollParent from \"./isScrollParent.js\";\nimport getNodeName from \"./getNodeName.js\";\nimport { isHTMLElement } from \"./instanceOf.js\";\nexport default function getScrollParent(node) {\n  if (['html', 'body', '#document'].indexOf(getNodeName(node)) >= 0) {\n    // $FlowFixMe[incompatible-return]: assume body is always available\n    return node.ownerDocument.body;\n  }\n\n  if (isHTMLElement(node) && isScrollParent(node)) {\n    return node;\n  }\n\n  return getScrollParent(getParentNode(node));\n}", "map": {"version": 3, "names": ["getParentNode", "isScrollParent", "getNodeName", "isHTMLElement", "getScrollParent", "node", "indexOf", "ownerDocument", "body"], "sources": ["D:/work/joyserver/client/node_modules/@popperjs/core/lib/dom-utils/getScrollParent.js"], "sourcesContent": ["import getParentNode from \"./getParentNode.js\";\nimport isScrollParent from \"./isScrollParent.js\";\nimport getNodeName from \"./getNodeName.js\";\nimport { isHTMLElement } from \"./instanceOf.js\";\nexport default function getScrollParent(node) {\n  if (['html', 'body', '#document'].indexOf(getNodeName(node)) >= 0) {\n    // $FlowFixMe[incompatible-return]: assume body is always available\n    return node.ownerDocument.body;\n  }\n\n  if (isHTMLElement(node) && isScrollParent(node)) {\n    return node;\n  }\n\n  return getScrollParent(getParentNode(node));\n}"], "mappings": "AAAA,OAAOA,aAAP,MAA0B,oBAA1B;AACA,OAAOC,cAAP,MAA2B,qBAA3B;AACA,OAAOC,WAAP,MAAwB,kBAAxB;AACA,SAASC,aAAT,QAA8B,iBAA9B;AACA,eAAe,SAASC,eAAT,CAAyBC,IAAzB,EAA+B;EAC5C,IAAI,CAAC,MAAD,EAAS,MAAT,EAAiB,WAAjB,EAA8BC,OAA9B,CAAsCJ,WAAW,CAACG,IAAD,CAAjD,KAA4D,CAAhE,EAAmE;IACjE;IACA,OAAOA,IAAI,CAACE,aAAL,CAAmBC,IAA1B;EACD;;EAED,IAAIL,aAAa,CAACE,IAAD,CAAb,IAAuBJ,cAAc,CAACI,IAAD,CAAzC,EAAiD;IAC/C,OAAOA,IAAP;EACD;;EAED,OAAOD,eAAe,CAACJ,aAAa,CAACK,IAAD,CAAd,CAAtB;AACD"}, "metadata": {}, "sourceType": "module"}