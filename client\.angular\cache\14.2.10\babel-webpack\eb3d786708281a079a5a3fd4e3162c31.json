{"ast": null, "code": "import { factory } from '../../utils/factory.js';\nimport { deepMap } from '../../utils/collection.js';\nvar name = 'conj';\nvar dependencies = ['typed'];\nexport var createConj = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed\n  } = _ref;\n  /**\n   * Compute the complex conjugate of a complex value.\n   * If `x = a+bi`, the complex conjugate of `x` is `a - bi`.\n   *\n   * For matrices, the function is evaluated element wise.\n   *\n   * Syntax:\n   *\n   *    math.conj(x)\n   *\n   * Examples:\n   *\n   *    math.conj(math.complex('2 + 3i'))  // returns Complex 2 - 3i\n   *    math.conj(math.complex('2 - 3i'))  // returns Complex 2 + 3i\n   *    math.conj(math.complex('-5.2i'))  // returns Complex 5.2i\n   *\n   * See also:\n   *\n   *    re, im, arg, abs\n   *\n   * @param {number | BigNumber | Complex | Array | Matrix} x\n   *            A complex number or array with complex numbers\n   * @return {number | BigNumber | Complex | Array | Matrix}\n   *            The complex conjugate of x\n   */\n\n  return typed(name, {\n    'number | BigNumber | Fraction': x => x,\n    Complex: x => x.conjugate(),\n    'Array | Matrix': typed.referToSelf(self => x => deepMap(x, self))\n  });\n});", "map": {"version": 3, "names": ["factory", "deepMap", "name", "dependencies", "createConj", "_ref", "typed", "x", "Complex", "conjugate", "referToSelf", "self"], "sources": ["D:/work/joyserver/client/node_modules/mathjs/lib/esm/function/complex/conj.js"], "sourcesContent": ["import { factory } from '../../utils/factory.js';\nimport { deepMap } from '../../utils/collection.js';\nvar name = 'conj';\nvar dependencies = ['typed'];\nexport var createConj = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed\n  } = _ref;\n  /**\n   * Compute the complex conjugate of a complex value.\n   * If `x = a+bi`, the complex conjugate of `x` is `a - bi`.\n   *\n   * For matrices, the function is evaluated element wise.\n   *\n   * Syntax:\n   *\n   *    math.conj(x)\n   *\n   * Examples:\n   *\n   *    math.conj(math.complex('2 + 3i'))  // returns Complex 2 - 3i\n   *    math.conj(math.complex('2 - 3i'))  // returns Complex 2 + 3i\n   *    math.conj(math.complex('-5.2i'))  // returns Complex 5.2i\n   *\n   * See also:\n   *\n   *    re, im, arg, abs\n   *\n   * @param {number | BigNumber | Complex | Array | Matrix} x\n   *            A complex number or array with complex numbers\n   * @return {number | BigNumber | Complex | Array | Matrix}\n   *            The complex conjugate of x\n   */\n  return typed(name, {\n    'number | BigNumber | Fraction': x => x,\n    Complex: x => x.conjugate(),\n    'Array | Matrix': typed.referToSelf(self => x => deepMap(x, self))\n  });\n});"], "mappings": "AAAA,SAASA,OAAT,QAAwB,wBAAxB;AACA,SAASC,OAAT,QAAwB,2BAAxB;AACA,IAAIC,IAAI,GAAG,MAAX;AACA,IAAIC,YAAY,GAAG,CAAC,OAAD,CAAnB;AACA,OAAO,IAAIC,UAAU,GAAG,eAAeJ,OAAO,CAACE,IAAD,EAAOC,YAAP,EAAqBE,IAAI,IAAI;EACzE,IAAI;IACFC;EADE,IAEAD,IAFJ;EAGA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EACE,OAAOC,KAAK,CAACJ,IAAD,EAAO;IACjB,iCAAiCK,CAAC,IAAIA,CADrB;IAEjBC,OAAO,EAAED,CAAC,IAAIA,CAAC,CAACE,SAAF,EAFG;IAGjB,kBAAkBH,KAAK,CAACI,WAAN,CAAkBC,IAAI,IAAIJ,CAAC,IAAIN,OAAO,CAACM,CAAD,EAAII,IAAJ,CAAtC;EAHD,CAAP,CAAZ;AAKD,CAlC6C,CAAvC"}, "metadata": {}, "sourceType": "module"}