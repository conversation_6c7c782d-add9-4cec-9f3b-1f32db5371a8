{"ast": null, "code": "import { isArray, isBigInt, isBigNumber, isMatrix, isNumber, isRange } from '../../utils/is.js';\nimport { factory } from '../../utils/factory.js';\nvar name = 'index';\nvar dependencies = ['Index', 'getMatrixDataType'];\nexport var createIndexTransform = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    Index,\n    getMatrixDataType\n  } = _ref;\n  /**\n   * Attach a transform function to math.index\n   * Adds a property transform containing the transform function.\n   *\n   * This transform creates a one-based index instead of a zero-based index\n   */\n\n  return function indexTransform() {\n    var args = [];\n\n    for (var i = 0, ii = arguments.length; i < ii; i++) {\n      var arg = arguments[i]; // change from one-based to zero based, convert BigNumber to number and leave Array of Booleans as is\n\n      if (isRange(arg)) {\n        arg.start--;\n        arg.end -= arg.step > 0 ? 0 : 2;\n      } else if (arg && arg.isSet === true) {\n        arg = arg.map(function (v) {\n          return v - 1;\n        });\n      } else if (isArray(arg) || isMatrix(arg)) {\n        if (getMatrixDataType(arg) !== 'boolean') {\n          arg = arg.map(function (v) {\n            return v - 1;\n          });\n        }\n      } else if (isNumber(arg) || isBigInt(arg)) {\n        arg--;\n      } else if (isBigNumber(arg)) {\n        arg = arg.toNumber() - 1;\n      } else if (typeof arg === 'string') {// leave as is\n      } else {\n        throw new TypeError('Dimension must be an Array, Matrix, number, bigint, string, or Range');\n      }\n\n      args[i] = arg;\n    }\n\n    var res = new Index();\n    Index.apply(res, args);\n    return res;\n  };\n}, {\n  isTransformFunction: true\n});", "map": {"version": 3, "names": ["isArray", "isBigInt", "isBigNumber", "isMatrix", "isNumber", "isRange", "factory", "name", "dependencies", "createIndexTransform", "_ref", "Index", "getMatrixDataType", "indexTransform", "args", "i", "ii", "arguments", "length", "arg", "start", "end", "step", "isSet", "map", "v", "toNumber", "TypeError", "res", "apply", "isTransformFunction"], "sources": ["D:/work/joyserver/client/node_modules/mathjs/lib/esm/expression/transform/index.transform.js"], "sourcesContent": ["import { isArray, isBigInt, isBigNumber, isMatrix, isNumber, isRange } from '../../utils/is.js';\nimport { factory } from '../../utils/factory.js';\nvar name = 'index';\nvar dependencies = ['Index', 'getMatrixDataType'];\nexport var createIndexTransform = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    Index,\n    getMatrixDataType\n  } = _ref;\n  /**\n   * Attach a transform function to math.index\n   * Adds a property transform containing the transform function.\n   *\n   * This transform creates a one-based index instead of a zero-based index\n   */\n  return function indexTransform() {\n    var args = [];\n    for (var i = 0, ii = arguments.length; i < ii; i++) {\n      var arg = arguments[i];\n\n      // change from one-based to zero based, convert BigNumber to number and leave Array of Booleans as is\n      if (isRange(arg)) {\n        arg.start--;\n        arg.end -= arg.step > 0 ? 0 : 2;\n      } else if (arg && arg.isSet === true) {\n        arg = arg.map(function (v) {\n          return v - 1;\n        });\n      } else if (isArray(arg) || isMatrix(arg)) {\n        if (getMatrixDataType(arg) !== 'boolean') {\n          arg = arg.map(function (v) {\n            return v - 1;\n          });\n        }\n      } else if (isNumber(arg) || isBigInt(arg)) {\n        arg--;\n      } else if (isBigNumber(arg)) {\n        arg = arg.toNumber() - 1;\n      } else if (typeof arg === 'string') {\n        // leave as is\n      } else {\n        throw new TypeError('Dimension must be an Array, Matrix, number, bigint, string, or Range');\n      }\n      args[i] = arg;\n    }\n    var res = new Index();\n    Index.apply(res, args);\n    return res;\n  };\n}, {\n  isTransformFunction: true\n});"], "mappings": "AAAA,SAASA,OAAT,EAAkBC,QAAlB,EAA4BC,WAA5B,EAAyCC,QAAzC,EAAmDC,QAAnD,EAA6DC,OAA7D,QAA4E,mBAA5E;AACA,SAASC,OAAT,QAAwB,wBAAxB;AACA,IAAIC,IAAI,GAAG,OAAX;AACA,IAAIC,YAAY,GAAG,CAAC,OAAD,EAAU,mBAAV,CAAnB;AACA,OAAO,IAAIC,oBAAoB,GAAG,eAAeH,OAAO,CAACC,IAAD,EAAOC,YAAP,EAAqBE,IAAI,IAAI;EACnF,IAAI;IACFC,KADE;IAEFC;EAFE,IAGAF,IAHJ;EAIA;AACF;AACA;AACA;AACA;AACA;;EACE,OAAO,SAASG,cAAT,GAA0B;IAC/B,IAAIC,IAAI,GAAG,EAAX;;IACA,KAAK,IAAIC,CAAC,GAAG,CAAR,EAAWC,EAAE,GAAGC,SAAS,CAACC,MAA/B,EAAuCH,CAAC,GAAGC,EAA3C,EAA+CD,CAAC,EAAhD,EAAoD;MAClD,IAAII,GAAG,GAAGF,SAAS,CAACF,CAAD,CAAnB,CADkD,CAGlD;;MACA,IAAIV,OAAO,CAACc,GAAD,CAAX,EAAkB;QAChBA,GAAG,CAACC,KAAJ;QACAD,GAAG,CAACE,GAAJ,IAAWF,GAAG,CAACG,IAAJ,GAAW,CAAX,GAAe,CAAf,GAAmB,CAA9B;MACD,CAHD,MAGO,IAAIH,GAAG,IAAIA,GAAG,CAACI,KAAJ,KAAc,IAAzB,EAA+B;QACpCJ,GAAG,GAAGA,GAAG,CAACK,GAAJ,CAAQ,UAAUC,CAAV,EAAa;UACzB,OAAOA,CAAC,GAAG,CAAX;QACD,CAFK,CAAN;MAGD,CAJM,MAIA,IAAIzB,OAAO,CAACmB,GAAD,CAAP,IAAgBhB,QAAQ,CAACgB,GAAD,CAA5B,EAAmC;QACxC,IAAIP,iBAAiB,CAACO,GAAD,CAAjB,KAA2B,SAA/B,EAA0C;UACxCA,GAAG,GAAGA,GAAG,CAACK,GAAJ,CAAQ,UAAUC,CAAV,EAAa;YACzB,OAAOA,CAAC,GAAG,CAAX;UACD,CAFK,CAAN;QAGD;MACF,CANM,MAMA,IAAIrB,QAAQ,CAACe,GAAD,CAAR,IAAiBlB,QAAQ,CAACkB,GAAD,CAA7B,EAAoC;QACzCA,GAAG;MACJ,CAFM,MAEA,IAAIjB,WAAW,CAACiB,GAAD,CAAf,EAAsB;QAC3BA,GAAG,GAAGA,GAAG,CAACO,QAAJ,KAAiB,CAAvB;MACD,CAFM,MAEA,IAAI,OAAOP,GAAP,KAAe,QAAnB,EAA6B,CAClC;MACD,CAFM,MAEA;QACL,MAAM,IAAIQ,SAAJ,CAAc,sEAAd,CAAN;MACD;;MACDb,IAAI,CAACC,CAAD,CAAJ,GAAUI,GAAV;IACD;;IACD,IAAIS,GAAG,GAAG,IAAIjB,KAAJ,EAAV;IACAA,KAAK,CAACkB,KAAN,CAAYD,GAAZ,EAAiBd,IAAjB;IACA,OAAOc,GAAP;EACD,CAjCD;AAkCD,CA7CuD,EA6CrD;EACDE,mBAAmB,EAAE;AADpB,CA7CqD,CAAjD"}, "metadata": {}, "sourceType": "module"}