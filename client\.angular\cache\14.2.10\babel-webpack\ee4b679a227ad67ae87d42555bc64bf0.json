{"ast": null, "code": "export var lcmDocs = {\n  name: 'lcm',\n  category: 'Arithmetic',\n  syntax: ['lcm(x, y)'],\n  description: 'Compute the least common multiple.',\n  examples: ['lcm(4, 6)', 'lcm(6, 21)', 'lcm(6, 21, 5)'],\n  seealso: ['gcd']\n};", "map": {"version": 3, "names": ["lcmDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["D:/work/joyserver/client/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/arithmetic/lcm.js"], "sourcesContent": ["export var lcmDocs = {\n  name: 'lcm',\n  category: 'Arithmetic',\n  syntax: ['lcm(x, y)'],\n  description: 'Compute the least common multiple.',\n  examples: ['lcm(4, 6)', 'lcm(6, 21)', 'lcm(6, 21, 5)'],\n  seealso: ['gcd']\n};"], "mappings": "AAAA,OAAO,IAAIA,OAAO,GAAG;EACnBC,IAAI,EAAE,KADa;EAEnBC,QAAQ,EAAE,YAFS;EAGnBC,MAAM,EAAE,CAAC,WAAD,CAHW;EAInBC,WAAW,EAAE,oCAJM;EAKnBC,QAAQ,EAAE,CAAC,WAAD,EAAc,YAAd,EAA4B,eAA5B,CALS;EAMnBC,OAAO,EAAE,CAAC,KAAD;AANU,CAAd"}, "metadata": {}, "sourceType": "module"}