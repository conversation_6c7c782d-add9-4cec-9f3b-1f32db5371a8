{"ast": null, "code": "export var mapSlicesDocs = {\n  name: 'mapSlices',\n  category: 'Matrix',\n  syntax: ['mapSlices(A, dim, callback)'],\n  description: 'Generate a matrix one dimension less than A by applying callback to ' + 'each slice of A along dimension dim.',\n  examples: ['A = [[1, 2], [3, 4]]', 'mapSlices(A, 1, sum)', // returns [4, 6]\n  'mapSlices(A, 2, product)' // returns [2, 12]\n  ],\n  seealso: ['map', 'forEach']\n};", "map": null, "metadata": {}, "sourceType": "module"}