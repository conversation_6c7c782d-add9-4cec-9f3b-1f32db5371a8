{"ast": null, "code": "import { flatten } from '../../utils/array.js';\nimport { factory } from '../../utils/factory.js';\nvar name = 'setPowerset';\nvar dependencies = ['typed', 'size', 'subset', 'compareNatural', 'Index'];\nexport var createSetPowerset = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed,\n    size,\n    subset,\n    compareNatural,\n    Index\n  } = _ref;\n  /**\n   * Create the powerset of a (multi)set. (The powerset contains very possible subsets of a (multi)set.)\n   * A multi-dimension array will be converted to a single-dimension array before the operation.\n   *\n   * Syntax:\n   *\n   *    math.setPowerset(set)\n   *\n   * Examples:\n   *\n   *    math.setPowerset([1, 2, 3])        // returns [[], [1], [2], [3], [1, 2], [1, 3], [2, 3], [1, 2, 3]]\n   *\n   * See also:\n   *\n   *    setCartesian\n   *\n   * @param {Array | Matrix}    a  A (multi)set\n   * @return {Array}    The powerset of the (multi)set\n   */\n\n  return typed(name, {\n    'Array | Matrix': function Array__Matrix(a) {\n      if (subset(size(a), new Index(0)) === 0) {\n        // if empty, return empty\n        return [];\n      }\n\n      var b = flatten(Array.isArray(a) ? a : a.toArray()).sort(compareNatural);\n      var result = [];\n      var number = 0;\n\n      while (number.toString(2).length <= b.length) {\n        result.push(_subset(b, number.toString(2).split('').reverse()));\n        number++;\n      } // can not return a matrix, because of the different size of the subarrays\n\n\n      return _sort(result);\n    }\n  }); // create subset\n\n  function _subset(array, bitarray) {\n    var result = [];\n\n    for (var i = 0; i < bitarray.length; i++) {\n      if (bitarray[i] === '1') {\n        result.push(array[i]);\n      }\n    }\n\n    return result;\n  } // sort subsests by length\n\n\n  function _sort(array) {\n    var temp = [];\n\n    for (var i = array.length - 1; i > 0; i--) {\n      for (var j = 0; j < i; j++) {\n        if (array[j].length > array[j + 1].length) {\n          temp = array[j];\n          array[j] = array[j + 1];\n          array[j + 1] = temp;\n        }\n      }\n    }\n\n    return array;\n  }\n});", "map": null, "metadata": {}, "sourceType": "module"}