{"ast": null, "code": "import { Observable } from '../Observable';\nimport { isArray } from '../util/isArray';\nimport { map } from '../operators/map';\nimport { isObject } from '../util/isObject';\nimport { from } from './from';\nexport function forkJoin(...sources) {\n  if (sources.length === 1) {\n    const first = sources[0];\n\n    if (isArray(first)) {\n      return forkJoinInternal(first, null);\n    }\n\n    if (isObject(first) && Object.getPrototypeOf(first) === Object.prototype) {\n      const keys = Object.keys(first);\n      return forkJoinInternal(keys.map(key => first[key]), keys);\n    }\n  }\n\n  if (typeof sources[sources.length - 1] === 'function') {\n    const resultSelector = sources.pop();\n    sources = sources.length === 1 && isArray(sources[0]) ? sources[0] : sources;\n    return forkJoinInternal(sources, null).pipe(map(args => resultSelector(...args)));\n  }\n\n  return forkJoinInternal(sources, null);\n}\n\nfunction forkJoinInternal(sources, keys) {\n  return new Observable(subscriber => {\n    const len = sources.length;\n\n    if (len === 0) {\n      subscriber.complete();\n      return;\n    }\n\n    const values = new Array(len);\n    let completed = 0;\n    let emitted = 0;\n\n    for (let i = 0; i < len; i++) {\n      const source = from(sources[i]);\n      let hasValue = false;\n      subscriber.add(source.subscribe({\n        next: value => {\n          if (!hasValue) {\n            hasValue = true;\n            emitted++;\n          }\n\n          values[i] = value;\n        },\n        error: err => subscriber.error(err),\n        complete: () => {\n          completed++;\n\n          if (completed === len || !hasValue) {\n            if (emitted === len) {\n              subscriber.next(keys ? keys.reduce((result, key, i) => (result[key] = values[i], result), {}) : values);\n            }\n\n            subscriber.complete();\n          }\n        }\n      }));\n    }\n  });\n} //# sourceMappingURL=forkJoin.js.map", "map": null, "metadata": {}, "sourceType": "module"}