{"ast": null, "code": "/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { ConstantNodeDependencies } from './dependenciesConstantNode.generated.js';\nimport { FunctionNodeDependencies } from './dependenciesFunctionNode.generated.js';\nimport { OperatorNodeDependencies } from './dependenciesOperatorNode.generated.js';\nimport { ParenthesisNodeDependencies } from './dependenciesParenthesisNode.generated.js';\nimport { SymbolNodeDependencies } from './dependenciesSymbolNode.generated.js';\nimport { equalDependencies } from './dependenciesEqual.generated.js';\nimport { isZeroDependencies } from './dependenciesIsZero.generated.js';\nimport { numericDependencies } from './dependenciesNumeric.generated.js';\nimport { parseDependencies } from './dependenciesParse.generated.js';\nimport { simplifyDependencies } from './dependenciesSimplify.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createDerivative } from '../../factoriesAny.js';\nexport var derivativeDependencies = {\n  ConstantNodeDependencies,\n  FunctionNodeDependencies,\n  OperatorNodeDependencies,\n  ParenthesisNodeDependencies,\n  SymbolNodeDependencies,\n  equalDependencies,\n  isZeroDependencies,\n  numericDependencies,\n  parseDependencies,\n  simplifyDependencies,\n  typedDependencies,\n  createDerivative\n};", "map": null, "metadata": {}, "sourceType": "module"}