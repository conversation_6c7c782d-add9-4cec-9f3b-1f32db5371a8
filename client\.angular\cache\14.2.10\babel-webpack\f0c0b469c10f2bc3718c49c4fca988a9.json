{"ast": null, "code": "import { factory } from '../../utils/factory.js';\nimport { multiplyNumber } from '../../plain/number/index.js';\nvar name = 'multiplyScalar';\nvar dependencies = ['typed'];\nexport var createMultiplyScalar = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed\n  } = _ref;\n  /**\n   * Multiply two scalar values, `x * y`.\n   * This function is meant for internal use: it is used by the public function\n   * `multiply`\n   *\n   * This function does not support collections (Array or Matrix).\n   *\n   * @param  {number | BigNumber | bigint | Fraction | Complex | Unit} x   First value to multiply\n   * @param  {number | BigNumber | bigint | Fraction | Complex} y          Second value to multiply\n   * @return {number | BigNumber | bigint | Fraction | Complex | Unit}     Multiplication of `x` and `y`\n   * @private\n   */\n\n  return typed('multiplyScalar', {\n    'number, number': multiplyNumber,\n    'Complex, Complex': function Complex_Complex(x, y) {\n      return x.mul(y);\n    },\n    'BigNumber, BigNumber': function BigNumber_BigNumber(x, y) {\n      return x.times(y);\n    },\n    'bigint, bigint': function bigint_bigint(x, y) {\n      return x * y;\n    },\n    'Fraction, Fraction': function Fraction_Fraction(x, y) {\n      return x.mul(y);\n    },\n    'number | Fraction | BigNumber | Complex, Unit': (x, y) => y.multiply(x),\n    'Unit, number | Fraction | BigNumber | Complex | Unit': (x, y) => x.multiply(y)\n  });\n});", "map": null, "metadata": {}, "sourceType": "module"}