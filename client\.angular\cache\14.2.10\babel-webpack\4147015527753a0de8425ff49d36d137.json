{"ast": null, "code": "import { Subscriber } from '../Subscriber';\nimport { Notification } from '../Notification';\nexport function materialize() {\n  return function materializeOperatorFunction(source) {\n    return source.lift(new MaterializeOperator());\n  };\n}\n\nclass MaterializeOperator {\n  call(subscriber, source) {\n    return source.subscribe(new MaterializeSubscriber(subscriber));\n  }\n\n}\n\nclass MaterializeSubscriber extends Subscriber {\n  constructor(destination) {\n    super(destination);\n  }\n\n  _next(value) {\n    this.destination.next(Notification.createNext(value));\n  }\n\n  _error(err) {\n    const destination = this.destination;\n    destination.next(Notification.createError(err));\n    destination.complete();\n  }\n\n  _complete() {\n    const destination = this.destination;\n    destination.next(Notification.createComplete());\n    destination.complete();\n  }\n\n} //# sourceMappingURL=materialize.js.map", "map": null, "metadata": {}, "sourceType": "module"}