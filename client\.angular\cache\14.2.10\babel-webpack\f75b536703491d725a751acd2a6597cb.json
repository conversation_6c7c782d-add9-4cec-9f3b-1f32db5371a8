{"ast": null, "code": "import { Subject } from '../Subject';\nimport { SimpleOuterSubscriber, innerSubscribe, SimpleInnerSubscriber } from '../innerSubscribe';\nexport function retryWhen(notifier) {\n  return source => source.lift(new RetryWhenOperator(notifier, source));\n}\n\nclass RetryWhenOperator {\n  constructor(notifier, source) {\n    this.notifier = notifier;\n    this.source = source;\n  }\n\n  call(subscriber, source) {\n    return source.subscribe(new RetryWhenSubscriber(subscriber, this.notifier, this.source));\n  }\n\n}\n\nclass RetryWhenSubscriber extends SimpleOuterSubscriber {\n  constructor(destination, notifier, source) {\n    super(destination);\n    this.notifier = notifier;\n    this.source = source;\n  }\n\n  error(err) {\n    if (!this.isStopped) {\n      let errors = this.errors;\n      let retries = this.retries;\n      let retriesSubscription = this.retriesSubscription;\n\n      if (!retries) {\n        errors = new Subject();\n\n        try {\n          const {\n            notifier\n          } = this;\n          retries = notifier(errors);\n        } catch (e) {\n          return super.error(e);\n        }\n\n        retriesSubscription = innerSubscribe(retries, new SimpleInnerSubscriber(this));\n      } else {\n        this.errors = undefined;\n        this.retriesSubscription = undefined;\n      }\n\n      this._unsubscribeAndRecycle();\n\n      this.errors = errors;\n      this.retries = retries;\n      this.retriesSubscription = retriesSubscription;\n      errors.next(err);\n    }\n  }\n\n  _unsubscribe() {\n    const {\n      errors,\n      retriesSubscription\n    } = this;\n\n    if (errors) {\n      errors.unsubscribe();\n      this.errors = undefined;\n    }\n\n    if (retriesSubscription) {\n      retriesSubscription.unsubscribe();\n      this.retriesSubscription = undefined;\n    }\n\n    this.retries = undefined;\n  }\n\n  notifyNext() {\n    const {\n      _unsubscribe\n    } = this;\n    this._unsubscribe = null;\n\n    this._unsubscribeAndRecycle();\n\n    this._unsubscribe = _unsubscribe;\n    this.source.subscribe(this);\n  }\n\n} //# sourceMappingURL=retryWhen.js.map", "map": null, "metadata": {}, "sourceType": "module"}