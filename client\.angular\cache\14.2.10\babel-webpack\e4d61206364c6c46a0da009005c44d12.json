{"ast": null, "code": "export var SQRT12Docs = {\n  name: 'SQRT1_2',\n  category: 'Constants',\n  syntax: ['SQRT1_2'],\n  description: 'Returns the square root of 1/2, approximately equal to 0.707',\n  examples: ['SQRT1_2', 'sqrt(1/2)'],\n  seealso: []\n};", "map": {"version": 3, "names": ["SQRT12Docs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["D:/work/joyserver/client/node_modules/mathjs/lib/esm/expression/embeddedDocs/constants/SQRT1_2.js"], "sourcesContent": ["export var SQRT12Docs = {\n  name: 'SQRT1_2',\n  category: 'Constants',\n  syntax: ['SQRT1_2'],\n  description: 'Returns the square root of 1/2, approximately equal to 0.707',\n  examples: ['SQRT1_2', 'sqrt(1/2)'],\n  seealso: []\n};"], "mappings": "AAAA,OAAO,IAAIA,UAAU,GAAG;EACtBC,IAAI,EAAE,SADgB;EAEtBC,QAAQ,EAAE,WAFY;EAGtBC,MAAM,EAAE,CAAC,SAAD,CAHc;EAItBC,WAAW,EAAE,8DAJS;EAKtBC,QAAQ,EAAE,CAAC,SAAD,EAAY,WAAZ,CALY;EAMtBC,OAAO,EAAE;AANa,CAAjB"}, "metadata": {}, "sourceType": "module"}