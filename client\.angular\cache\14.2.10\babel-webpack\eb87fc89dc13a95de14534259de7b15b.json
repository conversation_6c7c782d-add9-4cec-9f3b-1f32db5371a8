{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nimport { getSafeProperty } from '../../utils/customs.js';\nimport { factory } from '../../utils/factory.js';\nimport { isNode } from '../../utils/is.js';\nimport { hasOwnProperty } from '../../utils/object.js';\nimport { escape, stringify } from '../../utils/string.js';\nvar name = 'ObjectNode';\nvar dependencies = ['Node'];\nexport var createObjectNode = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    Node\n  } = _ref;\n\n  class ObjectNode extends Node {\n    /**\n     * @constructor ObjectNode\n     * @extends {Node}\n     * Holds an object with keys/values\n     * @param {Object.<string, Node>} [properties]   object with key/value pairs\n     */\n    constructor(properties) {\n      super();\n      this.properties = properties || {}; // validate input\n\n      if (properties) {\n        if (!(typeof properties === 'object') || !Object.keys(properties).every(function (key) {\n          return isNode(properties[key]);\n        })) {\n          throw new TypeError('Object containing Nodes expected');\n        }\n      }\n    }\n\n    get type() {\n      return name;\n    }\n\n    get isObjectNode() {\n      return true;\n    }\n    /**\n     * Compile a node into a JavaScript function.\n     * This basically pre-calculates as much as possible and only leaves open\n     * calculations which depend on a dynamic scope with variables.\n     * @param {Object} math     Math.js namespace with functions and constants.\n     * @param {Object} argNames An object with argument names as key and `true`\n     *                          as value. Used in the SymbolNode to optimize\n     *                          for arguments from user assigned functions\n     *                          (see FunctionAssignmentNode) or special symbols\n     *                          like `end` (see IndexNode).\n     * @return {function} Returns a function which can be called like:\n     *                        evalNode(scope: Object, args: Object, context: *)\n     */\n\n\n    _compile(math, argNames) {\n      var evalEntries = {};\n\n      for (var key in this.properties) {\n        if (hasOwnProperty(this.properties, key)) {\n          // we stringify/parse the key here to resolve unicode characters,\n          // so you cannot create a key like {\"co\\\\u006Estructor\": null}\n          var stringifiedKey = stringify(key);\n          var parsedKey = JSON.parse(stringifiedKey);\n          var prop = getSafeProperty(this.properties, key);\n          evalEntries[parsedKey] = prop._compile(math, argNames);\n        }\n      }\n\n      return function evalObjectNode(scope, args, context) {\n        var obj = {};\n\n        for (var _key in evalEntries) {\n          if (hasOwnProperty(evalEntries, _key)) {\n            obj[_key] = evalEntries[_key](scope, args, context);\n          }\n        }\n\n        return obj;\n      };\n    }\n    /**\n     * Execute a callback for each of the child nodes of this node\n     * @param {function(child: Node, path: string, parent: Node)} callback\n     */\n\n\n    forEach(callback) {\n      for (var key in this.properties) {\n        if (hasOwnProperty(this.properties, key)) {\n          callback(this.properties[key], 'properties[' + stringify(key) + ']', this);\n        }\n      }\n    }\n    /**\n     * Create a new ObjectNode whose children are the results of calling\n     * the provided callback function for each child of the original node.\n     * @param {function(child: Node, path: string, parent: Node): Node} callback\n     * @returns {ObjectNode} Returns a transformed copy of the node\n     */\n\n\n    map(callback) {\n      var properties = {};\n\n      for (var key in this.properties) {\n        if (hasOwnProperty(this.properties, key)) {\n          properties[key] = this._ifNode(callback(this.properties[key], 'properties[' + stringify(key) + ']', this));\n        }\n      }\n\n      return new ObjectNode(properties);\n    }\n    /**\n     * Create a clone of this node, a shallow copy\n     * @return {ObjectNode}\n     */\n\n\n    clone() {\n      var properties = {};\n\n      for (var key in this.properties) {\n        if (hasOwnProperty(this.properties, key)) {\n          properties[key] = this.properties[key];\n        }\n      }\n\n      return new ObjectNode(properties);\n    }\n    /**\n     * Get string representation\n     * @param {Object} options\n     * @return {string} str\n     * @override\n     */\n\n\n    _toString(options) {\n      var entries = [];\n\n      for (var key in this.properties) {\n        if (hasOwnProperty(this.properties, key)) {\n          entries.push(stringify(key) + ': ' + this.properties[key].toString(options));\n        }\n      }\n\n      return '{' + entries.join(', ') + '}';\n    }\n    /**\n     * Get a JSON representation of the node\n     * @returns {Object}\n     */\n\n\n    toJSON() {\n      return {\n        mathjs: name,\n        properties: this.properties\n      };\n    }\n    /**\n     * Instantiate an OperatorNode from its JSON representation\n     * @param {Object} json  An object structured like\n     *                       `{\"mathjs\": \"ObjectNode\", \"properties\": {...}}`,\n     *                       where mathjs is optional\n     * @returns {ObjectNode}\n     */\n\n\n    static fromJSON(json) {\n      return new ObjectNode(json.properties);\n    }\n    /**\n     * Get HTML representation\n     * @param {Object} options\n     * @return {string} str\n     * @override\n     */\n\n\n    _toHTML(options) {\n      var entries = [];\n\n      for (var key in this.properties) {\n        if (hasOwnProperty(this.properties, key)) {\n          entries.push('<span class=\"math-symbol math-property\">' + escape(key) + '</span>' + '<span class=\"math-operator math-assignment-operator ' + 'math-property-assignment-operator math-binary-operator\">' + ':</span>' + this.properties[key].toHTML(options));\n        }\n      }\n\n      return '<span class=\"math-parenthesis math-curly-parenthesis\">{</span>' + entries.join('<span class=\"math-separator\">,</span>') + '<span class=\"math-parenthesis math-curly-parenthesis\">}</span>';\n    }\n    /**\n     * Get LaTeX representation\n     * @param {Object} options\n     * @return {string} str\n     */\n\n\n    _toTex(options) {\n      var entries = [];\n\n      for (var key in this.properties) {\n        if (hasOwnProperty(this.properties, key)) {\n          entries.push('\\\\mathbf{' + key + ':} & ' + this.properties[key].toTex(options) + '\\\\\\\\');\n        }\n      }\n\n      var tex = '\\\\left\\\\{\\\\begin{array}{ll}' + entries.join('\\n') + '\\\\end{array}\\\\right\\\\}';\n      return tex;\n    }\n\n  }\n\n  _defineProperty(ObjectNode, \"name\", name);\n\n  return ObjectNode;\n}, {\n  isClass: true,\n  isNode: true\n});", "map": null, "metadata": {}, "sourceType": "module"}