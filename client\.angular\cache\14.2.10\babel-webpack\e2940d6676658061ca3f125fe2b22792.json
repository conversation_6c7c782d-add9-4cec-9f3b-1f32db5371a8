{"ast": null, "code": "import { ReplaySubject } from '../ReplaySubject';\nimport { multicast } from './multicast';\nexport function publishReplay(bufferSize, windowTime, selectorOrScheduler, scheduler) {\n  if (selectorOrScheduler && typeof selectorOrScheduler !== 'function') {\n    scheduler = selectorOrScheduler;\n  }\n\n  const selector = typeof selectorOrScheduler === 'function' ? selectorOrScheduler : undefined;\n  const subject = new ReplaySubject(bufferSize, windowTime, scheduler);\n  return source => multicast(() => subject, selector)(source);\n} //# sourceMappingURL=publishReplay.js.map", "map": null, "metadata": {}, "sourceType": "module"}