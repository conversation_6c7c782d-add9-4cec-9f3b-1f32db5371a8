{"ast": null, "code": "import { factory } from '../utils/factory.js';\nimport { createEmptyMap, toObject } from '../utils/map.js';\nvar name = 'Parser';\nvar dependencies = ['evaluate', 'parse'];\nexport var createParserClass = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    evaluate,\n    parse\n  } = _ref;\n  /**\n   * @constructor Parser\n   * Parser contains methods to evaluate or parse expressions, and has a number\n   * of convenience methods to get, set, and remove variables from memory. Parser\n   * keeps a scope containing variables in memory, which is used for all\n   * evaluations.\n   *\n   * Methods:\n   *    const result = parser.evaluate(expr)  // evaluate an expression\n   *    const value = parser.get(name)        // retrieve a variable from the parser\n   *    const values = parser.getAll()        // retrieve all defined variables\n   *    parser.set(name, value)               // set a variable in the parser\n   *    parser.remove(name)                   // clear a variable from the\n   *                                          // parsers scope\n   *    parser.clear()                        // clear the parsers scope\n   *\n   * Example usage:\n   *    const parser = new Parser()\n   *    // Note: there is a convenience method which can be used instead:\n   *    // const parser = new math.parser()\n   *\n   *    // evaluate expressions\n   *    parser.evaluate('sqrt(3^2 + 4^2)')        // 5\n   *    parser.evaluate('sqrt(-4)')               // 2i\n   *    parser.evaluate('2 inch in cm')           // 5.08 cm\n   *    parser.evaluate('cos(45 deg)')            // 0.7071067811865476\n   *\n   *    // define variables and functions\n   *    parser.evaluate('x = 7 / 2')              // 3.5\n   *    parser.evaluate('x + 3')                  // 6.5\n   *    parser.evaluate('f(x, y) = x^y')          // f(x, y)\n   *    parser.evaluate('f(2, 3)')                // 8\n   *\n   *    // get and set variables and functions\n   *    const x = parser.get('x')                 // 3.5\n   *    const f = parser.get('f')                 // function\n   *    const g = f(3, 2)                         // 9\n   *    parser.set('h', 500)\n   *    const i = parser.evaluate('h / 2')        // 250\n   *    parser.set('hello', function (name) {\n   *        return 'hello, ' + name + '!'\n   *    })\n   *    parser.evaluate('hello(\"user\")')          // \"hello, user!\"\n   *\n   *    // clear defined functions and variables\n   *    parser.clear()\n   *\n   */\n\n  function Parser() {\n    if (!(this instanceof Parser)) {\n      throw new SyntaxError('Constructor must be called with the new operator');\n    }\n\n    Object.defineProperty(this, 'scope', {\n      value: createEmptyMap(),\n      writable: false\n    });\n  }\n  /**\n   * Attach type information\n   */\n\n\n  Parser.prototype.type = 'Parser';\n  Parser.prototype.isParser = true;\n  /**\n   * Parse and evaluate the given expression\n   * @param {string | string[]} expr   A string containing an expression,\n   *                                   for example \"2+3\", or a list with expressions\n   * @return {*} result     The result, or undefined when the expression was empty\n   * @throws {Error}\n   */\n\n  Parser.prototype.evaluate = function (expr) {\n    // TODO: validate arguments\n    return evaluate(expr, this.scope);\n  };\n  /**\n   * Get a variable (a function or variable) by name from the parsers scope.\n   * Returns undefined when not found\n   * @param {string} name\n   * @return {* | undefined} value\n   */\n\n\n  Parser.prototype.get = function (name) {\n    // TODO: validate arguments\n    if (this.scope.has(name)) {\n      return this.scope.get(name);\n    }\n  };\n  /**\n   * Get a map with all defined variables\n   * @return {Object} values\n   */\n\n\n  Parser.prototype.getAll = function () {\n    return toObject(this.scope);\n  };\n  /**\n   * Get a map with all defined variables\n   * @return {Map} values\n   */\n\n\n  Parser.prototype.getAllAsMap = function () {\n    return this.scope;\n  };\n\n  function isValidVariableName(name) {\n    if (name.length === 0) {\n      return false;\n    }\n\n    for (var i = 0; i < name.length; i++) {\n      var cPrev = name.charAt(i - 1);\n      var c = name.charAt(i);\n      var cNext = name.charAt(i + 1);\n      var valid = parse.isAlpha(c, cPrev, cNext) || i > 0 && parse.isDigit(c);\n\n      if (!valid) {\n        return false;\n      }\n    }\n\n    return true;\n  }\n  /**\n   * Set a symbol (a function or variable) by name from the parsers scope.\n   * @param {string} name\n   * @param {* | undefined} value\n   */\n\n\n  Parser.prototype.set = function (name, value) {\n    if (!isValidVariableName(name)) {\n      throw new Error(\"Invalid variable name: '\".concat(name, \"'. Variable names must follow the specified rules.\"));\n    }\n\n    this.scope.set(name, value);\n    return value;\n  };\n  /**\n   * Remove a variable from the parsers scope\n   * @param {string} name\n   */\n\n\n  Parser.prototype.remove = function (name) {\n    this.scope.delete(name);\n  };\n  /**\n   * Clear the scope with variables and functions\n   */\n\n\n  Parser.prototype.clear = function () {\n    this.scope.clear();\n  };\n\n  return Parser;\n}, {\n  isClass: true\n});", "map": {"version": 3, "names": ["factory", "createEmptyMap", "toObject", "name", "dependencies", "createParserClass", "_ref", "evaluate", "parse", "<PERSON><PERSON><PERSON>", "SyntaxError", "Object", "defineProperty", "value", "writable", "prototype", "type", "<PERSON><PERSON><PERSON><PERSON>", "expr", "scope", "get", "has", "getAll", "getAllAsMap", "isValidVariableName", "length", "i", "cPrev", "char<PERSON>t", "c", "cNext", "valid", "isAlpha", "isDigit", "set", "Error", "concat", "remove", "delete", "clear", "isClass"], "sources": ["D:/work/joyserver/client/node_modules/mathjs/lib/esm/expression/Parser.js"], "sourcesContent": ["import { factory } from '../utils/factory.js';\nimport { createEmptyMap, toObject } from '../utils/map.js';\nvar name = 'Parser';\nvar dependencies = ['evaluate', 'parse'];\nexport var createParserClass = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    evaluate,\n    parse\n  } = _ref;\n  /**\n   * @constructor Parser\n   * Parser contains methods to evaluate or parse expressions, and has a number\n   * of convenience methods to get, set, and remove variables from memory. Parser\n   * keeps a scope containing variables in memory, which is used for all\n   * evaluations.\n   *\n   * Methods:\n   *    const result = parser.evaluate(expr)  // evaluate an expression\n   *    const value = parser.get(name)        // retrieve a variable from the parser\n   *    const values = parser.getAll()        // retrieve all defined variables\n   *    parser.set(name, value)               // set a variable in the parser\n   *    parser.remove(name)                   // clear a variable from the\n   *                                          // parsers scope\n   *    parser.clear()                        // clear the parsers scope\n   *\n   * Example usage:\n   *    const parser = new Parser()\n   *    // Note: there is a convenience method which can be used instead:\n   *    // const parser = new math.parser()\n   *\n   *    // evaluate expressions\n   *    parser.evaluate('sqrt(3^2 + 4^2)')        // 5\n   *    parser.evaluate('sqrt(-4)')               // 2i\n   *    parser.evaluate('2 inch in cm')           // 5.08 cm\n   *    parser.evaluate('cos(45 deg)')            // 0.7071067811865476\n   *\n   *    // define variables and functions\n   *    parser.evaluate('x = 7 / 2')              // 3.5\n   *    parser.evaluate('x + 3')                  // 6.5\n   *    parser.evaluate('f(x, y) = x^y')          // f(x, y)\n   *    parser.evaluate('f(2, 3)')                // 8\n   *\n   *    // get and set variables and functions\n   *    const x = parser.get('x')                 // 3.5\n   *    const f = parser.get('f')                 // function\n   *    const g = f(3, 2)                         // 9\n   *    parser.set('h', 500)\n   *    const i = parser.evaluate('h / 2')        // 250\n   *    parser.set('hello', function (name) {\n   *        return 'hello, ' + name + '!'\n   *    })\n   *    parser.evaluate('hello(\"user\")')          // \"hello, user!\"\n   *\n   *    // clear defined functions and variables\n   *    parser.clear()\n   *\n   */\n  function Parser() {\n    if (!(this instanceof Parser)) {\n      throw new SyntaxError('Constructor must be called with the new operator');\n    }\n    Object.defineProperty(this, 'scope', {\n      value: createEmptyMap(),\n      writable: false\n    });\n  }\n\n  /**\n   * Attach type information\n   */\n  Parser.prototype.type = 'Parser';\n  Parser.prototype.isParser = true;\n\n  /**\n   * Parse and evaluate the given expression\n   * @param {string | string[]} expr   A string containing an expression,\n   *                                   for example \"2+3\", or a list with expressions\n   * @return {*} result     The result, or undefined when the expression was empty\n   * @throws {Error}\n   */\n  Parser.prototype.evaluate = function (expr) {\n    // TODO: validate arguments\n    return evaluate(expr, this.scope);\n  };\n\n  /**\n   * Get a variable (a function or variable) by name from the parsers scope.\n   * Returns undefined when not found\n   * @param {string} name\n   * @return {* | undefined} value\n   */\n  Parser.prototype.get = function (name) {\n    // TODO: validate arguments\n    if (this.scope.has(name)) {\n      return this.scope.get(name);\n    }\n  };\n\n  /**\n   * Get a map with all defined variables\n   * @return {Object} values\n   */\n  Parser.prototype.getAll = function () {\n    return toObject(this.scope);\n  };\n\n  /**\n   * Get a map with all defined variables\n   * @return {Map} values\n   */\n  Parser.prototype.getAllAsMap = function () {\n    return this.scope;\n  };\n  function isValidVariableName(name) {\n    if (name.length === 0) {\n      return false;\n    }\n    for (var i = 0; i < name.length; i++) {\n      var cPrev = name.charAt(i - 1);\n      var c = name.charAt(i);\n      var cNext = name.charAt(i + 1);\n      var valid = parse.isAlpha(c, cPrev, cNext) || i > 0 && parse.isDigit(c);\n      if (!valid) {\n        return false;\n      }\n    }\n    return true;\n  }\n\n  /**\n   * Set a symbol (a function or variable) by name from the parsers scope.\n   * @param {string} name\n   * @param {* | undefined} value\n   */\n  Parser.prototype.set = function (name, value) {\n    if (!isValidVariableName(name)) {\n      throw new Error(\"Invalid variable name: '\".concat(name, \"'. Variable names must follow the specified rules.\"));\n    }\n    this.scope.set(name, value);\n    return value;\n  };\n\n  /**\n   * Remove a variable from the parsers scope\n   * @param {string} name\n   */\n  Parser.prototype.remove = function (name) {\n    this.scope.delete(name);\n  };\n\n  /**\n   * Clear the scope with variables and functions\n   */\n  Parser.prototype.clear = function () {\n    this.scope.clear();\n  };\n  return Parser;\n}, {\n  isClass: true\n});"], "mappings": "AAAA,SAASA,OAAT,QAAwB,qBAAxB;AACA,SAASC,cAAT,EAAyBC,QAAzB,QAAyC,iBAAzC;AACA,IAAIC,IAAI,GAAG,QAAX;AACA,IAAIC,YAAY,GAAG,CAAC,UAAD,EAAa,OAAb,CAAnB;AACA,OAAO,IAAIC,iBAAiB,GAAG,eAAeL,OAAO,CAACG,IAAD,EAAOC,YAAP,EAAqBE,IAAI,IAAI;EAChF,IAAI;IACFC,QADE;IAEFC;EAFE,IAGAF,IAHJ;EAIA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EACE,SAASG,MAAT,GAAkB;IAChB,IAAI,EAAE,gBAAgBA,MAAlB,CAAJ,EAA+B;MAC7B,MAAM,IAAIC,WAAJ,CAAgB,kDAAhB,CAAN;IACD;;IACDC,MAAM,CAACC,cAAP,CAAsB,IAAtB,EAA4B,OAA5B,EAAqC;MACnCC,KAAK,EAAEZ,cAAc,EADc;MAEnCa,QAAQ,EAAE;IAFyB,CAArC;EAID;EAED;AACF;AACA;;;EACEL,MAAM,CAACM,SAAP,CAAiBC,IAAjB,GAAwB,QAAxB;EACAP,MAAM,CAACM,SAAP,CAAiBE,QAAjB,GAA4B,IAA5B;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;;EACER,MAAM,CAACM,SAAP,CAAiBR,QAAjB,GAA4B,UAAUW,IAAV,EAAgB;IAC1C;IACA,OAAOX,QAAQ,CAACW,IAAD,EAAO,KAAKC,KAAZ,CAAf;EACD,CAHD;EAKA;AACF;AACA;AACA;AACA;AACA;;;EACEV,MAAM,CAACM,SAAP,CAAiBK,GAAjB,GAAuB,UAAUjB,IAAV,EAAgB;IACrC;IACA,IAAI,KAAKgB,KAAL,CAAWE,GAAX,CAAelB,IAAf,CAAJ,EAA0B;MACxB,OAAO,KAAKgB,KAAL,CAAWC,GAAX,CAAejB,IAAf,CAAP;IACD;EACF,CALD;EAOA;AACF;AACA;AACA;;;EACEM,MAAM,CAACM,SAAP,CAAiBO,MAAjB,GAA0B,YAAY;IACpC,OAAOpB,QAAQ,CAAC,KAAKiB,KAAN,CAAf;EACD,CAFD;EAIA;AACF;AACA;AACA;;;EACEV,MAAM,CAACM,SAAP,CAAiBQ,WAAjB,GAA+B,YAAY;IACzC,OAAO,KAAKJ,KAAZ;EACD,CAFD;;EAGA,SAASK,mBAAT,CAA6BrB,IAA7B,EAAmC;IACjC,IAAIA,IAAI,CAACsB,MAAL,KAAgB,CAApB,EAAuB;MACrB,OAAO,KAAP;IACD;;IACD,KAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGvB,IAAI,CAACsB,MAAzB,EAAiCC,CAAC,EAAlC,EAAsC;MACpC,IAAIC,KAAK,GAAGxB,IAAI,CAACyB,MAAL,CAAYF,CAAC,GAAG,CAAhB,CAAZ;MACA,IAAIG,CAAC,GAAG1B,IAAI,CAACyB,MAAL,CAAYF,CAAZ,CAAR;MACA,IAAII,KAAK,GAAG3B,IAAI,CAACyB,MAAL,CAAYF,CAAC,GAAG,CAAhB,CAAZ;MACA,IAAIK,KAAK,GAAGvB,KAAK,CAACwB,OAAN,CAAcH,CAAd,EAAiBF,KAAjB,EAAwBG,KAAxB,KAAkCJ,CAAC,GAAG,CAAJ,IAASlB,KAAK,CAACyB,OAAN,CAAcJ,CAAd,CAAvD;;MACA,IAAI,CAACE,KAAL,EAAY;QACV,OAAO,KAAP;MACD;IACF;;IACD,OAAO,IAAP;EACD;EAED;AACF;AACA;AACA;AACA;;;EACEtB,MAAM,CAACM,SAAP,CAAiBmB,GAAjB,GAAuB,UAAU/B,IAAV,EAAgBU,KAAhB,EAAuB;IAC5C,IAAI,CAACW,mBAAmB,CAACrB,IAAD,CAAxB,EAAgC;MAC9B,MAAM,IAAIgC,KAAJ,CAAU,2BAA2BC,MAA3B,CAAkCjC,IAAlC,EAAwC,oDAAxC,CAAV,CAAN;IACD;;IACD,KAAKgB,KAAL,CAAWe,GAAX,CAAe/B,IAAf,EAAqBU,KAArB;IACA,OAAOA,KAAP;EACD,CAND;EAQA;AACF;AACA;AACA;;;EACEJ,MAAM,CAACM,SAAP,CAAiBsB,MAAjB,GAA0B,UAAUlC,IAAV,EAAgB;IACxC,KAAKgB,KAAL,CAAWmB,MAAX,CAAkBnC,IAAlB;EACD,CAFD;EAIA;AACF;AACA;;;EACEM,MAAM,CAACM,SAAP,CAAiBwB,KAAjB,GAAyB,YAAY;IACnC,KAAKpB,KAAL,CAAWoB,KAAX;EACD,CAFD;;EAGA,OAAO9B,MAAP;AACD,CAzJoD,EAyJlD;EACD+B,OAAO,EAAE;AADR,CAzJkD,CAA9C"}, "metadata": {}, "sourceType": "module"}