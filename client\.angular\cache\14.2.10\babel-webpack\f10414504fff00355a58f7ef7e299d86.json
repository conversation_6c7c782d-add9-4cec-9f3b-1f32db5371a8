{"ast": null, "code": "export var qrDocs = {\n  name: 'qr',\n  category: 'Algebra',\n  syntax: ['qr(A)'],\n  description: 'Calculates the Matrix QR decomposition. Matrix `A` is decomposed in two matrices (`Q`, `R`) where `Q` is an orthogonal matrix and `R` is an upper triangular matrix.',\n  examples: ['qr([[1, -1,  4], [1,  4, -2], [1,  4,  2], [1,  -1, 0]])'],\n  seealso: ['lup', 'slu', 'matrix']\n};", "map": null, "metadata": {}, "sourceType": "module"}