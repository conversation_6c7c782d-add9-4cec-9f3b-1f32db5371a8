{"ast": null, "code": "import { broadcastSizes, broadcastTo } from '../../../utils/array.js';\nimport { deepStrictEqual } from '../../../utils/object.js';\n/**\n* Broadcasts two matrices, and return both in an array\n* It checks if it's possible with broadcasting rules\n*\n* @param {Matrix}   A      First Matrix\n* @param {Matrix}   B      Second Matrix\n*\n* @return {Matrix[]}      [ broadcastedA, broadcastedB ]\n*/\n\nexport function broadcast(A, B) {\n  if (deepStrictEqual(A.size(), B.size())) {\n    // If matrices have the same size return them\n    return [A, B];\n  } // calculate the broadcasted sizes\n\n\n  var newSize = broadcastSizes(A.size(), B.size()); // return the array with the two broadcasted matrices\n\n  return [A, B].map(M => _broadcastTo(M, newSize));\n}\n/**\n * Broadcasts a matrix to the given size.\n *\n * @param {Matrix} M - The matrix to be broadcasted.\n * @param {number[]} size - The desired size of the broadcasted matrix.\n * @returns {Matrix} The broadcasted matrix.\n * @throws {Error} If the size parameter is not an array of numbers.\n */\n\nfunction _broadcastTo(M, size) {\n  if (deepStrictEqual(M.size(), size)) {\n    return M;\n  }\n\n  return M.create(broadcastTo(M.valueOf(), size), M.datatype());\n}", "map": null, "metadata": {}, "sourceType": "module"}