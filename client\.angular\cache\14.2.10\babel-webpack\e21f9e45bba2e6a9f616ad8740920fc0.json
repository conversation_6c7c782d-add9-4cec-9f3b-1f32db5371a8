{"ast": null, "code": "/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { flattenDependencies } from './dependenciesFlatten.generated.js';\nimport { matrixDependencies } from './dependenciesMatrix.generated.js';\nimport { sizeDependencies } from './dependenciesSize.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createMatrixFromColumns } from '../../factoriesAny.js';\nexport var matrixFromColumnsDependencies = {\n  flattenDependencies,\n  matrixDependencies,\n  sizeDependencies,\n  typedDependencies,\n  createMatrixFromColumns\n};", "map": {"version": 3, "names": ["flattenDependencies", "matrixDependencies", "sizeDependencies", "typedDependencies", "createMatrixFromColumns", "matrixFromColumnsDependencies"], "sources": ["D:/work/joyserver/client/node_modules/mathjs/lib/esm/entry/dependenciesAny/dependenciesMatrixFromColumns.generated.js"], "sourcesContent": ["/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { flattenDependencies } from './dependenciesFlatten.generated.js';\nimport { matrixDependencies } from './dependenciesMatrix.generated.js';\nimport { sizeDependencies } from './dependenciesSize.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createMatrixFromColumns } from '../../factoriesAny.js';\nexport var matrixFromColumnsDependencies = {\n  flattenDependencies,\n  matrixDependencies,\n  sizeDependencies,\n  typedDependencies,\n  createMatrixFromColumns\n};"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,mBAAT,QAAoC,oCAApC;AACA,SAASC,kBAAT,QAAmC,mCAAnC;AACA,SAASC,gBAAT,QAAiC,iCAAjC;AACA,SAASC,iBAAT,QAAkC,kCAAlC;AACA,SAASC,uBAAT,QAAwC,uBAAxC;AACA,OAAO,IAAIC,6BAA6B,GAAG;EACzCL,mBADyC;EAEzCC,kBAFyC;EAGzCC,gBAHyC;EAIzCC,iBAJyC;EAKzCC;AALyC,CAApC"}, "metadata": {}, "sourceType": "module"}