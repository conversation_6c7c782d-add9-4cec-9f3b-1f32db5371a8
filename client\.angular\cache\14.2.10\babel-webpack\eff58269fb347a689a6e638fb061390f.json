{"ast": null, "code": "export var leafCountDocs = {\n  name: 'leafCount',\n  category: 'Algebra',\n  syntax: ['leafCount(expr)'],\n  description: 'Computes the number of leaves in the parse tree of the given expression',\n  examples: ['leafCount(\"e^(i*pi)-1\")', 'leafCount(parse(\"{a: 22/7, b: 10^(1/2)}\"))'],\n  seealso: ['simplify']\n};", "map": {"version": 3, "names": ["leafCountDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["D:/work/joyserver/client/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/algebra/leafCount.js"], "sourcesContent": ["export var leafCountDocs = {\n  name: 'leafCount',\n  category: 'Algebra',\n  syntax: ['leafCount(expr)'],\n  description: 'Computes the number of leaves in the parse tree of the given expression',\n  examples: ['leafCount(\"e^(i*pi)-1\")', 'leafCount(parse(\"{a: 22/7, b: 10^(1/2)}\"))'],\n  seealso: ['simplify']\n};"], "mappings": "AAAA,OAAO,IAAIA,aAAa,GAAG;EACzBC,IAAI,EAAE,WADmB;EAEzBC,QAAQ,EAAE,SAFe;EAGzBC,MAAM,EAAE,CAAC,iBAAD,CAHiB;EAIzBC,WAAW,EAAE,yEAJY;EAKzBC,QAAQ,EAAE,CAAC,yBAAD,EAA4B,4CAA5B,CALe;EAMzBC,OAAO,EAAE,CAAC,UAAD;AANgB,CAApB"}, "metadata": {}, "sourceType": "module"}