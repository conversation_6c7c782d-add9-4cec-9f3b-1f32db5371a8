{"ast": null, "code": "// Copyright (c) 2006-2024, <PERSON>, All Rights Reserved.\n// SPDX-License-Identifier: LGPL-2.1+\n// https://github.com/DrTimothyAldenDavis/SuiteSparse/tree/dev/CSparse/Source\n\n/**\n * Depth-first search and postorder of a tree rooted at node j\n *\n * @param {Number}  j               The tree node\n * @param {Number}  k\n * @param {Array}   w               The workspace array\n * @param {Number}  head            The index offset within the workspace for the head array\n * @param {Number}  next            The index offset within the workspace for the next array\n * @param {Array}   post            The post ordering array\n * @param {Number}  stack           The index offset within the workspace for the stack array\n */\nexport function csTdfs(j, k, w, head, next, post, stack) {\n  // variables\n  var top = 0; // place j on the stack\n\n  w[stack] = j; // while (stack is not empty)\n\n  while (top >= 0) {\n    // p = top of stack\n    var p = w[stack + top]; // i = youngest child of p\n\n    var i = w[head + p];\n\n    if (i === -1) {\n      // p has no unordered children left\n      top--; // node p is the kth postordered node\n\n      post[k++] = p;\n    } else {\n      // remove i from children of p\n      w[head + p] = w[next + i]; // increment top\n\n      ++top; // start dfs on child node i\n\n      w[stack + top] = i;\n    }\n  }\n\n  return k;\n}", "map": null, "metadata": {}, "sourceType": "module"}