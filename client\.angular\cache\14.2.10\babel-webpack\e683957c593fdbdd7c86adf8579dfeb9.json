{"ast": null, "code": "/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createSquare } from '../../factoriesAny.js';\nexport var squareDependencies = {\n  typedDependencies,\n  createSquare\n};", "map": {"version": 3, "names": ["typedDependencies", "createSquare", "squareDependencies"], "sources": ["D:/work/joyserver/client/node_modules/mathjs/lib/esm/entry/dependenciesAny/dependenciesSquare.generated.js"], "sourcesContent": ["/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createSquare } from '../../factoriesAny.js';\nexport var squareDependencies = {\n  typedDependencies,\n  createSquare\n};"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,iBAAT,QAAkC,kCAAlC;AACA,SAASC,YAAT,QAA6B,uBAA7B;AACA,OAAO,IAAIC,kBAAkB,GAAG;EAC9BF,iBAD8B;EAE9BC;AAF8B,CAAzB"}, "metadata": {}, "sourceType": "module"}