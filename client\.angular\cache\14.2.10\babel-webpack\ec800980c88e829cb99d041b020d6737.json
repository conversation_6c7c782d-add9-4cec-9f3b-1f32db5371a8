{"ast": null, "code": "import _asyncToGenerator from \"D:/work/joyserver/client/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { timer } from \"rxjs\";\nimport { Exam } from \"@core-types/exam.types\";\nimport { finalize } from \"rxjs/operators\";\nimport { LockFlag } from \"@core-types/joyshell.types\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@core-service/exam.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@core/service/auto-test.service\";\nimport * as i4 from \"@core/service/utils/util.service\";\nimport * as i5 from \"@core-modal/custom-loading/custom-loading.service\";\nimport * as i6 from \"@core-service/localStorage.service\";\nimport * as i7 from \"@ngx-translate/core\";\nimport * as i8 from \"@core/service/mobile/mobile.service\";\nimport * as i9 from \"@core/modal/custom-alert/custom-alert.service\";\nimport * as i10 from \"@angular/common\";\nimport * as i11 from \"../../core/modal/custom-confirm/custom-confirm.component\";\nimport * as i12 from \"../components/small-seat-num/small-seat-num.component\";\nimport * as i13 from \"../../core/directive/screenfull.directive\";\nimport * as i14 from \"../../core/directive/skin/css.directive\";\n\nfunction PersonInfoComponent_img_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 19);\n  }\n\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"src\", ctx_r0.entryInfo.personal.photo, i0.ɵɵsanitizeUrl);\n  }\n}\n\nfunction PersonInfoComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 20);\n  }\n}\n\nfunction PersonInfoComponent_div_14_div_1_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"span\", 23)(2, \"span\", 24);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\");\n    i0.ɵɵtext(5);\n    i0.ɵɵpipe(6, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"span\", 25);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    const info_r9 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(info_r9.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(6, 3, \"personInfo.colon\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(info_r9.value);\n  }\n}\n\nfunction PersonInfoComponent_div_14_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 22);\n    i0.ɵɵtemplate(1, PersonInfoComponent_div_14_div_1_ng_container_1_Template, 9, 5, \"ng-container\", 10);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const info_r9 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !!info_r9.value);\n  }\n}\n\nfunction PersonInfoComponent_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, PersonInfoComponent_div_14_div_1_Template, 2, 1, \"div\", 21);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.skin_personal);\n  }\n}\n\nfunction PersonInfoComponent_div_15_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 26)(2, \"span\", 27)(3, \"span\", 24);\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\");\n    i0.ɵɵtext(7);\n    i0.ɵɵpipe(8, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"span\", 28);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r12 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(5, 3, \"personInfo.name\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(8, 5, \"personInfo.colon\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r12.entryInfo.personal.full_name);\n  }\n}\n\nfunction PersonInfoComponent_div_15_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 29)(2, \"span\", 30)(3, \"span\", 24);\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\");\n    i0.ɵɵtext(7);\n    i0.ɵɵpipe(8, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"span\", 31);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r13 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(5, 3, \"personInfo.gender\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(8, 5, \"personInfo.colon\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r13.entryInfo.personal.gender);\n  }\n}\n\nfunction PersonInfoComponent_div_15_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 32)(2, \"span\", 33)(3, \"span\", 24);\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\");\n    i0.ɵɵtext(7);\n    i0.ɵɵpipe(8, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"span\", 34);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r14 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(5, 3, \"personInfo.permit\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(8, 5, \"personInfo.colon\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r14.entryPermitViewed);\n  }\n}\n\nfunction PersonInfoComponent_div_15_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 35)(2, \"span\", 36)(3, \"span\", 24);\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\");\n    i0.ɵɵtext(7);\n    i0.ɵɵpipe(8, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"span\", 37);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r15 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(5, 3, \"personInfo.id\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(8, 5, \"personInfo.colon\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r15.entryInfo.identity_id);\n  }\n}\n\nfunction PersonInfoComponent_div_15_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 38)(2, \"span\", 39)(3, \"span\", 24);\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\");\n    i0.ɵɵtext(7);\n    i0.ɵɵpipe(8, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"span\", 40);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r16 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(5, 3, \"personInfo.subject\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(8, 5, \"personInfo.colon\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r16.entryInfo.subject);\n  }\n}\n\nfunction PersonInfoComponent_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, PersonInfoComponent_div_15_ng_container_1_Template, 11, 7, \"ng-container\", 10);\n    i0.ɵɵtemplate(2, PersonInfoComponent_div_15_ng_container_2_Template, 11, 7, \"ng-container\", 10);\n    i0.ɵɵtemplate(3, PersonInfoComponent_div_15_ng_container_3_Template, 11, 7, \"ng-container\", 10);\n    i0.ɵɵtemplate(4, PersonInfoComponent_div_15_ng_container_4_Template, 11, 7, \"ng-container\", 10);\n    i0.ɵɵtemplate(5, PersonInfoComponent_div_15_ng_container_5_Template, 11, 7, \"ng-container\", 10);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.entryInfo.personal.full_name);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.entryInfo.personal.gender);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.entryInfo.permit);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.entryInfo.identity_id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.entryInfo.subject);\n  }\n}\n\nfunction PersonInfoComponent_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 41)(1, \"button\", 42);\n    i0.ɵɵlistener(\"click\", function PersonInfoComponent_div_17_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r17 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r17.confirmLogin());\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd()();\n  }\n\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 1, \"modal.confirm\"), \" \");\n  }\n}\n\nfunction PersonInfoComponent_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r20 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 41)(1, \"button\", 43);\n    i0.ɵɵlistener(\"click\", function PersonInfoComponent_div_18_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r19 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r19.confirmLogin());\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd()();\n  }\n\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"disabled\", ctx_r5.posting);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 2, \"modal.confirm\"), \" \");\n  }\n}\n\nfunction PersonInfoComponent_custom_confirm_27_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r22 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"custom-confirm\", 44);\n    i0.ɵɵlistener(\"onConfirm\", function PersonInfoComponent_custom_confirm_27_Template_custom_confirm_onConfirm_0_listener() {\n      i0.ɵɵrestoreView(_r22);\n      const ctx_r21 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r21.confirmDataFailed());\n    });\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"bodyText\", ctx_r6.translate.instant(\"clientError.getDataFailed\"))(\"cancelText\", \"\");\n  }\n}\n\nfunction PersonInfoComponent_custom_confirm_28_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r24 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"custom-confirm\", 45);\n    i0.ɵɵlistener(\"onConfirm\", function PersonInfoComponent_custom_confirm_28_Template_custom_confirm_onConfirm_0_listener() {\n      i0.ɵɵrestoreView(_r24);\n      const ctx_r23 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r23.confirmRedirectLogin());\n    })(\"onCancel\", function PersonInfoComponent_custom_confirm_28_Template_custom_confirm_onCancel_0_listener() {\n      i0.ɵɵrestoreView(_r24);\n      const ctx_r25 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r25.cancelRedirectLogin());\n    });\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"bodyText\", ctx_r7.translate.instant(\"personInfo.confirmRelogin\"));\n  }\n}\n\nconst _c0 = function (a0) {\n  return {\n    \"lan-en\": a0\n  };\n};\n\nexport class PersonInfoComponent {\n  constructor(examService, router, autoTestSer, utilSer, _loading, localStorageService, translate, mobile, _alertSvc) {\n    this.examService = examService;\n    this.router = router;\n    this.autoTestSer = autoTestSer;\n    this.utilSer = utilSer;\n    this._loading = _loading;\n    this.localStorageService = localStorageService;\n    this.translate = translate;\n    this.mobile = mobile;\n    this._alertSvc = _alertSvc;\n    this.infoErrTip = false;\n    this.subscriptions = [];\n    this.regInfo = localStorageService.getRegInfo();\n  }\n\n  ngOnInit() {\n    console.log(\"**** In page: personal-info\"); // FIX: 貌似loading控件有bug，某些情况下没有清除\n\n    this._loading.setValue(false);\n\n    this.entryInfo = this.examService.entryInfo;\n    this.entryInfo.personal.gender = this.transferGender();\n    this.joyshell = !!window.joyshell; // 如果已经开启自动试考\n\n    if (this.examService.autoTestOn) {\n      this.autoTest();\n    } else {\n      // 如果进入当前页才开始开启自动试考\n      this.subscriptions.push(this.examService.examEventsFromManager.subscribe(result => {\n        if (result.type === Exam.EventManager.AutoTest) {\n          this.autoTest();\n        }\n      }));\n    } // 如果skin里定制考生信息\n\n\n    this.skin_personal = this.examService.JTCustom.configs.personal_info;\n    this.examService.entryInfo.skin_personal = this.skin_personal; // 考生准考证号显示\n\n    if (this.examService.session.config.login_with !== \"permit\") {\n      this.entryPermitViewed = this.entryInfo.permit;\n    } else {\n      this.entryPermitViewed = this.entryInfo.permitViewed;\n    }\n  }\n\n  ngOnDestroy() {\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n    console.log(\"---- Leave page: personal-info\");\n  }\n\n  ngAfterViewInit() {\n    // skin 事件绑定调用\n    this.utilSer.JTCustomEvt(\"personInfoDomLoaded\");\n  }\n\n  transferGender() {\n    const gender = this.entryInfo.personal.gender;\n    let transKey;\n\n    if (gender) {\n      switch (gender) {\n        case \"0\":\n          transKey = \"personInfo.unknown\";\n          break;\n\n        case \"1\":\n          transKey = \"personInfo.male\";\n          break;\n\n        case \"2\":\n          transKey = \"personInfo.female\";\n          break;\n      }\n    }\n\n    return transKey ? this.translate.instant(transKey) : gender;\n  }\n\n  autoTest() {\n    if (this.autoTestSub) {\n      this.autoTestSub.unsubscribe();\n    }\n\n    this.autoTestSub = timer(5000, 5000).subscribe(t => {\n      this.autoTestSer.test({\n        pageType: \"confirmPersonInfo\",\n        isSubmitting: this.posting\n      });\n    });\n    this.subscriptions.push(this.autoTestSub);\n  }\n\n  confirmLogin() {\n    var _this = this;\n\n    return _asyncToGenerator(function* () {\n      _this._loading.setValue(true);\n\n      _this.posting = true;\n\n      if (_this.mobile.isInMobile && _this.examService.session?.config.lock_screen) {\n        const locked = yield _this.mobile.lock().catch(err => {\n          console.error(\"Mobile: lock error:\", err);\n          return false;\n        });\n\n        if (!locked) {\n          console.error(\"Mobile: request locking failed\");\n\n          _this._loading.setValue(false);\n\n          _this._alertSvc.setValue({\n            status: true,\n            info: {\n              bodyText: _this.translate.instant(\"mobile.lockFailed\")\n            }\n          });\n\n          return;\n        }\n      }\n\n      _this.examService.confirmLogin().toPromise().then( /*#__PURE__*/_asyncToGenerator(function* () {\n        if (joyshell && !window.is_local && !window.is_demo) {\n          _this.checkLocalSession();\n        } else {\n          _this.confirmLoginSuccess();\n\n          _this.posting = false;\n        }\n      })).catch(err => {\n        // TODO: 显示错误信息\n        // 取消loading，提示考生重试\n        _this.posting = false;\n\n        _this._loading.setValue(false);\n\n        _this._alertSvc.setValue({\n          status: true,\n          info: {\n            bodyText: _this.translate.instant(\"personInfo.errTip\")\n          }\n        });\n\n        console.log(\"ERROR: confirmLogin:\", err);\n      });\n    })();\n  }\n\n  checkLocalSession() {\n    // 获取本地form、response，和login接口获取的reponse.form_created和reponsse.version比较，本地response较新且form_created不一致就提交form、response\n    const session = this.examService.session;\n    joyshell.GetLocalSession(session.id, this.entryInfo.permit).then(localData => {\n      const onlineRes = this.entryInfo.response;\n      let localRes = {};\n      console.log(\"* Local form loaded [p]: succesfull\");\n\n      if (localData && localData.form) {\n        localRes = localData.response ? JSON.parse(localData.response) : console.log(\"** Local response [p]: undefined \");\n        const localForm = JSON.parse(localData.form);\n\n        if (this.ifPostLocalData(localRes, localForm, onlineRes)) {\n          // 本地较新时\n          this.patchLocalSession(localForm, localRes);\n        } else {\n          console.log(\"** use online data [p]\");\n          this.confirmLoginSuccess();\n          this.posting = false;\n        }\n      } else {\n        console.log(\"** Local form [p]: null\");\n        this.confirmLoginSuccess();\n        this.posting = false;\n      }\n    }, err => {\n      // 获取本地form、response异常\n      console.log(\"* Local form loaded [p]: failed\");\n      this.examService.localFormLoaded = false;\n      this.posting = false;\n    });\n  }\n\n  patchLocalSession(localForm, localRes) {\n    this.examService.patchFormAndResponse(localForm, localRes).pipe(finalize(() => {\n      this._loading.setValue(false);\n\n      this.posting = false;\n    })).subscribe(() => {\n      // 本地数据提交成功\n      this.confirmLoginSuccess();\n      console.log(\"* Local form response uploaded [p]: succesfull\");\n    }, () => {\n      // 本地数据提交失败提示：“数据读取失败，请重新登录”\n      console.log(\"* Local form response uploaded [p]: failed\");\n      this.examService.localFormLoaded = false;\n    });\n  }\n\n  ifPostLocalData(localRes, localForm, onlineRes) {\n    // 1. 本地和线上都有，本地版本号大，且form created不一致；2. 本地有线上没有；\n    if (onlineRes && onlineRes.form_created && localRes && localRes.sections) {\n      if (localRes.version > onlineRes.version && onlineRes.form_created !== localForm.created) {\n        console.log(`** Use local data [p]: online response.form_created: ${onlineRes.form_created}, localForm.created: ${localForm.created}`);\n        return true;\n      }\n    } else if (onlineRes && !onlineRes.form_created && localRes && localRes.sections) {\n      console.log(\"** Use local data [p], login.response: {}\");\n      return true;\n    }\n\n    return false;\n  }\n\n  confirmLoginSuccess() {\n    this.examService.examStatus.infoConfirm = true;\n    this.utilSer.JTCustomEvt(\"personInfoConfirmed\"); // #OnlineExam: 在线考试模式确认登录后再锁屏\n\n    if (window.is_online && joyshell && this.examService.session.config.lock_screen) {\n      window.joyshell.LockDesktop(\"lock\", {\n        flag: LockFlag.relax\n      });\n    } // 提交confirm login 之后再去跳转路由\n\n\n    const s = this.examService.session;\n\n    if (s.config.check_face && s.test !== 1) {\n      this.router.navigate([\"/exam/faceDetect\"], {\n        skipLocationChange: true\n      });\n    } else {\n      this.router.navigate([\"/exam/notice\"], {\n        skipLocationChange: true\n      });\n    }\n  }\n\n  cancelConfirm() {\n    this.showRedirectLoginTip = true;\n  }\n\n  confirmRedirectLogin() {\n    this.back(\"login\");\n  }\n\n  cancelRedirectLogin() {\n    this.showRedirectLoginTip = false;\n  }\n\n  back(type) {\n    if (type === \"login\") {\n      this.router.navigate([\"/exam/login\"], {\n        skipLocationChange: true\n      });\n    } else if (type === \"info\") {\n      this.infoErrTip = false;\n    }\n  }\n\n  confirmDataFailed() {\n    this.examService.redirectToLogin();\n  }\n\n}\n\nPersonInfoComponent.ɵfac = function PersonInfoComponent_Factory(t) {\n  return new (t || PersonInfoComponent)(i0.ɵɵdirectiveInject(i1.ExamService), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.AutoTestService), i0.ɵɵdirectiveInject(i4.UtilService), i0.ɵɵdirectiveInject(i5.CustomLoadingService), i0.ɵɵdirectiveInject(i6.LocalStorageService), i0.ɵɵdirectiveInject(i7.TranslateService), i0.ɵɵdirectiveInject(i8.MobileService), i0.ɵɵdirectiveInject(i9.CustomAlertService));\n};\n\nPersonInfoComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: PersonInfoComponent,\n  selectors: [[\"app-person-info\"]],\n  decls: 29,\n  vars: 22,\n  consts: [[1, \"personal-wrap\"], [1, \"yo-personal\", \"container-fluid\", \"wrap-entry-block\", 3, \"ngCss\", \"ngClass\"], [1, \"session-title\", \"mt-2\", \"mb-2\"], [1, \"entry-block\", \"person-info\", \"mb-3\"], [1, \"entry-block-title\"], [1, \"row\", \"info\", \"mt-sm-5\", \"mb-sm-5\"], [1, \"col-md-5\", \"col-sm-12\", \"mb-2\", \"wrap-img\", \"text-right\"], [\"alt\", \"\", 3, \"src\", 4, \"ngIf\"], [\"class\", \"default-photo\", 4, \"ngIf\"], [1, \"col-md-7\", \"col-12\", \"mb-2\", \"mt-2\", \"mt-md-0\"], [4, \"ngIf\"], [1, \"row\", \"justify-content-center\", \"mt-3\", \"mb-4\"], [\"class\", \"col-3 col-offset-3 text-center\", 4, \"ngIf\"], [1, \"col-3\", \"text-center\"], [\"id\", \"cancel-btn\", 1, \"yk-btn-secondary\", 3, \"click\"], [1, \"tip\"], [1, \"tip-detail\"], [3, \"bodyText\", \"cancelText\", \"onConfirm\", 4, \"ngIf\"], [3, \"bodyText\", \"onConfirm\", \"onCancel\", 4, \"ngIf\"], [\"alt\", \"\", 3, \"src\"], [1, \"default-photo\"], [\"class\", \"wrap-info\", 4, \"ngFor\", \"ngForOf\"], [1, \"wrap-info\"], [1, \"text-right\"], [1, \"info-name\"], [1, \"info-cont\"], [1, \"wrap-info\", \"full-name\"], [1, \"text-right\", \"full-name\"], [1, \"info-cont\", \"full-name\"], [1, \"wrap-info\", \"gender\"], [1, \"text-right\", \"gender\"], [1, \"info-cont\", \"mb-1\", \"mb-sm-2\", \"gender\"], [1, \"wrap-info\", \"permit\"], [1, \"text-right\", \"permit\"], [1, \"info-cont\", \"mb-1\", \"mb-sm-2\", \"permit\"], [1, \"wrap-info\", \"identity_id\"], [1, \"text-right\", \"identity_id\"], [1, \"info-cont\", \"mb-1\", \"mb-sm-2\", \"identity_id\"], [1, \"wrap-info\", \"subject\"], [1, \"text-right\", \"subject\"], [1, \"info-cont\", \"mb-1\", \"mb-sm-2\", \"subject\"], [1, \"col-3\", \"col-offset-3\", \"text-center\"], [\"id\", \"confirm-btn\", \"toggleFullscreen\", \"\", 1, \"yk-btn-primary\", 3, \"click\"], [\"id\", \"confirm-btn\", 1, \"yk-btn-primary\", 3, \"disabled\", \"click\"], [3, \"bodyText\", \"cancelText\", \"onConfirm\"], [3, \"bodyText\", \"onConfirm\", \"onCancel\"]],\n  template: function PersonInfoComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"div\", 0);\n      i0.ɵɵelement(1, \"app-small-seat-num\");\n      i0.ɵɵelementStart(2, \"div\", 1)(3, \"div\", 2);\n      i0.ɵɵtext(4);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(5, \"div\", 3)(6, \"div\", 4);\n      i0.ɵɵtext(7);\n      i0.ɵɵpipe(8, \"translate\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(9, \"div\", 5)(10, \"div\", 6);\n      i0.ɵɵtemplate(11, PersonInfoComponent_img_11_Template, 1, 1, \"img\", 7);\n      i0.ɵɵtemplate(12, PersonInfoComponent_div_12_Template, 1, 0, \"div\", 8);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(13, \"div\", 9);\n      i0.ɵɵtemplate(14, PersonInfoComponent_div_14_Template, 2, 1, \"div\", 10);\n      i0.ɵɵtemplate(15, PersonInfoComponent_div_15_Template, 6, 5, \"div\", 10);\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(16, \"div\", 11);\n      i0.ɵɵtemplate(17, PersonInfoComponent_div_17_Template, 4, 3, \"div\", 12);\n      i0.ɵɵtemplate(18, PersonInfoComponent_div_18_Template, 4, 4, \"div\", 12);\n      i0.ɵɵelementStart(19, \"div\", 13)(20, \"button\", 14);\n      i0.ɵɵlistener(\"click\", function PersonInfoComponent_Template_button_click_20_listener() {\n        return ctx.cancelConfirm();\n      });\n      i0.ɵɵtext(21);\n      i0.ɵɵpipe(22, \"translate\");\n      i0.ɵɵelementEnd()()()();\n      i0.ɵɵelementStart(23, \"div\", 15)(24, \"div\", 16);\n      i0.ɵɵtext(25);\n      i0.ɵɵpipe(26, \"translate\");\n      i0.ɵɵelementEnd()()()();\n      i0.ɵɵtemplate(27, PersonInfoComponent_custom_confirm_27_Template, 1, 2, \"custom-confirm\", 17);\n      i0.ɵɵtemplate(28, PersonInfoComponent_custom_confirm_28_Template, 1, 1, \"custom-confirm\", 18);\n    }\n\n    if (rf & 2) {\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngCss\", \"person_info\")(\"ngClass\", i0.ɵɵpureFunction1(20, _c0, ctx.utilSer.langSet === \"en\"));\n      i0.ɵɵadvance(2);\n      i0.ɵɵtextInterpolate(ctx.examService.session.title);\n      i0.ɵɵadvance(3);\n      i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(8, 14, \"personInfo.title\"));\n      i0.ɵɵadvance(4);\n      i0.ɵɵproperty(\"ngIf\", ctx.entryInfo.personal.photo);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", !ctx.entryInfo.personal.photo);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngIf\", ctx.skin_personal && ctx.skin_personal.length);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", !ctx.skin_personal);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngIf\", ctx.examService.session.config.lock_screen && !ctx.joyshell);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", !ctx.examService.session.config.lock_screen && !ctx.joyshell || ctx.joyshell);\n      i0.ɵɵadvance(3);\n      i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(22, 16, \"modal.cancel\"), \" \");\n      i0.ɵɵadvance(4);\n      i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(26, 18, \"personInfo.tip1\"));\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngIf\", !ctx.examService.localFormLoaded);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.showRedirectLoginTip);\n    }\n  },\n  dependencies: [i10.NgClass, i10.NgForOf, i10.NgIf, i11.CustomConfirmComponent, i12.SmallSeatNumComponent, i13.ToggleFullscreenDirective, i14.CssDirective, i7.TranslatePipe],\n  styles: [\".personal-wrap[_ngcontent-%COMP%]{width:100%;background-size:100% auto;position:absolute;top:0;bottom:0;overflow:auto}.person-info[_ngcontent-%COMP%]{max-width:900px}.info[_ngcontent-%COMP%]{margin:0;color:var(--ac-border, #333)}.info[_ngcontent-%COMP%]   .wrap-img[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{vertical-align:top}.info[_ngcontent-%COMP%]   .wrap-img[_ngcontent-%COMP%]   .default-photo[_ngcontent-%COMP%]{display:inline-block;background:url(l-default-photo.e056378b51bfae1d.png) 50% 50% no-repeat;background-size:contain}.info[_ngcontent-%COMP%]   span.info-name[_ngcontent-%COMP%]{display:inline-block;width:75px;text-align:justify;line-height:2.5;text-align-last:justify}.info[_ngcontent-%COMP%]   .info-cont[_ngcontent-%COMP%]{word-break:break-all;word-wrap:break-word;padding-left:10px}.tip[_ngcontent-%COMP%]{margin:30px auto;text-align:center}.tip[_ngcontent-%COMP%]   .tip-detail[_ngcontent-%COMP%]{display:inline-block;height:30px;line-height:30px;color:#383e50;font-size:14px;background-color:#e7edff;border-radius:15px;padding:0 20px}.yo-personal.lan-en[_ngcontent-%COMP%]   .info[_ngcontent-%COMP%]   span.info-name[_ngcontent-%COMP%]{width:160px;text-align:right;text-align-last:right}@media (min-width: 768px){.dl-horizontal[_ngcontent-%COMP%]   dd[_ngcontent-%COMP%]{margin-left:100px}.dl-horizontal[_ngcontent-%COMP%]   dt[_ngcontent-%COMP%]{width:80px}.wrap-img[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{height:200px}.default-photo[_ngcontent-%COMP%]{width:200px;height:200px}}@media (max-width: 767.98px){.wrap-img[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{height:120px}.default-photo[_ngcontent-%COMP%]{width:120px;height:120px}}\"]\n});", "map": null, "metadata": {}, "sourceType": "module"}