{"ast": null, "code": "export var octDocs = {\n  name: 'oct',\n  category: 'Utils',\n  syntax: ['oct(value)'],\n  description: 'Format a number as octal',\n  examples: ['oct(56)'],\n  seealso: ['bin', 'hex']\n};", "map": {"version": 3, "names": ["octDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["D:/work/joyserver/client/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/utils/oct.js"], "sourcesContent": ["export var octDocs = {\n  name: 'oct',\n  category: 'Utils',\n  syntax: ['oct(value)'],\n  description: 'Format a number as octal',\n  examples: ['oct(56)'],\n  seealso: ['bin', 'hex']\n};"], "mappings": "AAAA,OAAO,IAAIA,OAAO,GAAG;EACnBC,IAAI,EAAE,KADa;EAEnBC,QAAQ,EAAE,OAFS;EAGnBC,MAAM,EAAE,CAAC,YAAD,CAHW;EAInBC,WAAW,EAAE,0BAJM;EAKnBC,QAAQ,EAAE,CAAC,SAAD,CALS;EAMnBC,OAAO,EAAE,CAAC,KAAD,EAAQ,KAAR;AANU,CAAd"}, "metadata": {}, "sourceType": "module"}