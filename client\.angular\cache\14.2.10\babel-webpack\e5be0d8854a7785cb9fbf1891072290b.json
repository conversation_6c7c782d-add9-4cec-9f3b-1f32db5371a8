{"ast": null, "code": "// A Javascript implementaion of the \"xor128\" prng algorithm by\n// <PERSON>.  See http://www.jstatsoft.org/v08/i14/paper\n(function (global, module, define) {\n  function XorGen(seed) {\n    var me = this,\n        strseed = '';\n    me.x = 0;\n    me.y = 0;\n    me.z = 0;\n    me.w = 0; // Set up generator function.\n\n    me.next = function () {\n      var t = me.x ^ me.x << 11;\n      me.x = me.y;\n      me.y = me.z;\n      me.z = me.w;\n      return me.w ^= me.w >>> 19 ^ t ^ t >>> 8;\n    };\n\n    if (seed === (seed | 0)) {\n      // Integer seed.\n      me.x = seed;\n    } else {\n      // String seed.\n      strseed += seed;\n    } // Mix in string seed, then discard an initial batch of 64 values.\n\n\n    for (var k = 0; k < strseed.length + 64; k++) {\n      me.x ^= strseed.charCodeAt(k) | 0;\n      me.next();\n    }\n  }\n\n  function copy(f, t) {\n    t.x = f.x;\n    t.y = f.y;\n    t.z = f.z;\n    t.w = f.w;\n    return t;\n  }\n\n  function impl(seed, opts) {\n    var xg = new XorGen(seed),\n        state = opts && opts.state,\n        prng = function () {\n      return (xg.next() >>> 0) / 0x100000000;\n    };\n\n    prng.double = function () {\n      do {\n        var top = xg.next() >>> 11,\n            bot = (xg.next() >>> 0) / 0x100000000,\n            result = (top + bot) / (1 << 21);\n      } while (result === 0);\n\n      return result;\n    };\n\n    prng.int32 = xg.next;\n    prng.quick = prng;\n\n    if (state) {\n      if (typeof state == 'object') copy(state, xg);\n\n      prng.state = function () {\n        return copy(xg, {});\n      };\n    }\n\n    return prng;\n  }\n\n  if (module && module.exports) {\n    module.exports = impl;\n  } else if (define && define.amd) {\n    define(function () {\n      return impl;\n    });\n  } else {\n    this.xor128 = impl;\n  }\n})(this, typeof module == 'object' && module, // present in node.js\ntypeof define == 'function' && define // present with an AMD loader\n);", "map": {"version": 3, "names": ["global", "module", "define", "XorGen", "seed", "me", "strseed", "x", "y", "z", "w", "next", "t", "k", "length", "charCodeAt", "copy", "f", "impl", "opts", "xg", "state", "prng", "double", "top", "bot", "result", "int32", "quick", "exports", "amd", "xor128"], "sources": ["D:/work/joyserver/client/node_modules/seedrandom/lib/xor128.js"], "sourcesContent": ["// A Javascript implementaion of the \"xor128\" prng algorithm by\n// <PERSON>.  See http://www.jstatsoft.org/v08/i14/paper\n\n(function(global, module, define) {\n\nfunction XorGen(seed) {\n  var me = this, strseed = '';\n\n  me.x = 0;\n  me.y = 0;\n  me.z = 0;\n  me.w = 0;\n\n  // Set up generator function.\n  me.next = function() {\n    var t = me.x ^ (me.x << 11);\n    me.x = me.y;\n    me.y = me.z;\n    me.z = me.w;\n    return me.w ^= (me.w >>> 19) ^ t ^ (t >>> 8);\n  };\n\n  if (seed === (seed | 0)) {\n    // Integer seed.\n    me.x = seed;\n  } else {\n    // String seed.\n    strseed += seed;\n  }\n\n  // Mix in string seed, then discard an initial batch of 64 values.\n  for (var k = 0; k < strseed.length + 64; k++) {\n    me.x ^= strseed.charCodeAt(k) | 0;\n    me.next();\n  }\n}\n\nfunction copy(f, t) {\n  t.x = f.x;\n  t.y = f.y;\n  t.z = f.z;\n  t.w = f.w;\n  return t;\n}\n\nfunction impl(seed, opts) {\n  var xg = new XorGen(seed),\n      state = opts && opts.state,\n      prng = function() { return (xg.next() >>> 0) / 0x100000000; };\n  prng.double = function() {\n    do {\n      var top = xg.next() >>> 11,\n          bot = (xg.next() >>> 0) / 0x100000000,\n          result = (top + bot) / (1 << 21);\n    } while (result === 0);\n    return result;\n  };\n  prng.int32 = xg.next;\n  prng.quick = prng;\n  if (state) {\n    if (typeof(state) == 'object') copy(state, xg);\n    prng.state = function() { return copy(xg, {}); }\n  }\n  return prng;\n}\n\nif (module && module.exports) {\n  module.exports = impl;\n} else if (define && define.amd) {\n  define(function() { return impl; });\n} else {\n  this.xor128 = impl;\n}\n\n})(\n  this,\n  (typeof module) == 'object' && module,    // present in node.js\n  (typeof define) == 'function' && define   // present with an AMD loader\n);\n\n\n"], "mappings": "AAAA;AACA;AAEA,CAAC,UAASA,MAAT,EAAiBC,MAAjB,EAAyBC,MAAzB,EAAiC;EAElC,SAASC,MAAT,CAAgBC,IAAhB,EAAsB;IACpB,IAAIC,EAAE,GAAG,IAAT;IAAA,IAAeC,OAAO,GAAG,EAAzB;IAEAD,EAAE,CAACE,CAAH,GAAO,CAAP;IACAF,EAAE,CAACG,CAAH,GAAO,CAAP;IACAH,EAAE,CAACI,CAAH,GAAO,CAAP;IACAJ,EAAE,CAACK,CAAH,GAAO,CAAP,CANoB,CAQpB;;IACAL,EAAE,CAACM,IAAH,GAAU,YAAW;MACnB,IAAIC,CAAC,GAAGP,EAAE,CAACE,CAAH,GAAQF,EAAE,CAACE,CAAH,IAAQ,EAAxB;MACAF,EAAE,CAACE,CAAH,GAAOF,EAAE,CAACG,CAAV;MACAH,EAAE,CAACG,CAAH,GAAOH,EAAE,CAACI,CAAV;MACAJ,EAAE,CAACI,CAAH,GAAOJ,EAAE,CAACK,CAAV;MACA,OAAOL,EAAE,CAACK,CAAH,IAASL,EAAE,CAACK,CAAH,KAAS,EAAV,GAAgBE,CAAhB,GAAqBA,CAAC,KAAK,CAA1C;IACD,CAND;;IAQA,IAAIR,IAAI,MAAMA,IAAI,GAAG,CAAb,CAAR,EAAyB;MACvB;MACAC,EAAE,CAACE,CAAH,GAAOH,IAAP;IACD,CAHD,MAGO;MACL;MACAE,OAAO,IAAIF,IAAX;IACD,CAvBmB,CAyBpB;;;IACA,KAAK,IAAIS,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGP,OAAO,CAACQ,MAAR,GAAiB,EAArC,EAAyCD,CAAC,EAA1C,EAA8C;MAC5CR,EAAE,CAACE,CAAH,IAAQD,OAAO,CAACS,UAAR,CAAmBF,CAAnB,IAAwB,CAAhC;MACAR,EAAE,CAACM,IAAH;IACD;EACF;;EAED,SAASK,IAAT,CAAcC,CAAd,EAAiBL,CAAjB,EAAoB;IAClBA,CAAC,CAACL,CAAF,GAAMU,CAAC,CAACV,CAAR;IACAK,CAAC,CAACJ,CAAF,GAAMS,CAAC,CAACT,CAAR;IACAI,CAAC,CAACH,CAAF,GAAMQ,CAAC,CAACR,CAAR;IACAG,CAAC,CAACF,CAAF,GAAMO,CAAC,CAACP,CAAR;IACA,OAAOE,CAAP;EACD;;EAED,SAASM,IAAT,CAAcd,IAAd,EAAoBe,IAApB,EAA0B;IACxB,IAAIC,EAAE,GAAG,IAAIjB,MAAJ,CAAWC,IAAX,CAAT;IAAA,IACIiB,KAAK,GAAGF,IAAI,IAAIA,IAAI,CAACE,KADzB;IAAA,IAEIC,IAAI,GAAG,YAAW;MAAE,OAAO,CAACF,EAAE,CAACT,IAAH,OAAc,CAAf,IAAoB,WAA3B;IAAyC,CAFjE;;IAGAW,IAAI,CAACC,MAAL,GAAc,YAAW;MACvB,GAAG;QACD,IAAIC,GAAG,GAAGJ,EAAE,CAACT,IAAH,OAAc,EAAxB;QAAA,IACIc,GAAG,GAAG,CAACL,EAAE,CAACT,IAAH,OAAc,CAAf,IAAoB,WAD9B;QAAA,IAEIe,MAAM,GAAG,CAACF,GAAG,GAAGC,GAAP,KAAe,KAAK,EAApB,CAFb;MAGD,CAJD,QAISC,MAAM,KAAK,CAJpB;;MAKA,OAAOA,MAAP;IACD,CAPD;;IAQAJ,IAAI,CAACK,KAAL,GAAaP,EAAE,CAACT,IAAhB;IACAW,IAAI,CAACM,KAAL,GAAaN,IAAb;;IACA,IAAID,KAAJ,EAAW;MACT,IAAI,OAAOA,KAAP,IAAiB,QAArB,EAA+BL,IAAI,CAACK,KAAD,EAAQD,EAAR,CAAJ;;MAC/BE,IAAI,CAACD,KAAL,GAAa,YAAW;QAAE,OAAOL,IAAI,CAACI,EAAD,EAAK,EAAL,CAAX;MAAsB,CAAhD;IACD;;IACD,OAAOE,IAAP;EACD;;EAED,IAAIrB,MAAM,IAAIA,MAAM,CAAC4B,OAArB,EAA8B;IAC5B5B,MAAM,CAAC4B,OAAP,GAAiBX,IAAjB;EACD,CAFD,MAEO,IAAIhB,MAAM,IAAIA,MAAM,CAAC4B,GAArB,EAA0B;IAC/B5B,MAAM,CAAC,YAAW;MAAE,OAAOgB,IAAP;IAAc,CAA5B,CAAN;EACD,CAFM,MAEA;IACL,KAAKa,MAAL,GAAcb,IAAd;EACD;AAEA,CAvED,EAwEE,IAxEF,EAyEG,OAAOjB,MAAR,IAAmB,QAAnB,IAA+BA,MAzEjC,EAyE4C;AACzC,OAAOC,MAAR,IAAmB,UAAnB,IAAiCA,MA1EnC,CA0E4C;AA1E5C"}, "metadata": {}, "sourceType": "script"}