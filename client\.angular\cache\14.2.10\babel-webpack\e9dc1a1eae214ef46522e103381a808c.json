{"ast": null, "code": "export var DEFAULT_CONFIG = {\n  // minimum relative difference between two compared values,\n  // used by all comparison functions\n  relTol: 1e-12,\n  // minimum absolute difference between two compared values,\n  // used by all comparison functions\n  absTol: 1e-15,\n  // type of default matrix output. Choose 'matrix' (default) or 'array'\n  matrix: 'Matrix',\n  // type of default number output. Choose 'number' (default) 'BigNumber', 'bigint', or 'Fraction'\n  number: 'number',\n  // type of fallback used for config { number: 'bigint' } when a value cannot be represented\n  // in the configured numeric type. Choose 'number' (default) or 'BigNumber'.\n  numberFallback: 'number',\n  // number of significant digits in BigNumbers\n  precision: 64,\n  // predictable output type of functions. When true, output type depends only\n  // on the input types. When false (default), output type can vary depending\n  // on input values. For example `math.sqrt(-4)` returns `complex('2i')` when\n  // predictable is false, and returns `NaN` when true.\n  predictable: false,\n  // random seed for seeded pseudo random number generation\n  // null = randomly seed\n  randomSeed: null\n};", "map": null, "metadata": {}, "sourceType": "module"}