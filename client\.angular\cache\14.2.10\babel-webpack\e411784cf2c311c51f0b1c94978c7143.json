{"ast": null, "code": "/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { log2Dependencies } from './dependenciesLog2.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createRandomInt } from '../../factoriesAny.js';\nexport var randomIntDependencies = {\n  log2Dependencies,\n  typedDependencies,\n  createRandomInt\n};", "map": {"version": 3, "names": ["log2Dependencies", "typedDependencies", "createRandomInt", "randomIntDependencies"], "sources": ["D:/work/joyserver/client/node_modules/mathjs/lib/esm/entry/dependenciesAny/dependenciesRandomInt.generated.js"], "sourcesContent": ["/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { log2Dependencies } from './dependenciesLog2.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createRandomInt } from '../../factoriesAny.js';\nexport var randomIntDependencies = {\n  log2Dependencies,\n  typedDependencies,\n  createRandomInt\n};"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,gBAAT,QAAiC,iCAAjC;AACA,SAASC,iBAAT,QAAkC,kCAAlC;AACA,SAASC,eAAT,QAAgC,uBAAhC;AACA,OAAO,IAAIC,qBAAqB,GAAG;EACjCH,gBADiC;EAEjCC,iBAFiC;EAGjCC;AAHiC,CAA5B"}, "metadata": {}, "sourceType": "module"}