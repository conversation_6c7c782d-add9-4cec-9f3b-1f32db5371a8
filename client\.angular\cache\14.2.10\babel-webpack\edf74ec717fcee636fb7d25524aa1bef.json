{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nimport { format } from '../../utils/string.js';\nimport { typeOf } from '../../utils/is.js';\nimport { escapeLatex } from '../../utils/latex.js';\nimport { factory } from '../../utils/factory.js';\nvar name = 'ConstantNode';\nvar dependencies = ['Node'];\nexport var createConstantNode = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    Node\n  } = _ref;\n\n  class ConstantNode extends Node {\n    /**\n     * A ConstantNode holds a constant value like a number or string.\n     *\n     * Usage:\n     *\n     *     new ConstantNode(2.3)\n     *     new ConstantNode('hello')\n     *\n     * @param {*} value    Value can be any type (number, BigNumber, bigint, string, ...)\n     * @constructor ConstantNode\n     * @extends {Node}\n     */\n    constructor(value) {\n      super();\n      this.value = value;\n    }\n\n    get type() {\n      return name;\n    }\n\n    get isConstantNode() {\n      return true;\n    }\n    /**\n     * Compile a node into a JavaScript function.\n     * This basically pre-calculates as much as possible and only leaves open\n     * calculations which depend on a dynamic scope with variables.\n     * @param {Object} math     Math.js namespace with functions and constants.\n     * @param {Object} argNames An object with argument names as key and `true`\n     *                          as value. Used in the SymbolNode to optimize\n     *                          for arguments from user assigned functions\n     *                          (see FunctionAssignmentNode) or special symbols\n     *                          like `end` (see IndexNode).\n     * @return {function} Returns a function which can be called like:\n     *                        evalNode(scope: Object, args: Object, context: *)\n     */\n\n\n    _compile(math, argNames) {\n      var value = this.value;\n      return function evalConstantNode() {\n        return value;\n      };\n    }\n    /**\n     * Execute a callback for each of the child nodes of this node\n     * @param {function(child: Node, path: string, parent: Node)} callback\n     */\n\n\n    forEach(callback) {// nothing to do, we don't have any children\n    }\n    /**\n     * Create a new ConstantNode with children produced by the given callback.\n     * Trivial because there are no children.\n     * @param {function(child: Node, path: string, parent: Node) : Node} callback\n     * @returns {ConstantNode} Returns a clone of the node\n     */\n\n\n    map(callback) {\n      return this.clone();\n    }\n    /**\n     * Create a clone of this node, a shallow copy\n     * @return {ConstantNode}\n     */\n\n\n    clone() {\n      return new ConstantNode(this.value);\n    }\n    /**\n     * Get string representation\n     * @param {Object} options\n     * @return {string} str\n     */\n\n\n    _toString(options) {\n      return format(this.value, options);\n    }\n    /**\n     * Get HTML representation\n     * @param {Object} options\n     * @return {string} str\n     */\n\n\n    _toHTML(options) {\n      var value = this._toString(options);\n\n      switch (typeOf(this.value)) {\n        case 'number':\n        case 'bigint':\n        case 'BigNumber':\n        case 'Fraction':\n          return '<span class=\"math-number\">' + value + '</span>';\n\n        case 'string':\n          return '<span class=\"math-string\">' + value + '</span>';\n\n        case 'boolean':\n          return '<span class=\"math-boolean\">' + value + '</span>';\n\n        case 'null':\n          return '<span class=\"math-null-symbol\">' + value + '</span>';\n\n        case 'undefined':\n          return '<span class=\"math-undefined\">' + value + '</span>';\n\n        default:\n          return '<span class=\"math-symbol\">' + value + '</span>';\n      }\n    }\n    /**\n     * Get a JSON representation of the node\n     * @returns {Object}\n     */\n\n\n    toJSON() {\n      return {\n        mathjs: name,\n        value: this.value\n      };\n    }\n    /**\n     * Instantiate a ConstantNode from its JSON representation\n     * @param {Object} json  An object structured like\n     *                       `{\"mathjs\": \"SymbolNode\", value: 2.3}`,\n     *                       where mathjs is optional\n     * @returns {ConstantNode}\n     */\n\n\n    static fromJSON(json) {\n      return new ConstantNode(json.value);\n    }\n    /**\n     * Get LaTeX representation\n     * @param {Object} options\n     * @return {string} str\n     */\n\n\n    _toTex(options) {\n      var value = this._toString(options);\n\n      var type = typeOf(this.value);\n\n      switch (type) {\n        case 'string':\n          return '\\\\mathtt{' + escapeLatex(value) + '}';\n\n        case 'number':\n        case 'BigNumber':\n          {\n            var finite = type === 'BigNumber' ? this.value.isFinite() : isFinite(this.value);\n\n            if (!finite) {\n              return this.value.valueOf() < 0 ? '-\\\\infty' : '\\\\infty';\n            }\n\n            var index = value.toLowerCase().indexOf('e');\n\n            if (index !== -1) {\n              return value.substring(0, index) + '\\\\cdot10^{' + value.substring(index + 1) + '}';\n            }\n\n            return value;\n          }\n\n        case 'bigint':\n          {\n            return value.toString();\n          }\n\n        case 'Fraction':\n          return this.value.toLatex();\n\n        default:\n          return value;\n      }\n    }\n\n  }\n\n  _defineProperty(ConstantNode, \"name\", name);\n\n  return ConstantNode;\n}, {\n  isClass: true,\n  isNode: true\n});", "map": {"version": 3, "names": ["_defineProperty", "format", "typeOf", "escapeLatex", "factory", "name", "dependencies", "createConstantNode", "_ref", "Node", "ConstantNode", "constructor", "value", "type", "isConstantNode", "_compile", "math", "argNames", "evalConstantNode", "for<PERSON>ach", "callback", "map", "clone", "_toString", "options", "_toHTML", "toJSON", "mathjs", "fromJSON", "json", "_toTex", "finite", "isFinite", "valueOf", "index", "toLowerCase", "indexOf", "substring", "toString", "toLatex", "isClass", "isNode"], "sources": ["D:/work/joyserver/client/node_modules/mathjs/lib/esm/expression/node/ConstantNode.js"], "sourcesContent": ["import _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nimport { format } from '../../utils/string.js';\nimport { typeOf } from '../../utils/is.js';\nimport { escapeLatex } from '../../utils/latex.js';\nimport { factory } from '../../utils/factory.js';\nvar name = 'ConstantNode';\nvar dependencies = ['Node'];\nexport var createConstantNode = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    Node\n  } = _ref;\n  class ConstantNode extends Node {\n    /**\n     * A ConstantNode holds a constant value like a number or string.\n     *\n     * Usage:\n     *\n     *     new ConstantNode(2.3)\n     *     new ConstantNode('hello')\n     *\n     * @param {*} value    Value can be any type (number, BigNumber, bigint, string, ...)\n     * @constructor ConstantNode\n     * @extends {Node}\n     */\n    constructor(value) {\n      super();\n      this.value = value;\n    }\n    get type() {\n      return name;\n    }\n    get isConstantNode() {\n      return true;\n    }\n\n    /**\n     * Compile a node into a JavaScript function.\n     * This basically pre-calculates as much as possible and only leaves open\n     * calculations which depend on a dynamic scope with variables.\n     * @param {Object} math     Math.js namespace with functions and constants.\n     * @param {Object} argNames An object with argument names as key and `true`\n     *                          as value. Used in the SymbolNode to optimize\n     *                          for arguments from user assigned functions\n     *                          (see FunctionAssignmentNode) or special symbols\n     *                          like `end` (see IndexNode).\n     * @return {function} Returns a function which can be called like:\n     *                        evalNode(scope: Object, args: Object, context: *)\n     */\n    _compile(math, argNames) {\n      var value = this.value;\n      return function evalConstantNode() {\n        return value;\n      };\n    }\n\n    /**\n     * Execute a callback for each of the child nodes of this node\n     * @param {function(child: Node, path: string, parent: Node)} callback\n     */\n    forEach(callback) {\n      // nothing to do, we don't have any children\n    }\n\n    /**\n     * Create a new ConstantNode with children produced by the given callback.\n     * Trivial because there are no children.\n     * @param {function(child: Node, path: string, parent: Node) : Node} callback\n     * @returns {ConstantNode} Returns a clone of the node\n     */\n    map(callback) {\n      return this.clone();\n    }\n\n    /**\n     * Create a clone of this node, a shallow copy\n     * @return {ConstantNode}\n     */\n    clone() {\n      return new ConstantNode(this.value);\n    }\n\n    /**\n     * Get string representation\n     * @param {Object} options\n     * @return {string} str\n     */\n    _toString(options) {\n      return format(this.value, options);\n    }\n\n    /**\n     * Get HTML representation\n     * @param {Object} options\n     * @return {string} str\n     */\n    _toHTML(options) {\n      var value = this._toString(options);\n      switch (typeOf(this.value)) {\n        case 'number':\n        case 'bigint':\n        case 'BigNumber':\n        case 'Fraction':\n          return '<span class=\"math-number\">' + value + '</span>';\n        case 'string':\n          return '<span class=\"math-string\">' + value + '</span>';\n        case 'boolean':\n          return '<span class=\"math-boolean\">' + value + '</span>';\n        case 'null':\n          return '<span class=\"math-null-symbol\">' + value + '</span>';\n        case 'undefined':\n          return '<span class=\"math-undefined\">' + value + '</span>';\n        default:\n          return '<span class=\"math-symbol\">' + value + '</span>';\n      }\n    }\n\n    /**\n     * Get a JSON representation of the node\n     * @returns {Object}\n     */\n    toJSON() {\n      return {\n        mathjs: name,\n        value: this.value\n      };\n    }\n\n    /**\n     * Instantiate a ConstantNode from its JSON representation\n     * @param {Object} json  An object structured like\n     *                       `{\"mathjs\": \"SymbolNode\", value: 2.3}`,\n     *                       where mathjs is optional\n     * @returns {ConstantNode}\n     */\n    static fromJSON(json) {\n      return new ConstantNode(json.value);\n    }\n\n    /**\n     * Get LaTeX representation\n     * @param {Object} options\n     * @return {string} str\n     */\n    _toTex(options) {\n      var value = this._toString(options);\n      var type = typeOf(this.value);\n      switch (type) {\n        case 'string':\n          return '\\\\mathtt{' + escapeLatex(value) + '}';\n        case 'number':\n        case 'BigNumber':\n          {\n            var finite = type === 'BigNumber' ? this.value.isFinite() : isFinite(this.value);\n            if (!finite) {\n              return this.value.valueOf() < 0 ? '-\\\\infty' : '\\\\infty';\n            }\n            var index = value.toLowerCase().indexOf('e');\n            if (index !== -1) {\n              return value.substring(0, index) + '\\\\cdot10^{' + value.substring(index + 1) + '}';\n            }\n            return value;\n          }\n        case 'bigint':\n          {\n            return value.toString();\n          }\n        case 'Fraction':\n          return this.value.toLatex();\n        default:\n          return value;\n      }\n    }\n  }\n  _defineProperty(ConstantNode, \"name\", name);\n  return ConstantNode;\n}, {\n  isClass: true,\n  isNode: true\n});"], "mappings": "AAAA,OAAOA,eAAP,MAA4B,uCAA5B;AACA,SAASC,MAAT,QAAuB,uBAAvB;AACA,SAASC,MAAT,QAAuB,mBAAvB;AACA,SAASC,WAAT,QAA4B,sBAA5B;AACA,SAASC,OAAT,QAAwB,wBAAxB;AACA,IAAIC,IAAI,GAAG,cAAX;AACA,IAAIC,YAAY,GAAG,CAAC,MAAD,CAAnB;AACA,OAAO,IAAIC,kBAAkB,GAAG,eAAeH,OAAO,CAACC,IAAD,EAAOC,YAAP,EAAqBE,IAAI,IAAI;EACjF,IAAI;IACFC;EADE,IAEAD,IAFJ;;EAGA,MAAME,YAAN,SAA2BD,IAA3B,CAAgC;IAC9B;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACIE,WAAW,CAACC,KAAD,EAAQ;MACjB;MACA,KAAKA,KAAL,GAAaA,KAAb;IACD;;IACO,IAAJC,IAAI,GAAG;MACT,OAAOR,IAAP;IACD;;IACiB,IAAdS,cAAc,GAAG;MACnB,OAAO,IAAP;IACD;IAED;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;IACIC,QAAQ,CAACC,IAAD,EAAOC,QAAP,EAAiB;MACvB,IAAIL,KAAK,GAAG,KAAKA,KAAjB;MACA,OAAO,SAASM,gBAAT,GAA4B;QACjC,OAAON,KAAP;MACD,CAFD;IAGD;IAED;AACJ;AACA;AACA;;;IACIO,OAAO,CAACC,QAAD,EAAW,CAChB;IACD;IAED;AACJ;AACA;AACA;AACA;AACA;;;IACIC,GAAG,CAACD,QAAD,EAAW;MACZ,OAAO,KAAKE,KAAL,EAAP;IACD;IAED;AACJ;AACA;AACA;;;IACIA,KAAK,GAAG;MACN,OAAO,IAAIZ,YAAJ,CAAiB,KAAKE,KAAtB,CAAP;IACD;IAED;AACJ;AACA;AACA;AACA;;;IACIW,SAAS,CAACC,OAAD,EAAU;MACjB,OAAOvB,MAAM,CAAC,KAAKW,KAAN,EAAaY,OAAb,CAAb;IACD;IAED;AACJ;AACA;AACA;AACA;;;IACIC,OAAO,CAACD,OAAD,EAAU;MACf,IAAIZ,KAAK,GAAG,KAAKW,SAAL,CAAeC,OAAf,CAAZ;;MACA,QAAQtB,MAAM,CAAC,KAAKU,KAAN,CAAd;QACE,KAAK,QAAL;QACA,KAAK,QAAL;QACA,KAAK,WAAL;QACA,KAAK,UAAL;UACE,OAAO,+BAA+BA,KAA/B,GAAuC,SAA9C;;QACF,KAAK,QAAL;UACE,OAAO,+BAA+BA,KAA/B,GAAuC,SAA9C;;QACF,KAAK,SAAL;UACE,OAAO,gCAAgCA,KAAhC,GAAwC,SAA/C;;QACF,KAAK,MAAL;UACE,OAAO,oCAAoCA,KAApC,GAA4C,SAAnD;;QACF,KAAK,WAAL;UACE,OAAO,kCAAkCA,KAAlC,GAA0C,SAAjD;;QACF;UACE,OAAO,+BAA+BA,KAA/B,GAAuC,SAA9C;MAfJ;IAiBD;IAED;AACJ;AACA;AACA;;;IACIc,MAAM,GAAG;MACP,OAAO;QACLC,MAAM,EAAEtB,IADH;QAELO,KAAK,EAAE,KAAKA;MAFP,CAAP;IAID;IAED;AACJ;AACA;AACA;AACA;AACA;AACA;;;IACmB,OAARgB,QAAQ,CAACC,IAAD,EAAO;MACpB,OAAO,IAAInB,YAAJ,CAAiBmB,IAAI,CAACjB,KAAtB,CAAP;IACD;IAED;AACJ;AACA;AACA;AACA;;;IACIkB,MAAM,CAACN,OAAD,EAAU;MACd,IAAIZ,KAAK,GAAG,KAAKW,SAAL,CAAeC,OAAf,CAAZ;;MACA,IAAIX,IAAI,GAAGX,MAAM,CAAC,KAAKU,KAAN,CAAjB;;MACA,QAAQC,IAAR;QACE,KAAK,QAAL;UACE,OAAO,cAAcV,WAAW,CAACS,KAAD,CAAzB,GAAmC,GAA1C;;QACF,KAAK,QAAL;QACA,KAAK,WAAL;UACE;YACE,IAAImB,MAAM,GAAGlB,IAAI,KAAK,WAAT,GAAuB,KAAKD,KAAL,CAAWoB,QAAX,EAAvB,GAA+CA,QAAQ,CAAC,KAAKpB,KAAN,CAApE;;YACA,IAAI,CAACmB,MAAL,EAAa;cACX,OAAO,KAAKnB,KAAL,CAAWqB,OAAX,KAAuB,CAAvB,GAA2B,UAA3B,GAAwC,SAA/C;YACD;;YACD,IAAIC,KAAK,GAAGtB,KAAK,CAACuB,WAAN,GAAoBC,OAApB,CAA4B,GAA5B,CAAZ;;YACA,IAAIF,KAAK,KAAK,CAAC,CAAf,EAAkB;cAChB,OAAOtB,KAAK,CAACyB,SAAN,CAAgB,CAAhB,EAAmBH,KAAnB,IAA4B,YAA5B,GAA2CtB,KAAK,CAACyB,SAAN,CAAgBH,KAAK,GAAG,CAAxB,CAA3C,GAAwE,GAA/E;YACD;;YACD,OAAOtB,KAAP;UACD;;QACH,KAAK,QAAL;UACE;YACE,OAAOA,KAAK,CAAC0B,QAAN,EAAP;UACD;;QACH,KAAK,UAAL;UACE,OAAO,KAAK1B,KAAL,CAAW2B,OAAX,EAAP;;QACF;UACE,OAAO3B,KAAP;MAvBJ;IAyBD;;EAhK6B;;EAkKhCZ,eAAe,CAACU,YAAD,EAAe,MAAf,EAAuBL,IAAvB,CAAf;;EACA,OAAOK,YAAP;AACD,CAxKqD,EAwKnD;EACD8B,OAAO,EAAE,IADR;EAEDC,MAAM,EAAE;AAFP,CAxKmD,CAA/C"}, "metadata": {}, "sourceType": "module"}