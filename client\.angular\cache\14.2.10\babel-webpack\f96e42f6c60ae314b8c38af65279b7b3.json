{"ast": null, "code": "import { connectableObservableDescriptor } from '../observable/ConnectableObservable';\nexport function multicast(subjectOrSubjectFactory, selector) {\n  return function multicastOperatorFunction(source) {\n    let subjectFactory;\n\n    if (typeof subjectOrSubjectFactory === 'function') {\n      subjectFactory = subjectOrSubjectFactory;\n    } else {\n      subjectFactory = function subjectFactory() {\n        return subjectOrSubjectFactory;\n      };\n    }\n\n    if (typeof selector === 'function') {\n      return source.lift(new MulticastOperator(subjectFactory, selector));\n    }\n\n    const connectable = Object.create(source, connectableObservableDescriptor);\n    connectable.source = source;\n    connectable.subjectFactory = subjectFactory;\n    return connectable;\n  };\n}\nexport class MulticastOperator {\n  constructor(subjectFactory, selector) {\n    this.subjectFactory = subjectFactory;\n    this.selector = selector;\n  }\n\n  call(subscriber, source) {\n    const {\n      selector\n    } = this;\n    const subject = this.subjectFactory();\n    const subscription = selector(subject).subscribe(subscriber);\n    subscription.add(source.subscribe(subject));\n    return subscription;\n  }\n\n}", "map": {"version": 3, "names": ["connectableObservableDescriptor", "multicast", "subjectOrSubjectFactory", "selector", "multicastOperatorFunction", "source", "subjectFactory", "lift", "MulticastOperator", "connectable", "Object", "create", "constructor", "call", "subscriber", "subject", "subscription", "subscribe", "add"], "sources": ["D:/work/joyserver/client/node_modules/@angular-slider/ngx-slider/node_modules/rxjs/_esm2015/internal/operators/multicast.js"], "sourcesContent": ["import { connectableObservableDescriptor } from '../observable/ConnectableObservable';\nexport function multicast(subjectOrSubjectFactory, selector) {\n    return function multicastOperatorFunction(source) {\n        let subjectFactory;\n        if (typeof subjectOrSubjectFactory === 'function') {\n            subjectFactory = subjectOrSubjectFactory;\n        }\n        else {\n            subjectFactory = function subjectFactory() {\n                return subjectOrSubjectFactory;\n            };\n        }\n        if (typeof selector === 'function') {\n            return source.lift(new MulticastOperator(subjectFactory, selector));\n        }\n        const connectable = Object.create(source, connectableObservableDescriptor);\n        connectable.source = source;\n        connectable.subjectFactory = subjectFactory;\n        return connectable;\n    };\n}\nexport class MulticastOperator {\n    constructor(subjectFactory, selector) {\n        this.subjectFactory = subjectFactory;\n        this.selector = selector;\n    }\n    call(subscriber, source) {\n        const { selector } = this;\n        const subject = this.subjectFactory();\n        const subscription = selector(subject).subscribe(subscriber);\n        subscription.add(source.subscribe(subject));\n        return subscription;\n    }\n}\n"], "mappings": "AAAA,SAASA,+BAAT,QAAgD,qCAAhD;AACA,OAAO,SAASC,SAAT,CAAmBC,uBAAnB,EAA4CC,QAA5C,EAAsD;EACzD,OAAO,SAASC,yBAAT,CAAmCC,MAAnC,EAA2C;IAC9C,IAAIC,cAAJ;;IACA,IAAI,OAAOJ,uBAAP,KAAmC,UAAvC,EAAmD;MAC/CI,cAAc,GAAGJ,uBAAjB;IACH,CAFD,MAGK;MACDI,cAAc,GAAG,SAASA,cAAT,GAA0B;QACvC,OAAOJ,uBAAP;MACH,CAFD;IAGH;;IACD,IAAI,OAAOC,QAAP,KAAoB,UAAxB,EAAoC;MAChC,OAAOE,MAAM,CAACE,IAAP,CAAY,IAAIC,iBAAJ,CAAsBF,cAAtB,EAAsCH,QAAtC,CAAZ,CAAP;IACH;;IACD,MAAMM,WAAW,GAAGC,MAAM,CAACC,MAAP,CAAcN,MAAd,EAAsBL,+BAAtB,CAApB;IACAS,WAAW,CAACJ,MAAZ,GAAqBA,MAArB;IACAI,WAAW,CAACH,cAAZ,GAA6BA,cAA7B;IACA,OAAOG,WAAP;EACH,CAjBD;AAkBH;AACD,OAAO,MAAMD,iBAAN,CAAwB;EAC3BI,WAAW,CAACN,cAAD,EAAiBH,QAAjB,EAA2B;IAClC,KAAKG,cAAL,GAAsBA,cAAtB;IACA,KAAKH,QAAL,GAAgBA,QAAhB;EACH;;EACDU,IAAI,CAACC,UAAD,EAAaT,MAAb,EAAqB;IACrB,MAAM;MAAEF;IAAF,IAAe,IAArB;IACA,MAAMY,OAAO,GAAG,KAAKT,cAAL,EAAhB;IACA,MAAMU,YAAY,GAAGb,QAAQ,CAACY,OAAD,CAAR,CAAkBE,SAAlB,CAA4BH,UAA5B,CAArB;IACAE,YAAY,CAACE,GAAb,CAAiBb,MAAM,CAACY,SAAP,CAAiBF,OAAjB,CAAjB;IACA,OAAOC,YAAP;EACH;;AAX0B"}, "metadata": {}, "sourceType": "module"}