{"ast": null, "code": "export var configDocs = {\n  name: 'config',\n  category: 'Core',\n  syntax: ['config()', 'config(options)'],\n  description: 'Get configuration or change configuration.',\n  examples: ['config()', '1/3 + 1/4', 'config({number: \"Fraction\"})', '1/3 + 1/4'],\n  seealso: []\n};", "map": {"version": 3, "names": ["configDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["D:/work/joyserver/client/node_modules/mathjs/lib/esm/expression/embeddedDocs/core/config.js"], "sourcesContent": ["export var configDocs = {\n  name: 'config',\n  category: 'Core',\n  syntax: ['config()', 'config(options)'],\n  description: 'Get configuration or change configuration.',\n  examples: ['config()', '1/3 + 1/4', 'config({number: \"Fraction\"})', '1/3 + 1/4'],\n  seealso: []\n};"], "mappings": "AAAA,OAAO,IAAIA,UAAU,GAAG;EACtBC,IAAI,EAAE,QADgB;EAEtBC,QAAQ,EAAE,MAFY;EAGtBC,MAAM,EAAE,CAAC,UAAD,EAAa,iBAAb,CAHc;EAItBC,WAAW,EAAE,4CAJS;EAKtBC,QAAQ,EAAE,CAAC,UAAD,EAAa,WAAb,EAA0B,8BAA1B,EAA0D,WAA1D,CALY;EAMtBC,OAAO,EAAE;AANa,CAAjB"}, "metadata": {}, "sourceType": "module"}