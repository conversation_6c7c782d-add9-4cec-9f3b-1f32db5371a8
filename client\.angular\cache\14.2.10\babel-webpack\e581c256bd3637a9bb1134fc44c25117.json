{"ast": null, "code": "/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { ComplexDependencies } from './dependenciesComplexClass.generated.js';\nimport { fractionDependencies } from './dependenciesFraction.generated.js';\nimport { identityDependencies } from './dependenciesIdentity.generated.js';\nimport { invDependencies } from './dependenciesInv.generated.js';\nimport { matrixDependencies } from './dependenciesMatrix.generated.js';\nimport { multiplyDependencies } from './dependenciesMultiply.generated.js';\nimport { numberDependencies } from './dependenciesNumber.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createPow } from '../../factoriesAny.js';\nexport var powDependencies = {\n  ComplexDependencies,\n  fractionDependencies,\n  identityDependencies,\n  invDependencies,\n  matrixDependencies,\n  multiplyDependencies,\n  numberDependencies,\n  typedDependencies,\n  createPow\n};", "map": null, "metadata": {}, "sourceType": "module"}