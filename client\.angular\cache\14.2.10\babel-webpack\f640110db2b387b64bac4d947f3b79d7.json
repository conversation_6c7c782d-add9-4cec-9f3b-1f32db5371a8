{"ast": null, "code": "import { nearlyEqual as bigNearlyEqual } from '../../utils/bignumber/nearlyEqual.js';\nimport { nearlyEqual } from '../../utils/number.js';\nimport { factory } from '../../utils/factory.js';\nimport { createMatAlgo03xDSf } from '../../type/matrix/utils/matAlgo03xDSf.js';\nimport { createMatAlgo07xSSf } from '../../type/matrix/utils/matAlgo07xSSf.js';\nimport { createMatAlgo12xSfs } from '../../type/matrix/utils/matAlgo12xSfs.js';\nimport { createMatrixAlgorithmSuite } from '../../type/matrix/utils/matrixAlgorithmSuite.js';\nimport { createCompareUnits } from './compareUnits.js';\nvar name = 'smaller';\nvar dependencies = ['typed', 'config', 'bignumber', 'matrix', 'DenseMatrix', 'concat', 'SparseMatrix'];\nexport var createSmaller = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed,\n    config,\n    bignumber,\n    matrix,\n    DenseMatrix,\n    concat,\n    SparseMatrix\n  } = _ref;\n  var matAlgo03xDSf = createMatAlgo03xDSf({\n    typed\n  });\n  var matAlgo07xSSf = createMatAlgo07xSSf({\n    typed,\n    SparseMatrix\n  });\n  var matAlgo12xSfs = createMatAlgo12xSfs({\n    typed,\n    DenseMatrix\n  });\n  var matrixAlgorithmSuite = createMatrixAlgorithmSuite({\n    typed,\n    matrix,\n    concat\n  });\n  var compareUnits = createCompareUnits({\n    typed\n  });\n  /**\n   * Test whether value x is smaller than y.\n   *\n   * The function returns true when x is smaller than y and the relative\n   * difference between x and y is smaller than the configured relTol and absTol. The\n   * function cannot be used to compare values smaller than approximately 2.22e-16.\n   *\n   * For matrices, the function is evaluated element wise.\n   * Strings are compared by their numerical value.\n   *\n   * Syntax:\n   *\n   *    math.smaller(x, y)\n   *\n   * Examples:\n   *\n   *    math.smaller(2, 3)            // returns true\n   *    math.smaller(5, 2 * 2)        // returns false\n   *\n   *    const a = math.unit('5 cm')\n   *    const b = math.unit('2 inch')\n   *    math.smaller(a, b)            // returns true\n   *\n   * See also:\n   *\n   *    equal, unequal, smallerEq, smaller, smallerEq, compare\n   *\n   * @param  {number | BigNumber | bigint | Fraction | boolean | Unit | string | Array | Matrix} x First value to compare\n   * @param  {number | BigNumber | bigint | Fraction | boolean | Unit | string | Array | Matrix} y Second value to compare\n   * @return {boolean | Array | Matrix} Returns true when the x is smaller than y, else returns false\n   */\n\n  function bignumSmaller(x, y) {\n    return x.lt(y) && !bigNearlyEqual(x, y, config.relTol, config.absTol);\n  }\n\n  return typed(name, createSmallerNumber({\n    typed,\n    config\n  }), {\n    'boolean, boolean': (x, y) => x < y,\n    'BigNumber, BigNumber': bignumSmaller,\n    'bigint, bigint': (x, y) => x < y,\n    'Fraction, Fraction': (x, y) => x.compare(y) === -1,\n    'Fraction, BigNumber': function Fraction_BigNumber(x, y) {\n      return bignumSmaller(bignumber(x), y);\n    },\n    'BigNumber, Fraction': function BigNumber_Fraction(x, y) {\n      return bignumSmaller(x, bignumber(y));\n    },\n    'Complex, Complex': function Complex_Complex(x, y) {\n      throw new TypeError('No ordering relation is defined for complex numbers');\n    }\n  }, compareUnits, matrixAlgorithmSuite({\n    SS: matAlgo07xSSf,\n    DS: matAlgo03xDSf,\n    Ss: matAlgo12xSfs\n  }));\n});\nexport var createSmallerNumber = /* #__PURE__ */factory(name, ['typed', 'config'], _ref2 => {\n  var {\n    typed,\n    config\n  } = _ref2;\n  return typed(name, {\n    'number, number': function number_number(x, y) {\n      return x < y && !nearlyEqual(x, y, config.relTol, config.absTol);\n    }\n  });\n});", "map": null, "metadata": {}, "sourceType": "module"}