{"ast": null, "code": "import { Observable } from '../Observable';\nimport { AsyncSubject } from '../AsyncSubject';\nimport { map } from '../operators/map';\nimport { canReportError } from '../util/canReportError';\nimport { isScheduler } from '../util/isScheduler';\nimport { isArray } from '../util/isArray';\nexport function bindNodeCallback(callbackFunc, resultSelector, scheduler) {\n  if (resultSelector) {\n    if (isScheduler(resultSelector)) {\n      scheduler = resultSelector;\n    } else {\n      return (...args) => bindNodeCallback(callbackFunc, scheduler)(...args).pipe(map(args => isArray(args) ? resultSelector(...args) : resultSelector(args)));\n    }\n  }\n\n  return function (...args) {\n    const params = {\n      subject: undefined,\n      args,\n      callbackFunc,\n      scheduler,\n      context: this\n    };\n    return new Observable(subscriber => {\n      const {\n        context\n      } = params;\n      let {\n        subject\n      } = params;\n\n      if (!scheduler) {\n        if (!subject) {\n          subject = params.subject = new AsyncSubject();\n\n          const handler = (...innerArgs) => {\n            const err = innerArgs.shift();\n\n            if (err) {\n              subject.error(err);\n              return;\n            }\n\n            subject.next(innerArgs.length <= 1 ? innerArgs[0] : innerArgs);\n            subject.complete();\n          };\n\n          try {\n            callbackFunc.apply(context, [...args, handler]);\n          } catch (err) {\n            if (canReportError(subject)) {\n              subject.error(err);\n            } else {\n              console.warn(err);\n            }\n          }\n        }\n\n        return subject.subscribe(subscriber);\n      } else {\n        return scheduler.schedule(dispatch, 0, {\n          params,\n          subscriber,\n          context\n        });\n      }\n    });\n  };\n}\n\nfunction dispatch(state) {\n  const {\n    params,\n    subscriber,\n    context\n  } = state;\n  const {\n    callbackFunc,\n    args,\n    scheduler\n  } = params;\n  let subject = params.subject;\n\n  if (!subject) {\n    subject = params.subject = new AsyncSubject();\n\n    const handler = (...innerArgs) => {\n      const err = innerArgs.shift();\n\n      if (err) {\n        this.add(scheduler.schedule(dispatchError, 0, {\n          err,\n          subject\n        }));\n      } else {\n        const value = innerArgs.length <= 1 ? innerArgs[0] : innerArgs;\n        this.add(scheduler.schedule(dispatchNext, 0, {\n          value,\n          subject\n        }));\n      }\n    };\n\n    try {\n      callbackFunc.apply(context, [...args, handler]);\n    } catch (err) {\n      this.add(scheduler.schedule(dispatchError, 0, {\n        err,\n        subject\n      }));\n    }\n  }\n\n  this.add(subject.subscribe(subscriber));\n}\n\nfunction dispatchNext(arg) {\n  const {\n    value,\n    subject\n  } = arg;\n  subject.next(value);\n  subject.complete();\n}\n\nfunction dispatchError(arg) {\n  const {\n    err,\n    subject\n  } = arg;\n  subject.error(err);\n} //# sourceMappingURL=bindNodeCallback.js.map", "map": null, "metadata": {}, "sourceType": "module"}