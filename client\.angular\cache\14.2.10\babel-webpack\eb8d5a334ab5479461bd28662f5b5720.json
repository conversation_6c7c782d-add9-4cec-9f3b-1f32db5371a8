{"ast": null, "code": "import { arraySize as size } from '../../utils/array.js';\nimport { factory } from '../../utils/factory.js';\nvar name = 'sort';\nvar dependencies = ['typed', 'matrix', 'compare', 'compareNatural'];\nexport var createSort = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed,\n    matrix,\n    compare,\n    compareNatural\n  } = _ref;\n  var compareAsc = compare;\n\n  var compareDesc = (a, b) => -compare(a, b);\n  /**\n   * Sort the items in a matrix.\n   *\n   * Syntax:\n   *\n   *    math.sort(x)\n   *    math.sort(x, compare)\n   *\n   * Examples:\n   *\n   *    math.sort([5, 10, 1]) // returns [1, 5, 10]\n   *    math.sort(['C', 'B', 'A', 'D'], math.compareNatural)\n   *    // returns ['A', 'B', 'C', 'D']\n   *\n   *    function sortByLength (a, b) {\n   *      return a.length - b.length\n   *    }\n   *    math.sort(['<PERSON>', '<PERSON>', '<PERSON>'], sortByLength)\n   *    // returns ['<PERSON>', '<PERSON>', '<PERSON>']\n   *\n   * See also:\n   *\n   *    filter, forEach, map, compare, compareNatural\n   *\n   * @param {Matrix | Array} x    A one dimensional matrix or array to sort\n   * @param {Function | 'asc' | 'desc' | 'natural'} [compare='asc']\n   *        An optional _comparator function or name. The function is called as\n   *        `compare(a, b)`, and must return 1 when a > b, -1 when a < b,\n   *        and 0 when a == b.\n   * @return {Matrix | Array} Returns the sorted matrix.\n   */\n\n\n  return typed(name, {\n    Array: function Array(x) {\n      _arrayIsVector(x);\n\n      return x.sort(compareAsc);\n    },\n    Matrix: function Matrix(x) {\n      _matrixIsVector(x);\n\n      return matrix(x.toArray().sort(compareAsc), x.storage());\n    },\n    'Array, function': function Array_function(x, _comparator) {\n      _arrayIsVector(x);\n\n      return x.sort(_comparator);\n    },\n    'Matrix, function': function Matrix_function(x, _comparator) {\n      _matrixIsVector(x);\n\n      return matrix(x.toArray().sort(_comparator), x.storage());\n    },\n    'Array, string': function Array_string(x, order) {\n      _arrayIsVector(x);\n\n      return x.sort(_comparator(order));\n    },\n    'Matrix, string': function Matrix_string(x, order) {\n      _matrixIsVector(x);\n\n      return matrix(x.toArray().sort(_comparator(order)), x.storage());\n    }\n  });\n  /**\n   * Get the comparator for given order ('asc', 'desc', 'natural')\n   * @param {'asc' | 'desc' | 'natural'} order\n   * @return {Function} Returns a _comparator function\n   */\n\n  function _comparator(order) {\n    if (order === 'asc') {\n      return compareAsc;\n    } else if (order === 'desc') {\n      return compareDesc;\n    } else if (order === 'natural') {\n      return compareNatural;\n    } else {\n      throw new Error('String \"asc\", \"desc\", or \"natural\" expected');\n    }\n  }\n  /**\n   * Validate whether an array is one dimensional\n   * Throws an error when this is not the case\n   * @param {Array} array\n   * @private\n   */\n\n\n  function _arrayIsVector(array) {\n    if (size(array).length !== 1) {\n      throw new Error('One dimensional array expected');\n    }\n  }\n  /**\n   * Validate whether a matrix is one dimensional\n   * Throws an error when this is not the case\n   * @param {Matrix} matrix\n   * @private\n   */\n\n\n  function _matrixIsVector(matrix) {\n    if (matrix.size().length !== 1) {\n      throw new Error('One dimensional matrix expected');\n    }\n  }\n});", "map": {"version": 3, "names": ["arraySize", "size", "factory", "name", "dependencies", "createSort", "_ref", "typed", "matrix", "compare", "compareNatural", "compareAsc", "compareDesc", "a", "b", "Array", "x", "_arrayIsVector", "sort", "Matrix", "_matrixIsVector", "toArray", "storage", "Array_function", "_comparator", "Matrix_function", "Array_string", "order", "Matrix_string", "Error", "array", "length"], "sources": ["D:/work/joyserver/client/node_modules/mathjs/lib/esm/function/matrix/sort.js"], "sourcesContent": ["import { arraySize as size } from '../../utils/array.js';\nimport { factory } from '../../utils/factory.js';\nvar name = 'sort';\nvar dependencies = ['typed', 'matrix', 'compare', 'compareNatural'];\nexport var createSort = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed,\n    matrix,\n    compare,\n    compareNatural\n  } = _ref;\n  var compareAsc = compare;\n  var compareDesc = (a, b) => -compare(a, b);\n\n  /**\n   * Sort the items in a matrix.\n   *\n   * Syntax:\n   *\n   *    math.sort(x)\n   *    math.sort(x, compare)\n   *\n   * Examples:\n   *\n   *    math.sort([5, 10, 1]) // returns [1, 5, 10]\n   *    math.sort(['C', 'B', 'A', 'D'], math.compareNatural)\n   *    // returns ['A', 'B', 'C', 'D']\n   *\n   *    function sortByLength (a, b) {\n   *      return a.length - b.length\n   *    }\n   *    math.sort(['<PERSON>', '<PERSON>', '<PERSON>'], sortByLength)\n   *    // returns ['<PERSON>', '<PERSON>', '<PERSON>']\n   *\n   * See also:\n   *\n   *    filter, forEach, map, compare, compareNatural\n   *\n   * @param {Matrix | Array} x    A one dimensional matrix or array to sort\n   * @param {Function | 'asc' | 'desc' | 'natural'} [compare='asc']\n   *        An optional _comparator function or name. The function is called as\n   *        `compare(a, b)`, and must return 1 when a > b, -1 when a < b,\n   *        and 0 when a == b.\n   * @return {Matrix | Array} Returns the sorted matrix.\n   */\n  return typed(name, {\n    Array: function Array(x) {\n      _arrayIsVector(x);\n      return x.sort(compareAsc);\n    },\n    Matrix: function Matrix(x) {\n      _matrixIsVector(x);\n      return matrix(x.toArray().sort(compareAsc), x.storage());\n    },\n    'Array, function': function Array_function(x, _comparator) {\n      _arrayIsVector(x);\n      return x.sort(_comparator);\n    },\n    'Matrix, function': function Matrix_function(x, _comparator) {\n      _matrixIsVector(x);\n      return matrix(x.toArray().sort(_comparator), x.storage());\n    },\n    'Array, string': function Array_string(x, order) {\n      _arrayIsVector(x);\n      return x.sort(_comparator(order));\n    },\n    'Matrix, string': function Matrix_string(x, order) {\n      _matrixIsVector(x);\n      return matrix(x.toArray().sort(_comparator(order)), x.storage());\n    }\n  });\n\n  /**\n   * Get the comparator for given order ('asc', 'desc', 'natural')\n   * @param {'asc' | 'desc' | 'natural'} order\n   * @return {Function} Returns a _comparator function\n   */\n  function _comparator(order) {\n    if (order === 'asc') {\n      return compareAsc;\n    } else if (order === 'desc') {\n      return compareDesc;\n    } else if (order === 'natural') {\n      return compareNatural;\n    } else {\n      throw new Error('String \"asc\", \"desc\", or \"natural\" expected');\n    }\n  }\n\n  /**\n   * Validate whether an array is one dimensional\n   * Throws an error when this is not the case\n   * @param {Array} array\n   * @private\n   */\n  function _arrayIsVector(array) {\n    if (size(array).length !== 1) {\n      throw new Error('One dimensional array expected');\n    }\n  }\n\n  /**\n   * Validate whether a matrix is one dimensional\n   * Throws an error when this is not the case\n   * @param {Matrix} matrix\n   * @private\n   */\n  function _matrixIsVector(matrix) {\n    if (matrix.size().length !== 1) {\n      throw new Error('One dimensional matrix expected');\n    }\n  }\n});"], "mappings": "AAAA,SAASA,SAAS,IAAIC,IAAtB,QAAkC,sBAAlC;AACA,SAASC,OAAT,QAAwB,wBAAxB;AACA,IAAIC,IAAI,GAAG,MAAX;AACA,IAAIC,YAAY,GAAG,CAAC,OAAD,EAAU,QAAV,EAAoB,SAApB,EAA+B,gBAA/B,CAAnB;AACA,OAAO,IAAIC,UAAU,GAAG,eAAeH,OAAO,CAACC,IAAD,EAAOC,YAAP,EAAqBE,IAAI,IAAI;EACzE,IAAI;IACFC,KADE;IAEFC,MAFE;IAGFC,OAHE;IAIFC;EAJE,IAKAJ,IALJ;EAMA,IAAIK,UAAU,GAAGF,OAAjB;;EACA,IAAIG,WAAW,GAAG,CAACC,CAAD,EAAIC,CAAJ,KAAU,CAACL,OAAO,CAACI,CAAD,EAAIC,CAAJ,CAApC;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;EACE,OAAOP,KAAK,CAACJ,IAAD,EAAO;IACjBY,KAAK,EAAE,SAASA,KAAT,CAAeC,CAAf,EAAkB;MACvBC,cAAc,CAACD,CAAD,CAAd;;MACA,OAAOA,CAAC,CAACE,IAAF,CAAOP,UAAP,CAAP;IACD,CAJgB;IAKjBQ,MAAM,EAAE,SAASA,MAAT,CAAgBH,CAAhB,EAAmB;MACzBI,eAAe,CAACJ,CAAD,CAAf;;MACA,OAAOR,MAAM,CAACQ,CAAC,CAACK,OAAF,GAAYH,IAAZ,CAAiBP,UAAjB,CAAD,EAA+BK,CAAC,CAACM,OAAF,EAA/B,CAAb;IACD,CARgB;IASjB,mBAAmB,SAASC,cAAT,CAAwBP,CAAxB,EAA2BQ,WAA3B,EAAwC;MACzDP,cAAc,CAACD,CAAD,CAAd;;MACA,OAAOA,CAAC,CAACE,IAAF,CAAOM,WAAP,CAAP;IACD,CAZgB;IAajB,oBAAoB,SAASC,eAAT,CAAyBT,CAAzB,EAA4BQ,WAA5B,EAAyC;MAC3DJ,eAAe,CAACJ,CAAD,CAAf;;MACA,OAAOR,MAAM,CAACQ,CAAC,CAACK,OAAF,GAAYH,IAAZ,CAAiBM,WAAjB,CAAD,EAAgCR,CAAC,CAACM,OAAF,EAAhC,CAAb;IACD,CAhBgB;IAiBjB,iBAAiB,SAASI,YAAT,CAAsBV,CAAtB,EAAyBW,KAAzB,EAAgC;MAC/CV,cAAc,CAACD,CAAD,CAAd;;MACA,OAAOA,CAAC,CAACE,IAAF,CAAOM,WAAW,CAACG,KAAD,CAAlB,CAAP;IACD,CApBgB;IAqBjB,kBAAkB,SAASC,aAAT,CAAuBZ,CAAvB,EAA0BW,KAA1B,EAAiC;MACjDP,eAAe,CAACJ,CAAD,CAAf;;MACA,OAAOR,MAAM,CAACQ,CAAC,CAACK,OAAF,GAAYH,IAAZ,CAAiBM,WAAW,CAACG,KAAD,CAA5B,CAAD,EAAuCX,CAAC,CAACM,OAAF,EAAvC,CAAb;IACD;EAxBgB,CAAP,CAAZ;EA2BA;AACF;AACA;AACA;AACA;;EACE,SAASE,WAAT,CAAqBG,KAArB,EAA4B;IAC1B,IAAIA,KAAK,KAAK,KAAd,EAAqB;MACnB,OAAOhB,UAAP;IACD,CAFD,MAEO,IAAIgB,KAAK,KAAK,MAAd,EAAsB;MAC3B,OAAOf,WAAP;IACD,CAFM,MAEA,IAAIe,KAAK,KAAK,SAAd,EAAyB;MAC9B,OAAOjB,cAAP;IACD,CAFM,MAEA;MACL,MAAM,IAAImB,KAAJ,CAAU,6CAAV,CAAN;IACD;EACF;EAED;AACF;AACA;AACA;AACA;AACA;;;EACE,SAASZ,cAAT,CAAwBa,KAAxB,EAA+B;IAC7B,IAAI7B,IAAI,CAAC6B,KAAD,CAAJ,CAAYC,MAAZ,KAAuB,CAA3B,EAA8B;MAC5B,MAAM,IAAIF,KAAJ,CAAU,gCAAV,CAAN;IACD;EACF;EAED;AACF;AACA;AACA;AACA;AACA;;;EACE,SAAST,eAAT,CAAyBZ,MAAzB,EAAiC;IAC/B,IAAIA,MAAM,CAACP,IAAP,GAAc8B,MAAd,KAAyB,CAA7B,EAAgC;MAC9B,MAAM,IAAIF,KAAJ,CAAU,iCAAV,CAAN;IACD;EACF;AACF,CA5G6C,CAAvC"}, "metadata": {}, "sourceType": "module"}