{"ast": null, "code": "export var subsetDocs = {\n  name: 'subset',\n  category: 'Matrix',\n  syntax: ['value(index)', 'value(index) = replacement', 'subset(value, [index])', 'subset(value, [index], replacement)'],\n  description: 'Get or set a subset of the entries of a matrix or ' + 'characters of a string. ' + 'Indexes are one-based. There should be one index specification for ' + 'each dimension of the target. Each specification can be a single ' + 'index, a list of indices, or a range in colon notation `l:u`. ' + 'In a range, both the lower bound l and upper bound u are included; ' + 'and if a bound is omitted it defaults to the most extreme valid value. ' + 'The cartesian product of the indices specified in each dimension ' + 'determines the target of the operation.',\n  examples: ['d = [1, 2; 3, 4]', 'e = []', 'e[1, 1:2] = [5, 6]', 'e[2, :] = [7, 8]', 'f = d * e', 'f[2, 1]', 'f[:, 1]', 'f[[1,2], [1,3]] = [9, 10; 11, 12]', 'f'],\n  seealso: ['concat', 'det', 'diag', 'identity', 'inv', 'ones', 'range', 'size', 'squeeze', 'trace', 'transpose', 'zeros']\n};", "map": {"version": 3, "names": ["subsetDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["D:/work/joyserver/client/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/matrix/subset.js"], "sourcesContent": ["export var subsetDocs = {\n  name: 'subset',\n  category: 'Matrix',\n  syntax: ['value(index)', 'value(index) = replacement', 'subset(value, [index])', 'subset(value, [index], replacement)'],\n  description: 'Get or set a subset of the entries of a matrix or ' + 'characters of a string. ' + 'Indexes are one-based. There should be one index specification for ' + 'each dimension of the target. Each specification can be a single ' + 'index, a list of indices, or a range in colon notation `l:u`. ' + 'In a range, both the lower bound l and upper bound u are included; ' + 'and if a bound is omitted it defaults to the most extreme valid value. ' + 'The cartesian product of the indices specified in each dimension ' + 'determines the target of the operation.',\n  examples: ['d = [1, 2; 3, 4]', 'e = []', 'e[1, 1:2] = [5, 6]', 'e[2, :] = [7, 8]', 'f = d * e', 'f[2, 1]', 'f[:, 1]', 'f[[1,2], [1,3]] = [9, 10; 11, 12]', 'f'],\n  seealso: ['concat', 'det', 'diag', 'identity', 'inv', 'ones', 'range', 'size', 'squeeze', 'trace', 'transpose', 'zeros']\n};"], "mappings": "AAAA,OAAO,IAAIA,UAAU,GAAG;EACtBC,IAAI,EAAE,QADgB;EAEtBC,QAAQ,EAAE,QAFY;EAGtBC,MAAM,EAAE,CAAC,cAAD,EAAiB,4BAAjB,EAA+C,wBAA/C,EAAyE,qCAAzE,CAHc;EAItBC,WAAW,EAAE,uDAAuD,0BAAvD,GAAoF,qEAApF,GAA4J,mEAA5J,GAAkO,gEAAlO,GAAqS,qEAArS,GAA6W,yEAA7W,GAAyb,mEAAzb,GAA+f,yCAJtf;EAKtBC,QAAQ,EAAE,CAAC,kBAAD,EAAqB,QAArB,EAA+B,oBAA/B,EAAqD,kBAArD,EAAyE,WAAzE,EAAsF,SAAtF,EAAiG,SAAjG,EAA4G,mCAA5G,EAAiJ,GAAjJ,CALY;EAMtBC,OAAO,EAAE,CAAC,QAAD,EAAW,KAAX,EAAkB,MAAlB,EAA0B,UAA1B,EAAsC,KAAtC,EAA6C,MAA7C,EAAqD,OAArD,EAA8D,MAA9D,EAAsE,SAAtE,EAAiF,OAAjF,EAA0F,WAA1F,EAAuG,OAAvG;AANa,CAAjB"}, "metadata": {}, "sourceType": "module"}