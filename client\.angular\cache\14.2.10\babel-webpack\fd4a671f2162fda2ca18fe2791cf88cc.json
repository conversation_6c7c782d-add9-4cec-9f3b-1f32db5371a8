{"ast": null, "code": "import { createMatAlgo02xDS0 } from '../../type/matrix/utils/matAlgo02xDS0.js';\nimport { createMatAlgo11xS0s } from '../../type/matrix/utils/matAlgo11xS0s.js';\nimport { createMatAlgo14xDs } from '../../type/matrix/utils/matAlgo14xDs.js';\nimport { createMatAlgo01xDSid } from '../../type/matrix/utils/matAlgo01xDSid.js';\nimport { createMatAlgo10xSids } from '../../type/matrix/utils/matAlgo10xSids.js';\nimport { createMatAlgo08xS0Sid } from '../../type/matrix/utils/matAlgo08xS0Sid.js';\nimport { factory } from '../../utils/factory.js';\nimport { createMatrixAlgorithmSuite } from '../../type/matrix/utils/matrixAlgorithmSuite.js';\nimport { rightLogShiftNumber } from '../../plain/number/index.js';\nimport { createUseMatrixForArrayScalar } from './useMatrixForArrayScalar.js';\nvar name = 'rightLogShift';\nvar dependencies = ['typed', 'matrix', 'equalScalar', 'zeros', 'DenseMatrix', 'concat'];\nexport var createRightLogShift = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed,\n    matrix,\n    equalScalar,\n    zeros,\n    DenseMatrix,\n    concat\n  } = _ref;\n  var matAlgo01xDSid = createMatAlgo01xDSid({\n    typed\n  });\n  var matAlgo02xDS0 = createMatAlgo02xDS0({\n    typed,\n    equalScalar\n  });\n  var matAlgo08xS0Sid = createMatAlgo08xS0Sid({\n    typed,\n    equalScalar\n  });\n  var matAlgo10xSids = createMatAlgo10xSids({\n    typed,\n    DenseMatrix\n  });\n  var matAlgo11xS0s = createMatAlgo11xS0s({\n    typed,\n    equalScalar\n  });\n  var matAlgo14xDs = createMatAlgo14xDs({\n    typed\n  });\n  var matrixAlgorithmSuite = createMatrixAlgorithmSuite({\n    typed,\n    matrix,\n    concat\n  });\n  var useMatrixForArrayScalar = createUseMatrixForArrayScalar({\n    typed,\n    matrix\n  });\n  /**\n   * Bitwise right logical shift of value x by y number of bits, `x >>> y`.\n   * For matrices, the function is evaluated element wise.\n   * For units, the function is evaluated on the best prefix base.\n   *\n   * Syntax:\n   *\n   *    math.rightLogShift(x, y)\n   *\n   * Examples:\n   *\n   *    math.rightLogShift(4, 2)               // returns number 1\n   *\n   *    math.rightLogShift([16, 32, 64], 4)    // returns Array [1, 2, 4]\n   *\n   * See also:\n   *\n   *    bitAnd, bitNot, bitOr, bitXor, leftShift, rightLogShift\n   *\n   * @param  {number | Array | Matrix} x Value to be shifted\n   * @param  {number} y Amount of shifts\n   * @return {number | Array | Matrix} `x` zero-filled shifted right `y` times\n   */\n\n  return typed(name, {\n    'number, number': rightLogShiftNumber,\n    // 'BigNumber, BigNumber': ..., // TODO: implement BigNumber support for rightLogShift\n    'SparseMatrix, number | BigNumber': typed.referToSelf(self => (x, y) => {\n      // check scalar\n      if (equalScalar(y, 0)) {\n        return x.clone();\n      }\n\n      return matAlgo11xS0s(x, y, self, false);\n    }),\n    'DenseMatrix, number | BigNumber': typed.referToSelf(self => (x, y) => {\n      // check scalar\n      if (equalScalar(y, 0)) {\n        return x.clone();\n      }\n\n      return matAlgo14xDs(x, y, self, false);\n    }),\n    'number | BigNumber, SparseMatrix': typed.referToSelf(self => (x, y) => {\n      // check scalar\n      if (equalScalar(x, 0)) {\n        return zeros(y.size(), y.storage());\n      }\n\n      return matAlgo10xSids(y, x, self, true);\n    }),\n    'number | BigNumber, DenseMatrix': typed.referToSelf(self => (x, y) => {\n      // check scalar\n      if (equalScalar(x, 0)) {\n        return zeros(y.size(), y.storage());\n      }\n\n      return matAlgo14xDs(y, x, self, true);\n    })\n  }, useMatrixForArrayScalar, matrixAlgorithmSuite({\n    SS: matAlgo08xS0Sid,\n    DS: matAlgo01xDSid,\n    SD: matAlgo02xDS0\n  }));\n});", "map": {"version": 3, "names": ["createMatAlgo02xDS0", "createMatAlgo11xS0s", "createMatAlgo14xDs", "createMatAlgo01xDSid", "createMatAlgo10xSids", "createMatAlgo08xS0Sid", "factory", "createMatrixAlgorithmSuite", "rightLogShiftNumber", "createUseMatrixForArrayScalar", "name", "dependencies", "createRightLogShift", "_ref", "typed", "matrix", "equalScalar", "zeros", "DenseMatrix", "concat", "matAlgo01xDSid", "matAlgo02xDS0", "matAlgo08xS0Sid", "matAlgo10xSids", "matAlgo11xS0s", "matAlgo14xDs", "matrixAlgorithmSuite", "useMatrixForArrayScalar", "referToSelf", "self", "x", "y", "clone", "size", "storage", "SS", "DS", "SD"], "sources": ["D:/work/joyserver/client/node_modules/mathjs/lib/esm/function/bitwise/rightLogShift.js"], "sourcesContent": ["import { createMatAlgo02xDS0 } from '../../type/matrix/utils/matAlgo02xDS0.js';\nimport { createMatAlgo11xS0s } from '../../type/matrix/utils/matAlgo11xS0s.js';\nimport { createMatAlgo14xDs } from '../../type/matrix/utils/matAlgo14xDs.js';\nimport { createMatAlgo01xDSid } from '../../type/matrix/utils/matAlgo01xDSid.js';\nimport { createMatAlgo10xSids } from '../../type/matrix/utils/matAlgo10xSids.js';\nimport { createMatAlgo08xS0Sid } from '../../type/matrix/utils/matAlgo08xS0Sid.js';\nimport { factory } from '../../utils/factory.js';\nimport { createMatrixAlgorithmSuite } from '../../type/matrix/utils/matrixAlgorithmSuite.js';\nimport { rightLogShiftNumber } from '../../plain/number/index.js';\nimport { createUseMatrixForArrayScalar } from './useMatrixForArrayScalar.js';\nvar name = 'rightLogShift';\nvar dependencies = ['typed', 'matrix', 'equalScalar', 'zeros', 'DenseMatrix', 'concat'];\nexport var createRightLogShift = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed,\n    matrix,\n    equalScalar,\n    zeros,\n    DenseMatrix,\n    concat\n  } = _ref;\n  var matAlgo01xDSid = createMatAlgo01xDSid({\n    typed\n  });\n  var matAlgo02xDS0 = createMatAlgo02xDS0({\n    typed,\n    equalScalar\n  });\n  var matAlgo08xS0Sid = createMatAlgo08xS0Sid({\n    typed,\n    equalScalar\n  });\n  var matAlgo10xSids = createMatAlgo10xSids({\n    typed,\n    DenseMatrix\n  });\n  var matAlgo11xS0s = createMatAlgo11xS0s({\n    typed,\n    equalScalar\n  });\n  var matAlgo14xDs = createMatAlgo14xDs({\n    typed\n  });\n  var matrixAlgorithmSuite = createMatrixAlgorithmSuite({\n    typed,\n    matrix,\n    concat\n  });\n  var useMatrixForArrayScalar = createUseMatrixForArrayScalar({\n    typed,\n    matrix\n  });\n\n  /**\n   * Bitwise right logical shift of value x by y number of bits, `x >>> y`.\n   * For matrices, the function is evaluated element wise.\n   * For units, the function is evaluated on the best prefix base.\n   *\n   * Syntax:\n   *\n   *    math.rightLogShift(x, y)\n   *\n   * Examples:\n   *\n   *    math.rightLogShift(4, 2)               // returns number 1\n   *\n   *    math.rightLogShift([16, 32, 64], 4)    // returns Array [1, 2, 4]\n   *\n   * See also:\n   *\n   *    bitAnd, bitNot, bitOr, bitXor, leftShift, rightLogShift\n   *\n   * @param  {number | Array | Matrix} x Value to be shifted\n   * @param  {number} y Amount of shifts\n   * @return {number | Array | Matrix} `x` zero-filled shifted right `y` times\n   */\n\n  return typed(name, {\n    'number, number': rightLogShiftNumber,\n    // 'BigNumber, BigNumber': ..., // TODO: implement BigNumber support for rightLogShift\n\n    'SparseMatrix, number | BigNumber': typed.referToSelf(self => (x, y) => {\n      // check scalar\n      if (equalScalar(y, 0)) {\n        return x.clone();\n      }\n      return matAlgo11xS0s(x, y, self, false);\n    }),\n    'DenseMatrix, number | BigNumber': typed.referToSelf(self => (x, y) => {\n      // check scalar\n      if (equalScalar(y, 0)) {\n        return x.clone();\n      }\n      return matAlgo14xDs(x, y, self, false);\n    }),\n    'number | BigNumber, SparseMatrix': typed.referToSelf(self => (x, y) => {\n      // check scalar\n      if (equalScalar(x, 0)) {\n        return zeros(y.size(), y.storage());\n      }\n      return matAlgo10xSids(y, x, self, true);\n    }),\n    'number | BigNumber, DenseMatrix': typed.referToSelf(self => (x, y) => {\n      // check scalar\n      if (equalScalar(x, 0)) {\n        return zeros(y.size(), y.storage());\n      }\n      return matAlgo14xDs(y, x, self, true);\n    })\n  }, useMatrixForArrayScalar, matrixAlgorithmSuite({\n    SS: matAlgo08xS0Sid,\n    DS: matAlgo01xDSid,\n    SD: matAlgo02xDS0\n  }));\n});"], "mappings": "AAAA,SAASA,mBAAT,QAAoC,0CAApC;AACA,SAASC,mBAAT,QAAoC,0CAApC;AACA,SAASC,kBAAT,QAAmC,yCAAnC;AACA,SAASC,oBAAT,QAAqC,2CAArC;AACA,SAASC,oBAAT,QAAqC,2CAArC;AACA,SAASC,qBAAT,QAAsC,4CAAtC;AACA,SAASC,OAAT,QAAwB,wBAAxB;AACA,SAASC,0BAAT,QAA2C,iDAA3C;AACA,SAASC,mBAAT,QAAoC,6BAApC;AACA,SAASC,6BAAT,QAA8C,8BAA9C;AACA,IAAIC,IAAI,GAAG,eAAX;AACA,IAAIC,YAAY,GAAG,CAAC,OAAD,EAAU,QAAV,EAAoB,aAApB,EAAmC,OAAnC,EAA4C,aAA5C,EAA2D,QAA3D,CAAnB;AACA,OAAO,IAAIC,mBAAmB,GAAG,eAAeN,OAAO,CAACI,IAAD,EAAOC,YAAP,EAAqBE,IAAI,IAAI;EAClF,IAAI;IACFC,KADE;IAEFC,MAFE;IAGFC,WAHE;IAIFC,KAJE;IAKFC,WALE;IAMFC;EANE,IAOAN,IAPJ;EAQA,IAAIO,cAAc,GAAGjB,oBAAoB,CAAC;IACxCW;EADwC,CAAD,CAAzC;EAGA,IAAIO,aAAa,GAAGrB,mBAAmB,CAAC;IACtCc,KADsC;IAEtCE;EAFsC,CAAD,CAAvC;EAIA,IAAIM,eAAe,GAAGjB,qBAAqB,CAAC;IAC1CS,KAD0C;IAE1CE;EAF0C,CAAD,CAA3C;EAIA,IAAIO,cAAc,GAAGnB,oBAAoB,CAAC;IACxCU,KADwC;IAExCI;EAFwC,CAAD,CAAzC;EAIA,IAAIM,aAAa,GAAGvB,mBAAmB,CAAC;IACtCa,KADsC;IAEtCE;EAFsC,CAAD,CAAvC;EAIA,IAAIS,YAAY,GAAGvB,kBAAkB,CAAC;IACpCY;EADoC,CAAD,CAArC;EAGA,IAAIY,oBAAoB,GAAGnB,0BAA0B,CAAC;IACpDO,KADoD;IAEpDC,MAFoD;IAGpDI;EAHoD,CAAD,CAArD;EAKA,IAAIQ,uBAAuB,GAAGlB,6BAA6B,CAAC;IAC1DK,KAD0D;IAE1DC;EAF0D,CAAD,CAA3D;EAKA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EAEE,OAAOD,KAAK,CAACJ,IAAD,EAAO;IACjB,kBAAkBF,mBADD;IAEjB;IAEA,oCAAoCM,KAAK,CAACc,WAAN,CAAkBC,IAAI,IAAI,CAACC,CAAD,EAAIC,CAAJ,KAAU;MACtE;MACA,IAAIf,WAAW,CAACe,CAAD,EAAI,CAAJ,CAAf,EAAuB;QACrB,OAAOD,CAAC,CAACE,KAAF,EAAP;MACD;;MACD,OAAOR,aAAa,CAACM,CAAD,EAAIC,CAAJ,EAAOF,IAAP,EAAa,KAAb,CAApB;IACD,CANmC,CAJnB;IAWjB,mCAAmCf,KAAK,CAACc,WAAN,CAAkBC,IAAI,IAAI,CAACC,CAAD,EAAIC,CAAJ,KAAU;MACrE;MACA,IAAIf,WAAW,CAACe,CAAD,EAAI,CAAJ,CAAf,EAAuB;QACrB,OAAOD,CAAC,CAACE,KAAF,EAAP;MACD;;MACD,OAAOP,YAAY,CAACK,CAAD,EAAIC,CAAJ,EAAOF,IAAP,EAAa,KAAb,CAAnB;IACD,CANkC,CAXlB;IAkBjB,oCAAoCf,KAAK,CAACc,WAAN,CAAkBC,IAAI,IAAI,CAACC,CAAD,EAAIC,CAAJ,KAAU;MACtE;MACA,IAAIf,WAAW,CAACc,CAAD,EAAI,CAAJ,CAAf,EAAuB;QACrB,OAAOb,KAAK,CAACc,CAAC,CAACE,IAAF,EAAD,EAAWF,CAAC,CAACG,OAAF,EAAX,CAAZ;MACD;;MACD,OAAOX,cAAc,CAACQ,CAAD,EAAID,CAAJ,EAAOD,IAAP,EAAa,IAAb,CAArB;IACD,CANmC,CAlBnB;IAyBjB,mCAAmCf,KAAK,CAACc,WAAN,CAAkBC,IAAI,IAAI,CAACC,CAAD,EAAIC,CAAJ,KAAU;MACrE;MACA,IAAIf,WAAW,CAACc,CAAD,EAAI,CAAJ,CAAf,EAAuB;QACrB,OAAOb,KAAK,CAACc,CAAC,CAACE,IAAF,EAAD,EAAWF,CAAC,CAACG,OAAF,EAAX,CAAZ;MACD;;MACD,OAAOT,YAAY,CAACM,CAAD,EAAID,CAAJ,EAAOD,IAAP,EAAa,IAAb,CAAnB;IACD,CANkC;EAzBlB,CAAP,EAgCTF,uBAhCS,EAgCgBD,oBAAoB,CAAC;IAC/CS,EAAE,EAAEb,eAD2C;IAE/Cc,EAAE,EAAEhB,cAF2C;IAG/CiB,EAAE,EAAEhB;EAH2C,CAAD,CAhCpC,CAAZ;AAqCD,CAtGsD,CAAhD"}, "metadata": {}, "sourceType": "module"}