{"ast": null, "code": "import { isBigNumber, isCollection, isNumber } from '../../utils/is.js';\nimport { factory } from '../../utils/factory.js';\nimport { errorTransform } from './utils/errorTransform.js';\nimport { createCumSum } from '../../function/statistics/cumsum.js';\n/**\n * Attach a transform function to math.sum\n * Adds a property transform containing the transform function.\n *\n * This transform changed the last `dim` parameter of function sum\n * from one-based to zero based\n */\n\nvar name = 'cumsum';\nvar dependencies = ['typed', 'add', 'unaryPlus'];\nexport var createCumSumTransform = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed,\n    add,\n    unaryPlus\n  } = _ref;\n  var cumsum = createCumSum({\n    typed,\n    add,\n    unaryPlus\n  });\n  return typed(name, {\n    '...any': function any(args) {\n      // change last argument dim from one-based to zero-based\n      if (args.length === 2 && isCollection(args[0])) {\n        var dim = args[1];\n\n        if (isNumber(dim)) {\n          args[1] = dim - 1;\n        } else if (isBigNumber(dim)) {\n          args[1] = dim.minus(1);\n        }\n      }\n\n      try {\n        return cumsum.apply(null, args);\n      } catch (err) {\n        throw errorTransform(err);\n      }\n    }\n  });\n}, {\n  isTransformFunction: true\n});", "map": {"version": 3, "names": ["isBigNumber", "isCollection", "isNumber", "factory", "errorTransform", "createCumSum", "name", "dependencies", "createCumSumTransform", "_ref", "typed", "add", "unaryPlus", "cumsum", "any", "args", "length", "dim", "minus", "apply", "err", "isTransformFunction"], "sources": ["D:/work/joyserver/client/node_modules/mathjs/lib/esm/expression/transform/cumsum.transform.js"], "sourcesContent": ["import { isBigNumber, isCollection, isNumber } from '../../utils/is.js';\nimport { factory } from '../../utils/factory.js';\nimport { errorTransform } from './utils/errorTransform.js';\nimport { createCumSum } from '../../function/statistics/cumsum.js';\n\n/**\n * Attach a transform function to math.sum\n * Adds a property transform containing the transform function.\n *\n * This transform changed the last `dim` parameter of function sum\n * from one-based to zero based\n */\nvar name = 'cumsum';\nvar dependencies = ['typed', 'add', 'unaryPlus'];\nexport var createCumSumTransform = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed,\n    add,\n    unaryPlus\n  } = _ref;\n  var cumsum = createCumSum({\n    typed,\n    add,\n    unaryPlus\n  });\n  return typed(name, {\n    '...any': function any(args) {\n      // change last argument dim from one-based to zero-based\n      if (args.length === 2 && isCollection(args[0])) {\n        var dim = args[1];\n        if (isNumber(dim)) {\n          args[1] = dim - 1;\n        } else if (isBigNumber(dim)) {\n          args[1] = dim.minus(1);\n        }\n      }\n      try {\n        return cumsum.apply(null, args);\n      } catch (err) {\n        throw errorTransform(err);\n      }\n    }\n  });\n}, {\n  isTransformFunction: true\n});"], "mappings": "AAAA,SAASA,WAAT,EAAsBC,YAAtB,EAAoCC,QAApC,QAAoD,mBAApD;AACA,SAASC,OAAT,QAAwB,wBAAxB;AACA,SAASC,cAAT,QAA+B,2BAA/B;AACA,SAASC,YAAT,QAA6B,qCAA7B;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,IAAIC,IAAI,GAAG,QAAX;AACA,IAAIC,YAAY,GAAG,CAAC,OAAD,EAAU,KAAV,EAAiB,WAAjB,CAAnB;AACA,OAAO,IAAIC,qBAAqB,GAAG,eAAeL,OAAO,CAACG,IAAD,EAAOC,YAAP,EAAqBE,IAAI,IAAI;EACpF,IAAI;IACFC,KADE;IAEFC,GAFE;IAGFC;EAHE,IAIAH,IAJJ;EAKA,IAAII,MAAM,GAAGR,YAAY,CAAC;IACxBK,KADwB;IAExBC,GAFwB;IAGxBC;EAHwB,CAAD,CAAzB;EAKA,OAAOF,KAAK,CAACJ,IAAD,EAAO;IACjB,UAAU,SAASQ,GAAT,CAAaC,IAAb,EAAmB;MAC3B;MACA,IAAIA,IAAI,CAACC,MAAL,KAAgB,CAAhB,IAAqBf,YAAY,CAACc,IAAI,CAAC,CAAD,CAAL,CAArC,EAAgD;QAC9C,IAAIE,GAAG,GAAGF,IAAI,CAAC,CAAD,CAAd;;QACA,IAAIb,QAAQ,CAACe,GAAD,CAAZ,EAAmB;UACjBF,IAAI,CAAC,CAAD,CAAJ,GAAUE,GAAG,GAAG,CAAhB;QACD,CAFD,MAEO,IAAIjB,WAAW,CAACiB,GAAD,CAAf,EAAsB;UAC3BF,IAAI,CAAC,CAAD,CAAJ,GAAUE,GAAG,CAACC,KAAJ,CAAU,CAAV,CAAV;QACD;MACF;;MACD,IAAI;QACF,OAAOL,MAAM,CAACM,KAAP,CAAa,IAAb,EAAmBJ,IAAnB,CAAP;MACD,CAFD,CAEE,OAAOK,GAAP,EAAY;QACZ,MAAMhB,cAAc,CAACgB,GAAD,CAApB;MACD;IACF;EAhBgB,CAAP,CAAZ;AAkBD,CA7BwD,EA6BtD;EACDC,mBAAmB,EAAE;AADpB,CA7BsD,CAAlD"}, "metadata": {}, "sourceType": "module"}