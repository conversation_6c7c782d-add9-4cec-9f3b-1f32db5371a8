{"ast": null, "code": "const EmptyErrorImpl = (() => {\n  function EmptyErrorImpl() {\n    Error.call(this);\n    this.message = 'no elements in sequence';\n    this.name = 'EmptyError';\n    return this;\n  }\n\n  EmptyErrorImpl.prototype = Object.create(Error.prototype);\n  return EmptyErrorImpl;\n})();\n\nexport const EmptyError = EmptyErrorImpl; //# sourceMappingURL=EmptyError.js.map", "map": null, "metadata": {}, "sourceType": "module"}