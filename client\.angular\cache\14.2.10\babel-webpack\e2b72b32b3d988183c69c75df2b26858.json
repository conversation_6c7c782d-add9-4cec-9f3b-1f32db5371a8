{"ast": null, "code": "import { factory } from '../../../utils/factory.js';\nvar name = 'chain';\nvar dependencies = ['typed', 'Chain'];\nexport var createChain = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed,\n    Chain\n  } = _ref;\n  /**\n   * Wrap any value in a chain, allowing to perform chained operations on\n   * the value.\n   *\n   * All methods available in the math.js library can be called upon the chain,\n   * and then will be evaluated with the value itself as first argument.\n   * The chain can be closed by executing `chain.done()`, which returns\n   * the final value.\n   *\n   * The chain has a number of special functions:\n   *\n   * - `done()`     Finalize the chain and return the chain's value.\n   * - `valueOf()`  The same as `done()`\n   * - `toString()` Executes `math.format()` onto the chain's value, returning\n   *                a string representation of the value.\n   *\n   * Syntax:\n   *\n   *    math.chain(value)\n   *\n   * Examples:\n   *\n   *     math.chain(3)\n   *         .add(4)\n   *         .subtract(2)\n   *         .done()     // 5\n   *\n   *     math.chain( [[1, 2], [3, 4]] )\n   *         .subset(math.index(0, 0), 8)\n   *         .multiply(3)\n   *         .done()     // [[24, 6], [9, 12]]\n   *\n   * @param {*} [value]   A value of any type on which to start a chained operation.\n   * @return {math.Chain} The created chain\n   */\n\n  return typed(name, {\n    '': function _() {\n      return new Chain();\n    },\n    any: function any(value) {\n      return new Chain(value);\n    }\n  });\n});", "map": {"version": 3, "names": ["factory", "name", "dependencies", "<PERSON><PERSON><PERSON><PERSON>", "_ref", "typed", "Chain", "_", "any", "value"], "sources": ["D:/work/joyserver/client/node_modules/mathjs/lib/esm/type/chain/function/chain.js"], "sourcesContent": ["import { factory } from '../../../utils/factory.js';\nvar name = 'chain';\nvar dependencies = ['typed', 'Chain'];\nexport var createChain = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed,\n    Chain\n  } = _ref;\n  /**\n   * Wrap any value in a chain, allowing to perform chained operations on\n   * the value.\n   *\n   * All methods available in the math.js library can be called upon the chain,\n   * and then will be evaluated with the value itself as first argument.\n   * The chain can be closed by executing `chain.done()`, which returns\n   * the final value.\n   *\n   * The chain has a number of special functions:\n   *\n   * - `done()`     Finalize the chain and return the chain's value.\n   * - `valueOf()`  The same as `done()`\n   * - `toString()` Executes `math.format()` onto the chain's value, returning\n   *                a string representation of the value.\n   *\n   * Syntax:\n   *\n   *    math.chain(value)\n   *\n   * Examples:\n   *\n   *     math.chain(3)\n   *         .add(4)\n   *         .subtract(2)\n   *         .done()     // 5\n   *\n   *     math.chain( [[1, 2], [3, 4]] )\n   *         .subset(math.index(0, 0), 8)\n   *         .multiply(3)\n   *         .done()     // [[24, 6], [9, 12]]\n   *\n   * @param {*} [value]   A value of any type on which to start a chained operation.\n   * @return {math.Chain} The created chain\n   */\n  return typed(name, {\n    '': function _() {\n      return new Chain();\n    },\n    any: function any(value) {\n      return new Chain(value);\n    }\n  });\n});"], "mappings": "AAAA,SAASA,OAAT,QAAwB,2BAAxB;AACA,IAAIC,IAAI,GAAG,OAAX;AACA,IAAIC,YAAY,GAAG,CAAC,OAAD,EAAU,OAAV,CAAnB;AACA,OAAO,IAAIC,WAAW,GAAG,eAAeH,OAAO,CAACC,IAAD,EAAOC,YAAP,EAAqBE,IAAI,IAAI;EAC1E,IAAI;IACFC,KADE;IAEFC;EAFE,IAGAF,IAHJ;EAIA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EACE,OAAOC,KAAK,CAACJ,IAAD,EAAO;IACjB,IAAI,SAASM,CAAT,GAAa;MACf,OAAO,IAAID,KAAJ,EAAP;IACD,CAHgB;IAIjBE,GAAG,EAAE,SAASA,GAAT,CAAaC,KAAb,EAAoB;MACvB,OAAO,IAAIH,KAAJ,CAAUG,KAAV,CAAP;IACD;EANgB,CAAP,CAAZ;AAQD,CAhD8C,CAAxC"}, "metadata": {}, "sourceType": "module"}