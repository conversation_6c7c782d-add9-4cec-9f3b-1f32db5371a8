{"ast": null, "code": "/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { NodeDependencies } from './dependenciesNode.generated.js';\nimport { createArrayNode } from '../../factoriesAny.js';\nexport var ArrayNodeDependencies = {\n  NodeDependencies,\n  createArrayNode\n};", "map": {"version": 3, "names": ["NodeDependencies", "createArrayNode", "ArrayNodeDependencies"], "sources": ["D:/work/joyserver/client/node_modules/mathjs/lib/esm/entry/dependenciesAny/dependenciesArrayNode.generated.js"], "sourcesContent": ["/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { NodeDependencies } from './dependenciesNode.generated.js';\nimport { createArrayNode } from '../../factoriesAny.js';\nexport var ArrayNodeDependencies = {\n  NodeDependencies,\n  createArrayNode\n};"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,gBAAT,QAAiC,iCAAjC;AACA,SAASC,eAAT,QAAgC,uBAAhC;AACA,OAAO,IAAIC,qBAAqB,GAAG;EACjCF,gBADiC;EAEjCC;AAFiC,CAA5B"}, "metadata": {}, "sourceType": "module"}