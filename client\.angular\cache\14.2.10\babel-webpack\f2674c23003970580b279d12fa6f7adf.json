{"ast": null, "code": "import { Subject } from '../Subject';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function windowCount(windowSize, startWindowEvery = 0) {\n  const startEvery = startWindowEvery > 0 ? startWindowEvery : windowSize;\n  return operate((source, subscriber) => {\n    let windows = [new Subject()];\n    let starts = [];\n    let count = 0;\n    subscriber.next(windows[0].asObservable());\n    source.subscribe(createOperatorSubscriber(subscriber, value => {\n      for (const window of windows) {\n        window.next(value);\n      }\n\n      const c = count - windowSize + 1;\n\n      if (c >= 0 && c % startEvery === 0) {\n        windows.shift().complete();\n      }\n\n      if (++count % startEvery === 0) {\n        const window = new Subject();\n        windows.push(window);\n        subscriber.next(window.asObservable());\n      }\n    }, () => {\n      while (windows.length > 0) {\n        windows.shift().complete();\n      }\n\n      subscriber.complete();\n    }, err => {\n      while (windows.length > 0) {\n        windows.shift().error(err);\n      }\n\n      subscriber.error(err);\n    }, () => {\n      starts = null;\n      windows = null;\n    }));\n  });\n}", "map": {"version": 3, "names": ["Subject", "operate", "createOperatorSubscriber", "windowCount", "windowSize", "startWindowEvery", "startEvery", "source", "subscriber", "windows", "starts", "count", "next", "asObservable", "subscribe", "value", "window", "c", "shift", "complete", "push", "length", "err", "error"], "sources": ["D:/work/joyserver/client/node_modules/rxjs/dist/esm/internal/operators/windowCount.js"], "sourcesContent": ["import { Subject } from '../Subject';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function windowCount(windowSize, startWindowEvery = 0) {\n    const startEvery = startWindowEvery > 0 ? startWindowEvery : windowSize;\n    return operate((source, subscriber) => {\n        let windows = [new Subject()];\n        let starts = [];\n        let count = 0;\n        subscriber.next(windows[0].asObservable());\n        source.subscribe(createOperatorSubscriber(subscriber, (value) => {\n            for (const window of windows) {\n                window.next(value);\n            }\n            const c = count - windowSize + 1;\n            if (c >= 0 && c % startEvery === 0) {\n                windows.shift().complete();\n            }\n            if (++count % startEvery === 0) {\n                const window = new Subject();\n                windows.push(window);\n                subscriber.next(window.asObservable());\n            }\n        }, () => {\n            while (windows.length > 0) {\n                windows.shift().complete();\n            }\n            subscriber.complete();\n        }, (err) => {\n            while (windows.length > 0) {\n                windows.shift().error(err);\n            }\n            subscriber.error(err);\n        }, () => {\n            starts = null;\n            windows = null;\n        }));\n    });\n}\n"], "mappings": "AAAA,SAASA,OAAT,QAAwB,YAAxB;AACA,SAASC,OAAT,QAAwB,cAAxB;AACA,SAASC,wBAAT,QAAyC,sBAAzC;AACA,OAAO,SAASC,WAAT,CAAqBC,UAArB,EAAiCC,gBAAgB,GAAG,CAApD,EAAuD;EAC1D,MAAMC,UAAU,GAAGD,gBAAgB,GAAG,CAAnB,GAAuBA,gBAAvB,GAA0CD,UAA7D;EACA,OAAOH,OAAO,CAAC,CAACM,MAAD,EAASC,UAAT,KAAwB;IACnC,IAAIC,OAAO,GAAG,CAAC,IAAIT,OAAJ,EAAD,CAAd;IACA,IAAIU,MAAM,GAAG,EAAb;IACA,IAAIC,KAAK,GAAG,CAAZ;IACAH,UAAU,CAACI,IAAX,CAAgBH,OAAO,CAAC,CAAD,CAAP,CAAWI,YAAX,EAAhB;IACAN,MAAM,CAACO,SAAP,CAAiBZ,wBAAwB,CAACM,UAAD,EAAcO,KAAD,IAAW;MAC7D,KAAK,MAAMC,MAAX,IAAqBP,OAArB,EAA8B;QAC1BO,MAAM,CAACJ,IAAP,CAAYG,KAAZ;MACH;;MACD,MAAME,CAAC,GAAGN,KAAK,GAAGP,UAAR,GAAqB,CAA/B;;MACA,IAAIa,CAAC,IAAI,CAAL,IAAUA,CAAC,GAAGX,UAAJ,KAAmB,CAAjC,EAAoC;QAChCG,OAAO,CAACS,KAAR,GAAgBC,QAAhB;MACH;;MACD,IAAI,EAAER,KAAF,GAAUL,UAAV,KAAyB,CAA7B,EAAgC;QAC5B,MAAMU,MAAM,GAAG,IAAIhB,OAAJ,EAAf;QACAS,OAAO,CAACW,IAAR,CAAaJ,MAAb;QACAR,UAAU,CAACI,IAAX,CAAgBI,MAAM,CAACH,YAAP,EAAhB;MACH;IACJ,CAbwC,EAatC,MAAM;MACL,OAAOJ,OAAO,CAACY,MAAR,GAAiB,CAAxB,EAA2B;QACvBZ,OAAO,CAACS,KAAR,GAAgBC,QAAhB;MACH;;MACDX,UAAU,CAACW,QAAX;IACH,CAlBwC,EAkBrCG,GAAD,IAAS;MACR,OAAOb,OAAO,CAACY,MAAR,GAAiB,CAAxB,EAA2B;QACvBZ,OAAO,CAACS,KAAR,GAAgBK,KAAhB,CAAsBD,GAAtB;MACH;;MACDd,UAAU,CAACe,KAAX,CAAiBD,GAAjB;IACH,CAvBwC,EAuBtC,MAAM;MACLZ,MAAM,GAAG,IAAT;MACAD,OAAO,GAAG,IAAV;IACH,CA1BwC,CAAzC;EA2BH,CAhCa,CAAd;AAiCH"}, "metadata": {}, "sourceType": "module"}