{"ast": null, "code": "/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { ComplexDependencies } from './dependenciesComplexClass.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createSqrt } from '../../factoriesAny.js';\nexport var sqrtDependencies = {\n  ComplexDependencies,\n  typedDependencies,\n  createSqrt\n};", "map": {"version": 3, "names": ["ComplexDependencies", "typedDependencies", "createSqrt", "sqrtDependencies"], "sources": ["D:/work/joyserver/client/node_modules/mathjs/lib/esm/entry/dependenciesAny/dependenciesSqrt.generated.js"], "sourcesContent": ["/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { ComplexDependencies } from './dependenciesComplexClass.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createSqrt } from '../../factoriesAny.js';\nexport var sqrtDependencies = {\n  ComplexDependencies,\n  typedDependencies,\n  createSqrt\n};"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,mBAAT,QAAoC,yCAApC;AACA,SAASC,iBAAT,QAAkC,kCAAlC;AACA,SAASC,UAAT,QAA2B,uBAA3B;AACA,OAAO,IAAIC,gBAAgB,GAAG;EAC5BH,mBAD4B;EAE5BC,iBAF4B;EAG5BC;AAH4B,CAAvB"}, "metadata": {}, "sourceType": "module"}