{"ast": null, "code": "/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { BigNumberDependencies } from './dependenciesBigNumberClass.generated.js';\nimport { matrixDependencies } from './dependenciesMatrix.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createXgcd } from '../../factoriesAny.js';\nexport var xgcdDependencies = {\n  BigNumberDependencies,\n  matrixDependencies,\n  typedDependencies,\n  createXgcd\n};", "map": {"version": 3, "names": ["BigNumberDependencies", "matrixDependencies", "typedDependencies", "createXgcd", "xgcdDependencies"], "sources": ["D:/work/joyserver/client/node_modules/mathjs/lib/esm/entry/dependenciesAny/dependenciesXgcd.generated.js"], "sourcesContent": ["/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { BigNumberDependencies } from './dependenciesBigNumberClass.generated.js';\nimport { matrixDependencies } from './dependenciesMatrix.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createXgcd } from '../../factoriesAny.js';\nexport var xgcdDependencies = {\n  BigNumberDependencies,\n  matrixDependencies,\n  typedDependencies,\n  createXgcd\n};"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,qBAAT,QAAsC,2CAAtC;AACA,SAASC,kBAAT,QAAmC,mCAAnC;AACA,SAASC,iBAAT,QAAkC,kCAAlC;AACA,SAASC,UAAT,QAA2B,uBAA3B;AACA,OAAO,IAAIC,gBAAgB,GAAG;EAC5BJ,qBAD4B;EAE5BC,kBAF4B;EAG5BC,iBAH4B;EAI5BC;AAJ4B,CAAvB"}, "metadata": {}, "sourceType": "module"}