{"ast": null, "code": "import { asyncScheduler } from '../scheduler/async';\nimport { delayWhen } from './delayWhen';\nimport { timer } from '../observable/timer';\nexport function delay(due, scheduler = asyncScheduler) {\n  const duration = timer(due, scheduler);\n  return delayWhen(() => duration);\n}", "map": {"version": 3, "names": ["asyncScheduler", "<PERSON><PERSON>hen", "timer", "delay", "due", "scheduler", "duration"], "sources": ["D:/work/joyserver/client/node_modules/rxjs/dist/esm/internal/operators/delay.js"], "sourcesContent": ["import { asyncScheduler } from '../scheduler/async';\nimport { delayWhen } from './delayWhen';\nimport { timer } from '../observable/timer';\nexport function delay(due, scheduler = asyncScheduler) {\n    const duration = timer(due, scheduler);\n    return delayWhen(() => duration);\n}\n"], "mappings": "AAAA,SAASA,cAAT,QAA+B,oBAA/B;AACA,SAASC,SAAT,QAA0B,aAA1B;AACA,SAASC,KAAT,QAAsB,qBAAtB;AACA,OAAO,SAASC,KAAT,CAAeC,GAAf,EAAoBC,SAAS,GAAGL,cAAhC,EAAgD;EACnD,MAAMM,QAAQ,GAAGJ,KAAK,CAACE,GAAD,EAAMC,SAAN,CAAtB;EACA,OAAOJ,SAAS,CAAC,MAAMK,QAAP,CAAhB;AACH"}, "metadata": {}, "sourceType": "module"}