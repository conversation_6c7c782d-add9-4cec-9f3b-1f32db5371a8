{"ast": null, "code": "ace.define(\"ace/mode/python_highlight_rules\", [\"require\", \"exports\", \"module\", \"ace/lib/oop\", \"ace/mode/text_highlight_rules\"], function (acequire, exports, module) {\n  \"use strict\";\n\n  var oop = acequire(\"../lib/oop\");\n  var TextHighlightRules = acequire(\"./text_highlight_rules\").TextHighlightRules;\n\n  var PythonHighlightRules = function () {\n    var keywords = \"and|as|assert|break|class|continue|def|del|elif|else|except|exec|\" + \"finally|for|from|global|if|import|in|is|lambda|not|or|pass|print|\" + \"raise|return|try|while|with|yield|async|await\";\n    var builtinConstants = \"True|False|None|NotImplemented|Ellipsis|__debug__\";\n    var builtinFunctions = \"abs|divmod|input|open|staticmethod|all|enumerate|int|ord|str|any|\" + \"eval|isinstance|pow|sum|basestring|execfile|issubclass|print|super|\" + \"binfile|iter|property|tuple|bool|filter|len|range|type|bytearray|\" + \"float|list|raw_input|unichr|callable|format|locals|reduce|unicode|\" + \"chr|frozenset|long|reload|vars|classmethod|getattr|map|repr|xrange|\" + \"cmp|globals|max|reversed|zip|compile|hasattr|memoryview|round|\" + \"__import__|complex|hash|min|set|apply|delattr|help|next|setattr|\" + \"buffer|dict|hex|object|slice|coerce|dir|id|oct|sorted|intern\";\n    var keywordMapper = this.createKeywordMapper({\n      \"invalid.deprecated\": \"debugger\",\n      \"support.function\": builtinFunctions,\n      \"constant.language\": builtinConstants,\n      \"keyword\": keywords\n    }, \"identifier\");\n    var strPre = \"(?:r|u|ur|R|U|UR|Ur|uR)?\";\n    var decimalInteger = \"(?:(?:[1-9]\\\\d*)|(?:0))\";\n    var octInteger = \"(?:0[oO]?[0-7]+)\";\n    var hexInteger = \"(?:0[xX][\\\\dA-Fa-f]+)\";\n    var binInteger = \"(?:0[bB][01]+)\";\n    var integer = \"(?:\" + decimalInteger + \"|\" + octInteger + \"|\" + hexInteger + \"|\" + binInteger + \")\";\n    var exponent = \"(?:[eE][+-]?\\\\d+)\";\n    var fraction = \"(?:\\\\.\\\\d+)\";\n    var intPart = \"(?:\\\\d+)\";\n    var pointFloat = \"(?:(?:\" + intPart + \"?\" + fraction + \")|(?:\" + intPart + \"\\\\.))\";\n    var exponentFloat = \"(?:(?:\" + pointFloat + \"|\" + intPart + \")\" + exponent + \")\";\n    var floatNumber = \"(?:\" + exponentFloat + \"|\" + pointFloat + \")\";\n    var stringEscape = \"\\\\\\\\(x[0-9A-Fa-f]{2}|[0-7]{3}|[\\\\\\\\abfnrtv'\\\"]|U[0-9A-Fa-f]{8}|u[0-9A-Fa-f]{4})\";\n    this.$rules = {\n      \"start\": [{\n        token: \"comment\",\n        regex: \"#.*$\"\n      }, {\n        token: \"string\",\n        // multi line \"\"\" string start\n        regex: strPre + '\"{3}',\n        next: \"qqstring3\"\n      }, {\n        token: \"string\",\n        // \" string\n        regex: strPre + '\"(?=.)',\n        next: \"qqstring\"\n      }, {\n        token: \"string\",\n        // multi line ''' string start\n        regex: strPre + \"'{3}\",\n        next: \"qstring3\"\n      }, {\n        token: \"string\",\n        // ' string\n        regex: strPre + \"'(?=.)\",\n        next: \"qstring\"\n      }, {\n        token: \"constant.numeric\",\n        // imaginary\n        regex: \"(?:\" + floatNumber + \"|\\\\d+)[jJ]\\\\b\"\n      }, {\n        token: \"constant.numeric\",\n        // float\n        regex: floatNumber\n      }, {\n        token: \"constant.numeric\",\n        // long integer\n        regex: integer + \"[lL]\\\\b\"\n      }, {\n        token: \"constant.numeric\",\n        // integer\n        regex: integer + \"\\\\b\"\n      }, {\n        token: keywordMapper,\n        regex: \"[a-zA-Z_$][a-zA-Z0-9_$]*\\\\b\"\n      }, {\n        token: \"keyword.operator\",\n        regex: \"\\\\+|\\\\-|\\\\*|\\\\*\\\\*|\\\\/|\\\\/\\\\/|%|<<|>>|&|\\\\||\\\\^|~|<|>|<=|=>|==|!=|<>|=\"\n      }, {\n        token: \"paren.lparen\",\n        regex: \"[\\\\[\\\\(\\\\{]\"\n      }, {\n        token: \"paren.rparen\",\n        regex: \"[\\\\]\\\\)\\\\}]\"\n      }, {\n        token: \"text\",\n        regex: \"\\\\s+\"\n      }],\n      \"qqstring3\": [{\n        token: \"constant.language.escape\",\n        regex: stringEscape\n      }, {\n        token: \"string\",\n        // multi line \"\"\" string end\n        regex: '\"{3}',\n        next: \"start\"\n      }, {\n        defaultToken: \"string\"\n      }],\n      \"qstring3\": [{\n        token: \"constant.language.escape\",\n        regex: stringEscape\n      }, {\n        token: \"string\",\n        // multi line ''' string end\n        regex: \"'{3}\",\n        next: \"start\"\n      }, {\n        defaultToken: \"string\"\n      }],\n      \"qqstring\": [{\n        token: \"constant.language.escape\",\n        regex: stringEscape\n      }, {\n        token: \"string\",\n        regex: \"\\\\\\\\$\",\n        next: \"qqstring\"\n      }, {\n        token: \"string\",\n        regex: '\"|$',\n        next: \"start\"\n      }, {\n        defaultToken: \"string\"\n      }],\n      \"qstring\": [{\n        token: \"constant.language.escape\",\n        regex: stringEscape\n      }, {\n        token: \"string\",\n        regex: \"\\\\\\\\$\",\n        next: \"qstring\"\n      }, {\n        token: \"string\",\n        regex: \"'|$\",\n        next: \"start\"\n      }, {\n        defaultToken: \"string\"\n      }]\n    };\n  };\n\n  oop.inherits(PythonHighlightRules, TextHighlightRules);\n  exports.PythonHighlightRules = PythonHighlightRules;\n});\nace.define(\"ace/mode/folding/pythonic\", [\"require\", \"exports\", \"module\", \"ace/lib/oop\", \"ace/mode/folding/fold_mode\"], function (acequire, exports, module) {\n  \"use strict\";\n\n  var oop = acequire(\"../../lib/oop\");\n  var BaseFoldMode = acequire(\"./fold_mode\").FoldMode;\n\n  var FoldMode = exports.FoldMode = function (markers) {\n    this.foldingStartMarker = new RegExp(\"([\\\\[{])(?:\\\\s*)$|(\" + markers + \")(?:\\\\s*)(?:#.*)?$\");\n  };\n\n  oop.inherits(FoldMode, BaseFoldMode);\n  (function () {\n    this.getFoldWidgetRange = function (session, foldStyle, row) {\n      var line = session.getLine(row);\n      var match = line.match(this.foldingStartMarker);\n\n      if (match) {\n        if (match[1]) return this.openingBracketBlock(session, match[1], row, match.index);\n        if (match[2]) return this.indentationBlock(session, row, match.index + match[2].length);\n        return this.indentationBlock(session, row);\n      }\n    };\n  }).call(FoldMode.prototype);\n});\nace.define(\"ace/mode/python\", [\"require\", \"exports\", \"module\", \"ace/lib/oop\", \"ace/mode/text\", \"ace/mode/python_highlight_rules\", \"ace/mode/folding/pythonic\", \"ace/range\"], function (acequire, exports, module) {\n  \"use strict\";\n\n  var oop = acequire(\"../lib/oop\");\n  var TextMode = acequire(\"./text\").Mode;\n  var PythonHighlightRules = acequire(\"./python_highlight_rules\").PythonHighlightRules;\n  var PythonFoldMode = acequire(\"./folding/pythonic\").FoldMode;\n  var Range = acequire(\"../range\").Range;\n\n  var Mode = function () {\n    this.HighlightRules = PythonHighlightRules;\n    this.foldingRules = new PythonFoldMode(\"\\\\:\");\n    this.$behaviour = this.$defaultBehaviour;\n  };\n\n  oop.inherits(Mode, TextMode);\n  (function () {\n    this.lineCommentStart = \"#\";\n\n    this.getNextLineIndent = function (state, line, tab) {\n      var indent = this.$getIndent(line);\n      var tokenizedLine = this.getTokenizer().getLineTokens(line, state);\n      var tokens = tokenizedLine.tokens;\n\n      if (tokens.length && tokens[tokens.length - 1].type == \"comment\") {\n        return indent;\n      }\n\n      if (state == \"start\") {\n        var match = line.match(/^.*[\\{\\(\\[:]\\s*$/);\n\n        if (match) {\n          indent += tab;\n        }\n      }\n\n      return indent;\n    };\n\n    var outdents = {\n      \"pass\": 1,\n      \"return\": 1,\n      \"raise\": 1,\n      \"break\": 1,\n      \"continue\": 1\n    };\n\n    this.checkOutdent = function (state, line, input) {\n      if (input !== \"\\r\\n\" && input !== \"\\r\" && input !== \"\\n\") return false;\n      var tokens = this.getTokenizer().getLineTokens(line.trim(), state).tokens;\n      if (!tokens) return false;\n\n      do {\n        var last = tokens.pop();\n      } while (last && (last.type == \"comment\" || last.type == \"text\" && last.value.match(/^\\s+$/)));\n\n      if (!last) return false;\n      return last.type == \"keyword\" && outdents[last.value];\n    };\n\n    this.autoOutdent = function (state, doc, row) {\n      row += 1;\n      var indent = this.$getIndent(doc.getLine(row));\n      var tab = doc.getTabString();\n      if (indent.slice(-tab.length) == tab) doc.remove(new Range(row, indent.length - tab.length, row, indent.length));\n    };\n\n    this.$id = \"ace/mode/python\";\n  }).call(Mode.prototype);\n  exports.Mode = Mode;\n});", "map": null, "metadata": {}, "sourceType": "script"}