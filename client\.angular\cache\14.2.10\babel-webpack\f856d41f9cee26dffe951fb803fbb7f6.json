{"ast": null, "code": "import _asyncToGenerator from \"D:/work/joyserver/client/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport hmacSHA256 from \"crypto-js/hmac-sha256\";\nimport { Hex as cryptoHex } from \"crypto-js/enc-hex\";\nimport { HttpParams, HttpUrlEncodingCodec } from \"@angular/common/http\";\nimport QRCode from \"qrcode\";\nimport { CLOUD_SECRET_PASSWORD } from \"@core-types/constants\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./utils/util.service\";\nimport * as i2 from \"./exam.service\";\nimport * as i3 from \"./api.service\";\n\nclass CustomEncoder extends HttpUrlEncodingCodec {\n  encodeKey(key) {\n    return encodeURIComponent(key);\n  }\n\n  encodeValue(value) {\n    return encodeURIComponent(value);\n  }\n\n}\n\nexport class AuthorizationService {\n  constructor(utilSer, examSer, apiSer) {\n    this.utilSer = utilSer;\n    this.examSer = examSer;\n    this.apiSer = apiSer;\n    this.password = CLOUD_SECRET_PASSWORD;\n  }\n\n  initQrCode(data, eleId) {\n    const elementId = eleId || \"qrCode-can\";\n    const url = this.createPostUrl(\"mobile\", data);\n    QRCode.toCanvas(document.getElementById(elementId), url, function (error) {\n      if (error) console.error(error);\n      console.log(\"**** QR inited success!\");\n    });\n  }\n\n  createPostUrl(type, data, method) {\n    let url = type === \"pc\" ? this.apiSer.applyAuthorOnline : this.apiSer.mobileApplyAuthorOnline;\n\n    if (method === \"POST\") {\n      return url;\n    }\n\n    const params = new HttpParams({\n      encoder: new CustomEncoder()\n    }).set(\"ts\", data[\"ts\"]).set(\"nonce\", data[\"nonce\"]).set(\"sign\", data[\"sign\"]).set(\"uuid\", data[\"event_id\"]).set(\"source\", \"c\").set(\"code\", data[\"eventCode\"]).set(\"s_id\", data[\"s_id\"]).set(\"entries\", data[\"entry_id\"]).set(\"content\", data[\"reason\"]).set(\"token\", data[\"token\"]);\n    url += \"?\" + params.toString();\n    return url;\n  }\n\n  createPostData(reason, eventCode, localCode) {\n    var _this = this;\n\n    return _asyncToGenerator(function* () {\n      const event_id = _this.utilSer._createGuid();\n\n      const entry_id = _this.examSer.entryInfo.entry_id;\n\n      const nonce = _this.utilSer._createCode();\n\n      const s_id = _this.examSer.session.id;\n      const ts = new Date().getTime() + \"\";\n      const uuid = event_id;\n      const source = \"c\";\n      const token = yield _this.getToken(localCode); //const sign = Md5.hashStr(event_id + password);\n\n      const sign = _this.HMAC256(_this.password, `code=${eventCode}content=${reason}entries=${entry_id}nonce=${nonce}source=${source}s_id=${s_id}ts=${ts}token=${token}uuid=${uuid}`);\n\n      return {\n        token,\n        reason,\n        event_id,\n        entry_id,\n        nonce,\n        sign,\n        s_id,\n        ts,\n        eventCode\n      };\n    })();\n  }\n\n  getToken(code) {\n    var _this2 = this;\n\n    return _asyncToGenerator(function* () {\n      let token;\n      _this2.examSer.offline_auth_code = code;\n\n      if (window.joyshell) {\n        token = yield window.joyshell.EncryptToken(code);\n      } else {\n        token = yield _this2.apiSer.genToken(code);\n      }\n\n      return token;\n    })();\n  }\n\n  HMAC256(key, data) {\n    const hash = hmacSHA256(data, key);\n    return hash.toString(cryptoHex);\n  }\n\n}\n\nAuthorizationService.ɵfac = function AuthorizationService_Factory(t) {\n  return new (t || AuthorizationService)(i0.ɵɵinject(i1.UtilService), i0.ɵɵinject(i2.ExamService), i0.ɵɵinject(i3.APIService));\n};\n\nAuthorizationService.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n  token: AuthorizationService,\n  factory: AuthorizationService.ɵfac,\n  providedIn: \"root\"\n});", "map": {"version": 3, "mappings": ";AAIA,OAAOA,UAAP,MAAuB,uBAAvB;AACA,SAASC,GAAG,IAAIC,SAAhB,QAAiC,mBAAjC;AAEA,SAASC,UAAT,EAAqBC,oBAArB,QAAiD,sBAAjD;AACA,OAAOC,MAAP,MAAmB,QAAnB;AACA,SAASC,qBAAT,QAAsC,uBAAtC;;;;;;AAEA,MAAMC,aAAN,SAA4BH,oBAA5B,CAAgD;EAC9CI,SAAS,CAACC,GAAD,EAAY;IACnB,OAAOC,kBAAkB,CAACD,GAAD,CAAzB;EACD;;EAEDE,WAAW,CAACC,KAAD,EAAc;IACvB,OAAOF,kBAAkB,CAACE,KAAD,CAAzB;EACD;;AAP6C;;AAahD,OAAM,MAAOC,oBAAP,CAA2B;EAG/BC,YAAoBC,OAApB,EAAkDC,OAAlD,EAAgFC,MAAhF,EAAkG;IAA9E;IAA8B;IAA8B;IAFhF,gBAAWX,qBAAX;EAEsG;;EAEtGY,UAAU,CAACC,IAAD,EAAOC,KAAP,EAAa;IACrB,MAAMC,SAAS,GAAGD,KAAK,IAAI,YAA3B;IACA,MAAME,GAAG,GAAG,KAAKC,aAAL,CAAmB,QAAnB,EAA6BJ,IAA7B,CAAZ;IACAd,MAAM,CAACmB,QAAP,CAAgBC,QAAQ,CAACC,cAAT,CAAwBL,SAAxB,CAAhB,EAAoDC,GAApD,EAAyD,UAAUK,KAAV,EAAe;MACtE,IAAIA,KAAJ,EAAWC,OAAO,CAACD,KAAR,CAAcA,KAAd;MACXC,OAAO,CAACC,GAAR,CAAY,yBAAZ;IACD,CAHD;EAID;;EAEDN,aAAa,CAACO,IAAD,EAAOX,IAAP,EAAaY,MAAb,EAAoB;IAC/B,IAAIT,GAAG,GAAGQ,IAAI,KAAK,IAAT,GAAgB,KAAKb,MAAL,CAAYe,iBAA5B,GAAgD,KAAKf,MAAL,CAAYgB,uBAAtE;;IACA,IAAIF,MAAM,KAAK,MAAf,EAAuB;MACrB,OAAOT,GAAP;IACD;;IACD,MAAMY,MAAM,GAAG,IAAI/B,UAAJ,CAAe;MAAEgC,OAAO,EAAE,IAAI5B,aAAJ;IAAX,CAAf,EACZ6B,GADY,CACR,IADQ,EACFjB,IAAI,CAAC,IAAD,CADF,EAEZiB,GAFY,CAER,OAFQ,EAECjB,IAAI,CAAC,OAAD,CAFL,EAGZiB,GAHY,CAGR,MAHQ,EAGAjB,IAAI,CAAC,MAAD,CAHJ,EAIZiB,GAJY,CAIR,MAJQ,EAIAjB,IAAI,CAAC,UAAD,CAJJ,EAKZiB,GALY,CAKR,QALQ,EAKE,GALF,EAMZA,GANY,CAMR,MANQ,EAMAjB,IAAI,CAAC,WAAD,CANJ,EAOZiB,GAPY,CAOR,MAPQ,EAOAjB,IAAI,CAAC,MAAD,CAPJ,EAQZiB,GARY,CAQR,SARQ,EAQGjB,IAAI,CAAC,UAAD,CARP,EASZiB,GATY,CASR,SATQ,EASGjB,IAAI,CAAC,QAAD,CATP,EAUZiB,GAVY,CAUR,OAVQ,EAUCjB,IAAI,CAAC,OAAD,CAVL,CAAf;IAWAG,GAAG,IAAI,MAAMY,MAAM,CAACG,QAAP,EAAb;IACA,OAAOf,GAAP;EACD;;EAEKgB,cAAc,CAACC,MAAD,EAASC,SAAT,EAAoBC,SAApB,EAA6B;IAAA;;IAAA;MAC/C,MAAMC,QAAQ,GAAG,KAAI,CAAC3B,OAAL,CAAa4B,WAAb,EAAjB;;MACA,MAAMC,QAAQ,GAAG,KAAI,CAAC5B,OAAL,CAAa6B,SAAb,CAAuBD,QAAxC;;MACA,MAAME,KAAK,GAAG,KAAI,CAAC/B,OAAL,CAAagC,WAAb,EAAd;;MACA,MAAMC,IAAI,GAAG,KAAI,CAAChC,OAAL,CAAaiC,OAAb,CAAqBC,EAAlC;MACA,MAAMC,EAAE,GAAG,IAAIC,IAAJ,GAAWC,OAAX,KAAuB,EAAlC;MACA,MAAMC,IAAI,GAAGZ,QAAb;MACA,MAAMa,MAAM,GAAG,GAAf;MAEA,MAAMC,KAAK,SAAS,KAAI,CAACC,QAAL,CAAchB,SAAd,CAApB,CAT+C,CAU/C;;MACA,MAAMiB,IAAI,GAAG,KAAI,CAACC,OAAL,CACX,KAAI,CAACC,QADM,EAEX,QAAQpB,SAAS,WAAWD,MAAM,WAAWK,QAAQ,SAASE,KAAK,UAAUS,MAAM,QAAQP,IAAI,MAAMG,EAAE,SAASK,KAAK,QAAQF,IAAI,EAFtH,CAAb;;MAKA,OAAO;QACLE,KADK;QAELjB,MAFK;QAGLG,QAHK;QAILE,QAJK;QAKLE,KALK;QAMLY,IANK;QAOLV,IAPK;QAQLG,EARK;QASLX;MATK,CAAP;IAhB+C;EA2BhD;;EAEKiB,QAAQ,CAACI,IAAD,EAAK;IAAA;;IAAA;MACjB,IAAIL,KAAJ;MACA,MAAI,CAACxC,OAAL,CAAa8C,iBAAb,GAAiCD,IAAjC;;MACA,IAAIE,MAAM,CAACC,QAAX,EAAqB;QACnBR,KAAK,SAASO,MAAM,CAACC,QAAP,CAAgBC,YAAhB,CAA6BJ,IAA7B,CAAd;MACD,CAFD,MAEO;QACLL,KAAK,SAAS,MAAI,CAACvC,MAAL,CAAYiD,QAAZ,CAAqBL,IAArB,CAAd;MACD;;MACD,OAAOL,KAAP;IARiB;EASlB;;EAEDG,OAAO,CAAClD,GAAD,EAAcU,IAAd,EAA0B;IAC/B,MAAMgD,IAAI,GAAGnE,UAAU,CAACmB,IAAD,EAAOV,GAAP,CAAvB;IACA,OAAO0D,IAAI,CAAC9B,QAAL,CAAcnC,SAAd,CAAP;EACD;;AA7E8B;;;mBAApBW,sBAAoBuD;AAAA;;;SAApBvD;EAAoBwD,SAApBxD,oBAAoB;EAAAyD,YAFnB", "names": ["hmacSHA256", "Hex", "cryptoHex", "HttpParams", "HttpUrlEncodingCodec", "QRCode", "CLOUD_SECRET_PASSWORD", "CustomEncoder", "encodeKey", "key", "encodeURIComponent", "encodeValue", "value", "AuthorizationService", "constructor", "utilSer", "examSer", "apiSer", "initQrCode", "data", "eleId", "elementId", "url", "createPostUrl", "to<PERSON><PERSON><PERSON>", "document", "getElementById", "error", "console", "log", "type", "method", "applyAuthorOnline", "mobileApplyAuthorOnline", "params", "encoder", "set", "toString", "createPostData", "reason", "eventCode", "localCode", "event_id", "_createGuid", "entry_id", "entryInfo", "nonce", "_createCode", "s_id", "session", "id", "ts", "Date", "getTime", "uuid", "source", "token", "getToken", "sign", "HMAC256", "password", "code", "offline_auth_code", "window", "joyshell", "EncryptToken", "genToken", "hash", "i0", "factory", "providedIn"], "sourceRoot": "", "sources": ["D:\\work\\joyserver\\client\\src\\app\\core\\service\\authorization.service.ts"], "sourcesContent": ["import { element } from \"protractor\";\nimport { Injectable } from \"@angular/core\";\nimport { UtilService } from \"./utils/util.service\";\nimport { ExamService } from \"./exam.service\";\nimport hmacSHA256 from \"crypto-js/hmac-sha256\";\nimport { Hex as cryptoHex } from \"crypto-js/enc-hex\";\nimport { APIService } from \"./api.service\";\nimport { HttpParams, HttpUrlEncodingCodec } from \"@angular/common/http\";\nimport QRCode from \"qrcode\";\nimport { CLOUD_SECRET_PASSWORD } from \"@core-types/constants\";\n\nclass CustomEncoder extends HttpUrlEncodingCodec {\n  encodeKey(key: string): string {\n    return encodeURIComponent(key);\n  }\n\n  encodeValue(value: string): string {\n    return encodeURIComponent(value);\n  }\n}\n\n@Injectable({\n  providedIn: \"root\",\n})\nexport class AuthorizationService {\n  password = CLOUD_SECRET_PASSWORD;\n\n  constructor(private utilSer: UtilService, private examSer: ExamService, private apiSer: APIService) {}\n\n  initQrCode(data, eleId?) {\n    const elementId = eleId || \"qrCode-can\";\n    const url = this.createPostUrl(\"mobile\", data);\n    QRCode.toCanvas(document.getElementById(elementId), url, function (error) {\n      if (error) console.error(error);\n      console.log(\"**** QR inited success!\");\n    });\n  }\n\n  createPostUrl(type, data, method?) {\n    let url = type === \"pc\" ? this.apiSer.applyAuthorOnline : this.apiSer.mobileApplyAuthorOnline;\n    if (method === \"POST\") {\n      return url;\n    }\n    const params = new HttpParams({ encoder: new CustomEncoder() })\n      .set(\"ts\", data[\"ts\"])\n      .set(\"nonce\", data[\"nonce\"])\n      .set(\"sign\", data[\"sign\"])\n      .set(\"uuid\", data[\"event_id\"])\n      .set(\"source\", \"c\")\n      .set(\"code\", data[\"eventCode\"])\n      .set(\"s_id\", data[\"s_id\"])\n      .set(\"entries\", data[\"entry_id\"])\n      .set(\"content\", data[\"reason\"])\n      .set(\"token\", data[\"token\"]);\n    url += \"?\" + params.toString();\n    return url;\n  }\n\n  async createPostData(reason, eventCode, localCode) {\n    const event_id = this.utilSer._createGuid();\n    const entry_id = this.examSer.entryInfo.entry_id;\n    const nonce = this.utilSer._createCode();\n    const s_id = this.examSer.session.id;\n    const ts = new Date().getTime() + \"\";\n    const uuid = event_id;\n    const source = \"c\";\n\n    const token = await this.getToken(localCode);\n    //const sign = Md5.hashStr(event_id + password);\n    const sign = this.HMAC256(\n      this.password,\n      `code=${eventCode}content=${reason}entries=${entry_id}nonce=${nonce}source=${source}s_id=${s_id}ts=${ts}token=${token}uuid=${uuid}`\n    );\n\n    return {\n      token,\n      reason,\n      event_id,\n      entry_id,\n      nonce,\n      sign,\n      s_id,\n      ts,\n      eventCode,\n    };\n  }\n\n  async getToken(code) {\n    let token;\n    this.examSer.offline_auth_code = code;\n    if (window.joyshell) {\n      token = await window.joyshell.EncryptToken(code);\n    } else {\n      token = await this.apiSer.genToken(code);\n    }\n    return token;\n  }\n\n  HMAC256(key: string, data: string) {\n    const hash = hmacSHA256(data, key);\n    return hash.toString(cryptoHex);\n  }\n}\n"]}, "metadata": {}, "sourceType": "module"}