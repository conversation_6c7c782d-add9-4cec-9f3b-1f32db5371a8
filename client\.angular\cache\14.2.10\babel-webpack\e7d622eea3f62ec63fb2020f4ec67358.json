{"ast": null, "code": "import { factory } from '../../utils/factory.js';\nimport { asinhNumber } from '../../plain/number/index.js';\nvar name = 'asinh';\nvar dependencies = ['typed'];\nexport var createAsinh = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed\n  } = _ref;\n  /**\n   * Calculate the hyperbolic arcsine of a value,\n   * defined as `asinh(x) = ln(x + sqrt(x^2 + 1))`.\n   *\n   * To avoid confusion with the matrix hyperbolic arcsine, this function\n   * does not apply to matrices.\n   *\n   * Syntax:\n   *\n   *    math.asinh(x)\n   *\n   * Examples:\n   *\n   *    math.asinh(0.5)       // returns 0.48121182505960347\n   *\n   * See also:\n   *\n   *    acosh, atanh\n   *\n   * @param {number | BigNumber | Complex} x  Function input\n   * @return {number | BigNumber | Complex} Hyperbolic arcsine of x\n   */\n\n  return typed('asinh', {\n    number: asinhNumber,\n    Complex: function Complex(x) {\n      return x.asinh();\n    },\n    BigNumber: function BigNumber(x) {\n      return x.asinh();\n    }\n  });\n});", "map": null, "metadata": {}, "sourceType": "module"}