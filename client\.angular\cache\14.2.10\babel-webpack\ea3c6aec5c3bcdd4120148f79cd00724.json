{"ast": null, "code": "import { factory } from '../../utils/factory.js';\nexport var createTrigUnit = /* #__PURE__ */factory('trigUnit', ['typed'], _ref => {\n  var {\n    typed\n  } = _ref;\n  return {\n    Unit: typed.referToSelf(self => x => {\n      if (!x.hasBase(x.constructor.BASE_UNITS.ANGLE)) {\n        throw new TypeError('Unit in function cot is no angle');\n      }\n\n      return typed.find(self, x.valueType())(x.value);\n    })\n  };\n});", "map": null, "metadata": {}, "sourceType": "module"}