{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/extends\";\nimport typedFunction from 'typed-function';\nimport { ArgumentsError } from '../error/ArgumentsError.js';\nimport { DimensionError } from '../error/DimensionError.js';\nimport { IndexError } from '../error/IndexError.js';\nimport { factory, isFactory } from '../utils/factory.js';\nimport { isAccessorNode, isArray, isArrayNode, isAssignmentNode, isBigInt, isBigNumber, isBlockNode, isBoolean, isChain, isCollection, isComplex, isConditionalNode, isConstantNode, isDate, isDenseMatrix, isFraction, isFunction, isFunctionAssignmentNode, isFunctionNode, isHelp, isIndex, isIndexNode, isMap, isMatrix, isNode, isNull, isNumber, isObject, isObjectNode, isObjectWrappingMap, isOperatorNode, isParenthesisNode, isPartitionedMap, isRange, isRangeNode, isRegExp, isRelationalNode, isResultSet, isSparseMatrix, isString, isSymbolNode, isUndefined, isUnit } from '../utils/is.js';\nimport { deepFlatten, isLegacyFactory } from '../utils/object.js';\nimport * as emitter from './../utils/emitter.js';\nimport { DEFAULT_CONFIG } from './config.js';\nimport { configFactory } from './function/config.js';\nimport { importFactory } from './function/import.js';\n/**\n * Create a mathjs instance from given factory functions and optionally config\n *\n * Usage:\n *\n *     const mathjs1 = create({ createAdd, createMultiply, ...})\n *     const config = { number: 'BigNumber' }\n *     const mathjs2 = create(all, config)\n *\n * @param {Object} [factories] An object with factory functions\n *                             The object can contain nested objects,\n *                             all nested objects will be flattened.\n * @param {Object} [config]    Available options:\n *                            {number} relTol\n *                              Minimum relative difference between two\n *                              compared values, used by all comparison functions.\n *                            {number} absTol\n *                              Minimum absolute difference between two\n *                              compared values, used by all comparison functions.\n *                            {string} matrix\n *                              A string 'Matrix' (default) or 'Array'.\n *                            {string} number\n *                              A string 'number' (default), 'BigNumber', or 'Fraction'\n *                            {number} precision\n *                              The number of significant digits for BigNumbers.\n *                              Not applicable for Numbers.\n *                            {boolean} predictable\n *                              Predictable output type of functions. When true,\n *                              output type depends only on the input types. When\n *                              false (default), output type can vary depending\n *                              on input values. For example `math.sqrt(-4)`\n *                              returns `complex('2i')` when predictable is false, and\n *                              returns `NaN` when true.\n *                            {string} randomSeed\n *                              Random seed for seeded pseudo random number generator.\n *                              Set to null to randomly seed.\n * @returns {Object} Returns a bare-bone math.js instance containing\n *                   functions:\n *                   - `import` to add new functions\n *                   - `config` to change configuration\n *                   - `on`, `off`, `once`, `emit` for events\n */\n\nexport function create(factories, config) {\n  var configInternal = _extends({}, DEFAULT_CONFIG, config); // simple test for ES5 support\n\n\n  if (typeof Object.create !== 'function') {\n    throw new Error('ES5 not supported by this JavaScript engine. ' + 'Please load the es5-shim and es5-sham library for compatibility.');\n  } // create the mathjs instance\n\n\n  var math = emitter.mixin({\n    // only here for backward compatibility for legacy factory functions\n    isNumber,\n    isComplex,\n    isBigNumber,\n    isBigInt,\n    isFraction,\n    isUnit,\n    isString,\n    isArray,\n    isMatrix,\n    isCollection,\n    isDenseMatrix,\n    isSparseMatrix,\n    isRange,\n    isIndex,\n    isBoolean,\n    isResultSet,\n    isHelp,\n    isFunction,\n    isDate,\n    isRegExp,\n    isObject,\n    isMap,\n    isPartitionedMap,\n    isObjectWrappingMap,\n    isNull,\n    isUndefined,\n    isAccessorNode,\n    isArrayNode,\n    isAssignmentNode,\n    isBlockNode,\n    isConditionalNode,\n    isConstantNode,\n    isFunctionAssignmentNode,\n    isFunctionNode,\n    isIndexNode,\n    isNode,\n    isObjectNode,\n    isOperatorNode,\n    isParenthesisNode,\n    isRangeNode,\n    isRelationalNode,\n    isSymbolNode,\n    isChain\n  }); // load config function and apply provided config\n\n  math.config = configFactory(configInternal, math.emit);\n  math.expression = {\n    transform: {},\n    mathWithTransform: {\n      config: math.config\n    }\n  }; // cached factories and instances used by function load\n\n  var legacyFactories = [];\n  var legacyInstances = [];\n  /**\n   * Load a function or data type from a factory.\n   * If the function or data type already exists, the existing instance is\n   * returned.\n   * @param {Function} factory\n   * @returns {*}\n   */\n\n  function load(factory) {\n    if (isFactory(factory)) {\n      return factory(math);\n    }\n\n    var firstProperty = factory[Object.keys(factory)[0]];\n\n    if (isFactory(firstProperty)) {\n      return firstProperty(math);\n    }\n\n    if (!isLegacyFactory(factory)) {\n      console.warn('Factory object with properties `type`, `name`, and `factory` expected', factory);\n      throw new Error('Factory object with properties `type`, `name`, and `factory` expected');\n    }\n\n    var index = legacyFactories.indexOf(factory);\n    var instance;\n\n    if (index === -1) {\n      // doesn't yet exist\n      if (factory.math === true) {\n        // pass with math namespace\n        instance = factory.factory(math.type, configInternal, load, math.typed, math);\n      } else {\n        instance = factory.factory(math.type, configInternal, load, math.typed);\n      } // append to the cache\n\n\n      legacyFactories.push(factory);\n      legacyInstances.push(instance);\n    } else {\n      // already existing function, return the cached instance\n      instance = legacyInstances[index];\n    }\n\n    return instance;\n  }\n\n  var importedFactories = {}; // load the import function\n\n  function lazyTyped() {\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n\n    return math.typed.apply(math.typed, args);\n  }\n\n  lazyTyped.isTypedFunction = typedFunction.isTypedFunction;\n  var internalImport = importFactory(lazyTyped, load, math, importedFactories);\n  math.import = internalImport; // listen for changes in config, import all functions again when changed\n  // TODO: move this listener into the import function?\n\n  math.on('config', () => {\n    Object.values(importedFactories).forEach(factory => {\n      if (factory && factory.meta && factory.meta.recreateOnConfigChange) {\n        // FIXME: only re-create when the current instance is the same as was initially created\n        // FIXME: delete the functions/constants before importing them again?\n        internalImport(factory, {\n          override: true\n        });\n      }\n    });\n  }); // the create function exposed on the mathjs instance is bound to\n  // the factory functions passed before\n\n  math.create = create.bind(null, factories); // export factory function\n\n  math.factory = factory; // import the factory functions like createAdd as an array instead of object,\n  // else they will get a different naming (`createAdd` instead of `add`).\n\n  math.import(Object.values(deepFlatten(factories)));\n  math.ArgumentsError = ArgumentsError;\n  math.DimensionError = DimensionError;\n  math.IndexError = IndexError;\n  return math;\n}", "map": null, "metadata": {}, "sourceType": "module"}