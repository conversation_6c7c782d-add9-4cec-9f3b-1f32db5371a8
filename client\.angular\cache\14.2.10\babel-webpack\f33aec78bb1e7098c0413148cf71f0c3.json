{"ast": null, "code": "(function (e, t) {\n  \"object\" == typeof exports && \"undefined\" != typeof module ? module.exports = t() : \"function\" == typeof define && define.amd ? define(t) : (e = \"undefined\" != typeof globalThis ? globalThis : e || self).Dexie = t();\n})(this, function () {\n  \"use strict\";\n\n  var s = function (e, t) {\n    return (s = Object.setPrototypeOf || {\n      __proto__: []\n    } instanceof Array && function (e, t) {\n      e.__proto__ = t;\n    } || function (e, t) {\n      for (var n in t) Object.prototype.hasOwnProperty.call(t, n) && (e[n] = t[n]);\n    })(e, t);\n  };\n\n  var _ = function () {\n    return (_ = Object.assign || function (e) {\n      for (var t, n = 1, r = arguments.length; n < r; n++) for (var i in t = arguments[n]) Object.prototype.hasOwnProperty.call(t, i) && (e[i] = t[i]);\n\n      return e;\n    }).apply(this, arguments);\n  };\n\n  function i(e, t, n) {\n    if (n || 2 === arguments.length) for (var r, i = 0, o = t.length; i < o; i++) !r && i in t || ((r = r || Array.prototype.slice.call(t, 0, i))[i] = t[i]);\n    return e.concat(r || Array.prototype.slice.call(t));\n  }\n\n  var f = \"undefined\" != typeof globalThis ? globalThis : \"undefined\" != typeof self ? self : \"undefined\" != typeof window ? window : global,\n      x = Object.keys,\n      k = Array.isArray;\n\n  function a(t, n) {\n    return \"object\" != typeof n || x(n).forEach(function (e) {\n      t[e] = n[e];\n    }), t;\n  }\n\n  \"undefined\" == typeof Promise || f.Promise || (f.Promise = Promise);\n  var c = Object.getPrototypeOf,\n      n = {}.hasOwnProperty;\n\n  function m(e, t) {\n    return n.call(e, t);\n  }\n\n  function r(t, n) {\n    \"function\" == typeof n && (n = n(c(t))), (\"undefined\" == typeof Reflect ? x : Reflect.ownKeys)(n).forEach(function (e) {\n      l(t, e, n[e]);\n    });\n  }\n\n  var u = Object.defineProperty;\n\n  function l(e, t, n, r) {\n    u(e, t, a(n && m(n, \"get\") && \"function\" == typeof n.get ? {\n      get: n.get,\n      set: n.set,\n      configurable: !0\n    } : {\n      value: n,\n      configurable: !0,\n      writable: !0\n    }, r));\n  }\n\n  function o(t) {\n    return {\n      from: function (e) {\n        return t.prototype = Object.create(e.prototype), l(t.prototype, \"constructor\", t), {\n          extend: r.bind(null, t.prototype)\n        };\n      }\n    };\n  }\n\n  var h = Object.getOwnPropertyDescriptor;\n  var d = [].slice;\n\n  function b(e, t, n) {\n    return d.call(e, t, n);\n  }\n\n  function p(e, t) {\n    return t(e);\n  }\n\n  function y(e) {\n    if (!e) throw new Error(\"Assertion Failed\");\n  }\n\n  function v(e) {\n    f.setImmediate ? setImmediate(e) : setTimeout(e, 0);\n  }\n\n  function O(e, t) {\n    if (\"string\" == typeof t && m(e, t)) return e[t];\n    if (!t) return e;\n\n    if (\"string\" != typeof t) {\n      for (var n = [], r = 0, i = t.length; r < i; ++r) {\n        var o = O(e, t[r]);\n        n.push(o);\n      }\n\n      return n;\n    }\n\n    var a = t.indexOf(\".\");\n\n    if (-1 !== a) {\n      var u = e[t.substr(0, a)];\n      return null == u ? void 0 : O(u, t.substr(a + 1));\n    }\n  }\n\n  function P(e, t, n) {\n    if (e && void 0 !== t && !(\"isFrozen\" in Object && Object.isFrozen(e))) if (\"string\" != typeof t && \"length\" in t) {\n      y(\"string\" != typeof n && \"length\" in n);\n\n      for (var r = 0, i = t.length; r < i; ++r) P(e, t[r], n[r]);\n    } else {\n      var o,\n          a,\n          u = t.indexOf(\".\");\n      -1 !== u ? (o = t.substr(0, u), \"\" === (a = t.substr(u + 1)) ? void 0 === n ? k(e) && !isNaN(parseInt(o)) ? e.splice(o, 1) : delete e[o] : e[o] = n : P(u = !(u = e[o]) || !m(e, o) ? e[o] = {} : u, a, n)) : void 0 === n ? k(e) && !isNaN(parseInt(t)) ? e.splice(t, 1) : delete e[t] : e[t] = n;\n    }\n  }\n\n  function g(e) {\n    var t,\n        n = {};\n\n    for (t in e) m(e, t) && (n[t] = e[t]);\n\n    return n;\n  }\n\n  var t = [].concat;\n\n  function w(e) {\n    return t.apply([], e);\n  }\n\n  var e = \"BigUint64Array,BigInt64Array,Array,Boolean,String,Date,RegExp,Blob,File,FileList,FileSystemFileHandle,FileSystemDirectoryHandle,ArrayBuffer,DataView,Uint8ClampedArray,ImageBitmap,ImageData,Map,Set,CryptoKey\".split(\",\").concat(w([8, 16, 32, 64].map(function (t) {\n    return [\"Int\", \"Uint\", \"Float\"].map(function (e) {\n      return e + t + \"Array\";\n    });\n  }))).filter(function (e) {\n    return f[e];\n  }),\n      K = new Set(e.map(function (e) {\n    return f[e];\n  }));\n  var E = null;\n\n  function S(e) {\n    E = new WeakMap();\n\n    e = function e(t) {\n      if (!t || \"object\" != typeof t) return t;\n      var n = E.get(t);\n      if (n) return n;\n\n      if (k(t)) {\n        n = [], E.set(t, n);\n\n        for (var r = 0, i = t.length; r < i; ++r) n.push(e(t[r]));\n      } else if (K.has(t.constructor)) n = t;else {\n        var o,\n            a = c(t);\n\n        for (o in n = a === Object.prototype ? {} : Object.create(a), E.set(t, n), t) m(t, o) && (n[o] = e(t[o]));\n      }\n\n      return n;\n    }(e);\n\n    return E = null, e;\n  }\n\n  var j = {}.toString;\n\n  function A(e) {\n    return j.call(e).slice(8, -1);\n  }\n\n  var C = \"undefined\" != typeof Symbol ? Symbol.iterator : \"@@iterator\",\n      T = \"symbol\" == typeof C ? function (e) {\n    var t;\n    return null != e && (t = e[C]) && t.apply(e);\n  } : function () {\n    return null;\n  };\n\n  function q(e, t) {\n    t = e.indexOf(t);\n    return 0 <= t && e.splice(t, 1), 0 <= t;\n  }\n\n  var D = {};\n\n  function I(e) {\n    var t, n, r, i;\n\n    if (1 === arguments.length) {\n      if (k(e)) return e.slice();\n      if (this === D && \"string\" == typeof e) return [e];\n\n      if (i = T(e)) {\n        for (n = []; !(r = i.next()).done;) n.push(r.value);\n\n        return n;\n      }\n\n      if (null == e) return [e];\n      if (\"number\" != typeof (t = e.length)) return [e];\n\n      for (n = new Array(t); t--;) n[t] = e[t];\n\n      return n;\n    }\n\n    for (t = arguments.length, n = new Array(t); t--;) n[t] = arguments[t];\n\n    return n;\n  }\n\n  var B = \"undefined\" != typeof Symbol ? function (e) {\n    return \"AsyncFunction\" === e[Symbol.toStringTag];\n  } : function () {\n    return !1;\n  },\n      R = [\"Unknown\", \"Constraint\", \"Data\", \"TransactionInactive\", \"ReadOnly\", \"Version\", \"NotFound\", \"InvalidState\", \"InvalidAccess\", \"Abort\", \"Timeout\", \"QuotaExceeded\", \"Syntax\", \"DataClone\"],\n      M = [\"Modify\", \"Bulk\", \"OpenFailed\", \"VersionChange\", \"Schema\", \"Upgrade\", \"InvalidTable\", \"MissingAPI\", \"NoSuchDatabase\", \"InvalidArgument\", \"SubTransaction\", \"Unsupported\", \"Internal\", \"DatabaseClosed\", \"PrematureCommit\", \"ForeignAwait\"].concat(R),\n      F = {\n    VersionChanged: \"Database version changed by other database connection\",\n    DatabaseClosed: \"Database has been closed\",\n    Abort: \"Transaction aborted\",\n    TransactionInactive: \"Transaction has already completed or failed\",\n    MissingAPI: \"IndexedDB API missing. Please visit https://tinyurl.com/y2uuvskb\"\n  };\n\n  function N(e, t) {\n    this.name = e, this.message = t;\n  }\n\n  function L(e, t) {\n    return e + \". Errors: \" + Object.keys(t).map(function (e) {\n      return t[e].toString();\n    }).filter(function (e, t, n) {\n      return n.indexOf(e) === t;\n    }).join(\"\\n\");\n  }\n\n  function U(e, t, n, r) {\n    this.failures = t, this.failedKeys = r, this.successCount = n, this.message = L(e, t);\n  }\n\n  function V(e, t) {\n    this.name = \"BulkError\", this.failures = Object.keys(t).map(function (e) {\n      return t[e];\n    }), this.failuresByPos = t, this.message = L(e, this.failures);\n  }\n\n  o(N).from(Error).extend({\n    toString: function () {\n      return this.name + \": \" + this.message;\n    }\n  }), o(U).from(N), o(V).from(N);\n  var z = M.reduce(function (e, t) {\n    return e[t] = t + \"Error\", e;\n  }, {}),\n      W = N,\n      Y = M.reduce(function (e, n) {\n    var r = n + \"Error\";\n\n    function t(e, t) {\n      this.name = r, e ? \"string\" == typeof e ? (this.message = \"\".concat(e).concat(t ? \"\\n \" + t : \"\"), this.inner = t || null) : \"object\" == typeof e && (this.message = \"\".concat(e.name, \" \").concat(e.message), this.inner = e) : (this.message = F[n] || r, this.inner = null);\n    }\n\n    return o(t).from(W), e[n] = t, e;\n  }, {});\n  Y.Syntax = SyntaxError, Y.Type = TypeError, Y.Range = RangeError;\n  var $ = R.reduce(function (e, t) {\n    return e[t + \"Error\"] = Y[t], e;\n  }, {});\n  var Q = M.reduce(function (e, t) {\n    return -1 === [\"Syntax\", \"Type\", \"Range\"].indexOf(t) && (e[t + \"Error\"] = Y[t]), e;\n  }, {});\n\n  function G() {}\n\n  function X(e) {\n    return e;\n  }\n\n  function H(t, n) {\n    return null == t || t === X ? n : function (e) {\n      return n(t(e));\n    };\n  }\n\n  function J(e, t) {\n    return function () {\n      e.apply(this, arguments), t.apply(this, arguments);\n    };\n  }\n\n  function Z(i, o) {\n    return i === G ? o : function () {\n      var e = i.apply(this, arguments);\n      void 0 !== e && (arguments[0] = e);\n      var t = this.onsuccess,\n          n = this.onerror;\n      this.onsuccess = null, this.onerror = null;\n      var r = o.apply(this, arguments);\n      return t && (this.onsuccess = this.onsuccess ? J(t, this.onsuccess) : t), n && (this.onerror = this.onerror ? J(n, this.onerror) : n), void 0 !== r ? r : e;\n    };\n  }\n\n  function ee(n, r) {\n    return n === G ? r : function () {\n      n.apply(this, arguments);\n      var e = this.onsuccess,\n          t = this.onerror;\n      this.onsuccess = this.onerror = null, r.apply(this, arguments), e && (this.onsuccess = this.onsuccess ? J(e, this.onsuccess) : e), t && (this.onerror = this.onerror ? J(t, this.onerror) : t);\n    };\n  }\n\n  function te(i, o) {\n    return i === G ? o : function (e) {\n      var t = i.apply(this, arguments);\n      a(e, t);\n      var n = this.onsuccess,\n          r = this.onerror;\n      this.onsuccess = null, this.onerror = null;\n      e = o.apply(this, arguments);\n      return n && (this.onsuccess = this.onsuccess ? J(n, this.onsuccess) : n), r && (this.onerror = this.onerror ? J(r, this.onerror) : r), void 0 === t ? void 0 === e ? void 0 : e : a(t, e);\n    };\n  }\n\n  function ne(e, t) {\n    return e === G ? t : function () {\n      return !1 !== t.apply(this, arguments) && e.apply(this, arguments);\n    };\n  }\n\n  function re(i, o) {\n    return i === G ? o : function () {\n      var e = i.apply(this, arguments);\n\n      if (e && \"function\" == typeof e.then) {\n        for (var t = this, n = arguments.length, r = new Array(n); n--;) r[n] = arguments[n];\n\n        return e.then(function () {\n          return o.apply(t, r);\n        });\n      }\n\n      return o.apply(this, arguments);\n    };\n  }\n\n  Q.ModifyError = U, Q.DexieError = N, Q.BulkError = V;\n  var ie = \"undefined\" != typeof location && /^(http|https):\\/\\/(localhost|127\\.0\\.0\\.1)/.test(location.href);\n\n  function oe(e) {\n    ie = e;\n  }\n\n  var ae = {},\n      ue = 100,\n      e = \"undefined\" == typeof Promise ? [] : function () {\n    var e = Promise.resolve();\n    if (\"undefined\" == typeof crypto || !crypto.subtle) return [e, c(e), e];\n    var t = crypto.subtle.digest(\"SHA-512\", new Uint8Array([0]));\n    return [t, c(t), e];\n  }(),\n      R = e[0],\n      M = e[1],\n      e = e[2],\n      M = M && M.then,\n      se = R && R.constructor,\n      ce = !!e;\n\n  var le = function (e, t) {\n    be.push([e, t]), he && (queueMicrotask(Se), he = !1);\n  },\n      fe = !0,\n      he = !0,\n      de = [],\n      pe = [],\n      ye = X,\n      ve = {\n    id: \"global\",\n    global: !0,\n    ref: 0,\n    unhandleds: [],\n    onunhandled: G,\n    pgp: !1,\n    env: {},\n    finalize: G\n  },\n      me = ve,\n      be = [],\n      ge = 0,\n      we = [];\n\n  function _e(e) {\n    if (\"object\" != typeof this) throw new TypeError(\"Promises must be constructed via new\");\n    this._listeners = [], this._lib = !1;\n    var t = this._PSD = me;\n\n    if (\"function\" != typeof e) {\n      if (e !== ae) throw new TypeError(\"Not a function\");\n      return this._state = arguments[1], this._value = arguments[2], void (!1 === this._state && Oe(this, this._value));\n    }\n\n    this._state = null, this._value = null, ++t.ref, function t(r, e) {\n      try {\n        e(function (n) {\n          if (null === r._state) {\n            if (n === r) throw new TypeError(\"A promise cannot be resolved with itself.\");\n            var e = r._lib && je();\n            n && \"function\" == typeof n.then ? t(r, function (e, t) {\n              n instanceof _e ? n._then(e, t) : n.then(e, t);\n            }) : (r._state = !0, r._value = n, Pe(r)), e && Ae();\n          }\n        }, Oe.bind(null, r));\n      } catch (e) {\n        Oe(r, e);\n      }\n    }(this, e);\n  }\n\n  var xe = {\n    get: function () {\n      var u = me,\n          t = Me;\n\n      function e(n, r) {\n        var i = this,\n            o = !u.global && (u !== me || t !== Me),\n            a = o && !Ue(),\n            e = new _e(function (e, t) {\n          Ke(i, new ke(Qe(n, u, o, a), Qe(r, u, o, a), e, t, u));\n        });\n        return this._consoleTask && (e._consoleTask = this._consoleTask), e;\n      }\n\n      return e.prototype = ae, e;\n    },\n    set: function (e) {\n      l(this, \"then\", e && e.prototype === ae ? xe : {\n        get: function () {\n          return e;\n        },\n        set: xe.set\n      });\n    }\n  };\n\n  function ke(e, t, n, r, i) {\n    this.onFulfilled = \"function\" == typeof e ? e : null, this.onRejected = \"function\" == typeof t ? t : null, this.resolve = n, this.reject = r, this.psd = i;\n  }\n\n  function Oe(e, t) {\n    var n, r;\n    pe.push(t), null === e._state && (n = e._lib && je(), t = ye(t), e._state = !1, e._value = t, r = e, de.some(function (e) {\n      return e._value === r._value;\n    }) || de.push(r), Pe(e), n && Ae());\n  }\n\n  function Pe(e) {\n    var t = e._listeners;\n    e._listeners = [];\n\n    for (var n = 0, r = t.length; n < r; ++n) Ke(e, t[n]);\n\n    var i = e._PSD;\n    --i.ref || i.finalize(), 0 === ge && (++ge, le(function () {\n      0 == --ge && Ce();\n    }, []));\n  }\n\n  function Ke(e, t) {\n    if (null !== e._state) {\n      var n = e._state ? t.onFulfilled : t.onRejected;\n      if (null === n) return (e._state ? t.resolve : t.reject)(e._value);\n      ++t.psd.ref, ++ge, le(Ee, [n, e, t]);\n    } else e._listeners.push(t);\n  }\n\n  function Ee(e, t, n) {\n    try {\n      var r,\n          i = t._value;\n      !t._state && pe.length && (pe = []), r = ie && t._consoleTask ? t._consoleTask.run(function () {\n        return e(i);\n      }) : e(i), t._state || -1 !== pe.indexOf(i) || function (e) {\n        var t = de.length;\n\n        for (; t;) if (de[--t]._value === e._value) return de.splice(t, 1);\n      }(t), n.resolve(r);\n    } catch (e) {\n      n.reject(e);\n    } finally {\n      0 == --ge && Ce(), --n.psd.ref || n.psd.finalize();\n    }\n  }\n\n  function Se() {\n    $e(ve, function () {\n      je() && Ae();\n    });\n  }\n\n  function je() {\n    var e = fe;\n    return he = fe = !1, e;\n  }\n\n  function Ae() {\n    var e, t, n;\n\n    do {\n      for (; 0 < be.length;) for (e = be, be = [], n = e.length, t = 0; t < n; ++t) {\n        var r = e[t];\n        r[0].apply(null, r[1]);\n      }\n    } while (0 < be.length);\n\n    he = fe = !0;\n  }\n\n  function Ce() {\n    var e = de;\n    de = [], e.forEach(function (e) {\n      e._PSD.onunhandled.call(null, e._value, e);\n    });\n\n    for (var t = we.slice(0), n = t.length; n;) t[--n]();\n  }\n\n  function Te(e) {\n    return new _e(ae, !1, e);\n  }\n\n  function qe(n, r) {\n    var i = me;\n    return function () {\n      var e = je(),\n          t = me;\n\n      try {\n        return We(i, !0), n.apply(this, arguments);\n      } catch (e) {\n        r && r(e);\n      } finally {\n        We(t, !1), e && Ae();\n      }\n    };\n  }\n\n  r(_e.prototype, {\n    then: xe,\n    _then: function (e, t) {\n      Ke(this, new ke(null, null, e, t, me));\n    },\n    catch: function (e) {\n      if (1 === arguments.length) return this.then(null, e);\n      var t = e,\n          n = arguments[1];\n      return \"function\" == typeof t ? this.then(null, function (e) {\n        return (e instanceof t ? n : Te)(e);\n      }) : this.then(null, function (e) {\n        return (e && e.name === t ? n : Te)(e);\n      });\n    },\n    finally: function (t) {\n      return this.then(function (e) {\n        return _e.resolve(t()).then(function () {\n          return e;\n        });\n      }, function (e) {\n        return _e.resolve(t()).then(function () {\n          return Te(e);\n        });\n      });\n    },\n    timeout: function (r, i) {\n      var o = this;\n      return r < 1 / 0 ? new _e(function (e, t) {\n        var n = setTimeout(function () {\n          return t(new Y.Timeout(i));\n        }, r);\n        o.then(e, t).finally(clearTimeout.bind(null, n));\n      }) : this;\n    }\n  }), \"undefined\" != typeof Symbol && Symbol.toStringTag && l(_e.prototype, Symbol.toStringTag, \"Dexie.Promise\"), ve.env = Ye(), r(_e, {\n    all: function () {\n      var o = I.apply(null, arguments).map(Ve);\n      return new _e(function (n, r) {\n        0 === o.length && n([]);\n        var i = o.length;\n        o.forEach(function (e, t) {\n          return _e.resolve(e).then(function (e) {\n            o[t] = e, --i || n(o);\n          }, r);\n        });\n      });\n    },\n    resolve: function (n) {\n      return n instanceof _e ? n : n && \"function\" == typeof n.then ? new _e(function (e, t) {\n        n.then(e, t);\n      }) : new _e(ae, !0, n);\n    },\n    reject: Te,\n    race: function () {\n      var e = I.apply(null, arguments).map(Ve);\n      return new _e(function (t, n) {\n        e.map(function (e) {\n          return _e.resolve(e).then(t, n);\n        });\n      });\n    },\n    PSD: {\n      get: function () {\n        return me;\n      },\n      set: function (e) {\n        return me = e;\n      }\n    },\n    totalEchoes: {\n      get: function () {\n        return Me;\n      }\n    },\n    newPSD: Ne,\n    usePSD: $e,\n    scheduler: {\n      get: function () {\n        return le;\n      },\n      set: function (e) {\n        le = e;\n      }\n    },\n    rejectionMapper: {\n      get: function () {\n        return ye;\n      },\n      set: function (e) {\n        ye = e;\n      }\n    },\n    follow: function (i, n) {\n      return new _e(function (e, t) {\n        return Ne(function (n, r) {\n          var e = me;\n          e.unhandleds = [], e.onunhandled = r, e.finalize = J(function () {\n            var t,\n                e = this;\n            t = function () {\n              0 === e.unhandleds.length ? n() : r(e.unhandleds[0]);\n            }, we.push(function e() {\n              t(), we.splice(we.indexOf(e), 1);\n            }), ++ge, le(function () {\n              0 == --ge && Ce();\n            }, []);\n          }, e.finalize), i();\n        }, n, e, t);\n      });\n    }\n  }), se && (se.allSettled && l(_e, \"allSettled\", function () {\n    var e = I.apply(null, arguments).map(Ve);\n    return new _e(function (n) {\n      0 === e.length && n([]);\n      var r = e.length,\n          i = new Array(r);\n      e.forEach(function (e, t) {\n        return _e.resolve(e).then(function (e) {\n          return i[t] = {\n            status: \"fulfilled\",\n            value: e\n          };\n        }, function (e) {\n          return i[t] = {\n            status: \"rejected\",\n            reason: e\n          };\n        }).then(function () {\n          return --r || n(i);\n        });\n      });\n    });\n  }), se.any && \"undefined\" != typeof AggregateError && l(_e, \"any\", function () {\n    var e = I.apply(null, arguments).map(Ve);\n    return new _e(function (n, r) {\n      0 === e.length && r(new AggregateError([]));\n      var i = e.length,\n          o = new Array(i);\n      e.forEach(function (e, t) {\n        return _e.resolve(e).then(function (e) {\n          return n(e);\n        }, function (e) {\n          o[t] = e, --i || r(new AggregateError(o));\n        });\n      });\n    });\n  }), se.withResolvers && (_e.withResolvers = se.withResolvers));\n  var De = {\n    awaits: 0,\n    echoes: 0,\n    id: 0\n  },\n      Ie = 0,\n      Be = [],\n      Re = 0,\n      Me = 0,\n      Fe = 0;\n\n  function Ne(e, t, n, r) {\n    var i = me,\n        o = Object.create(i);\n    o.parent = i, o.ref = 0, o.global = !1, o.id = ++Fe, ve.env, o.env = ce ? {\n      Promise: _e,\n      PromiseProp: {\n        value: _e,\n        configurable: !0,\n        writable: !0\n      },\n      all: _e.all,\n      race: _e.race,\n      allSettled: _e.allSettled,\n      any: _e.any,\n      resolve: _e.resolve,\n      reject: _e.reject\n    } : {}, t && a(o, t), ++i.ref, o.finalize = function () {\n      --this.parent.ref || this.parent.finalize();\n    };\n    r = $e(o, e, n, r);\n    return 0 === o.ref && o.finalize(), r;\n  }\n\n  function Le() {\n    return De.id || (De.id = ++Ie), ++De.awaits, De.echoes += ue, De.id;\n  }\n\n  function Ue() {\n    return !!De.awaits && (0 == --De.awaits && (De.id = 0), De.echoes = De.awaits * ue, !0);\n  }\n\n  function Ve(e) {\n    return De.echoes && e && e.constructor === se ? (Le(), e.then(function (e) {\n      return Ue(), e;\n    }, function (e) {\n      return Ue(), Xe(e);\n    })) : e;\n  }\n\n  function ze() {\n    var e = Be[Be.length - 1];\n    Be.pop(), We(e, !1);\n  }\n\n  function We(e, t) {\n    var n,\n        r = me;\n    (t ? !De.echoes || Re++ && e === me : !Re || --Re && e === me) || queueMicrotask(t ? function (e) {\n      ++Me, De.echoes && 0 != --De.echoes || (De.echoes = De.awaits = De.id = 0), Be.push(me), We(e, !0);\n    }.bind(null, e) : ze), e !== me && (me = e, r === ve && (ve.env = Ye()), ce && (n = ve.env.Promise, t = e.env, (r.global || e.global) && (Object.defineProperty(f, \"Promise\", t.PromiseProp), n.all = t.all, n.race = t.race, n.resolve = t.resolve, n.reject = t.reject, t.allSettled && (n.allSettled = t.allSettled), t.any && (n.any = t.any))));\n  }\n\n  function Ye() {\n    var e = f.Promise;\n    return ce ? {\n      Promise: e,\n      PromiseProp: Object.getOwnPropertyDescriptor(f, \"Promise\"),\n      all: e.all,\n      race: e.race,\n      allSettled: e.allSettled,\n      any: e.any,\n      resolve: e.resolve,\n      reject: e.reject\n    } : {};\n  }\n\n  function $e(e, t, n, r, i) {\n    var o = me;\n\n    try {\n      return We(e, !0), t(n, r, i);\n    } finally {\n      We(o, !1);\n    }\n  }\n\n  function Qe(t, n, r, i) {\n    return \"function\" != typeof t ? t : function () {\n      var e = me;\n      r && Le(), We(n, !0);\n\n      try {\n        return t.apply(this, arguments);\n      } finally {\n        We(e, !1), i && queueMicrotask(Ue);\n      }\n    };\n  }\n\n  function Ge(e) {\n    Promise === se && 0 === De.echoes ? 0 === Re ? e() : enqueueNativeMicroTask(e) : setTimeout(e, 0);\n  }\n\n  -1 === (\"\" + M).indexOf(\"[native code]\") && (Le = Ue = G);\n  var Xe = _e.reject;\n  var He = String.fromCharCode(65535),\n      Je = \"Invalid key provided. Keys must be of type string, number, Date or Array<string | number | Date>.\",\n      Ze = \"String expected.\",\n      et = [],\n      tt = \"__dbnames\",\n      nt = \"readonly\",\n      rt = \"readwrite\";\n\n  function it(e, t) {\n    return e ? t ? function () {\n      return e.apply(this, arguments) && t.apply(this, arguments);\n    } : e : t;\n  }\n\n  var ot = {\n    type: 3,\n    lower: -1 / 0,\n    lowerOpen: !1,\n    upper: [[]],\n    upperOpen: !1\n  };\n\n  function at(t) {\n    return \"string\" != typeof t || /\\./.test(t) ? function (e) {\n      return e;\n    } : function (e) {\n      return void 0 === e[t] && t in e && delete (e = S(e))[t], e;\n    };\n  }\n\n  function ut() {\n    throw Y.Type();\n  }\n\n  function st(e, t) {\n    try {\n      var n = ct(e),\n          r = ct(t);\n      if (n !== r) return \"Array\" === n ? 1 : \"Array\" === r ? -1 : \"binary\" === n ? 1 : \"binary\" === r ? -1 : \"string\" === n ? 1 : \"string\" === r ? -1 : \"Date\" === n ? 1 : \"Date\" !== r ? NaN : -1;\n\n      switch (n) {\n        case \"number\":\n        case \"Date\":\n        case \"string\":\n          return t < e ? 1 : e < t ? -1 : 0;\n\n        case \"binary\":\n          return function (e, t) {\n            for (var n = e.length, r = t.length, i = n < r ? n : r, o = 0; o < i; ++o) if (e[o] !== t[o]) return e[o] < t[o] ? -1 : 1;\n\n            return n === r ? 0 : n < r ? -1 : 1;\n          }(lt(e), lt(t));\n\n        case \"Array\":\n          return function (e, t) {\n            for (var n = e.length, r = t.length, i = n < r ? n : r, o = 0; o < i; ++o) {\n              var a = st(e[o], t[o]);\n              if (0 !== a) return a;\n            }\n\n            return n === r ? 0 : n < r ? -1 : 1;\n          }(e, t);\n      }\n    } catch (e) {}\n\n    return NaN;\n  }\n\n  function ct(e) {\n    var t = typeof e;\n    if (\"object\" != t) return t;\n    if (ArrayBuffer.isView(e)) return \"binary\";\n    e = A(e);\n    return \"ArrayBuffer\" === e ? \"binary\" : e;\n  }\n\n  function lt(e) {\n    return e instanceof Uint8Array ? e : ArrayBuffer.isView(e) ? new Uint8Array(e.buffer, e.byteOffset, e.byteLength) : new Uint8Array(e);\n  }\n\n  var ft = (ht.prototype._trans = function (e, r, t) {\n    var n = this._tx || me.trans,\n        i = this.name,\n        o = ie && \"undefined\" != typeof console && console.createTask && console.createTask(\"Dexie: \".concat(\"readonly\" === e ? \"read\" : \"write\", \" \").concat(this.name));\n\n    function a(e, t, n) {\n      if (!n.schema[i]) throw new Y.NotFound(\"Table \" + i + \" not part of transaction\");\n      return r(n.idbtrans, n);\n    }\n\n    var u = je();\n\n    try {\n      var s = n && n.db._novip === this.db._novip ? n === me.trans ? n._promise(e, a, t) : Ne(function () {\n        return n._promise(e, a, t);\n      }, {\n        trans: n,\n        transless: me.transless || me\n      }) : function t(n, r, i, o) {\n        if (n.idbdb && (n._state.openComplete || me.letThrough || n._vip)) {\n          var a = n._createTransaction(r, i, n._dbSchema);\n\n          try {\n            a.create(), n._state.PR1398_maxLoop = 3;\n          } catch (e) {\n            return e.name === z.InvalidState && n.isOpen() && 0 < --n._state.PR1398_maxLoop ? (console.warn(\"Dexie: Need to reopen db\"), n.close({\n              disableAutoOpen: !1\n            }), n.open().then(function () {\n              return t(n, r, i, o);\n            })) : Xe(e);\n          }\n\n          return a._promise(r, function (e, t) {\n            return Ne(function () {\n              return me.trans = a, o(e, t, a);\n            });\n          }).then(function (e) {\n            if (\"readwrite\" === r) try {\n              a.idbtrans.commit();\n            } catch (e) {}\n            return \"readonly\" === r ? e : a._completion.then(function () {\n              return e;\n            });\n          });\n        }\n\n        if (n._state.openComplete) return Xe(new Y.DatabaseClosed(n._state.dbOpenError));\n\n        if (!n._state.isBeingOpened) {\n          if (!n._state.autoOpen) return Xe(new Y.DatabaseClosed());\n          n.open().catch(G);\n        }\n\n        return n._state.dbReadyPromise.then(function () {\n          return t(n, r, i, o);\n        });\n      }(this.db, e, [this.name], a);\n      return o && (s._consoleTask = o, s = s.catch(function (e) {\n        return console.trace(e), Xe(e);\n      })), s;\n    } finally {\n      u && Ae();\n    }\n  }, ht.prototype.get = function (t, e) {\n    var n = this;\n    return t && t.constructor === Object ? this.where(t).first(e) : null == t ? Xe(new Y.Type(\"Invalid argument to Table.get()\")) : this._trans(\"readonly\", function (e) {\n      return n.core.get({\n        trans: e,\n        key: t\n      }).then(function (e) {\n        return n.hook.reading.fire(e);\n      });\n    }).then(e);\n  }, ht.prototype.where = function (o) {\n    if (\"string\" == typeof o) return new this.db.WhereClause(this, o);\n    if (k(o)) return new this.db.WhereClause(this, \"[\".concat(o.join(\"+\"), \"]\"));\n    var n = x(o);\n    if (1 === n.length) return this.where(n[0]).equals(o[n[0]]);\n    var e = this.schema.indexes.concat(this.schema.primKey).filter(function (t) {\n      if (t.compound && n.every(function (e) {\n        return 0 <= t.keyPath.indexOf(e);\n      })) {\n        for (var e = 0; e < n.length; ++e) if (-1 === n.indexOf(t.keyPath[e])) return !1;\n\n        return !0;\n      }\n\n      return !1;\n    }).sort(function (e, t) {\n      return e.keyPath.length - t.keyPath.length;\n    })[0];\n\n    if (e && this.db._maxKey !== He) {\n      var t = e.keyPath.slice(0, n.length);\n      return this.where(t).equals(t.map(function (e) {\n        return o[e];\n      }));\n    }\n\n    !e && ie && console.warn(\"The query \".concat(JSON.stringify(o), \" on \").concat(this.name, \" would benefit from a \") + \"compound index [\".concat(n.join(\"+\"), \"]\"));\n    var a = this.schema.idxByName;\n\n    function u(e, t) {\n      return 0 === st(e, t);\n    }\n\n    var r = n.reduce(function (e, t) {\n      var n = e[0],\n          r = e[1],\n          e = a[t],\n          i = o[t];\n      return [n || e, n || !e ? it(r, e && e.multi ? function (e) {\n        e = O(e, t);\n        return k(e) && e.some(function (e) {\n          return u(i, e);\n        });\n      } : function (e) {\n        return u(i, O(e, t));\n      }) : r];\n    }, [null, null]),\n        t = r[0],\n        r = r[1];\n    return t ? this.where(t.name).equals(o[t.keyPath]).filter(r) : e ? this.filter(r) : this.where(n).equals(\"\");\n  }, ht.prototype.filter = function (e) {\n    return this.toCollection().and(e);\n  }, ht.prototype.count = function (e) {\n    return this.toCollection().count(e);\n  }, ht.prototype.offset = function (e) {\n    return this.toCollection().offset(e);\n  }, ht.prototype.limit = function (e) {\n    return this.toCollection().limit(e);\n  }, ht.prototype.each = function (e) {\n    return this.toCollection().each(e);\n  }, ht.prototype.toArray = function (e) {\n    return this.toCollection().toArray(e);\n  }, ht.prototype.toCollection = function () {\n    return new this.db.Collection(new this.db.WhereClause(this));\n  }, ht.prototype.orderBy = function (e) {\n    return new this.db.Collection(new this.db.WhereClause(this, k(e) ? \"[\".concat(e.join(\"+\"), \"]\") : e));\n  }, ht.prototype.reverse = function () {\n    return this.toCollection().reverse();\n  }, ht.prototype.mapToClass = function (r) {\n    var e,\n        t = this.db,\n        n = this.name;\n\n    function i() {\n      return null !== e && e.apply(this, arguments) || this;\n    }\n\n    (this.schema.mappedClass = r).prototype instanceof ut && (function (e, t) {\n      if (\"function\" != typeof t && null !== t) throw new TypeError(\"Class extends value \" + String(t) + \" is not a constructor or null\");\n\n      function n() {\n        this.constructor = e;\n      }\n\n      s(e, t), e.prototype = null === t ? Object.create(t) : (n.prototype = t.prototype, new n());\n    }(i, e = r), Object.defineProperty(i.prototype, \"db\", {\n      get: function () {\n        return t;\n      },\n      enumerable: !1,\n      configurable: !0\n    }), i.prototype.table = function () {\n      return n;\n    }, r = i);\n\n    for (var o = new Set(), a = r.prototype; a; a = c(a)) Object.getOwnPropertyNames(a).forEach(function (e) {\n      return o.add(e);\n    });\n\n    function u(e) {\n      if (!e) return e;\n      var t,\n          n = Object.create(r.prototype);\n\n      for (t in e) if (!o.has(t)) try {\n        n[t] = e[t];\n      } catch (e) {}\n\n      return n;\n    }\n\n    return this.schema.readHook && this.hook.reading.unsubscribe(this.schema.readHook), this.schema.readHook = u, this.hook(\"reading\", u), r;\n  }, ht.prototype.defineClass = function () {\n    return this.mapToClass(function (e) {\n      a(this, e);\n    });\n  }, ht.prototype.add = function (t, n) {\n    var r = this,\n        e = this.schema.primKey,\n        i = e.auto,\n        o = e.keyPath,\n        a = t;\n    return o && i && (a = at(o)(t)), this._trans(\"readwrite\", function (e) {\n      return r.core.mutate({\n        trans: e,\n        type: \"add\",\n        keys: null != n ? [n] : null,\n        values: [a]\n      });\n    }).then(function (e) {\n      return e.numFailures ? _e.reject(e.failures[0]) : e.lastResult;\n    }).then(function (e) {\n      if (o) try {\n        P(t, o, e);\n      } catch (e) {}\n      return e;\n    });\n  }, ht.prototype.update = function (e, t) {\n    if (\"object\" != typeof e || k(e)) return this.where(\":id\").equals(e).modify(t);\n    e = O(e, this.schema.primKey.keyPath);\n    return void 0 === e ? Xe(new Y.InvalidArgument(\"Given object does not contain its primary key\")) : this.where(\":id\").equals(e).modify(t);\n  }, ht.prototype.put = function (t, n) {\n    var r = this,\n        e = this.schema.primKey,\n        i = e.auto,\n        o = e.keyPath,\n        a = t;\n    return o && i && (a = at(o)(t)), this._trans(\"readwrite\", function (e) {\n      return r.core.mutate({\n        trans: e,\n        type: \"put\",\n        values: [a],\n        keys: null != n ? [n] : null\n      });\n    }).then(function (e) {\n      return e.numFailures ? _e.reject(e.failures[0]) : e.lastResult;\n    }).then(function (e) {\n      if (o) try {\n        P(t, o, e);\n      } catch (e) {}\n      return e;\n    });\n  }, ht.prototype.delete = function (t) {\n    var n = this;\n    return this._trans(\"readwrite\", function (e) {\n      return n.core.mutate({\n        trans: e,\n        type: \"delete\",\n        keys: [t]\n      });\n    }).then(function (e) {\n      return e.numFailures ? _e.reject(e.failures[0]) : void 0;\n    });\n  }, ht.prototype.clear = function () {\n    var t = this;\n    return this._trans(\"readwrite\", function (e) {\n      return t.core.mutate({\n        trans: e,\n        type: \"deleteRange\",\n        range: ot\n      });\n    }).then(function (e) {\n      return e.numFailures ? _e.reject(e.failures[0]) : void 0;\n    });\n  }, ht.prototype.bulkGet = function (t) {\n    var n = this;\n    return this._trans(\"readonly\", function (e) {\n      return n.core.getMany({\n        keys: t,\n        trans: e\n      }).then(function (e) {\n        return e.map(function (e) {\n          return n.hook.reading.fire(e);\n        });\n      });\n    });\n  }, ht.prototype.bulkAdd = function (r, e, t) {\n    var o = this,\n        a = Array.isArray(e) ? e : void 0,\n        u = (t = t || (a ? void 0 : e)) ? t.allKeys : void 0;\n    return this._trans(\"readwrite\", function (e) {\n      var t = o.schema.primKey,\n          n = t.auto,\n          t = t.keyPath;\n      if (t && a) throw new Y.InvalidArgument(\"bulkAdd(): keys argument invalid on tables with inbound keys\");\n      if (a && a.length !== r.length) throw new Y.InvalidArgument(\"Arguments objects and keys must have the same length\");\n      var i = r.length,\n          t = t && n ? r.map(at(t)) : r;\n      return o.core.mutate({\n        trans: e,\n        type: \"add\",\n        keys: a,\n        values: t,\n        wantResults: u\n      }).then(function (e) {\n        var t = e.numFailures,\n            n = e.results,\n            r = e.lastResult,\n            e = e.failures;\n        if (0 === t) return u ? n : r;\n        throw new V(\"\".concat(o.name, \".bulkAdd(): \").concat(t, \" of \").concat(i, \" operations failed\"), e);\n      });\n    });\n  }, ht.prototype.bulkPut = function (r, e, t) {\n    var o = this,\n        a = Array.isArray(e) ? e : void 0,\n        u = (t = t || (a ? void 0 : e)) ? t.allKeys : void 0;\n    return this._trans(\"readwrite\", function (e) {\n      var t = o.schema.primKey,\n          n = t.auto,\n          t = t.keyPath;\n      if (t && a) throw new Y.InvalidArgument(\"bulkPut(): keys argument invalid on tables with inbound keys\");\n      if (a && a.length !== r.length) throw new Y.InvalidArgument(\"Arguments objects and keys must have the same length\");\n      var i = r.length,\n          t = t && n ? r.map(at(t)) : r;\n      return o.core.mutate({\n        trans: e,\n        type: \"put\",\n        keys: a,\n        values: t,\n        wantResults: u\n      }).then(function (e) {\n        var t = e.numFailures,\n            n = e.results,\n            r = e.lastResult,\n            e = e.failures;\n        if (0 === t) return u ? n : r;\n        throw new V(\"\".concat(o.name, \".bulkPut(): \").concat(t, \" of \").concat(i, \" operations failed\"), e);\n      });\n    });\n  }, ht.prototype.bulkUpdate = function (t) {\n    var h = this,\n        n = this.core,\n        r = t.map(function (e) {\n      return e.key;\n    }),\n        i = t.map(function (e) {\n      return e.changes;\n    }),\n        d = [];\n    return this._trans(\"readwrite\", function (e) {\n      return n.getMany({\n        trans: e,\n        keys: r,\n        cache: \"clone\"\n      }).then(function (c) {\n        var l = [],\n            f = [];\n        t.forEach(function (e, t) {\n          var n = e.key,\n              r = e.changes,\n              i = c[t];\n\n          if (i) {\n            for (var o = 0, a = Object.keys(r); o < a.length; o++) {\n              var u = a[o],\n                  s = r[u];\n\n              if (u === h.schema.primKey.keyPath) {\n                if (0 !== st(s, n)) throw new Y.Constraint(\"Cannot update primary key in bulkUpdate()\");\n              } else P(i, u, s);\n            }\n\n            d.push(t), l.push(n), f.push(i);\n          }\n        });\n        var s = l.length;\n        return n.mutate({\n          trans: e,\n          type: \"put\",\n          keys: l,\n          values: f,\n          updates: {\n            keys: r,\n            changeSpecs: i\n          }\n        }).then(function (e) {\n          var t = e.numFailures,\n              n = e.failures;\n          if (0 === t) return s;\n\n          for (var r = 0, i = Object.keys(n); r < i.length; r++) {\n            var o,\n                a = i[r],\n                u = d[Number(a)];\n            null != u && (o = n[a], delete n[a], n[u] = o);\n          }\n\n          throw new V(\"\".concat(h.name, \".bulkUpdate(): \").concat(t, \" of \").concat(s, \" operations failed\"), n);\n        });\n      });\n    });\n  }, ht.prototype.bulkDelete = function (t) {\n    var r = this,\n        i = t.length;\n    return this._trans(\"readwrite\", function (e) {\n      return r.core.mutate({\n        trans: e,\n        type: \"delete\",\n        keys: t\n      });\n    }).then(function (e) {\n      var t = e.numFailures,\n          n = e.lastResult,\n          e = e.failures;\n      if (0 === t) return n;\n      throw new V(\"\".concat(r.name, \".bulkDelete(): \").concat(t, \" of \").concat(i, \" operations failed\"), e);\n    });\n  }, ht);\n\n  function ht() {}\n\n  function dt(i) {\n    function t(e, t) {\n      if (t) {\n        for (var n = arguments.length, r = new Array(n - 1); --n;) r[n - 1] = arguments[n];\n\n        return a[e].subscribe.apply(null, r), i;\n      }\n\n      if (\"string\" == typeof e) return a[e];\n    }\n\n    var a = {};\n    t.addEventType = u;\n\n    for (var e = 1, n = arguments.length; e < n; ++e) u(arguments[e]);\n\n    return t;\n\n    function u(e, n, r) {\n      if (\"object\" != typeof e) {\n        var i;\n        n = n || ne;\n        var o = {\n          subscribers: [],\n          fire: r = r || G,\n          subscribe: function (e) {\n            -1 === o.subscribers.indexOf(e) && (o.subscribers.push(e), o.fire = n(o.fire, e));\n          },\n          unsubscribe: function (t) {\n            o.subscribers = o.subscribers.filter(function (e) {\n              return e !== t;\n            }), o.fire = o.subscribers.reduce(n, r);\n          }\n        };\n        return a[e] = t[e] = o;\n      }\n\n      x(i = e).forEach(function (e) {\n        var t = i[e];\n        if (k(t)) u(e, i[e][0], i[e][1]);else {\n          if (\"asap\" !== t) throw new Y.InvalidArgument(\"Invalid event config\");\n          var n = u(e, X, function () {\n            for (var e = arguments.length, t = new Array(e); e--;) t[e] = arguments[e];\n\n            n.subscribers.forEach(function (e) {\n              v(function () {\n                e.apply(null, t);\n              });\n            });\n          });\n        }\n      });\n    }\n  }\n\n  function pt(e, t) {\n    return o(t).from({\n      prototype: e\n    }), t;\n  }\n\n  function yt(e, t) {\n    return !(e.filter || e.algorithm || e.or) && (t ? e.justLimit : !e.replayFilter);\n  }\n\n  function vt(e, t) {\n    e.filter = it(e.filter, t);\n  }\n\n  function mt(e, t, n) {\n    var r = e.replayFilter;\n    e.replayFilter = r ? function () {\n      return it(r(), t());\n    } : t, e.justLimit = n && !r;\n  }\n\n  function bt(e, t) {\n    if (e.isPrimKey) return t.primaryKey;\n    var n = t.getIndexByKeyPath(e.index);\n    if (!n) throw new Y.Schema(\"KeyPath \" + e.index + \" on object store \" + t.name + \" is not indexed\");\n    return n;\n  }\n\n  function gt(e, t, n) {\n    var r = bt(e, t.schema);\n    return t.openCursor({\n      trans: n,\n      values: !e.keysOnly,\n      reverse: \"prev\" === e.dir,\n      unique: !!e.unique,\n      query: {\n        index: r,\n        range: e.range\n      }\n    });\n  }\n\n  function wt(e, o, t, n) {\n    var a = e.replayFilter ? it(e.filter, e.replayFilter()) : e.filter;\n\n    if (e.or) {\n      var u = {},\n          r = function (e, t, n) {\n        var r, i;\n        a && !a(t, n, function (e) {\n          return t.stop(e);\n        }, function (e) {\n          return t.fail(e);\n        }) || (\"[object ArrayBuffer]\" === (i = \"\" + (r = t.primaryKey)) && (i = \"\" + new Uint8Array(r)), m(u, i) || (u[i] = !0, o(e, t, n)));\n      };\n\n      return Promise.all([e.or._iterate(r, t), _t(gt(e, n, t), e.algorithm, r, !e.keysOnly && e.valueMapper)]);\n    }\n\n    return _t(gt(e, n, t), it(e.algorithm, a), o, !e.keysOnly && e.valueMapper);\n  }\n\n  function _t(e, r, i, o) {\n    var a = qe(o ? function (e, t, n) {\n      return i(o(e), t, n);\n    } : i);\n    return e.then(function (n) {\n      if (n) return n.start(function () {\n        var t = function () {\n          return n.continue();\n        };\n\n        r && !r(n, function (e) {\n          return t = e;\n        }, function (e) {\n          n.stop(e), t = G;\n        }, function (e) {\n          n.fail(e), t = G;\n        }) || a(n.value, n, function (e) {\n          return t = e;\n        }), t();\n      });\n    });\n  }\n\n  var e = Symbol(),\n      xt = (kt.prototype.execute = function (e) {\n    if (void 0 !== this.add) {\n      var t = this.add;\n      if (k(t)) return i(i([], k(e) ? e : [], !0), t, !0).sort();\n      if (\"number\" == typeof t) return (Number(e) || 0) + t;\n      if (\"bigint\" == typeof t) try {\n        return BigInt(e) + t;\n      } catch (e) {\n        return BigInt(0) + t;\n      }\n      throw new TypeError(\"Invalid term \".concat(t));\n    }\n\n    if (void 0 !== this.remove) {\n      var n = this.remove;\n      if (k(n)) return k(e) ? e.filter(function (e) {\n        return !n.includes(e);\n      }).sort() : [];\n      if (\"number\" == typeof n) return Number(e) - n;\n      if (\"bigint\" == typeof n) try {\n        return BigInt(e) - n;\n      } catch (e) {\n        return BigInt(0) - n;\n      }\n      throw new TypeError(\"Invalid subtrahend \".concat(n));\n    }\n\n    t = null === (t = this.replacePrefix) || void 0 === t ? void 0 : t[0];\n    return t && \"string\" == typeof e && e.startsWith(t) ? this.replacePrefix[1] + e.substring(t.length) : e;\n  }, kt);\n\n  function kt(e) {\n    Object.assign(this, e);\n  }\n\n  var Ot = (Pt.prototype._read = function (e, t) {\n    var n = this._ctx;\n    return n.error ? n.table._trans(null, Xe.bind(null, n.error)) : n.table._trans(\"readonly\", e).then(t);\n  }, Pt.prototype._write = function (e) {\n    var t = this._ctx;\n    return t.error ? t.table._trans(null, Xe.bind(null, t.error)) : t.table._trans(\"readwrite\", e, \"locked\");\n  }, Pt.prototype._addAlgorithm = function (e) {\n    var t = this._ctx;\n    t.algorithm = it(t.algorithm, e);\n  }, Pt.prototype._iterate = function (e, t) {\n    return wt(this._ctx, e, t, this._ctx.table.core);\n  }, Pt.prototype.clone = function (e) {\n    var t = Object.create(this.constructor.prototype),\n        n = Object.create(this._ctx);\n    return e && a(n, e), t._ctx = n, t;\n  }, Pt.prototype.raw = function () {\n    return this._ctx.valueMapper = null, this;\n  }, Pt.prototype.each = function (t) {\n    var n = this._ctx;\n    return this._read(function (e) {\n      return wt(n, t, e, n.table.core);\n    });\n  }, Pt.prototype.count = function (e) {\n    var i = this;\n    return this._read(function (e) {\n      var t = i._ctx,\n          n = t.table.core;\n      if (yt(t, !0)) return n.count({\n        trans: e,\n        query: {\n          index: bt(t, n.schema),\n          range: t.range\n        }\n      }).then(function (e) {\n        return Math.min(e, t.limit);\n      });\n      var r = 0;\n      return wt(t, function () {\n        return ++r, !1;\n      }, e, n).then(function () {\n        return r;\n      });\n    }).then(e);\n  }, Pt.prototype.sortBy = function (e, t) {\n    var n = e.split(\".\").reverse(),\n        r = n[0],\n        i = n.length - 1;\n\n    function o(e, t) {\n      return t ? o(e[n[t]], t - 1) : e[r];\n    }\n\n    var a = \"next\" === this._ctx.dir ? 1 : -1;\n\n    function u(e, t) {\n      return st(o(e, i), o(t, i)) * a;\n    }\n\n    return this.toArray(function (e) {\n      return e.sort(u);\n    }).then(t);\n  }, Pt.prototype.toArray = function (e) {\n    var o = this;\n    return this._read(function (e) {\n      var t = o._ctx;\n\n      if (\"next\" === t.dir && yt(t, !0) && 0 < t.limit) {\n        var n = t.valueMapper,\n            r = bt(t, t.table.core.schema);\n        return t.table.core.query({\n          trans: e,\n          limit: t.limit,\n          values: !0,\n          query: {\n            index: r,\n            range: t.range\n          }\n        }).then(function (e) {\n          e = e.result;\n          return n ? e.map(n) : e;\n        });\n      }\n\n      var i = [];\n      return wt(t, function (e) {\n        return i.push(e);\n      }, e, t.table.core).then(function () {\n        return i;\n      });\n    }, e);\n  }, Pt.prototype.offset = function (t) {\n    var e = this._ctx;\n    return t <= 0 || (e.offset += t, yt(e) ? mt(e, function () {\n      var n = t;\n      return function (e, t) {\n        return 0 === n || (1 === n ? --n : t(function () {\n          e.advance(n), n = 0;\n        }), !1);\n      };\n    }) : mt(e, function () {\n      var e = t;\n      return function () {\n        return --e < 0;\n      };\n    })), this;\n  }, Pt.prototype.limit = function (e) {\n    return this._ctx.limit = Math.min(this._ctx.limit, e), mt(this._ctx, function () {\n      var r = e;\n      return function (e, t, n) {\n        return --r <= 0 && t(n), 0 <= r;\n      };\n    }, !0), this;\n  }, Pt.prototype.until = function (r, i) {\n    return vt(this._ctx, function (e, t, n) {\n      return !r(e.value) || (t(n), i);\n    }), this;\n  }, Pt.prototype.first = function (e) {\n    return this.limit(1).toArray(function (e) {\n      return e[0];\n    }).then(e);\n  }, Pt.prototype.last = function (e) {\n    return this.reverse().first(e);\n  }, Pt.prototype.filter = function (t) {\n    var e;\n    return vt(this._ctx, function (e) {\n      return t(e.value);\n    }), (e = this._ctx).isMatch = it(e.isMatch, t), this;\n  }, Pt.prototype.and = function (e) {\n    return this.filter(e);\n  }, Pt.prototype.or = function (e) {\n    return new this.db.WhereClause(this._ctx.table, e, this);\n  }, Pt.prototype.reverse = function () {\n    return this._ctx.dir = \"prev\" === this._ctx.dir ? \"next\" : \"prev\", this._ondirectionchange && this._ondirectionchange(this._ctx.dir), this;\n  }, Pt.prototype.desc = function () {\n    return this.reverse();\n  }, Pt.prototype.eachKey = function (n) {\n    var e = this._ctx;\n    return e.keysOnly = !e.isMatch, this.each(function (e, t) {\n      n(t.key, t);\n    });\n  }, Pt.prototype.eachUniqueKey = function (e) {\n    return this._ctx.unique = \"unique\", this.eachKey(e);\n  }, Pt.prototype.eachPrimaryKey = function (n) {\n    var e = this._ctx;\n    return e.keysOnly = !e.isMatch, this.each(function (e, t) {\n      n(t.primaryKey, t);\n    });\n  }, Pt.prototype.keys = function (e) {\n    var t = this._ctx;\n    t.keysOnly = !t.isMatch;\n    var n = [];\n    return this.each(function (e, t) {\n      n.push(t.key);\n    }).then(function () {\n      return n;\n    }).then(e);\n  }, Pt.prototype.primaryKeys = function (e) {\n    var n = this._ctx;\n    if (\"next\" === n.dir && yt(n, !0) && 0 < n.limit) return this._read(function (e) {\n      var t = bt(n, n.table.core.schema);\n      return n.table.core.query({\n        trans: e,\n        values: !1,\n        limit: n.limit,\n        query: {\n          index: t,\n          range: n.range\n        }\n      });\n    }).then(function (e) {\n      return e.result;\n    }).then(e);\n    n.keysOnly = !n.isMatch;\n    var r = [];\n    return this.each(function (e, t) {\n      r.push(t.primaryKey);\n    }).then(function () {\n      return r;\n    }).then(e);\n  }, Pt.prototype.uniqueKeys = function (e) {\n    return this._ctx.unique = \"unique\", this.keys(e);\n  }, Pt.prototype.firstKey = function (e) {\n    return this.limit(1).keys(function (e) {\n      return e[0];\n    }).then(e);\n  }, Pt.prototype.lastKey = function (e) {\n    return this.reverse().firstKey(e);\n  }, Pt.prototype.distinct = function () {\n    var e = this._ctx,\n        e = e.index && e.table.schema.idxByName[e.index];\n    if (!e || !e.multi) return this;\n    var n = {};\n    return vt(this._ctx, function (e) {\n      var t = e.primaryKey.toString(),\n          e = m(n, t);\n      return n[t] = !0, !e;\n    }), this;\n  }, Pt.prototype.modify = function (w) {\n    var n = this,\n        r = this._ctx;\n    return this._write(function (d) {\n      var a, u, p;\n      p = \"function\" == typeof w ? w : (a = x(w), u = a.length, function (e) {\n        for (var t = !1, n = 0; n < u; ++n) {\n          var r = a[n],\n              i = w[r],\n              o = O(e, r);\n          i instanceof xt ? (P(e, r, i.execute(o)), t = !0) : o !== i && (P(e, r, i), t = !0);\n        }\n\n        return t;\n      });\n      var y = r.table.core,\n          e = y.schema.primaryKey,\n          v = e.outbound,\n          m = e.extractKey,\n          b = 200,\n          e = n.db._options.modifyChunkSize;\n      e && (b = \"object\" == typeof e ? e[y.name] || e[\"*\"] || 200 : e);\n\n      function g(e, t) {\n        var n = t.failures,\n            t = t.numFailures;\n        c += e - t;\n\n        for (var r = 0, i = x(n); r < i.length; r++) {\n          var o = i[r];\n          s.push(n[o]);\n        }\n      }\n\n      var s = [],\n          c = 0,\n          t = [];\n      return n.clone().primaryKeys().then(function (l) {\n        function f(s) {\n          var c = Math.min(b, l.length - s);\n          return y.getMany({\n            trans: d,\n            keys: l.slice(s, s + c),\n            cache: \"immutable\"\n          }).then(function (e) {\n            for (var n = [], t = [], r = v ? [] : null, i = [], o = 0; o < c; ++o) {\n              var a = e[o],\n                  u = {\n                value: S(a),\n                primKey: l[s + o]\n              };\n              !1 !== p.call(u, u.value, u) && (null == u.value ? i.push(l[s + o]) : v || 0 === st(m(a), m(u.value)) ? (t.push(u.value), v && r.push(l[s + o])) : (i.push(l[s + o]), n.push(u.value)));\n            }\n\n            return Promise.resolve(0 < n.length && y.mutate({\n              trans: d,\n              type: \"add\",\n              values: n\n            }).then(function (e) {\n              for (var t in e.failures) i.splice(parseInt(t), 1);\n\n              g(n.length, e);\n            })).then(function () {\n              return (0 < t.length || h && \"object\" == typeof w) && y.mutate({\n                trans: d,\n                type: \"put\",\n                keys: r,\n                values: t,\n                criteria: h,\n                changeSpec: \"function\" != typeof w && w,\n                isAdditionalChunk: 0 < s\n              }).then(function (e) {\n                return g(t.length, e);\n              });\n            }).then(function () {\n              return (0 < i.length || h && w === Kt) && y.mutate({\n                trans: d,\n                type: \"delete\",\n                keys: i,\n                criteria: h,\n                isAdditionalChunk: 0 < s\n              }).then(function (e) {\n                return g(i.length, e);\n              });\n            }).then(function () {\n              return l.length > s + c && f(s + b);\n            });\n          });\n        }\n\n        var h = yt(r) && r.limit === 1 / 0 && (\"function\" != typeof w || w === Kt) && {\n          index: r.index,\n          range: r.range\n        };\n        return f(0).then(function () {\n          if (0 < s.length) throw new U(\"Error modifying one or more objects\", s, c, t);\n          return l.length;\n        });\n      });\n    });\n  }, Pt.prototype.delete = function () {\n    var i = this._ctx,\n        n = i.range;\n    return yt(i) && (i.isPrimKey || 3 === n.type) ? this._write(function (e) {\n      var t = i.table.core.schema.primaryKey,\n          r = n;\n      return i.table.core.count({\n        trans: e,\n        query: {\n          index: t,\n          range: r\n        }\n      }).then(function (n) {\n        return i.table.core.mutate({\n          trans: e,\n          type: \"deleteRange\",\n          range: r\n        }).then(function (e) {\n          var t = e.failures;\n          e.lastResult, e.results;\n          e = e.numFailures;\n          if (e) throw new U(\"Could not delete some values\", Object.keys(t).map(function (e) {\n            return t[e];\n          }), n - e);\n          return n - e;\n        });\n      });\n    }) : this.modify(Kt);\n  }, Pt);\n\n  function Pt() {}\n\n  var Kt = function (e, t) {\n    return t.value = null;\n  };\n\n  function Et(e, t) {\n    return e < t ? -1 : e === t ? 0 : 1;\n  }\n\n  function St(e, t) {\n    return t < e ? -1 : e === t ? 0 : 1;\n  }\n\n  function jt(e, t, n) {\n    e = e instanceof Dt ? new e.Collection(e) : e;\n    return e._ctx.error = new (n || TypeError)(t), e;\n  }\n\n  function At(e) {\n    return new e.Collection(e, function () {\n      return qt(\"\");\n    }).limit(0);\n  }\n\n  function Ct(e, s, n, r) {\n    var i,\n        c,\n        l,\n        f,\n        h,\n        d,\n        p,\n        y = n.length;\n    if (!n.every(function (e) {\n      return \"string\" == typeof e;\n    })) return jt(e, Ze);\n\n    function t(e) {\n      i = \"next\" === e ? function (e) {\n        return e.toUpperCase();\n      } : function (e) {\n        return e.toLowerCase();\n      }, c = \"next\" === e ? function (e) {\n        return e.toLowerCase();\n      } : function (e) {\n        return e.toUpperCase();\n      }, l = \"next\" === e ? Et : St;\n      var t = n.map(function (e) {\n        return {\n          lower: c(e),\n          upper: i(e)\n        };\n      }).sort(function (e, t) {\n        return l(e.lower, t.lower);\n      });\n      f = t.map(function (e) {\n        return e.upper;\n      }), h = t.map(function (e) {\n        return e.lower;\n      }), p = \"next\" === (d = e) ? \"\" : r;\n    }\n\n    t(\"next\");\n    e = new e.Collection(e, function () {\n      return Tt(f[0], h[y - 1] + r);\n    });\n\n    e._ondirectionchange = function (e) {\n      t(e);\n    };\n\n    var v = 0;\n    return e._addAlgorithm(function (e, t, n) {\n      var r = e.key;\n      if (\"string\" != typeof r) return !1;\n      var i = c(r);\n      if (s(i, h, v)) return !0;\n\n      for (var o = null, a = v; a < y; ++a) {\n        var u = function (e, t, n, r, i, o) {\n          for (var a = Math.min(e.length, r.length), u = -1, s = 0; s < a; ++s) {\n            var c = t[s];\n            if (c !== r[s]) return i(e[s], n[s]) < 0 ? e.substr(0, s) + n[s] + n.substr(s + 1) : i(e[s], r[s]) < 0 ? e.substr(0, s) + r[s] + n.substr(s + 1) : 0 <= u ? e.substr(0, u) + t[u] + n.substr(u + 1) : null;\n            i(e[s], c) < 0 && (u = s);\n          }\n\n          return a < r.length && \"next\" === o ? e + n.substr(e.length) : a < e.length && \"prev\" === o ? e.substr(0, n.length) : u < 0 ? null : e.substr(0, u) + r[u] + n.substr(u + 1);\n        }(r, i, f[a], h[a], l, d);\n\n        null === u && null === o ? v = a + 1 : (null === o || 0 < l(o, u)) && (o = u);\n      }\n\n      return t(null !== o ? function () {\n        e.continue(o + p);\n      } : n), !1;\n    }), e;\n  }\n\n  function Tt(e, t, n, r) {\n    return {\n      type: 2,\n      lower: e,\n      upper: t,\n      lowerOpen: n,\n      upperOpen: r\n    };\n  }\n\n  function qt(e) {\n    return {\n      type: 1,\n      lower: e,\n      upper: e\n    };\n  }\n\n  var Dt = (Object.defineProperty(It.prototype, \"Collection\", {\n    get: function () {\n      return this._ctx.table.db.Collection;\n    },\n    enumerable: !1,\n    configurable: !0\n  }), It.prototype.between = function (e, t, n, r) {\n    n = !1 !== n, r = !0 === r;\n\n    try {\n      return 0 < this._cmp(e, t) || 0 === this._cmp(e, t) && (n || r) && (!n || !r) ? At(this) : new this.Collection(this, function () {\n        return Tt(e, t, !n, !r);\n      });\n    } catch (e) {\n      return jt(this, Je);\n    }\n  }, It.prototype.equals = function (e) {\n    return null == e ? jt(this, Je) : new this.Collection(this, function () {\n      return qt(e);\n    });\n  }, It.prototype.above = function (e) {\n    return null == e ? jt(this, Je) : new this.Collection(this, function () {\n      return Tt(e, void 0, !0);\n    });\n  }, It.prototype.aboveOrEqual = function (e) {\n    return null == e ? jt(this, Je) : new this.Collection(this, function () {\n      return Tt(e, void 0, !1);\n    });\n  }, It.prototype.below = function (e) {\n    return null == e ? jt(this, Je) : new this.Collection(this, function () {\n      return Tt(void 0, e, !1, !0);\n    });\n  }, It.prototype.belowOrEqual = function (e) {\n    return null == e ? jt(this, Je) : new this.Collection(this, function () {\n      return Tt(void 0, e);\n    });\n  }, It.prototype.startsWith = function (e) {\n    return \"string\" != typeof e ? jt(this, Ze) : this.between(e, e + He, !0, !0);\n  }, It.prototype.startsWithIgnoreCase = function (e) {\n    return \"\" === e ? this.startsWith(e) : Ct(this, function (e, t) {\n      return 0 === e.indexOf(t[0]);\n    }, [e], He);\n  }, It.prototype.equalsIgnoreCase = function (e) {\n    return Ct(this, function (e, t) {\n      return e === t[0];\n    }, [e], \"\");\n  }, It.prototype.anyOfIgnoreCase = function () {\n    var e = I.apply(D, arguments);\n    return 0 === e.length ? At(this) : Ct(this, function (e, t) {\n      return -1 !== t.indexOf(e);\n    }, e, \"\");\n  }, It.prototype.startsWithAnyOfIgnoreCase = function () {\n    var e = I.apply(D, arguments);\n    return 0 === e.length ? At(this) : Ct(this, function (t, e) {\n      return e.some(function (e) {\n        return 0 === t.indexOf(e);\n      });\n    }, e, He);\n  }, It.prototype.anyOf = function () {\n    var t = this,\n        i = I.apply(D, arguments),\n        o = this._cmp;\n\n    try {\n      i.sort(o);\n    } catch (e) {\n      return jt(this, Je);\n    }\n\n    if (0 === i.length) return At(this);\n    var e = new this.Collection(this, function () {\n      return Tt(i[0], i[i.length - 1]);\n    });\n\n    e._ondirectionchange = function (e) {\n      o = \"next\" === e ? t._ascending : t._descending, i.sort(o);\n    };\n\n    var a = 0;\n    return e._addAlgorithm(function (e, t, n) {\n      for (var r = e.key; 0 < o(r, i[a]);) if (++a === i.length) return t(n), !1;\n\n      return 0 === o(r, i[a]) || (t(function () {\n        e.continue(i[a]);\n      }), !1);\n    }), e;\n  }, It.prototype.notEqual = function (e) {\n    return this.inAnyRange([[-1 / 0, e], [e, this.db._maxKey]], {\n      includeLowers: !1,\n      includeUppers: !1\n    });\n  }, It.prototype.noneOf = function () {\n    var e = I.apply(D, arguments);\n    if (0 === e.length) return new this.Collection(this);\n\n    try {\n      e.sort(this._ascending);\n    } catch (e) {\n      return jt(this, Je);\n    }\n\n    var t = e.reduce(function (e, t) {\n      return e ? e.concat([[e[e.length - 1][1], t]]) : [[-1 / 0, t]];\n    }, null);\n    return t.push([e[e.length - 1], this.db._maxKey]), this.inAnyRange(t, {\n      includeLowers: !1,\n      includeUppers: !1\n    });\n  }, It.prototype.inAnyRange = function (e, t) {\n    var o = this,\n        a = this._cmp,\n        u = this._ascending,\n        n = this._descending,\n        s = this._min,\n        c = this._max;\n    if (0 === e.length) return At(this);\n    if (!e.every(function (e) {\n      return void 0 !== e[0] && void 0 !== e[1] && u(e[0], e[1]) <= 0;\n    })) return jt(this, \"First argument to inAnyRange() must be an Array of two-value Arrays [lower,upper] where upper must not be lower than lower\", Y.InvalidArgument);\n    var r = !t || !1 !== t.includeLowers,\n        i = t && !0 === t.includeUppers;\n    var l,\n        f = u;\n\n    function h(e, t) {\n      return f(e[0], t[0]);\n    }\n\n    try {\n      (l = e.reduce(function (e, t) {\n        for (var n = 0, r = e.length; n < r; ++n) {\n          var i = e[n];\n\n          if (a(t[0], i[1]) < 0 && 0 < a(t[1], i[0])) {\n            i[0] = s(i[0], t[0]), i[1] = c(i[1], t[1]);\n            break;\n          }\n        }\n\n        return n === r && e.push(t), e;\n      }, [])).sort(h);\n    } catch (e) {\n      return jt(this, Je);\n    }\n\n    var d = 0,\n        p = i ? function (e) {\n      return 0 < u(e, l[d][1]);\n    } : function (e) {\n      return 0 <= u(e, l[d][1]);\n    },\n        y = r ? function (e) {\n      return 0 < n(e, l[d][0]);\n    } : function (e) {\n      return 0 <= n(e, l[d][0]);\n    };\n    var v = p,\n        e = new this.Collection(this, function () {\n      return Tt(l[0][0], l[l.length - 1][1], !r, !i);\n    });\n    return e._ondirectionchange = function (e) {\n      f = \"next\" === e ? (v = p, u) : (v = y, n), l.sort(h);\n    }, e._addAlgorithm(function (e, t, n) {\n      for (var r, i = e.key; v(i);) if (++d === l.length) return t(n), !1;\n\n      return !p(r = i) && !y(r) || (0 === o._cmp(i, l[d][1]) || 0 === o._cmp(i, l[d][0]) || t(function () {\n        f === u ? e.continue(l[d][0]) : e.continue(l[d][1]);\n      }), !1);\n    }), e;\n  }, It.prototype.startsWithAnyOf = function () {\n    var e = I.apply(D, arguments);\n    return e.every(function (e) {\n      return \"string\" == typeof e;\n    }) ? 0 === e.length ? At(this) : this.inAnyRange(e.map(function (e) {\n      return [e, e + He];\n    })) : jt(this, \"startsWithAnyOf() only works with strings\");\n  }, It);\n\n  function It() {}\n\n  function Bt(t) {\n    return qe(function (e) {\n      return Rt(e), t(e.target.error), !1;\n    });\n  }\n\n  function Rt(e) {\n    e.stopPropagation && e.stopPropagation(), e.preventDefault && e.preventDefault();\n  }\n\n  var Mt = \"storagemutated\",\n      Ft = \"x-storagemutated-1\",\n      Nt = dt(null, Mt),\n      Lt = (Ut.prototype._lock = function () {\n    return y(!me.global), ++this._reculock, 1 !== this._reculock || me.global || (me.lockOwnerFor = this), this;\n  }, Ut.prototype._unlock = function () {\n    if (y(!me.global), 0 == --this._reculock) for (me.global || (me.lockOwnerFor = null); 0 < this._blockedFuncs.length && !this._locked();) {\n      var e = this._blockedFuncs.shift();\n\n      try {\n        $e(e[1], e[0]);\n      } catch (e) {}\n    }\n    return this;\n  }, Ut.prototype._locked = function () {\n    return this._reculock && me.lockOwnerFor !== this;\n  }, Ut.prototype.create = function (t) {\n    var n = this;\n    if (!this.mode) return this;\n    var e = this.db.idbdb,\n        r = this.db._state.dbOpenError;\n    if (y(!this.idbtrans), !t && !e) switch (r && r.name) {\n      case \"DatabaseClosedError\":\n        throw new Y.DatabaseClosed(r);\n\n      case \"MissingAPIError\":\n        throw new Y.MissingAPI(r.message, r);\n\n      default:\n        throw new Y.OpenFailed(r);\n    }\n    if (!this.active) throw new Y.TransactionInactive();\n    return y(null === this._completion._state), (t = this.idbtrans = t || (this.db.core || e).transaction(this.storeNames, this.mode, {\n      durability: this.chromeTransactionDurability\n    })).onerror = qe(function (e) {\n      Rt(e), n._reject(t.error);\n    }), t.onabort = qe(function (e) {\n      Rt(e), n.active && n._reject(new Y.Abort(t.error)), n.active = !1, n.on(\"abort\").fire(e);\n    }), t.oncomplete = qe(function () {\n      n.active = !1, n._resolve(), \"mutatedParts\" in t && Nt.storagemutated.fire(t.mutatedParts);\n    }), this;\n  }, Ut.prototype._promise = function (n, r, i) {\n    var o = this;\n    if (\"readwrite\" === n && \"readwrite\" !== this.mode) return Xe(new Y.ReadOnly(\"Transaction is readonly\"));\n    if (!this.active) return Xe(new Y.TransactionInactive());\n    if (this._locked()) return new _e(function (e, t) {\n      o._blockedFuncs.push([function () {\n        o._promise(n, r, i).then(e, t);\n      }, me]);\n    });\n    if (i) return Ne(function () {\n      var e = new _e(function (e, t) {\n        o._lock();\n\n        var n = r(e, t, o);\n        n && n.then && n.then(e, t);\n      });\n      return e.finally(function () {\n        return o._unlock();\n      }), e._lib = !0, e;\n    });\n    var e = new _e(function (e, t) {\n      var n = r(e, t, o);\n      n && n.then && n.then(e, t);\n    });\n    return e._lib = !0, e;\n  }, Ut.prototype._root = function () {\n    return this.parent ? this.parent._root() : this;\n  }, Ut.prototype.waitFor = function (e) {\n    var t,\n        r = this._root(),\n        i = _e.resolve(e);\n\n    r._waitingFor ? r._waitingFor = r._waitingFor.then(function () {\n      return i;\n    }) : (r._waitingFor = i, r._waitingQueue = [], t = r.idbtrans.objectStore(r.storeNames[0]), function e() {\n      for (++r._spinCount; r._waitingQueue.length;) r._waitingQueue.shift()();\n\n      r._waitingFor && (t.get(-1 / 0).onsuccess = e);\n    }());\n    var o = r._waitingFor;\n    return new _e(function (t, n) {\n      i.then(function (e) {\n        return r._waitingQueue.push(qe(t.bind(null, e)));\n      }, function (e) {\n        return r._waitingQueue.push(qe(n.bind(null, e)));\n      }).finally(function () {\n        r._waitingFor === o && (r._waitingFor = null);\n      });\n    });\n  }, Ut.prototype.abort = function () {\n    this.active && (this.active = !1, this.idbtrans && this.idbtrans.abort(), this._reject(new Y.Abort()));\n  }, Ut.prototype.table = function (e) {\n    var t = this._memoizedTables || (this._memoizedTables = {});\n    if (m(t, e)) return t[e];\n    var n = this.schema[e];\n    if (!n) throw new Y.NotFound(\"Table \" + e + \" not part of transaction\");\n    n = new this.db.Table(e, n, this);\n    return n.core = this.db.core.table(e), t[e] = n;\n  }, Ut);\n\n  function Ut() {}\n\n  function Vt(e, t, n, r, i, o, a) {\n    return {\n      name: e,\n      keyPath: t,\n      unique: n,\n      multi: r,\n      auto: i,\n      compound: o,\n      src: (n && !a ? \"&\" : \"\") + (r ? \"*\" : \"\") + (i ? \"++\" : \"\") + zt(t)\n    };\n  }\n\n  function zt(e) {\n    return \"string\" == typeof e ? e : e ? \"[\" + [].join.call(e, \"+\") + \"]\" : \"\";\n  }\n\n  function Wt(e, t, n) {\n    return {\n      name: e,\n      primKey: t,\n      indexes: n,\n      mappedClass: null,\n      idxByName: (r = function (e) {\n        return [e.name, e];\n      }, n.reduce(function (e, t, n) {\n        n = r(t, n);\n        return n && (e[n[0]] = n[1]), e;\n      }, {}))\n    };\n    var r;\n  }\n\n  var Yt = function (e) {\n    try {\n      return e.only([[]]), Yt = function () {\n        return [[]];\n      }, [[]];\n    } catch (e) {\n      return Yt = function () {\n        return He;\n      }, He;\n    }\n  };\n\n  function $t(t) {\n    return null == t ? function () {} : \"string\" == typeof t ? 1 === (n = t).split(\".\").length ? function (e) {\n      return e[n];\n    } : function (e) {\n      return O(e, n);\n    } : function (e) {\n      return O(e, t);\n    };\n    var n;\n  }\n\n  function Qt(e) {\n    return [].slice.call(e);\n  }\n\n  var Gt = 0;\n\n  function Xt(e) {\n    return null == e ? \":id\" : \"string\" == typeof e ? e : \"[\".concat(e.join(\"+\"), \"]\");\n  }\n\n  function Ht(e, i, t) {\n    function _(e) {\n      if (3 === e.type) return null;\n      if (4 === e.type) throw new Error(\"Cannot convert never type to IDBKeyRange\");\n      var t = e.lower,\n          n = e.upper,\n          r = e.lowerOpen,\n          e = e.upperOpen;\n      return void 0 === t ? void 0 === n ? null : i.upperBound(n, !!e) : void 0 === n ? i.lowerBound(t, !!r) : i.bound(t, n, !!r, !!e);\n    }\n\n    function n(e) {\n      var h,\n          w = e.name;\n      return {\n        name: w,\n        schema: e,\n        mutate: function (e) {\n          var y = e.trans,\n              v = e.type,\n              m = e.keys,\n              b = e.values,\n              g = e.range;\n          return new Promise(function (t, e) {\n            t = qe(t);\n            var n = y.objectStore(w),\n                r = null == n.keyPath,\n                i = \"put\" === v || \"add\" === v;\n            if (!i && \"delete\" !== v && \"deleteRange\" !== v) throw new Error(\"Invalid operation type: \" + v);\n            var o,\n                a = (m || b || {\n              length: 1\n            }).length;\n            if (m && b && m.length !== b.length) throw new Error(\"Given keys array must have same length as given values array.\");\n            if (0 === a) return t({\n              numFailures: 0,\n              failures: {},\n              results: [],\n              lastResult: void 0\n            });\n\n            function u(e) {\n              ++l, Rt(e);\n            }\n\n            var s = [],\n                c = [],\n                l = 0;\n\n            if (\"deleteRange\" === v) {\n              if (4 === g.type) return t({\n                numFailures: l,\n                failures: c,\n                results: [],\n                lastResult: void 0\n              });\n              3 === g.type ? s.push(o = n.clear()) : s.push(o = n.delete(_(g)));\n            } else {\n              var r = i ? r ? [b, m] : [b, null] : [m, null],\n                  f = r[0],\n                  h = r[1];\n              if (i) for (var d = 0; d < a; ++d) s.push(o = h && void 0 !== h[d] ? n[v](f[d], h[d]) : n[v](f[d])), o.onerror = u;else for (d = 0; d < a; ++d) s.push(o = n[v](f[d])), o.onerror = u;\n            }\n\n            function p(e) {\n              e = e.target.result, s.forEach(function (e, t) {\n                return null != e.error && (c[t] = e.error);\n              }), t({\n                numFailures: l,\n                failures: c,\n                results: \"delete\" === v ? m : s.map(function (e) {\n                  return e.result;\n                }),\n                lastResult: e\n              });\n            }\n\n            o.onerror = function (e) {\n              u(e), p(e);\n            }, o.onsuccess = p;\n          });\n        },\n        getMany: function (e) {\n          var f = e.trans,\n              h = e.keys;\n          return new Promise(function (t, e) {\n            t = qe(t);\n\n            for (var n, r = f.objectStore(w), i = h.length, o = new Array(i), a = 0, u = 0, s = function (e) {\n              e = e.target;\n              o[e._pos] = e.result, ++u === a && t(o);\n            }, c = Bt(e), l = 0; l < i; ++l) null != h[l] && ((n = r.get(h[l]))._pos = l, n.onsuccess = s, n.onerror = c, ++a);\n\n            0 === a && t(o);\n          });\n        },\n        get: function (e) {\n          var r = e.trans,\n              i = e.key;\n          return new Promise(function (t, e) {\n            t = qe(t);\n            var n = r.objectStore(w).get(i);\n            n.onsuccess = function (e) {\n              return t(e.target.result);\n            }, n.onerror = Bt(e);\n          });\n        },\n        query: (h = s, function (f) {\n          return new Promise(function (n, e) {\n            n = qe(n);\n\n            var r,\n                i,\n                o,\n                t = f.trans,\n                a = f.values,\n                u = f.limit,\n                s = f.query,\n                c = u === 1 / 0 ? void 0 : u,\n                l = s.index,\n                s = s.range,\n                t = t.objectStore(w),\n                l = l.isPrimaryKey ? t : t.index(l.name),\n                s = _(s);\n\n            if (0 === u) return n({\n              result: []\n            });\n            h ? ((c = a ? l.getAll(s, c) : l.getAllKeys(s, c)).onsuccess = function (e) {\n              return n({\n                result: e.target.result\n              });\n            }, c.onerror = Bt(e)) : (r = 0, i = !a && \"openKeyCursor\" in l ? l.openKeyCursor(s) : l.openCursor(s), o = [], i.onsuccess = function (e) {\n              var t = i.result;\n              return t ? (o.push(a ? t.value : t.primaryKey), ++r === u ? n({\n                result: o\n              }) : void t.continue()) : n({\n                result: o\n              });\n            }, i.onerror = Bt(e));\n          });\n        }),\n        openCursor: function (e) {\n          var c = e.trans,\n              o = e.values,\n              a = e.query,\n              u = e.reverse,\n              l = e.unique;\n          return new Promise(function (t, n) {\n            t = qe(t);\n            var e = a.index,\n                r = a.range,\n                i = c.objectStore(w),\n                i = e.isPrimaryKey ? i : i.index(e.name),\n                e = u ? l ? \"prevunique\" : \"prev\" : l ? \"nextunique\" : \"next\",\n                s = !o && \"openKeyCursor\" in i ? i.openKeyCursor(_(r), e) : i.openCursor(_(r), e);\n            s.onerror = Bt(n), s.onsuccess = qe(function (e) {\n              var r,\n                  i,\n                  o,\n                  a,\n                  u = s.result;\n              u ? (u.___id = ++Gt, u.done = !1, r = u.continue.bind(u), i = (i = u.continuePrimaryKey) && i.bind(u), o = u.advance.bind(u), a = function () {\n                throw new Error(\"Cursor not stopped\");\n              }, u.trans = c, u.stop = u.continue = u.continuePrimaryKey = u.advance = function () {\n                throw new Error(\"Cursor not started\");\n              }, u.fail = qe(n), u.next = function () {\n                var e = this,\n                    t = 1;\n                return this.start(function () {\n                  return t-- ? e.continue() : e.stop();\n                }).then(function () {\n                  return e;\n                });\n              }, u.start = function (e) {\n                function t() {\n                  if (s.result) try {\n                    e();\n                  } catch (e) {\n                    u.fail(e);\n                  } else u.done = !0, u.start = function () {\n                    throw new Error(\"Cursor behind last entry\");\n                  }, u.stop();\n                }\n\n                var n = new Promise(function (t, e) {\n                  t = qe(t), s.onerror = Bt(e), u.fail = e, u.stop = function (e) {\n                    u.stop = u.continue = u.continuePrimaryKey = u.advance = a, t(e);\n                  };\n                });\n                return s.onsuccess = qe(function (e) {\n                  s.onsuccess = t, t();\n                }), u.continue = r, u.continuePrimaryKey = i, u.advance = o, t(), n;\n              }, t(u)) : t(null);\n            }, n);\n          });\n        },\n        count: function (e) {\n          var t = e.query,\n              i = e.trans,\n              o = t.index,\n              a = t.range;\n          return new Promise(function (t, e) {\n            var n = i.objectStore(w),\n                r = o.isPrimaryKey ? n : n.index(o.name),\n                n = _(a),\n                r = n ? r.count(n) : r.count();\n\n            r.onsuccess = qe(function (e) {\n              return t(e.target.result);\n            }), r.onerror = Bt(e);\n          });\n        }\n      };\n    }\n\n    var r,\n        o,\n        a,\n        u = (o = t, a = Qt((r = e).objectStoreNames), {\n      schema: {\n        name: r.name,\n        tables: a.map(function (e) {\n          return o.objectStore(e);\n        }).map(function (t) {\n          var e = t.keyPath,\n              n = t.autoIncrement,\n              r = k(e),\n              i = {},\n              n = {\n            name: t.name,\n            primaryKey: {\n              name: null,\n              isPrimaryKey: !0,\n              outbound: null == e,\n              compound: r,\n              keyPath: e,\n              autoIncrement: n,\n              unique: !0,\n              extractKey: $t(e)\n            },\n            indexes: Qt(t.indexNames).map(function (e) {\n              return t.index(e);\n            }).map(function (e) {\n              var t = e.name,\n                  n = e.unique,\n                  r = e.multiEntry,\n                  e = e.keyPath,\n                  r = {\n                name: t,\n                compound: k(e),\n                keyPath: e,\n                unique: n,\n                multiEntry: r,\n                extractKey: $t(e)\n              };\n              return i[Xt(e)] = r;\n            }),\n            getIndexByKeyPath: function (e) {\n              return i[Xt(e)];\n            }\n          };\n          return i[\":id\"] = n.primaryKey, null != e && (i[Xt(e)] = n.primaryKey), n;\n        })\n      },\n      hasGetAll: 0 < a.length && \"getAll\" in o.objectStore(a[0]) && !(\"undefined\" != typeof navigator && /Safari/.test(navigator.userAgent) && !/(Chrome\\/|Edge\\/)/.test(navigator.userAgent) && [].concat(navigator.userAgent.match(/Safari\\/(\\d*)/))[1] < 604)\n    }),\n        t = u.schema,\n        s = u.hasGetAll,\n        u = t.tables.map(n),\n        c = {};\n    return u.forEach(function (e) {\n      return c[e.name] = e;\n    }), {\n      stack: \"dbcore\",\n      transaction: e.transaction.bind(e),\n      table: function (e) {\n        if (!c[e]) throw new Error(\"Table '\".concat(e, \"' not found\"));\n        return c[e];\n      },\n      MIN_KEY: -1 / 0,\n      MAX_KEY: Yt(i),\n      schema: t\n    };\n  }\n\n  function Jt(e, t, n, r) {\n    var i = n.IDBKeyRange;\n    return n.indexedDB, {\n      dbcore: (r = Ht(t, i, r), e.dbcore.reduce(function (e, t) {\n        t = t.create;\n        return _(_({}, e), t(e));\n      }, r))\n    };\n  }\n\n  function Zt(n, e) {\n    var t = e.db,\n        e = Jt(n._middlewares, t, n._deps, e);\n    n.core = e.dbcore, n.tables.forEach(function (e) {\n      var t = e.name;\n      n.core.schema.tables.some(function (e) {\n        return e.name === t;\n      }) && (e.core = n.core.table(t), n[t] instanceof n.Table && (n[t].core = e.core));\n    });\n  }\n\n  function en(i, e, t, o) {\n    t.forEach(function (n) {\n      var r = o[n];\n      e.forEach(function (e) {\n        var t = function e(t, n) {\n          return h(t, n) || (t = c(t)) && e(t, n);\n        }(e, n);\n\n        (!t || \"value\" in t && void 0 === t.value) && (e === i.Transaction.prototype || e instanceof i.Transaction ? l(e, n, {\n          get: function () {\n            return this.table(n);\n          },\n          set: function (e) {\n            u(this, n, {\n              value: e,\n              writable: !0,\n              configurable: !0,\n              enumerable: !0\n            });\n          }\n        }) : e[n] = new i.Table(n, r));\n      });\n    });\n  }\n\n  function tn(n, e) {\n    e.forEach(function (e) {\n      for (var t in e) e[t] instanceof n.Table && delete e[t];\n    });\n  }\n\n  function nn(e, t) {\n    return e._cfg.version - t._cfg.version;\n  }\n\n  function rn(n, r, i, e) {\n    var o = n._dbSchema;\n    i.objectStoreNames.contains(\"$meta\") && !o.$meta && (o.$meta = Wt(\"$meta\", hn(\"\")[0], []), n._storeNames.push(\"$meta\"));\n\n    var a = n._createTransaction(\"readwrite\", n._storeNames, o);\n\n    a.create(i), a._completion.catch(e);\n\n    var u = a._reject.bind(a),\n        s = me.transless || me;\n\n    Ne(function () {\n      return me.trans = a, me.transless = s, 0 !== r ? (Zt(n, i), t = r, ((e = a).storeNames.includes(\"$meta\") ? e.table(\"$meta\").get(\"version\").then(function (e) {\n        return null != e ? e : t;\n      }) : _e.resolve(t)).then(function (e) {\n        return c = e, l = a, f = i, t = [], e = (s = n)._versions, h = s._dbSchema = ln(0, s.idbdb, f), 0 !== (e = e.filter(function (e) {\n          return e._cfg.version >= c;\n        })).length ? (e.forEach(function (u) {\n          t.push(function () {\n            var t = h,\n                e = u._cfg.dbschema;\n            fn(s, t, f), fn(s, e, f), h = s._dbSchema = e;\n            var n = an(t, e);\n            n.add.forEach(function (e) {\n              un(f, e[0], e[1].primKey, e[1].indexes);\n            }), n.change.forEach(function (e) {\n              if (e.recreate) throw new Y.Upgrade(\"Not yet support for changing primary key\");\n              var t = f.objectStore(e.name);\n              e.add.forEach(function (e) {\n                return cn(t, e);\n              }), e.change.forEach(function (e) {\n                t.deleteIndex(e.name), cn(t, e);\n              }), e.del.forEach(function (e) {\n                return t.deleteIndex(e);\n              });\n            });\n            var r = u._cfg.contentUpgrade;\n\n            if (r && u._cfg.version > c) {\n              Zt(s, f), l._memoizedTables = {};\n              var i = g(e);\n              n.del.forEach(function (e) {\n                i[e] = t[e];\n              }), tn(s, [s.Transaction.prototype]), en(s, [s.Transaction.prototype], x(i), i), l.schema = i;\n              var o,\n                  a = B(r);\n              a && Le();\n              n = _e.follow(function () {\n                var e;\n                (o = r(l)) && a && (e = Ue.bind(null, null), o.then(e, e));\n              });\n              return o && \"function\" == typeof o.then ? _e.resolve(o) : n.then(function () {\n                return o;\n              });\n            }\n          }), t.push(function (e) {\n            var t,\n                n,\n                r = u._cfg.dbschema;\n            t = r, n = e, [].slice.call(n.db.objectStoreNames).forEach(function (e) {\n              return null == t[e] && n.db.deleteObjectStore(e);\n            }), tn(s, [s.Transaction.prototype]), en(s, [s.Transaction.prototype], s._storeNames, s._dbSchema), l.schema = s._dbSchema;\n          }), t.push(function (e) {\n            s.idbdb.objectStoreNames.contains(\"$meta\") && (Math.ceil(s.idbdb.version / 10) === u._cfg.version ? (s.idbdb.deleteObjectStore(\"$meta\"), delete s._dbSchema.$meta, s._storeNames = s._storeNames.filter(function (e) {\n              return \"$meta\" !== e;\n            })) : e.objectStore(\"$meta\").put(u._cfg.version, \"version\"));\n          });\n        }), function e() {\n          return t.length ? _e.resolve(t.shift()(l.idbtrans)).then(e) : _e.resolve();\n        }().then(function () {\n          sn(h, f);\n        })) : _e.resolve();\n        var s, c, l, f, t, h;\n      }).catch(u)) : (x(o).forEach(function (e) {\n        un(i, e, o[e].primKey, o[e].indexes);\n      }), Zt(n, i), void _e.follow(function () {\n        return n.on.populate.fire(a);\n      }).catch(u));\n      var e, t;\n    });\n  }\n\n  function on(e, r) {\n    sn(e._dbSchema, r), r.db.version % 10 != 0 || r.objectStoreNames.contains(\"$meta\") || r.db.createObjectStore(\"$meta\").add(Math.ceil(r.db.version / 10 - 1), \"version\");\n    var t = ln(0, e.idbdb, r);\n    fn(e, e._dbSchema, r);\n\n    for (var n = 0, i = an(t, e._dbSchema).change; n < i.length; n++) {\n      var o = function (t) {\n        if (t.change.length || t.recreate) return console.warn(\"Unable to patch indexes of table \".concat(t.name, \" because it has changes on the type of index or primary key.\")), {\n          value: void 0\n        };\n        var n = r.objectStore(t.name);\n        t.add.forEach(function (e) {\n          ie && console.debug(\"Dexie upgrade patch: Creating missing index \".concat(t.name, \".\").concat(e.src)), cn(n, e);\n        });\n      }(i[n]);\n\n      if (\"object\" == typeof o) return o.value;\n    }\n  }\n\n  function an(e, t) {\n    var n,\n        r = {\n      del: [],\n      add: [],\n      change: []\n    };\n\n    for (n in e) t[n] || r.del.push(n);\n\n    for (n in t) {\n      var i = e[n],\n          o = t[n];\n\n      if (i) {\n        var a = {\n          name: n,\n          def: o,\n          recreate: !1,\n          del: [],\n          add: [],\n          change: []\n        };\n        if (\"\" + (i.primKey.keyPath || \"\") != \"\" + (o.primKey.keyPath || \"\") || i.primKey.auto !== o.primKey.auto) a.recreate = !0, r.change.push(a);else {\n          var u = i.idxByName,\n              s = o.idxByName,\n              c = void 0;\n\n          for (c in u) s[c] || a.del.push(c);\n\n          for (c in s) {\n            var l = u[c],\n                f = s[c];\n            l ? l.src !== f.src && a.change.push(f) : a.add.push(f);\n          }\n\n          (0 < a.del.length || 0 < a.add.length || 0 < a.change.length) && r.change.push(a);\n        }\n      } else r.add.push([n, o]);\n    }\n\n    return r;\n  }\n\n  function un(e, t, n, r) {\n    var i = e.db.createObjectStore(t, n.keyPath ? {\n      keyPath: n.keyPath,\n      autoIncrement: n.auto\n    } : {\n      autoIncrement: n.auto\n    });\n    return r.forEach(function (e) {\n      return cn(i, e);\n    }), i;\n  }\n\n  function sn(t, n) {\n    x(t).forEach(function (e) {\n      n.db.objectStoreNames.contains(e) || (ie && console.debug(\"Dexie: Creating missing table\", e), un(n, e, t[e].primKey, t[e].indexes));\n    });\n  }\n\n  function cn(e, t) {\n    e.createIndex(t.name, t.keyPath, {\n      unique: t.unique,\n      multiEntry: t.multi\n    });\n  }\n\n  function ln(e, t, u) {\n    var s = {};\n    return b(t.objectStoreNames, 0).forEach(function (e) {\n      for (var t = u.objectStore(e), n = Vt(zt(a = t.keyPath), a || \"\", !0, !1, !!t.autoIncrement, a && \"string\" != typeof a, !0), r = [], i = 0; i < t.indexNames.length; ++i) {\n        var o = t.index(t.indexNames[i]),\n            a = o.keyPath,\n            o = Vt(o.name, a, !!o.unique, !!o.multiEntry, !1, a && \"string\" != typeof a, !1);\n        r.push(o);\n      }\n\n      s[e] = Wt(e, n, r);\n    }), s;\n  }\n\n  function fn(e, t, n) {\n    for (var r = n.db.objectStoreNames, i = 0; i < r.length; ++i) {\n      var o = r[i],\n          a = n.objectStore(o);\n      e._hasGetAll = \"getAll\" in a;\n\n      for (var u = 0; u < a.indexNames.length; ++u) {\n        var s = a.indexNames[u],\n            c = a.index(s).keyPath,\n            l = \"string\" == typeof c ? c : \"[\" + b(c).join(\"+\") + \"]\";\n        !t[o] || (c = t[o].idxByName[l]) && (c.name = s, delete t[o].idxByName[l], t[o].idxByName[s] = c);\n      }\n    }\n\n    \"undefined\" != typeof navigator && /Safari/.test(navigator.userAgent) && !/(Chrome\\/|Edge\\/)/.test(navigator.userAgent) && f.WorkerGlobalScope && f instanceof f.WorkerGlobalScope && [].concat(navigator.userAgent.match(/Safari\\/(\\d*)/))[1] < 604 && (e._hasGetAll = !1);\n  }\n\n  function hn(e) {\n    return e.split(\",\").map(function (e, t) {\n      var n = (e = e.trim()).replace(/([&*]|\\+\\+)/g, \"\"),\n          r = /^\\[/.test(n) ? n.match(/^\\[(.*)\\]$/)[1].split(\"+\") : n;\n      return Vt(n, r || null, /\\&/.test(e), /\\*/.test(e), /\\+\\+/.test(e), k(r), 0 === t);\n    });\n  }\n\n  var dn = (pn.prototype._parseStoresSpec = function (r, i) {\n    x(r).forEach(function (e) {\n      if (null !== r[e]) {\n        var t = hn(r[e]),\n            n = t.shift();\n        if (n.unique = !0, n.multi) throw new Y.Schema(\"Primary key cannot be multi-valued\");\n        t.forEach(function (e) {\n          if (e.auto) throw new Y.Schema(\"Only primary key can be marked as autoIncrement (++)\");\n          if (!e.keyPath) throw new Y.Schema(\"Index must have a name and cannot be an empty string\");\n        }), i[e] = Wt(e, n, t);\n      }\n    });\n  }, pn.prototype.stores = function (e) {\n    var t = this.db;\n    this._cfg.storesSource = this._cfg.storesSource ? a(this._cfg.storesSource, e) : e;\n    var e = t._versions,\n        n = {},\n        r = {};\n    return e.forEach(function (e) {\n      a(n, e._cfg.storesSource), r = e._cfg.dbschema = {}, e._parseStoresSpec(n, r);\n    }), t._dbSchema = r, tn(t, [t._allTables, t, t.Transaction.prototype]), en(t, [t._allTables, t, t.Transaction.prototype, this._cfg.tables], x(r), r), t._storeNames = x(r), this;\n  }, pn.prototype.upgrade = function (e) {\n    return this._cfg.contentUpgrade = re(this._cfg.contentUpgrade || G, e), this;\n  }, pn);\n\n  function pn() {}\n\n  function yn(e, t) {\n    var n = e._dbNamesDB;\n    return n || (n = e._dbNamesDB = new er(tt, {\n      addons: [],\n      indexedDB: e,\n      IDBKeyRange: t\n    })).version(1).stores({\n      dbnames: \"name\"\n    }), n.table(\"dbnames\");\n  }\n\n  function vn(e) {\n    return e && \"function\" == typeof e.databases;\n  }\n\n  function mn(e) {\n    return Ne(function () {\n      return me.letThrough = !0, e();\n    });\n  }\n\n  function bn(e) {\n    return !(\"from\" in e);\n  }\n\n  var gn = function (e, t) {\n    if (!this) {\n      var n = new gn();\n      return e && \"d\" in e && a(n, e), n;\n    }\n\n    a(this, arguments.length ? {\n      d: 1,\n      from: e,\n      to: 1 < arguments.length ? t : e\n    } : {\n      d: 0\n    });\n  };\n\n  function wn(e, t, n) {\n    var r = st(t, n);\n\n    if (!isNaN(r)) {\n      if (0 < r) throw RangeError();\n      if (bn(e)) return a(e, {\n        from: t,\n        to: n,\n        d: 1\n      });\n      var i = e.l,\n          r = e.r;\n      if (st(n, e.from) < 0) return i ? wn(i, t, n) : e.l = {\n        from: t,\n        to: n,\n        d: 1,\n        l: null,\n        r: null\n      }, On(e);\n      if (0 < st(t, e.to)) return r ? wn(r, t, n) : e.r = {\n        from: t,\n        to: n,\n        d: 1,\n        l: null,\n        r: null\n      }, On(e);\n      st(t, e.from) < 0 && (e.from = t, e.l = null, e.d = r ? r.d + 1 : 1), 0 < st(n, e.to) && (e.to = n, e.r = null, e.d = e.l ? e.l.d + 1 : 1);\n      n = !e.r;\n      i && !e.l && _n(e, i), r && n && _n(e, r);\n    }\n  }\n\n  function _n(e, t) {\n    bn(t) || function e(t, n) {\n      var r = n.from,\n          i = n.to,\n          o = n.l,\n          n = n.r;\n      wn(t, r, i), o && e(t, o), n && e(t, n);\n    }(e, t);\n  }\n\n  function xn(e, t) {\n    var n = kn(t),\n        r = n.next();\n    if (r.done) return !1;\n\n    for (var i = r.value, o = kn(e), a = o.next(i.from), u = a.value; !r.done && !a.done;) {\n      if (st(u.from, i.to) <= 0 && 0 <= st(u.to, i.from)) return !0;\n      st(i.from, u.from) < 0 ? i = (r = n.next(u.from)).value : u = (a = o.next(i.from)).value;\n    }\n\n    return !1;\n  }\n\n  function kn(e) {\n    var n = bn(e) ? null : {\n      s: 0,\n      n: e\n    };\n    return {\n      next: function (e) {\n        for (var t = 0 < arguments.length; n;) switch (n.s) {\n          case 0:\n            if (n.s = 1, t) for (; n.n.l && st(e, n.n.from) < 0;) n = {\n              up: n,\n              n: n.n.l,\n              s: 1\n            };else for (; n.n.l;) n = {\n              up: n,\n              n: n.n.l,\n              s: 1\n            };\n\n          case 1:\n            if (n.s = 2, !t || st(e, n.n.to) <= 0) return {\n              value: n.n,\n              done: !1\n            };\n\n          case 2:\n            if (n.n.r) {\n              n.s = 3, n = {\n                up: n,\n                n: n.n.r,\n                s: 0\n              };\n              continue;\n            }\n\n          case 3:\n            n = n.up;\n        }\n\n        return {\n          done: !0\n        };\n      }\n    };\n  }\n\n  function On(e) {\n    var t,\n        n,\n        r = ((null === (t = e.r) || void 0 === t ? void 0 : t.d) || 0) - ((null === (n = e.l) || void 0 === n ? void 0 : n.d) || 0),\n        i = 1 < r ? \"r\" : r < -1 ? \"l\" : \"\";\n    i && (t = \"r\" == i ? \"l\" : \"r\", n = _({}, e), r = e[i], e.from = r.from, e.to = r.to, e[i] = r[i], n[i] = r[t], (e[t] = n).d = Pn(n)), e.d = Pn(e);\n  }\n\n  function Pn(e) {\n    var t = e.r,\n        e = e.l;\n    return (t ? e ? Math.max(t.d, e.d) : t.d : e ? e.d : 0) + 1;\n  }\n\n  function Kn(t, n) {\n    return x(n).forEach(function (e) {\n      t[e] ? _n(t[e], n[e]) : t[e] = function e(t) {\n        var n,\n            r,\n            i = {};\n\n        for (n in t) m(t, n) && (r = t[n], i[n] = !r || \"object\" != typeof r || K.has(r.constructor) ? r : e(r));\n\n        return i;\n      }(n[e]);\n    }), t;\n  }\n\n  function En(t, n) {\n    return t.all || n.all || Object.keys(t).some(function (e) {\n      return n[e] && xn(n[e], t[e]);\n    });\n  }\n\n  r(gn.prototype, ((M = {\n    add: function (e) {\n      return _n(this, e), this;\n    },\n    addKey: function (e) {\n      return wn(this, e, e), this;\n    },\n    addKeys: function (e) {\n      var t = this;\n      return e.forEach(function (e) {\n        return wn(t, e, e);\n      }), this;\n    },\n    hasKey: function (e) {\n      var t = kn(this).next(e).value;\n      return t && st(t.from, e) <= 0 && 0 <= st(t.to, e);\n    }\n  })[C] = function () {\n    return kn(this);\n  }, M));\n  var Sn = {},\n      jn = {},\n      An = !1;\n\n  function Cn(e) {\n    Kn(jn, e), An || (An = !0, setTimeout(function () {\n      An = !1, Tn(jn, !(jn = {}));\n    }, 0));\n  }\n\n  function Tn(e, t) {\n    void 0 === t && (t = !1);\n    var n = new Set();\n    if (e.all) for (var r = 0, i = Object.values(Sn); r < i.length; r++) qn(a = i[r], e, n, t);else for (var o in e) {\n      var a,\n          u = /^idb\\:\\/\\/(.*)\\/(.*)\\//.exec(o);\n      u && (o = u[1], u = u[2], (a = Sn[\"idb://\".concat(o, \"/\").concat(u)]) && qn(a, e, n, t));\n    }\n    n.forEach(function (e) {\n      return e();\n    });\n  }\n\n  function qn(e, t, n, r) {\n    for (var i = [], o = 0, a = Object.entries(e.queries.query); o < a.length; o++) {\n      for (var u = a[o], s = u[0], c = [], l = 0, f = u[1]; l < f.length; l++) {\n        var h = f[l];\n        En(t, h.obsSet) ? h.subscribers.forEach(function (e) {\n          return n.add(e);\n        }) : r && c.push(h);\n      }\n\n      r && i.push([s, c]);\n    }\n\n    if (r) for (var d = 0, p = i; d < p.length; d++) {\n      var y = p[d],\n          s = y[0],\n          c = y[1];\n      e.queries.query[s] = c;\n    }\n  }\n\n  function Dn(f) {\n    var h = f._state,\n        r = f._deps.indexedDB;\n    if (h.isBeingOpened || f.idbdb) return h.dbReadyPromise.then(function () {\n      return h.dbOpenError ? Xe(h.dbOpenError) : f;\n    });\n    h.isBeingOpened = !0, h.dbOpenError = null, h.openComplete = !1;\n    var t = h.openCanceller,\n        d = Math.round(10 * f.verno),\n        p = !1;\n\n    function e() {\n      if (h.openCanceller !== t) throw new Y.DatabaseClosed(\"db.open() was cancelled\");\n    }\n\n    function y() {\n      return new _e(function (s, n) {\n        if (e(), !r) throw new Y.MissingAPI();\n        var c = f.name,\n            l = h.autoSchema || !d ? r.open(c) : r.open(c, d);\n        if (!l) throw new Y.MissingAPI();\n        l.onerror = Bt(n), l.onblocked = qe(f._fireOnBlocked), l.onupgradeneeded = qe(function (e) {\n          var t;\n          v = l.transaction, h.autoSchema && !f._options.allowEmptyDB ? (l.onerror = Rt, v.abort(), l.result.close(), (t = r.deleteDatabase(c)).onsuccess = t.onerror = qe(function () {\n            n(new Y.NoSuchDatabase(\"Database \".concat(c, \" doesnt exist\")));\n          })) : (v.onerror = Bt(n), e = e.oldVersion > Math.pow(2, 62) ? 0 : e.oldVersion, m = e < 1, f.idbdb = l.result, p && on(f, v), rn(f, e / 10, v, n));\n        }, n), l.onsuccess = qe(function () {\n          v = null;\n          var e,\n              t,\n              n,\n              r,\n              i,\n              o = f.idbdb = l.result,\n              a = b(o.objectStoreNames);\n          if (0 < a.length) try {\n            var u = o.transaction(1 === (r = a).length ? r[0] : r, \"readonly\");\n            if (h.autoSchema) t = o, n = u, (e = f).verno = t.version / 10, n = e._dbSchema = ln(0, t, n), e._storeNames = b(t.objectStoreNames, 0), en(e, [e._allTables], x(n), n);else if (fn(f, f._dbSchema, u), ((i = an(ln(0, (i = f).idbdb, u), i._dbSchema)).add.length || i.change.some(function (e) {\n              return e.add.length || e.change.length;\n            })) && !p) return console.warn(\"Dexie SchemaDiff: Schema was extended without increasing the number passed to db.version(). Dexie will add missing parts and increment native version number to workaround this.\"), o.close(), d = o.version + 1, p = !0, s(y());\n            Zt(f, u);\n          } catch (e) {}\n          et.push(f), o.onversionchange = qe(function (e) {\n            h.vcFired = !0, f.on(\"versionchange\").fire(e);\n          }), o.onclose = qe(function (e) {\n            f.on(\"close\").fire(e);\n          }), m && (i = f._deps, u = c, o = i.indexedDB, i = i.IDBKeyRange, vn(o) || u === tt || yn(o, i).put({\n            name: u\n          }).catch(G)), s();\n        }, n);\n      }).catch(function (e) {\n        switch (null == e ? void 0 : e.name) {\n          case \"UnknownError\":\n            if (0 < h.PR1398_maxLoop) return h.PR1398_maxLoop--, console.warn(\"Dexie: Workaround for Chrome UnknownError on open()\"), y();\n            break;\n\n          case \"VersionError\":\n            if (0 < d) return d = 0, y();\n        }\n\n        return _e.reject(e);\n      });\n    }\n\n    var n,\n        i = h.dbReadyResolve,\n        v = null,\n        m = !1;\n    return _e.race([t, (\"undefined\" == typeof navigator ? _e.resolve() : !navigator.userAgentData && /Safari\\//.test(navigator.userAgent) && !/Chrom(e|ium)\\//.test(navigator.userAgent) && indexedDB.databases ? new Promise(function (e) {\n      function t() {\n        return indexedDB.databases().finally(e);\n      }\n\n      n = setInterval(t, 100), t();\n    }).finally(function () {\n      return clearInterval(n);\n    }) : Promise.resolve()).then(y)]).then(function () {\n      return e(), h.onReadyBeingFired = [], _e.resolve(mn(function () {\n        return f.on.ready.fire(f.vip);\n      })).then(function e() {\n        if (0 < h.onReadyBeingFired.length) {\n          var t = h.onReadyBeingFired.reduce(re, G);\n          return h.onReadyBeingFired = [], _e.resolve(mn(function () {\n            return t(f.vip);\n          })).then(e);\n        }\n      });\n    }).finally(function () {\n      h.openCanceller === t && (h.onReadyBeingFired = null, h.isBeingOpened = !1);\n    }).catch(function (e) {\n      h.dbOpenError = e;\n\n      try {\n        v && v.abort();\n      } catch (e) {}\n\n      return t === h.openCanceller && f._close(), Xe(e);\n    }).finally(function () {\n      h.openComplete = !0, i();\n    }).then(function () {\n      var n;\n      return m && (n = {}, f.tables.forEach(function (t) {\n        t.schema.indexes.forEach(function (e) {\n          e.name && (n[\"idb://\".concat(f.name, \"/\").concat(t.name, \"/\").concat(e.name)] = new gn(-1 / 0, [[[]]]));\n        }), n[\"idb://\".concat(f.name, \"/\").concat(t.name, \"/\")] = n[\"idb://\".concat(f.name, \"/\").concat(t.name, \"/:dels\")] = new gn(-1 / 0, [[[]]]);\n      }), Nt(Mt).fire(n), Tn(n, !0)), f;\n    });\n  }\n\n  function In(t) {\n    function e(e) {\n      return t.next(e);\n    }\n\n    var r = n(e),\n        i = n(function (e) {\n      return t.throw(e);\n    });\n\n    function n(n) {\n      return function (e) {\n        var t = n(e),\n            e = t.value;\n        return t.done ? e : e && \"function\" == typeof e.then ? e.then(r, i) : k(e) ? Promise.all(e).then(r, i) : r(e);\n      };\n    }\n\n    return n(e)();\n  }\n\n  function Bn(e, t, n) {\n    for (var r = k(e) ? e.slice() : [e], i = 0; i < n; ++i) r.push(t);\n\n    return r;\n  }\n\n  var Rn = {\n    stack: \"dbcore\",\n    name: \"VirtualIndexMiddleware\",\n    level: 1,\n    create: function (f) {\n      return _(_({}, f), {\n        table: function (e) {\n          var a = f.table(e),\n              t = a.schema,\n              u = {},\n              s = [];\n\n          function c(e, t, n) {\n            var r = Xt(e),\n                i = u[r] = u[r] || [],\n                o = null == e ? 0 : \"string\" == typeof e ? 1 : e.length,\n                a = 0 < t,\n                a = _(_({}, n), {\n              name: a ? \"\".concat(r, \"(virtual-from:\").concat(n.name, \")\") : n.name,\n              lowLevelIndex: n,\n              isVirtual: a,\n              keyTail: t,\n              keyLength: o,\n              extractKey: $t(e),\n              unique: !a && n.unique\n            });\n\n            return i.push(a), a.isPrimaryKey || s.push(a), 1 < o && c(2 === o ? e[0] : e.slice(0, o - 1), t + 1, n), i.sort(function (e, t) {\n              return e.keyTail - t.keyTail;\n            }), a;\n          }\n\n          e = c(t.primaryKey.keyPath, 0, t.primaryKey);\n          u[\":id\"] = [e];\n\n          for (var n = 0, r = t.indexes; n < r.length; n++) {\n            var i = r[n];\n            c(i.keyPath, 0, i);\n          }\n\n          function l(e) {\n            var t,\n                n = e.query.index;\n            return n.isVirtual ? _(_({}, e), {\n              query: {\n                index: n.lowLevelIndex,\n                range: (t = e.query.range, n = n.keyTail, {\n                  type: 1 === t.type ? 2 : t.type,\n                  lower: Bn(t.lower, t.lowerOpen ? f.MAX_KEY : f.MIN_KEY, n),\n                  lowerOpen: !0,\n                  upper: Bn(t.upper, t.upperOpen ? f.MIN_KEY : f.MAX_KEY, n),\n                  upperOpen: !0\n                })\n              }\n            }) : e;\n          }\n\n          return _(_({}, a), {\n            schema: _(_({}, t), {\n              primaryKey: e,\n              indexes: s,\n              getIndexByKeyPath: function (e) {\n                return (e = u[Xt(e)]) && e[0];\n              }\n            }),\n            count: function (e) {\n              return a.count(l(e));\n            },\n            query: function (e) {\n              return a.query(l(e));\n            },\n            openCursor: function (t) {\n              var e = t.query.index,\n                  r = e.keyTail,\n                  n = e.isVirtual,\n                  i = e.keyLength;\n              return n ? a.openCursor(l(t)).then(function (e) {\n                return e && o(e);\n              }) : a.openCursor(t);\n\n              function o(n) {\n                return Object.create(n, {\n                  continue: {\n                    value: function (e) {\n                      null != e ? n.continue(Bn(e, t.reverse ? f.MAX_KEY : f.MIN_KEY, r)) : t.unique ? n.continue(n.key.slice(0, i).concat(t.reverse ? f.MIN_KEY : f.MAX_KEY, r)) : n.continue();\n                    }\n                  },\n                  continuePrimaryKey: {\n                    value: function (e, t) {\n                      n.continuePrimaryKey(Bn(e, f.MAX_KEY, r), t);\n                    }\n                  },\n                  primaryKey: {\n                    get: function () {\n                      return n.primaryKey;\n                    }\n                  },\n                  key: {\n                    get: function () {\n                      var e = n.key;\n                      return 1 === i ? e[0] : e.slice(0, i);\n                    }\n                  },\n                  value: {\n                    get: function () {\n                      return n.value;\n                    }\n                  }\n                });\n              }\n            }\n          });\n        }\n      });\n    }\n  };\n\n  function Mn(i, o, a, u) {\n    return a = a || {}, u = u || \"\", x(i).forEach(function (e) {\n      var t, n, r;\n      m(o, e) ? (t = i[e], n = o[e], \"object\" == typeof t && \"object\" == typeof n && t && n ? (r = A(t)) !== A(n) ? a[u + e] = o[e] : \"Object\" === r ? Mn(t, n, a, u + e + \".\") : t !== n && (a[u + e] = o[e]) : t !== n && (a[u + e] = o[e])) : a[u + e] = void 0;\n    }), x(o).forEach(function (e) {\n      m(i, e) || (a[u + e] = o[e]);\n    }), a;\n  }\n\n  function Fn(e, t) {\n    return \"delete\" === t.type ? t.keys : t.keys || t.values.map(e.extractKey);\n  }\n\n  var Nn = {\n    stack: \"dbcore\",\n    name: \"HooksMiddleware\",\n    level: 2,\n    create: function (e) {\n      return _(_({}, e), {\n        table: function (r) {\n          var y = e.table(r),\n              v = y.schema.primaryKey;\n          return _(_({}, y), {\n            mutate: function (e) {\n              var t = me.trans,\n                  n = t.table(r).hook,\n                  h = n.deleting,\n                  d = n.creating,\n                  p = n.updating;\n\n              switch (e.type) {\n                case \"add\":\n                  if (d.fire === G) break;\n                  return t._promise(\"readwrite\", function () {\n                    return a(e);\n                  }, !0);\n\n                case \"put\":\n                  if (d.fire === G && p.fire === G) break;\n                  return t._promise(\"readwrite\", function () {\n                    return a(e);\n                  }, !0);\n\n                case \"delete\":\n                  if (h.fire === G) break;\n                  return t._promise(\"readwrite\", function () {\n                    return a(e);\n                  }, !0);\n\n                case \"deleteRange\":\n                  if (h.fire === G) break;\n                  return t._promise(\"readwrite\", function () {\n                    return function n(r, i, o) {\n                      return y.query({\n                        trans: r,\n                        values: !1,\n                        query: {\n                          index: v,\n                          range: i\n                        },\n                        limit: o\n                      }).then(function (e) {\n                        var t = e.result;\n                        return a({\n                          type: \"delete\",\n                          keys: t,\n                          trans: r\n                        }).then(function (e) {\n                          return 0 < e.numFailures ? Promise.reject(e.failures[0]) : t.length < o ? {\n                            failures: [],\n                            numFailures: 0,\n                            lastResult: void 0\n                          } : n(r, _(_({}, i), {\n                            lower: t[t.length - 1],\n                            lowerOpen: !0\n                          }), o);\n                        });\n                      });\n                    }(e.trans, e.range, 1e4);\n                  }, !0);\n              }\n\n              return y.mutate(e);\n\n              function a(c) {\n                var e,\n                    t,\n                    n,\n                    l = me.trans,\n                    f = c.keys || Fn(v, c);\n                if (!f) throw new Error(\"Keys missing\");\n                return \"delete\" !== (c = \"add\" === c.type || \"put\" === c.type ? _(_({}, c), {\n                  keys: f\n                }) : _({}, c)).type && (c.values = i([], c.values, !0)), c.keys && (c.keys = i([], c.keys, !0)), e = y, n = f, (\"add\" === (t = c).type ? Promise.resolve([]) : e.getMany({\n                  trans: t.trans,\n                  keys: n,\n                  cache: \"immutable\"\n                })).then(function (u) {\n                  var s = f.map(function (e, t) {\n                    var n,\n                        r,\n                        i,\n                        o = u[t],\n                        a = {\n                      onerror: null,\n                      onsuccess: null\n                    };\n                    return \"delete\" === c.type ? h.fire.call(a, e, o, l) : \"add\" === c.type || void 0 === o ? (n = d.fire.call(a, e, c.values[t], l), null == e && null != n && (c.keys[t] = e = n, v.outbound || P(c.values[t], v.keyPath, e))) : (n = Mn(o, c.values[t]), (r = p.fire.call(a, n, e, o, l)) && (i = c.values[t], Object.keys(r).forEach(function (e) {\n                      m(i, e) ? i[e] = r[e] : P(i, e, r[e]);\n                    }))), a;\n                  });\n                  return y.mutate(c).then(function (e) {\n                    for (var t = e.failures, n = e.results, r = e.numFailures, e = e.lastResult, i = 0; i < f.length; ++i) {\n                      var o = (n || f)[i],\n                          a = s[i];\n                      null == o ? a.onerror && a.onerror(t[i]) : a.onsuccess && a.onsuccess(\"put\" === c.type && u[i] ? c.values[i] : o);\n                    }\n\n                    return {\n                      failures: t,\n                      results: n,\n                      numFailures: r,\n                      lastResult: e\n                    };\n                  }).catch(function (t) {\n                    return s.forEach(function (e) {\n                      return e.onerror && e.onerror(t);\n                    }), Promise.reject(t);\n                  });\n                });\n              }\n            }\n          });\n        }\n      });\n    }\n  };\n\n  function Ln(e, t, n) {\n    try {\n      if (!t) return null;\n      if (t.keys.length < e.length) return null;\n\n      for (var r = [], i = 0, o = 0; i < t.keys.length && o < e.length; ++i) 0 === st(t.keys[i], e[o]) && (r.push(n ? S(t.values[i]) : t.values[i]), ++o);\n\n      return r.length === e.length ? r : null;\n    } catch (e) {\n      return null;\n    }\n  }\n\n  var Un = {\n    stack: \"dbcore\",\n    level: -1,\n    create: function (t) {\n      return {\n        table: function (e) {\n          var n = t.table(e);\n          return _(_({}, n), {\n            getMany: function (t) {\n              if (!t.cache) return n.getMany(t);\n              var e = Ln(t.keys, t.trans._cache, \"clone\" === t.cache);\n              return e ? _e.resolve(e) : n.getMany(t).then(function (e) {\n                return t.trans._cache = {\n                  keys: t.keys,\n                  values: \"clone\" === t.cache ? S(e) : e\n                }, e;\n              });\n            },\n            mutate: function (e) {\n              return \"add\" !== e.type && (e.trans._cache = null), n.mutate(e);\n            }\n          });\n        }\n      };\n    }\n  };\n\n  function Vn(e, t) {\n    return \"readonly\" === e.trans.mode && !!e.subscr && !e.trans.explicit && \"disabled\" !== e.trans.db._options.cache && !t.schema.primaryKey.outbound;\n  }\n\n  function zn(e, t) {\n    switch (e) {\n      case \"query\":\n        return t.values && !t.unique;\n\n      case \"get\":\n      case \"getMany\":\n      case \"count\":\n      case \"openCursor\":\n        return !1;\n    }\n  }\n\n  var Wn = {\n    stack: \"dbcore\",\n    level: 0,\n    name: \"Observability\",\n    create: function (b) {\n      var g = b.schema.name,\n          w = new gn(b.MIN_KEY, b.MAX_KEY);\n      return _(_({}, b), {\n        transaction: function (e, t, n) {\n          if (me.subscr && \"readonly\" !== t) throw new Y.ReadOnly(\"Readwrite transaction in liveQuery context. Querier source: \".concat(me.querier));\n          return b.transaction(e, t, n);\n        },\n        table: function (d) {\n          var p = b.table(d),\n              y = p.schema,\n              v = y.primaryKey,\n              e = y.indexes,\n              c = v.extractKey,\n              l = v.outbound,\n              m = v.autoIncrement && e.filter(function (e) {\n            return e.compound && e.keyPath.includes(v.keyPath);\n          }),\n              t = _(_({}, p), {\n            mutate: function (a) {\n              function u(e) {\n                return e = \"idb://\".concat(g, \"/\").concat(d, \"/\").concat(e), n[e] || (n[e] = new gn());\n              }\n\n              var e,\n                  o,\n                  s,\n                  t = a.trans,\n                  n = a.mutatedParts || (a.mutatedParts = {}),\n                  r = u(\"\"),\n                  i = u(\":dels\"),\n                  c = a.type,\n                  l = \"deleteRange\" === a.type ? [a.range] : \"delete\" === a.type ? [a.keys] : a.values.length < 50 ? [Fn(v, a).filter(function (e) {\n                return e;\n              }), a.values] : [],\n                  f = l[0],\n                  h = l[1],\n                  l = a.trans._cache;\n              return k(f) ? (r.addKeys(f), (l = \"delete\" === c || f.length === h.length ? Ln(f, l) : null) || i.addKeys(f), (l || h) && (e = u, o = l, s = h, y.indexes.forEach(function (t) {\n                var n = e(t.name || \"\");\n\n                function r(e) {\n                  return null != e ? t.extractKey(e) : null;\n                }\n\n                function i(e) {\n                  return t.multiEntry && k(e) ? e.forEach(function (e) {\n                    return n.addKey(e);\n                  }) : n.addKey(e);\n                }\n\n                (o || s).forEach(function (e, t) {\n                  var n = o && r(o[t]),\n                      t = s && r(s[t]);\n                  0 !== st(n, t) && (null != n && i(n), null != t && i(t));\n                });\n              }))) : f ? (h = {\n                from: null !== (h = f.lower) && void 0 !== h ? h : b.MIN_KEY,\n                to: null !== (h = f.upper) && void 0 !== h ? h : b.MAX_KEY\n              }, i.add(h), r.add(h)) : (r.add(w), i.add(w), y.indexes.forEach(function (e) {\n                return u(e.name).add(w);\n              })), p.mutate(a).then(function (o) {\n                return !f || \"add\" !== a.type && \"put\" !== a.type || (r.addKeys(o.results), m && m.forEach(function (t) {\n                  for (var e = a.values.map(function (e) {\n                    return t.extractKey(e);\n                  }), n = t.keyPath.findIndex(function (e) {\n                    return e === v.keyPath;\n                  }), r = 0, i = o.results.length; r < i; ++r) e[r][n] = o.results[r];\n\n                  u(t.name).addKeys(e);\n                })), t.mutatedParts = Kn(t.mutatedParts || {}, n), o;\n              });\n            }\n          }),\n              e = function (e) {\n            var t = e.query,\n                e = t.index,\n                t = t.range;\n            return [e, new gn(null !== (e = t.lower) && void 0 !== e ? e : b.MIN_KEY, null !== (t = t.upper) && void 0 !== t ? t : b.MAX_KEY)];\n          },\n              f = {\n            get: function (e) {\n              return [v, new gn(e.key)];\n            },\n            getMany: function (e) {\n              return [v, new gn().addKeys(e.keys)];\n            },\n            count: e,\n            query: e,\n            openCursor: e\n          };\n\n          return x(f).forEach(function (s) {\n            t[s] = function (i) {\n              var e = me.subscr,\n                  t = !!e,\n                  n = Vn(me, p) && zn(s, i) ? i.obsSet = {} : e;\n\n              if (t) {\n                var r = function (e) {\n                  e = \"idb://\".concat(g, \"/\").concat(d, \"/\").concat(e);\n                  return n[e] || (n[e] = new gn());\n                },\n                    o = r(\"\"),\n                    a = r(\":dels\"),\n                    e = f[s](i),\n                    t = e[0],\n                    e = e[1];\n\n                if ((\"query\" === s && t.isPrimaryKey && !i.values ? a : r(t.name || \"\")).add(e), !t.isPrimaryKey) {\n                  if (\"count\" !== s) {\n                    var u = \"query\" === s && l && i.values && p.query(_(_({}, i), {\n                      values: !1\n                    }));\n                    return p[s].apply(this, arguments).then(function (t) {\n                      if (\"query\" === s) {\n                        if (l && i.values) return u.then(function (e) {\n                          e = e.result;\n                          return o.addKeys(e), t;\n                        });\n                        var e = i.values ? t.result.map(c) : t.result;\n                        (i.values ? o : a).addKeys(e);\n                      } else if (\"openCursor\" === s) {\n                        var n = t,\n                            r = i.values;\n                        return n && Object.create(n, {\n                          key: {\n                            get: function () {\n                              return a.addKey(n.primaryKey), n.key;\n                            }\n                          },\n                          primaryKey: {\n                            get: function () {\n                              var e = n.primaryKey;\n                              return a.addKey(e), e;\n                            }\n                          },\n                          value: {\n                            get: function () {\n                              return r && o.addKey(n.primaryKey), n.value;\n                            }\n                          }\n                        });\n                      }\n\n                      return t;\n                    });\n                  }\n\n                  a.add(w);\n                }\n              }\n\n              return p[s].apply(this, arguments);\n            };\n          }), t;\n        }\n      });\n    }\n  };\n\n  function Yn(e, t, n) {\n    if (0 === n.numFailures) return t;\n    if (\"deleteRange\" === t.type) return null;\n    var r = t.keys ? t.keys.length : \"values\" in t && t.values ? t.values.length : 1;\n    if (n.numFailures === r) return null;\n    t = _({}, t);\n    return k(t.keys) && (t.keys = t.keys.filter(function (e, t) {\n      return !(t in n.failures);\n    })), \"values\" in t && k(t.values) && (t.values = t.values.filter(function (e, t) {\n      return !(t in n.failures);\n    })), t;\n  }\n\n  function $n(e, t) {\n    return n = e, (void 0 === (r = t).lower || (r.lowerOpen ? 0 < st(n, r.lower) : 0 <= st(n, r.lower))) && (e = e, void 0 === (t = t).upper || (t.upperOpen ? st(e, t.upper) < 0 : st(e, t.upper) <= 0));\n    var n, r;\n  }\n\n  function Qn(e, d, t, n, r, i) {\n    if (!t || 0 === t.length) return e;\n    var o = d.query.index,\n        p = o.multiEntry,\n        y = d.query.range,\n        v = n.schema.primaryKey.extractKey,\n        m = o.extractKey,\n        a = (o.lowLevelIndex || o).extractKey,\n        t = t.reduce(function (e, t) {\n      var n = e,\n          r = [];\n      if (\"add\" === t.type || \"put\" === t.type) for (var i = new gn(), o = t.values.length - 1; 0 <= o; --o) {\n        var a,\n            u = t.values[o],\n            s = v(u);\n        i.hasKey(s) || (a = m(u), (p && k(a) ? a.some(function (e) {\n          return $n(e, y);\n        }) : $n(a, y)) && (i.addKey(s), r.push(u)));\n      }\n\n      switch (t.type) {\n        case \"add\":\n          var c = new gn().addKeys(d.values ? e.map(function (e) {\n            return v(e);\n          }) : e),\n              n = e.concat(d.values ? r.filter(function (e) {\n            e = v(e);\n            return !c.hasKey(e) && (c.addKey(e), !0);\n          }) : r.map(function (e) {\n            return v(e);\n          }).filter(function (e) {\n            return !c.hasKey(e) && (c.addKey(e), !0);\n          }));\n          break;\n\n        case \"put\":\n          var l = new gn().addKeys(t.values.map(function (e) {\n            return v(e);\n          }));\n          n = e.filter(function (e) {\n            return !l.hasKey(d.values ? v(e) : e);\n          }).concat(d.values ? r : r.map(function (e) {\n            return v(e);\n          }));\n          break;\n\n        case \"delete\":\n          var f = new gn().addKeys(t.keys);\n          n = e.filter(function (e) {\n            return !f.hasKey(d.values ? v(e) : e);\n          });\n          break;\n\n        case \"deleteRange\":\n          var h = t.range;\n          n = e.filter(function (e) {\n            return !$n(v(e), h);\n          });\n      }\n\n      return n;\n    }, e);\n    return t === e ? e : (t.sort(function (e, t) {\n      return st(a(e), a(t)) || st(v(e), v(t));\n    }), d.limit && d.limit < 1 / 0 && (t.length > d.limit ? t.length = d.limit : e.length === d.limit && t.length < d.limit && (r.dirty = !0)), i ? Object.freeze(t) : t);\n  }\n\n  function Gn(e, t) {\n    return 0 === st(e.lower, t.lower) && 0 === st(e.upper, t.upper) && !!e.lowerOpen == !!t.lowerOpen && !!e.upperOpen == !!t.upperOpen;\n  }\n\n  function Xn(e, t) {\n    return function (e, t, n, r) {\n      if (void 0 === e) return void 0 !== t ? -1 : 0;\n      if (void 0 === t) return 1;\n\n      if (0 === (t = st(e, t))) {\n        if (n && r) return 0;\n        if (n) return 1;\n        if (r) return -1;\n      }\n\n      return t;\n    }(e.lower, t.lower, e.lowerOpen, t.lowerOpen) <= 0 && 0 <= function (e, t, n, r) {\n      if (void 0 === e) return void 0 !== t ? 1 : 0;\n      if (void 0 === t) return -1;\n\n      if (0 === (t = st(e, t))) {\n        if (n && r) return 0;\n        if (n) return -1;\n        if (r) return 1;\n      }\n\n      return t;\n    }(e.upper, t.upper, e.upperOpen, t.upperOpen);\n  }\n\n  function Hn(n, r, i, e) {\n    n.subscribers.add(i), e.addEventListener(\"abort\", function () {\n      var e, t;\n      n.subscribers.delete(i), 0 === n.subscribers.size && (e = n, t = r, setTimeout(function () {\n        0 === e.subscribers.size && q(t, e);\n      }, 3e3));\n    });\n  }\n\n  var Jn = {\n    stack: \"dbcore\",\n    level: 0,\n    name: \"Cache\",\n    create: function (k) {\n      var O = k.schema.name;\n      return _(_({}, k), {\n        transaction: function (g, w, e) {\n          var _,\n              t,\n              x = k.transaction(g, w, e);\n\n          return \"readwrite\" === w && (t = (_ = new AbortController()).signal, e = function (b) {\n            return function () {\n              if (_.abort(), \"readwrite\" === w) {\n                for (var t = new Set(), e = 0, n = g; e < n.length; e++) {\n                  var r = n[e],\n                      i = Sn[\"idb://\".concat(O, \"/\").concat(r)];\n\n                  if (i) {\n                    var o = k.table(r),\n                        a = i.optimisticOps.filter(function (e) {\n                      return e.trans === x;\n                    });\n                    if (x._explicit && b && x.mutatedParts) for (var u = 0, s = Object.values(i.queries.query); u < s.length; u++) for (var c = 0, l = (d = s[u]).slice(); c < l.length; c++) En((p = l[c]).obsSet, x.mutatedParts) && (q(d, p), p.subscribers.forEach(function (e) {\n                      return t.add(e);\n                    }));else if (0 < a.length) {\n                      i.optimisticOps = i.optimisticOps.filter(function (e) {\n                        return e.trans !== x;\n                      });\n\n                      for (var f = 0, h = Object.values(i.queries.query); f < h.length; f++) for (var d, p, y, v = 0, m = (d = h[f]).slice(); v < m.length; v++) null != (p = m[v]).res && x.mutatedParts && (b && !p.dirty ? (y = Object.isFrozen(p.res), y = Qn(p.res, p.req, a, o, p, y), p.dirty ? (q(d, p), p.subscribers.forEach(function (e) {\n                        return t.add(e);\n                      })) : y !== p.res && (p.res = y, p.promise = _e.resolve({\n                        result: y\n                      }))) : (p.dirty && q(d, p), p.subscribers.forEach(function (e) {\n                        return t.add(e);\n                      })));\n                    }\n                  }\n                }\n\n                t.forEach(function (e) {\n                  return e();\n                });\n              }\n            };\n          }, x.addEventListener(\"abort\", e(!1), {\n            signal: t\n          }), x.addEventListener(\"error\", e(!1), {\n            signal: t\n          }), x.addEventListener(\"complete\", e(!0), {\n            signal: t\n          })), x;\n        },\n        table: function (c) {\n          var l = k.table(c),\n              i = l.schema.primaryKey;\n          return _(_({}, l), {\n            mutate: function (t) {\n              var e = me.trans;\n              if (i.outbound || \"disabled\" === e.db._options.cache || e.explicit || \"readwrite\" !== e.idbtrans.mode) return l.mutate(t);\n              var n = Sn[\"idb://\".concat(O, \"/\").concat(c)];\n              if (!n) return l.mutate(t);\n              e = l.mutate(t);\n              return \"add\" !== t.type && \"put\" !== t.type || !(50 <= t.values.length || Fn(i, t).some(function (e) {\n                return null == e;\n              })) ? (n.optimisticOps.push(t), t.mutatedParts && Cn(t.mutatedParts), e.then(function (e) {\n                0 < e.numFailures && (q(n.optimisticOps, t), (e = Yn(0, t, e)) && n.optimisticOps.push(e), t.mutatedParts && Cn(t.mutatedParts));\n              }), e.catch(function () {\n                q(n.optimisticOps, t), t.mutatedParts && Cn(t.mutatedParts);\n              })) : e.then(function (r) {\n                var e = Yn(0, _(_({}, t), {\n                  values: t.values.map(function (e, t) {\n                    var n;\n                    if (r.failures[t]) return e;\n                    e = null !== (n = i.keyPath) && void 0 !== n && n.includes(\".\") ? S(e) : _({}, e);\n                    return P(e, i.keyPath, r.results[t]), e;\n                  })\n                }), r);\n                n.optimisticOps.push(e), queueMicrotask(function () {\n                  return t.mutatedParts && Cn(t.mutatedParts);\n                });\n              }), e;\n            },\n            query: function (t) {\n              if (!Vn(me, l) || !zn(\"query\", t)) return l.query(t);\n\n              var i = \"immutable\" === (null === (o = me.trans) || void 0 === o ? void 0 : o.db._options.cache),\n                  e = me,\n                  n = e.requery,\n                  r = e.signal,\n                  o = function (e, t, n, r) {\n                var i = Sn[\"idb://\".concat(e, \"/\").concat(t)];\n                if (!i) return [];\n                if (!(t = i.queries[n])) return [null, !1, i, null];\n                var o = t[(r.query ? r.query.index.name : null) || \"\"];\n                if (!o) return [null, !1, i, null];\n\n                switch (n) {\n                  case \"query\":\n                    var a = o.find(function (e) {\n                      return e.req.limit === r.limit && e.req.values === r.values && Gn(e.req.query.range, r.query.range);\n                    });\n                    return a ? [a, !0, i, o] : [o.find(function (e) {\n                      return (\"limit\" in e.req ? e.req.limit : 1 / 0) >= r.limit && (!r.values || e.req.values) && Xn(e.req.query.range, r.query.range);\n                    }), !1, i, o];\n\n                  case \"count\":\n                    a = o.find(function (e) {\n                      return Gn(e.req.query.range, r.query.range);\n                    });\n                    return [a, !!a, i, o];\n                }\n              }(O, c, \"query\", t),\n                  a = o[0],\n                  e = o[1],\n                  u = o[2],\n                  s = o[3];\n\n              return a && e ? a.obsSet = t.obsSet : (e = l.query(t).then(function (e) {\n                var t = e.result;\n\n                if (a && (a.res = t), i) {\n                  for (var n = 0, r = t.length; n < r; ++n) Object.freeze(t[n]);\n\n                  Object.freeze(t);\n                } else e.result = S(t);\n\n                return e;\n              }).catch(function (e) {\n                return s && a && q(s, a), Promise.reject(e);\n              }), a = {\n                obsSet: t.obsSet,\n                promise: e,\n                subscribers: new Set(),\n                type: \"query\",\n                req: t,\n                dirty: !1\n              }, s ? s.push(a) : (s = [a], (u = u || (Sn[\"idb://\".concat(O, \"/\").concat(c)] = {\n                queries: {\n                  query: {},\n                  count: {}\n                },\n                objs: new Map(),\n                optimisticOps: [],\n                unsignaledParts: {}\n              })).queries.query[t.query.index.name || \"\"] = s)), Hn(a, s, n, r), a.promise.then(function (e) {\n                return {\n                  result: Qn(e.result, t, null == u ? void 0 : u.optimisticOps, l, a, i)\n                };\n              });\n            }\n          });\n        }\n      });\n    }\n  };\n\n  function Zn(e, r) {\n    return new Proxy(e, {\n      get: function (e, t, n) {\n        return \"db\" === t ? r : Reflect.get(e, t, n);\n      }\n    });\n  }\n\n  var er = (tr.prototype.version = function (t) {\n    if (isNaN(t) || t < .1) throw new Y.Type(\"Given version is not a positive number\");\n    if (t = Math.round(10 * t) / 10, this.idbdb || this._state.isBeingOpened) throw new Y.Schema(\"Cannot add version when database is open\");\n    this.verno = Math.max(this.verno, t);\n    var e = this._versions,\n        n = e.filter(function (e) {\n      return e._cfg.version === t;\n    })[0];\n    return n || (n = new this.Version(t), e.push(n), e.sort(nn), n.stores({}), this._state.autoSchema = !1, n);\n  }, tr.prototype._whenReady = function (e) {\n    var n = this;\n    return this.idbdb && (this._state.openComplete || me.letThrough || this._vip) ? e() : new _e(function (e, t) {\n      if (n._state.openComplete) return t(new Y.DatabaseClosed(n._state.dbOpenError));\n\n      if (!n._state.isBeingOpened) {\n        if (!n._state.autoOpen) return void t(new Y.DatabaseClosed());\n        n.open().catch(G);\n      }\n\n      n._state.dbReadyPromise.then(e, t);\n    }).then(e);\n  }, tr.prototype.use = function (e) {\n    var t = e.stack,\n        n = e.create,\n        r = e.level,\n        i = e.name;\n    i && this.unuse({\n      stack: t,\n      name: i\n    });\n    e = this._middlewares[t] || (this._middlewares[t] = []);\n    return e.push({\n      stack: t,\n      create: n,\n      level: null == r ? 10 : r,\n      name: i\n    }), e.sort(function (e, t) {\n      return e.level - t.level;\n    }), this;\n  }, tr.prototype.unuse = function (e) {\n    var t = e.stack,\n        n = e.name,\n        r = e.create;\n    return t && this._middlewares[t] && (this._middlewares[t] = this._middlewares[t].filter(function (e) {\n      return r ? e.create !== r : !!n && e.name !== n;\n    })), this;\n  }, tr.prototype.open = function () {\n    var e = this;\n    return $e(ve, function () {\n      return Dn(e);\n    });\n  }, tr.prototype._close = function () {\n    var n = this._state,\n        e = et.indexOf(this);\n\n    if (0 <= e && et.splice(e, 1), this.idbdb) {\n      try {\n        this.idbdb.close();\n      } catch (e) {}\n\n      this.idbdb = null;\n    }\n\n    n.isBeingOpened || (n.dbReadyPromise = new _e(function (e) {\n      n.dbReadyResolve = e;\n    }), n.openCanceller = new _e(function (e, t) {\n      n.cancelOpen = t;\n    }));\n  }, tr.prototype.close = function (e) {\n    var t = (void 0 === e ? {\n      disableAutoOpen: !0\n    } : e).disableAutoOpen,\n        e = this._state;\n    t ? (e.isBeingOpened && e.cancelOpen(new Y.DatabaseClosed()), this._close(), e.autoOpen = !1, e.dbOpenError = new Y.DatabaseClosed()) : (this._close(), e.autoOpen = this._options.autoOpen || e.isBeingOpened, e.openComplete = !1, e.dbOpenError = null);\n  }, tr.prototype.delete = function (n) {\n    var i = this;\n    void 0 === n && (n = {\n      disableAutoOpen: !0\n    });\n    var o = 0 < arguments.length && \"object\" != typeof arguments[0],\n        a = this._state;\n    return new _e(function (r, t) {\n      function e() {\n        i.close(n);\n\n        var e = i._deps.indexedDB.deleteDatabase(i.name);\n\n        e.onsuccess = qe(function () {\n          var e, t, n;\n          e = i._deps, t = i.name, n = e.indexedDB, e = e.IDBKeyRange, vn(n) || t === tt || yn(n, e).delete(t).catch(G), r();\n        }), e.onerror = Bt(t), e.onblocked = i._fireOnBlocked;\n      }\n\n      if (o) throw new Y.InvalidArgument(\"Invalid closeOptions argument to db.delete()\");\n      a.isBeingOpened ? a.dbReadyPromise.then(e) : e();\n    });\n  }, tr.prototype.backendDB = function () {\n    return this.idbdb;\n  }, tr.prototype.isOpen = function () {\n    return null !== this.idbdb;\n  }, tr.prototype.hasBeenClosed = function () {\n    var e = this._state.dbOpenError;\n    return e && \"DatabaseClosed\" === e.name;\n  }, tr.prototype.hasFailed = function () {\n    return null !== this._state.dbOpenError;\n  }, tr.prototype.dynamicallyOpened = function () {\n    return this._state.autoSchema;\n  }, Object.defineProperty(tr.prototype, \"tables\", {\n    get: function () {\n      var t = this;\n      return x(this._allTables).map(function (e) {\n        return t._allTables[e];\n      });\n    },\n    enumerable: !1,\n    configurable: !0\n  }), tr.prototype.transaction = function () {\n    var e = function (e, t, n) {\n      var r = arguments.length;\n      if (r < 2) throw new Y.InvalidArgument(\"Too few arguments\");\n\n      for (var i = new Array(r - 1); --r;) i[r - 1] = arguments[r];\n\n      return n = i.pop(), [e, w(i), n];\n    }.apply(this, arguments);\n\n    return this._transaction.apply(this, e);\n  }, tr.prototype._transaction = function (e, t, n) {\n    var r = this,\n        i = me.trans;\n    i && i.db === this && -1 === e.indexOf(\"!\") || (i = null);\n    var o,\n        a,\n        u = -1 !== e.indexOf(\"?\");\n    e = e.replace(\"!\", \"\").replace(\"?\", \"\");\n\n    try {\n      if (a = t.map(function (e) {\n        e = e instanceof r.Table ? e.name : e;\n        if (\"string\" != typeof e) throw new TypeError(\"Invalid table argument to Dexie.transaction(). Only Table or String are allowed\");\n        return e;\n      }), \"r\" == e || e === nt) o = nt;else {\n        if (\"rw\" != e && e != rt) throw new Y.InvalidArgument(\"Invalid transaction mode: \" + e);\n        o = rt;\n      }\n\n      if (i) {\n        if (i.mode === nt && o === rt) {\n          if (!u) throw new Y.SubTransaction(\"Cannot enter a sub-transaction with READWRITE mode when parent transaction is READONLY\");\n          i = null;\n        }\n\n        i && a.forEach(function (e) {\n          if (i && -1 === i.storeNames.indexOf(e)) {\n            if (!u) throw new Y.SubTransaction(\"Table \" + e + \" not included in parent transaction.\");\n            i = null;\n          }\n        }), u && i && !i.active && (i = null);\n      }\n    } catch (n) {\n      return i ? i._promise(null, function (e, t) {\n        t(n);\n      }) : Xe(n);\n    }\n\n    var s = function i(o, a, u, s, c) {\n      return _e.resolve().then(function () {\n        var e = me.transless || me,\n            t = o._createTransaction(a, u, o._dbSchema, s);\n\n        if (t.explicit = !0, e = {\n          trans: t,\n          transless: e\n        }, s) t.idbtrans = s.idbtrans;else try {\n          t.create(), t.idbtrans._explicit = !0, o._state.PR1398_maxLoop = 3;\n        } catch (e) {\n          return e.name === z.InvalidState && o.isOpen() && 0 < --o._state.PR1398_maxLoop ? (console.warn(\"Dexie: Need to reopen db\"), o.close({\n            disableAutoOpen: !1\n          }), o.open().then(function () {\n            return i(o, a, u, null, c);\n          })) : Xe(e);\n        }\n        var n,\n            r = B(c);\n        return r && Le(), e = _e.follow(function () {\n          var e;\n          (n = c.call(t, t)) && (r ? (e = Ue.bind(null, null), n.then(e, e)) : \"function\" == typeof n.next && \"function\" == typeof n.throw && (n = In(n)));\n        }, e), (n && \"function\" == typeof n.then ? _e.resolve(n).then(function (e) {\n          return t.active ? e : Xe(new Y.PrematureCommit(\"Transaction committed too early. See http://bit.ly/2kdckMn\"));\n        }) : e.then(function () {\n          return n;\n        })).then(function (e) {\n          return s && t._resolve(), t._completion.then(function () {\n            return e;\n          });\n        }).catch(function (e) {\n          return t._reject(e), Xe(e);\n        });\n      });\n    }.bind(null, this, o, a, i, n);\n\n    return i ? i._promise(o, s, \"lock\") : me.trans ? $e(me.transless, function () {\n      return r._whenReady(s);\n    }) : this._whenReady(s);\n  }, tr.prototype.table = function (e) {\n    if (!m(this._allTables, e)) throw new Y.InvalidTable(\"Table \".concat(e, \" does not exist\"));\n    return this._allTables[e];\n  }, tr);\n\n  function tr(e, t) {\n    var o = this;\n    this._middlewares = {}, this.verno = 0;\n    var n = tr.dependencies;\n    this._options = t = _({\n      addons: tr.addons,\n      autoOpen: !0,\n      indexedDB: n.indexedDB,\n      IDBKeyRange: n.IDBKeyRange,\n      cache: \"cloned\"\n    }, t), this._deps = {\n      indexedDB: t.indexedDB,\n      IDBKeyRange: t.IDBKeyRange\n    };\n    n = t.addons;\n    this._dbSchema = {}, this._versions = [], this._storeNames = [], this._allTables = {}, this.idbdb = null, this._novip = this;\n    var a,\n        r,\n        u,\n        i,\n        s,\n        c = {\n      dbOpenError: null,\n      isBeingOpened: !1,\n      onReadyBeingFired: null,\n      openComplete: !1,\n      dbReadyResolve: G,\n      dbReadyPromise: null,\n      cancelOpen: G,\n      openCanceller: null,\n      autoSchema: !0,\n      PR1398_maxLoop: 3,\n      autoOpen: t.autoOpen\n    };\n    c.dbReadyPromise = new _e(function (e) {\n      c.dbReadyResolve = e;\n    }), c.openCanceller = new _e(function (e, t) {\n      c.cancelOpen = t;\n    }), this._state = c, this.name = e, this.on = dt(this, \"populate\", \"blocked\", \"versionchange\", \"close\", {\n      ready: [re, G]\n    }), this.on.ready.subscribe = p(this.on.ready.subscribe, function (i) {\n      return function (n, r) {\n        tr.vip(function () {\n          var t,\n              e = o._state;\n          e.openComplete ? (e.dbOpenError || _e.resolve().then(n), r && i(n)) : e.onReadyBeingFired ? (e.onReadyBeingFired.push(n), r && i(n)) : (i(n), t = o, r || i(function e() {\n            t.on.ready.unsubscribe(n), t.on.ready.unsubscribe(e);\n          }));\n        });\n      };\n    }), this.Collection = (a = this, pt(Ot.prototype, function (e, t) {\n      this.db = a;\n      var n = ot,\n          r = null;\n      if (t) try {\n        n = t();\n      } catch (e) {\n        r = e;\n      }\n      var i = e._ctx,\n          t = i.table,\n          e = t.hook.reading.fire;\n      this._ctx = {\n        table: t,\n        index: i.index,\n        isPrimKey: !i.index || t.schema.primKey.keyPath && i.index === t.schema.primKey.name,\n        range: n,\n        keysOnly: !1,\n        dir: \"next\",\n        unique: \"\",\n        algorithm: null,\n        filter: null,\n        replayFilter: null,\n        justLimit: !0,\n        isMatch: null,\n        offset: 0,\n        limit: 1 / 0,\n        error: r,\n        or: i.or,\n        valueMapper: e !== X ? e : null\n      };\n    })), this.Table = (r = this, pt(ft.prototype, function (e, t, n) {\n      this.db = r, this._tx = n, this.name = e, this.schema = t, this.hook = r._allTables[e] ? r._allTables[e].hook : dt(null, {\n        creating: [Z, G],\n        reading: [H, X],\n        updating: [te, G],\n        deleting: [ee, G]\n      });\n    })), this.Transaction = (u = this, pt(Lt.prototype, function (e, t, n, r, i) {\n      var o = this;\n      this.db = u, this.mode = e, this.storeNames = t, this.schema = n, this.chromeTransactionDurability = r, this.idbtrans = null, this.on = dt(this, \"complete\", \"error\", \"abort\"), this.parent = i || null, this.active = !0, this._reculock = 0, this._blockedFuncs = [], this._resolve = null, this._reject = null, this._waitingFor = null, this._waitingQueue = null, this._spinCount = 0, this._completion = new _e(function (e, t) {\n        o._resolve = e, o._reject = t;\n      }), this._completion.then(function () {\n        o.active = !1, o.on.complete.fire();\n      }, function (e) {\n        var t = o.active;\n        return o.active = !1, o.on.error.fire(e), o.parent ? o.parent._reject(e) : t && o.idbtrans && o.idbtrans.abort(), Xe(e);\n      });\n    })), this.Version = (i = this, pt(dn.prototype, function (e) {\n      this.db = i, this._cfg = {\n        version: e,\n        storesSource: null,\n        dbschema: {},\n        tables: {},\n        contentUpgrade: null\n      };\n    })), this.WhereClause = (s = this, pt(Dt.prototype, function (e, t, n) {\n      if (this.db = s, this._ctx = {\n        table: e,\n        index: \":id\" === t ? null : t,\n        or: n\n      }, this._cmp = this._ascending = st, this._descending = function (e, t) {\n        return st(t, e);\n      }, this._max = function (e, t) {\n        return 0 < st(e, t) ? e : t;\n      }, this._min = function (e, t) {\n        return st(e, t) < 0 ? e : t;\n      }, this._IDBKeyRange = s._deps.IDBKeyRange, !this._IDBKeyRange) throw new Y.MissingAPI();\n    })), this.on(\"versionchange\", function (e) {\n      0 < e.newVersion ? console.warn(\"Another connection wants to upgrade database '\".concat(o.name, \"'. Closing db now to resume the upgrade.\")) : console.warn(\"Another connection wants to delete database '\".concat(o.name, \"'. Closing db now to resume the delete request.\")), o.close({\n        disableAutoOpen: !1\n      });\n    }), this.on(\"blocked\", function (e) {\n      !e.newVersion || e.newVersion < e.oldVersion ? console.warn(\"Dexie.delete('\".concat(o.name, \"') was blocked\")) : console.warn(\"Upgrade '\".concat(o.name, \"' blocked by other connection holding version \").concat(e.oldVersion / 10));\n    }), this._maxKey = Yt(t.IDBKeyRange), this._createTransaction = function (e, t, n, r) {\n      return new o.Transaction(e, t, n, o._options.chromeTransactionDurability, r);\n    }, this._fireOnBlocked = function (t) {\n      o.on(\"blocked\").fire(t), et.filter(function (e) {\n        return e.name === o.name && e !== o && !e._state.vcFired;\n      }).map(function (e) {\n        return e.on(\"versionchange\").fire(t);\n      });\n    }, this.use(Un), this.use(Jn), this.use(Wn), this.use(Rn), this.use(Nn);\n    var l = new Proxy(this, {\n      get: function (e, t, n) {\n        if (\"_vip\" === t) return !0;\n        if (\"table\" === t) return function (e) {\n          return Zn(o.table(e), l);\n        };\n        var r = Reflect.get(e, t, n);\n        return r instanceof ft ? Zn(r, l) : \"tables\" === t ? r.map(function (e) {\n          return Zn(e, l);\n        }) : \"_createTransaction\" === t ? function () {\n          return Zn(r.apply(this, arguments), l);\n        } : r;\n      }\n    });\n    this.vip = l, n.forEach(function (e) {\n      return e(o);\n    });\n  }\n\n  var nr,\n      M = \"undefined\" != typeof Symbol && \"observable\" in Symbol ? Symbol.observable : \"@@observable\",\n      rr = (ir.prototype.subscribe = function (e, t, n) {\n    return this._subscribe(e && \"function\" != typeof e ? e : {\n      next: e,\n      error: t,\n      complete: n\n    });\n  }, ir.prototype[M] = function () {\n    return this;\n  }, ir);\n\n  function ir(e) {\n    this._subscribe = e;\n  }\n\n  try {\n    nr = {\n      indexedDB: f.indexedDB || f.mozIndexedDB || f.webkitIndexedDB || f.msIndexedDB,\n      IDBKeyRange: f.IDBKeyRange || f.webkitIDBKeyRange\n    };\n  } catch (e) {\n    nr = {\n      indexedDB: null,\n      IDBKeyRange: null\n    };\n  }\n\n  function or(h) {\n    var d,\n        p = !1,\n        e = new rr(function (r) {\n      var i = B(h);\n      var o,\n          a = !1,\n          u = {},\n          s = {},\n          e = {\n        get closed() {\n          return a;\n        },\n\n        unsubscribe: function () {\n          a || (a = !0, o && o.abort(), c && Nt.storagemutated.unsubscribe(f));\n        }\n      };\n      r.start && r.start(e);\n\n      var c = !1,\n          l = function () {\n        return Ge(t);\n      };\n\n      var f = function (e) {\n        Kn(u, e), En(s, u) && l();\n      },\n          t = function () {\n        var t, n, e;\n        !a && nr.indexedDB && (u = {}, t = {}, o && o.abort(), o = new AbortController(), e = function (e) {\n          var t = je();\n\n          try {\n            i && Le();\n            var n = Ne(h, e);\n            return n = i ? n.finally(Ue) : n;\n          } finally {\n            t && Ae();\n          }\n        }(n = {\n          subscr: t,\n          signal: o.signal,\n          requery: l,\n          querier: h,\n          trans: null\n        }), Promise.resolve(e).then(function (e) {\n          p = !0, d = e, a || n.signal.aborted || (u = {}, function (e) {\n            for (var t in e) if (m(e, t)) return;\n\n            return 1;\n          }(s = t) || c || (Nt(Mt, f), c = !0), Ge(function () {\n            return !a && r.next && r.next(e);\n          }));\n        }, function (e) {\n          p = !1, [\"DatabaseClosedError\", \"AbortError\"].includes(null == e ? void 0 : e.name) || a || Ge(function () {\n            a || r.error && r.error(e);\n          });\n        }));\n      };\n\n      return setTimeout(l, 0), e;\n    });\n    return e.hasValue = function () {\n      return p;\n    }, e.getValue = function () {\n      return d;\n    }, e;\n  }\n\n  var ar = er;\n\n  function ur(e) {\n    var t = cr;\n\n    try {\n      cr = !0, Nt.storagemutated.fire(e), Tn(e, !0);\n    } finally {\n      cr = t;\n    }\n  }\n\n  r(ar, _(_({}, Q), {\n    delete: function (e) {\n      return new ar(e, {\n        addons: []\n      }).delete();\n    },\n    exists: function (e) {\n      return new ar(e, {\n        addons: []\n      }).open().then(function (e) {\n        return e.close(), !0;\n      }).catch(\"NoSuchDatabaseError\", function () {\n        return !1;\n      });\n    },\n    getDatabaseNames: function (e) {\n      try {\n        return t = ar.dependencies, n = t.indexedDB, t = t.IDBKeyRange, (vn(n) ? Promise.resolve(n.databases()).then(function (e) {\n          return e.map(function (e) {\n            return e.name;\n          }).filter(function (e) {\n            return e !== tt;\n          });\n        }) : yn(n, t).toCollection().primaryKeys()).then(e);\n      } catch (e) {\n        return Xe(new Y.MissingAPI());\n      }\n\n      var t, n;\n    },\n    defineClass: function () {\n      return function (e) {\n        a(this, e);\n      };\n    },\n    ignoreTransaction: function (e) {\n      return me.trans ? $e(me.transless, e) : e();\n    },\n    vip: mn,\n    async: function (t) {\n      return function () {\n        try {\n          var e = In(t.apply(this, arguments));\n          return e && \"function\" == typeof e.then ? e : _e.resolve(e);\n        } catch (e) {\n          return Xe(e);\n        }\n      };\n    },\n    spawn: function (e, t, n) {\n      try {\n        var r = In(e.apply(n, t || []));\n        return r && \"function\" == typeof r.then ? r : _e.resolve(r);\n      } catch (e) {\n        return Xe(e);\n      }\n    },\n    currentTransaction: {\n      get: function () {\n        return me.trans || null;\n      }\n    },\n    waitFor: function (e, t) {\n      t = _e.resolve(\"function\" == typeof e ? ar.ignoreTransaction(e) : e).timeout(t || 6e4);\n      return me.trans ? me.trans.waitFor(t) : t;\n    },\n    Promise: _e,\n    debug: {\n      get: function () {\n        return ie;\n      },\n      set: function (e) {\n        oe(e);\n      }\n    },\n    derive: o,\n    extend: a,\n    props: r,\n    override: p,\n    Events: dt,\n    on: Nt,\n    liveQuery: or,\n    extendObservabilitySet: Kn,\n    getByKeyPath: O,\n    setByKeyPath: P,\n    delByKeyPath: function (t, e) {\n      \"string\" == typeof e ? P(t, e, void 0) : \"length\" in e && [].map.call(e, function (e) {\n        P(t, e, void 0);\n      });\n    },\n    shallowClone: g,\n    deepClone: S,\n    getObjectDiff: Mn,\n    cmp: st,\n    asap: v,\n    minKey: -1 / 0,\n    addons: [],\n    connections: et,\n    errnames: z,\n    dependencies: nr,\n    cache: Sn,\n    semVer: \"4.0.10\",\n    version: \"4.0.10\".split(\".\").map(function (e) {\n      return parseInt(e);\n    }).reduce(function (e, t, n) {\n      return e + t / Math.pow(10, 2 * n);\n    })\n  })), ar.maxKey = Yt(ar.dependencies.IDBKeyRange), \"undefined\" != typeof dispatchEvent && \"undefined\" != typeof addEventListener && (Nt(Mt, function (e) {\n    cr || (e = new CustomEvent(Ft, {\n      detail: e\n    }), cr = !0, dispatchEvent(e), cr = !1);\n  }), addEventListener(Ft, function (e) {\n    e = e.detail;\n    cr || ur(e);\n  }));\n\n  var sr,\n      cr = !1,\n      lr = function () {};\n\n  return \"undefined\" != typeof BroadcastChannel && ((lr = function () {\n    (sr = new BroadcastChannel(Ft)).onmessage = function (e) {\n      return e.data && ur(e.data);\n    };\n  })(), \"function\" == typeof sr.unref && sr.unref(), Nt(Mt, function (e) {\n    cr || sr.postMessage(e);\n  })), \"undefined\" != typeof addEventListener && (addEventListener(\"pagehide\", function (e) {\n    if (!er.disableBfCache && e.persisted) {\n      ie && console.debug(\"Dexie: handling persisted pagehide\"), null != sr && sr.close();\n\n      for (var t = 0, n = et; t < n.length; t++) n[t].close({\n        disableAutoOpen: !1\n      });\n    }\n  }), addEventListener(\"pageshow\", function (e) {\n    !er.disableBfCache && e.persisted && (ie && console.debug(\"Dexie: handling persisted pageshow\"), lr(), ur({\n      all: new gn(-1 / 0, [[]])\n    }));\n  })), _e.rejectionMapper = function (e, t) {\n    return !e || e instanceof N || e instanceof TypeError || e instanceof SyntaxError || !e.name || !$[e.name] ? e : (t = new $[e.name](t || e.message, e), \"stack\" in e && l(t, \"stack\", {\n      get: function () {\n        return this.inner.stack;\n      }\n    }), t);\n  }, oe(ie), _(er, Object.freeze({\n    __proto__: null,\n    Dexie: er,\n    liveQuery: or,\n    Entity: ut,\n    cmp: st,\n    PropModSymbol: e,\n    PropModification: xt,\n    replacePrefix: function (e, t) {\n      return new xt({\n        replacePrefix: [e, t]\n      });\n    },\n    add: function (e) {\n      return new xt({\n        add: e\n      });\n    },\n    remove: function (e) {\n      return new xt({\n        remove: e\n      });\n    },\n    default: er,\n    RangeSet: gn,\n    mergeRanges: _n,\n    rangesOverlap: xn\n  }), {\n    default: er\n  }), er;\n}); //# sourceMappingURL=dexie.min.js.map", "map": null, "metadata": {}, "sourceType": "script"}