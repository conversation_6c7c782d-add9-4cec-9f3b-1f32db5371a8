{"ast": null, "code": "import { Observable } from '../Observable';\nexport function throwError(error, scheduler) {\n  if (!scheduler) {\n    return new Observable(subscriber => subscriber.error(error));\n  } else {\n    return new Observable(subscriber => scheduler.schedule(dispatch, 0, {\n      error,\n      subscriber\n    }));\n  }\n}\n\nfunction dispatch({\n  error,\n  subscriber\n}) {\n  subscriber.error(error);\n} //# sourceMappingURL=throwError.js.map", "map": null, "metadata": {}, "sourceType": "module"}