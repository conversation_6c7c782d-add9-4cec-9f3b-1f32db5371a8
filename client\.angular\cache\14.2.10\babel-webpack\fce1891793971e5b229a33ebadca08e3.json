{"ast": null, "code": "import { async } from '../scheduler/async';\nimport { isDate } from '../util/isDate';\nimport { SimpleOuterSubscriber, innerSubscribe, SimpleInnerSubscriber } from '../innerSubscribe';\nexport function timeoutWith(due, withObservable, scheduler = async) {\n  return source => {\n    let absoluteTimeout = isDate(due);\n    let waitFor = absoluteTimeout ? +due - scheduler.now() : Math.abs(due);\n    return source.lift(new TimeoutWithOperator(waitFor, absoluteTimeout, withObservable, scheduler));\n  };\n}\n\nclass TimeoutWithOperator {\n  constructor(waitFor, absoluteTimeout, withObservable, scheduler) {\n    this.waitFor = waitFor;\n    this.absoluteTimeout = absoluteTimeout;\n    this.withObservable = withObservable;\n    this.scheduler = scheduler;\n  }\n\n  call(subscriber, source) {\n    return source.subscribe(new TimeoutWithSubscriber(subscriber, this.absoluteTimeout, this.waitFor, this.withObservable, this.scheduler));\n  }\n\n}\n\nclass TimeoutWithSubscriber extends SimpleOuterSubscriber {\n  constructor(destination, absoluteTimeout, waitFor, withObservable, scheduler) {\n    super(destination);\n    this.absoluteTimeout = absoluteTimeout;\n    this.waitFor = waitFor;\n    this.withObservable = withObservable;\n    this.scheduler = scheduler;\n    this.scheduleTimeout();\n  }\n\n  static dispatchTimeout(subscriber) {\n    const {\n      withObservable\n    } = subscriber;\n\n    subscriber._unsubscribeAndRecycle();\n\n    subscriber.add(innerSubscribe(withObservable, new SimpleInnerSubscriber(subscriber)));\n  }\n\n  scheduleTimeout() {\n    const {\n      action\n    } = this;\n\n    if (action) {\n      this.action = action.schedule(this, this.waitFor);\n    } else {\n      this.add(this.action = this.scheduler.schedule(TimeoutWithSubscriber.dispatchTimeout, this.waitFor, this));\n    }\n  }\n\n  _next(value) {\n    if (!this.absoluteTimeout) {\n      this.scheduleTimeout();\n    }\n\n    super._next(value);\n  }\n\n  _unsubscribe() {\n    this.action = undefined;\n    this.scheduler = null;\n    this.withObservable = null;\n  }\n\n} //# sourceMappingURL=timeoutWith.js.map", "map": null, "metadata": {}, "sourceType": "module"}