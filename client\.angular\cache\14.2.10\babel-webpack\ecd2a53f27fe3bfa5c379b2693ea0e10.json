{"ast": null, "code": "var hash = {\n  left: 'right',\n  right: 'left',\n  bottom: 'top',\n  top: 'bottom'\n};\nexport default function getOppositePlacement(placement) {\n  return placement.replace(/left|right|bottom|top/g, function (matched) {\n    return hash[matched];\n  });\n}", "map": {"version": 3, "names": ["hash", "left", "right", "bottom", "top", "getOppositePlacement", "placement", "replace", "matched"], "sources": ["D:/work/joyserver/client/node_modules/@popperjs/core/lib/utils/getOppositePlacement.js"], "sourcesContent": ["var hash = {\n  left: 'right',\n  right: 'left',\n  bottom: 'top',\n  top: 'bottom'\n};\nexport default function getOppositePlacement(placement) {\n  return placement.replace(/left|right|bottom|top/g, function (matched) {\n    return hash[matched];\n  });\n}"], "mappings": "AAAA,IAAIA,IAAI,GAAG;EACTC,IAAI,EAAE,OADG;EAETC,KAAK,EAAE,MAFE;EAGTC,MAAM,EAAE,KAHC;EAITC,GAAG,EAAE;AAJI,CAAX;AAMA,eAAe,SAASC,oBAAT,CAA8BC,SAA9B,EAAyC;EACtD,OAAOA,SAAS,CAACC,OAAV,CAAkB,wBAAlB,EAA4C,UAAUC,OAAV,EAAmB;IACpE,OAAOR,IAAI,CAACQ,OAAD,CAAX;EACD,CAFM,CAAP;AAGD"}, "metadata": {}, "sourceType": "module"}