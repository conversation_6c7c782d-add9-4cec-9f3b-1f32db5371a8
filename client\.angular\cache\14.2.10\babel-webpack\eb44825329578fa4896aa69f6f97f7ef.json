{"ast": null, "code": "import { createOr } from '../../function/logical/or.js';\nimport { factory } from '../../utils/factory.js';\nimport { isCollection } from '../../utils/is.js';\nvar name = 'or';\nvar dependencies = ['typed', 'matrix', 'equalScalar', 'DenseMatrix', 'concat'];\nexport var createOrTransform = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed,\n    matrix,\n    equalScalar,\n    DenseMatrix,\n    concat\n  } = _ref;\n  var or = createOr({\n    typed,\n    matrix,\n    equalScalar,\n    DenseMatrix,\n    concat\n  });\n\n  function orTransform(args, math, scope) {\n    var condition1 = args[0].compile().evaluate(scope);\n\n    if (!isCollection(condition1) && or(condition1, false)) {\n      return true;\n    }\n\n    var condition2 = args[1].compile().evaluate(scope);\n    return or(condition1, condition2);\n  }\n\n  orTransform.rawArgs = true;\n  return orTransform;\n}, {\n  isTransformFunction: true\n});", "map": {"version": 3, "names": ["createOr", "factory", "isCollection", "name", "dependencies", "createOrTransform", "_ref", "typed", "matrix", "equalScalar", "DenseMatrix", "concat", "or", "orTransform", "args", "math", "scope", "condition1", "compile", "evaluate", "condition2", "rawArgs", "isTransformFunction"], "sources": ["D:/work/joyserver/client/node_modules/mathjs/lib/esm/expression/transform/or.transform.js"], "sourcesContent": ["import { createOr } from '../../function/logical/or.js';\nimport { factory } from '../../utils/factory.js';\nimport { isCollection } from '../../utils/is.js';\nvar name = 'or';\nvar dependencies = ['typed', 'matrix', 'equalScalar', 'DenseMatrix', 'concat'];\nexport var createOrTransform = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed,\n    matrix,\n    equalScalar,\n    DenseMatrix,\n    concat\n  } = _ref;\n  var or = createOr({\n    typed,\n    matrix,\n    equalScalar,\n    DenseMatrix,\n    concat\n  });\n  function orTransform(args, math, scope) {\n    var condition1 = args[0].compile().evaluate(scope);\n    if (!isCollection(condition1) && or(condition1, false)) {\n      return true;\n    }\n    var condition2 = args[1].compile().evaluate(scope);\n    return or(condition1, condition2);\n  }\n  orTransform.rawArgs = true;\n  return orTransform;\n}, {\n  isTransformFunction: true\n});"], "mappings": "AAAA,SAASA,QAAT,QAAyB,8BAAzB;AACA,SAASC,OAAT,QAAwB,wBAAxB;AACA,SAASC,YAAT,QAA6B,mBAA7B;AACA,IAAIC,IAAI,GAAG,IAAX;AACA,IAAIC,YAAY,GAAG,CAAC,OAAD,EAAU,QAAV,EAAoB,aAApB,EAAmC,aAAnC,EAAkD,QAAlD,CAAnB;AACA,OAAO,IAAIC,iBAAiB,GAAG,eAAeJ,OAAO,CAACE,IAAD,EAAOC,YAAP,EAAqBE,IAAI,IAAI;EAChF,IAAI;IACFC,KADE;IAEFC,MAFE;IAGFC,WAHE;IAIFC,WAJE;IAKFC;EALE,IAMAL,IANJ;EAOA,IAAIM,EAAE,GAAGZ,QAAQ,CAAC;IAChBO,KADgB;IAEhBC,MAFgB;IAGhBC,WAHgB;IAIhBC,WAJgB;IAKhBC;EALgB,CAAD,CAAjB;;EAOA,SAASE,WAAT,CAAqBC,IAArB,EAA2BC,IAA3B,EAAiCC,KAAjC,EAAwC;IACtC,IAAIC,UAAU,GAAGH,IAAI,CAAC,CAAD,CAAJ,CAAQI,OAAR,GAAkBC,QAAlB,CAA2BH,KAA3B,CAAjB;;IACA,IAAI,CAACd,YAAY,CAACe,UAAD,CAAb,IAA6BL,EAAE,CAACK,UAAD,EAAa,KAAb,CAAnC,EAAwD;MACtD,OAAO,IAAP;IACD;;IACD,IAAIG,UAAU,GAAGN,IAAI,CAAC,CAAD,CAAJ,CAAQI,OAAR,GAAkBC,QAAlB,CAA2BH,KAA3B,CAAjB;IACA,OAAOJ,EAAE,CAACK,UAAD,EAAaG,UAAb,CAAT;EACD;;EACDP,WAAW,CAACQ,OAAZ,GAAsB,IAAtB;EACA,OAAOR,WAAP;AACD,CAzBoD,EAyBlD;EACDS,mBAAmB,EAAE;AADpB,CAzBkD,CAA9C"}, "metadata": {}, "sourceType": "module"}