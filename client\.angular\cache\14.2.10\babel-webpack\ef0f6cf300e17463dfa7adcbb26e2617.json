{"ast": null, "code": "export var kronDocs = {\n  name: 'kron',\n  category: 'Matrix',\n  syntax: ['kron(x, y)'],\n  description: 'Calculates the <PERSON>ronecker product of 2 matrices or vectors.',\n  examples: ['kron([[1, 0], [0, 1]], [[1, 2], [3, 4]])', 'kron([1,1], [2,3,4])'],\n  seealso: ['multiply', 'dot', 'cross']\n};", "map": {"version": 3, "names": ["kronDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["D:/work/joyserver/client/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/matrix/kron.js"], "sourcesContent": ["export var kronDocs = {\n  name: 'kron',\n  category: 'Matrix',\n  syntax: ['kron(x, y)'],\n  description: 'Calculates the <PERSON>ronecker product of 2 matrices or vectors.',\n  examples: ['kron([[1, 0], [0, 1]], [[1, 2], [3, 4]])', 'kron([1,1], [2,3,4])'],\n  seealso: ['multiply', 'dot', 'cross']\n};"], "mappings": "AAAA,OAAO,IAAIA,QAAQ,GAAG;EACpBC,IAAI,EAAE,MADc;EAEpBC,QAAQ,EAAE,QAFU;EAGpBC,MAAM,EAAE,CAAC,YAAD,CAHY;EAIpBC,WAAW,EAAE,4DAJO;EAKpBC,QAAQ,EAAE,CAAC,0CAAD,EAA6C,sBAA7C,CALU;EAMpBC,OAAO,EAAE,CAAC,UAAD,EAAa,KAAb,EAAoB,OAApB;AANW,CAAf"}, "metadata": {}, "sourceType": "module"}