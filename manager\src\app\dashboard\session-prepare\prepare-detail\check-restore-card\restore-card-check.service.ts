import { Injectable, <PERSON><PERSON><PERSON>roy } from "@angular/core";
import { BehaviorSubject, Observable } from "rxjs";
import { map, shareReplay, distinctUntilChanged } from "rxjs/operators";
import { SettingService } from "@app/core/http/setting.service";
import { SocketService } from "@app/core/services/socket.service";
import { RestoreCardAction, RestoreCardActions, RestoreCardCheckStatus } from "@app/core/io/io.message.consts";
import * as M from "@app/core/io";
import {
  RestoreCardServiceState,
  ClientState,
  SaveCheckResultTrigger,
  RestoreCardClientInfo,
  RestoreCardCheckInfo,
  RestoreCardClientDisplay,
  RestoreCardWebSocketMessage,
  ClientCheckResult,
  RestoreCardBatchResult,
} from "./restore-card-check.types";
import { RestoreCardDataAdapter } from "./restore-card-data.adapter";

@Injectable()
export class RestoreCardCheckService implements OnD<PERSON>roy {
  private serviceState = new BehaviorSubject<RestoreCardServiceState>({
    sessionId: "",
    clientStates: new Map<number, ClientState>(),
    counts: {
      success: 0,
      failed: 0,
      total: 0,
    },
    isLoading: false,
    lastRefresh: 0,
  });

  private clientCheckModal = new BehaviorSubject<boolean>(false);
  private saveCheckResultTrigger = new BehaviorSubject<SaveCheckResultTrigger>({ trigger: false });
  private singleClientLoadingStates = new Map<number, BehaviorSubject<boolean>>();

  // 自动保存相关属性
  private autoSaveTimer: any;
  private readonly AUTO_SAVE_INTERVAL = 10000; // 5秒间隔
  private readonly MIN_SAVE_INTERVAL = 3000; // 最小保存间隔3秒，防止频繁保存

  // 超时重试相关属性
  private timeoutRetryTimers = new Map<number, any>(); // 存储每个客户端的重试定时器
  private timeoutRetryAttempts = new Map<number, number>(); // 存储每个客户端的重试次数
  private readonly RETRY_INTERVAL = 45000; // 45秒重试间隔
  private readonly MAX_RETRY_ATTEMPTS = 10; // 最大重试次数
  private lastSaveTime = 0;

  public serviceState$ = this.serviceState.asObservable();
  public clientCheckModal$ = this.clientCheckModal.asObservable();
  public saveCheckResultTrigger$ = this.saveCheckResultTrigger.asObservable();

  public clientList$: Observable<RestoreCardClientDisplay[]> = this.serviceState$.pipe(
    map((state) => this.transformAllClientStates(state)),
    distinctUntilChanged(),
    shareReplay(1)
  );

  public loading$ = this.serviceState$.pipe(
    map((state) => state.isLoading),
    distinctUntilChanged(),
    shareReplay(1)
  );

  public counts$ = this.serviceState$.pipe(
    map((state) => state.counts),
    distinctUntilChanged(),
    shareReplay(1)
  );

  constructor(private settingService: SettingService, private socketService: SocketService) {
    this.startAutoSave();
  }

  ngOnDestroy(): void {
    console.log("RestoreCardCheckService: Destroying service, cleaning up resources");

    this.stopAutoSave();
    this.stopAllTimeoutRetries();
    this.serviceState.complete();
    this.clientCheckModal.complete();
    this.saveCheckResultTrigger.complete();
    this.singleClientLoadingStates.forEach((subject) => subject.complete());
    this.singleClientLoadingStates.clear();
  }

  private isRestoreCardBatchResponse(response: any): response is { info: RestoreCardBatchResult } {
    return response && response.info && Array.isArray(response.info.results);
  }

  private isVerifyResponse(response: any): response is { info: { clients: ClientCheckResult[] } } {
    return response && response.info && Array.isArray(response.info.clients);
  }

  // 模态框控制方法
  openClientCheckModal(): void {
    this.clientCheckModal.next(true);
  }

  closeClientCheckModal(): void {
    this.clientCheckModal.next(false);
  }

  // 数据更新方法
  setSessionId(sessionId: string): void {
    this.updateServiceState({ sessionId });
    // 重启自动保存以确保新会话的数据安全
    this.startAutoSave();
  }

  updateClientList(clientStates: ClientState[]): void {
    try {
      // 直接处理ClientState数组，无需格式转换
      const newClientStatesMap = new Map<number, ClientState>();

      clientStates.forEach((clientState) => {
        if (RestoreCardDataAdapter.validateClientState(clientState)) {
          newClientStatesMap.set(clientState.clientInfo.seat_number, clientState);
        }
      });

      // 更新客户端状态映射

      this.updateServiceState({
        clientStates: newClientStatesMap,
        lastRefresh: Date.now(),
      });

      // 使用全量重新计算（因为是批量更新）
      this.updateCountsFullRecalculation();
      this.checkAutoSave();
    } catch (error) {
      console.error("RestoreCardCheckService: Error updating client list:", error);
    }
  }

  // 保留全量重新计算方法，用于批量更新场景
  private updateCountsFullRecalculation(): void {
    const currentState = this.serviceState.getValue();
    const clientStates = Array.from(currentState.clientStates.values());

    const success = clientStates.filter((state) => state.checkInfo.status === "Success").length;
    const failed = clientStates.filter((state) => state.checkInfo.status === "Failed").length;
    const timeout = clientStates.filter((state) => state.checkInfo.status === "Timeout").length;
    const total = clientStates.length;

    this.updateServiceState({
      counts: {
        success,
        failed: failed + timeout, // 将timeout也计入failed
        total,
      },
    });

    console.log(
      `RestoreCardCheckService: Full count recalculation - Success: ${success}, Failed: ${
        failed + timeout
      }, Total: ${total}`
    );
  }

  // 批量状态更新方法 - 性能优化核心
  public updateMultipleClientStatuses(
    updates: Array<{
      seatNumber: number;
      status: RestoreCardCheckStatus;
      statusText?: string;
    }>
  ): void {
    if (updates.length === 0) return;

    const currentState = this.serviceState.getValue();
    const newClientStates = new Map(currentState.clientStates);
    let hasChanges = false;

    // 批量处理所有更新
    updates.forEach(({ seatNumber, status, statusText }) => {
      const existing = newClientStates.get(seatNumber);
      if (existing) {
        const updatedState = RestoreCardDataAdapter.mergeClientState(existing, {
          checkInfo: {
            ...existing.checkInfo,
            status,
            statusText: statusText || this.getStatusText(status),
          },
          lastUpdated: Date.now(),
        });
        newClientStates.set(seatNumber, updatedState);
        hasChanges = true;

        // 检查是否需要启动超时重试
        this.checkTimeoutRetry(seatNumber, status);
      }
    });

    if (hasChanges) {
      // 一次性更新状态
      this.updateServiceState({
        clientStates: newClientStates,
        lastRefresh: Date.now(),
      });

      // 批量更新后重新计算计数
      this.updateCountsFullRecalculation();
      this.checkAutoSave();

      console.log(`RestoreCardCheckService: Batch updated ${updates.length} client statuses`);
    }
  }

  checkClient(): void {
    console.log("CheckRestoreCard: Check Client restore card");
    const currentSessionId = this.serviceState.getValue().sessionId;

    this.updateServiceState({ isLoading: true });
    this.settingService.restoreCardCheck("client", currentSessionId, ["write", "reboot", "verify"]).subscribe((res) => {
      console.log("CheckRestoreCard: Client restore card response:", res);
      this.updateServiceState({ isLoading: false });
    });
  }

  checkClientWithRefresh(): void {
    console.log("CheckRestoreCard: Check Client with refresh - Starting refresh and check process");
    const currentSessionId = this.serviceState.getValue().sessionId;

    this.updateServiceState({ isLoading: true });
    console.log("CheckRestoreCard: Set loading state to true, starting refresh...");

    // 首先刷新最新状态
    this.settingService.restoreCardCheck("client", currentSessionId, "verify").subscribe(
      (refreshRes) => {
        console.log("CheckRestoreCard: Client verify response:", refreshRes);

        if (this.isVerifyResponse(refreshRes)) {
          this.updateClientListFromServerData(refreshRes.info.clients);
        }

        // 刷新完成后执行检查
        const currentState = this.serviceState.getValue();
        const notCheckedSeats = this.getNotCheckedClientSeats();

        // 如果没有客户端状态数据（首次检查），直接执行原有逻辑
        if (currentState.clientStates.size === 0) {
          console.log("CheckRestoreCard: No client state data, performing initial check");
        } else if (notCheckedSeats.length === 0) {
          console.log("CheckRestoreCard: All clients already checked");
          this.updateServiceState({ isLoading: false });
          return;
        } else {
          console.log(`CheckRestoreCard: Starting check for ${notCheckedSeats.length} not checked clients`);
        }
        this.settingService
          .restoreCardCheck("client", currentSessionId, ["write", "reboot", "verify"])
          .subscribe((checkRes) => {
            console.log("CheckRestoreCard: Client restore card response:", checkRes);
            this.updateServiceState({ isLoading: false });
          });
      },
      (error) => {
        console.error("CheckRestoreCard: Failed to refresh client status:", error);
        this.updateServiceState({ isLoading: false });
      }
    );
  }

  checkClientVerify(): void {
    console.log("CheckRestoreCard: Check Client Verify");
    const currentSessionId = this.serviceState.getValue().sessionId;

    this.updateServiceState({ isLoading: true });
    this.settingService.restoreCardCheck("client", currentSessionId, "verify").subscribe((res) => {
      console.log("CheckRestoreCard: Client verify response:", res);
      this.updateServiceState({ isLoading: false });

      if (this.isVerifyResponse(res)) {
        this.updateClientListFromServerData(res.info.clients);
      }
    });
  }

  // 新增：批量操作方法
  checkClientBatch(actions: RestoreCardAction[]): Observable<any> {
    console.log("CheckRestoreCard: Check Client Batch", actions);
    const currentSessionId = this.serviceState.getValue().sessionId;

    this.updateServiceState({ isLoading: true });
    return this.settingService.restoreCardCheck("client", currentSessionId, actions);
  }

  // 新增：reboot专用方法
  checkClientReboot(): void {
    console.log("CheckRestoreCard: Check Client Reboot");
    const currentSessionId = this.serviceState.getValue().sessionId;

    this.updateServiceState({ isLoading: true });
    this.settingService.restoreCardCheck("client", currentSessionId, "reboot").subscribe((res) => {
      console.log("CheckRestoreCard: Client reboot response:", res);
      this.updateServiceState({ isLoading: false });
      if (res.status === "success") {
        console.log("CheckRestoreCard: Client reboot success");
      } else {
        console.error("CheckRestoreCard: Client reboot failed:", res);
      }
    });
  }

  checkManagerReboot(): void {
    console.log("CheckRestoreCard: Check Manager Reboot");
    const currentSessionId = this.serviceState.getValue().sessionId;

    this.settingService.restoreCardCheck("manager", currentSessionId, "reboot").subscribe((res) => {
      console.log("CheckRestoreCard: Manager reboot response:", res);
      // Manager reboot不支持，会返回错误
      if (res.status === "failed") {
        console.warn("CheckRestoreCard: Manager reboot not supported");
      }
    });
  }

  // 新增：写入后重启流程
  checkClientWriteAndReboot(): void {
    console.log("CheckRestoreCard: Check Client Write and Reboot");
    const currentSessionId = this.serviceState.getValue().sessionId;

    this.updateServiceState({ isLoading: true });
    this.settingService.restoreCardCheck("client", currentSessionId, ["write", "reboot"]).subscribe((res) => {
      console.log("CheckRestoreCard: Client write and reboot response:", res);
      this.updateServiceState({ isLoading: false });

      if (res.status === "success") {
        if (this.isRestoreCardBatchResponse(res)) {
          // 处理批量操作结果
          const results = res.info.results;
          const writeResult = results.find((r) => r.action === "write");
          const rebootResult = results.find((r) => r.action === "reboot");

          if (writeResult && writeResult.status === "success") {
            console.log("CheckRestoreCard: Client write success");
          }

          if (rebootResult && rebootResult.status === "success") {
            console.log("CheckRestoreCard: Client reboot success");
          }
        } else {
          console.log("CheckRestoreCard: Operation completed successfully");
        }
      } else {
        // 处理失败情况
        console.error("CheckRestoreCard: Client write and reboot failed:", res);
      }
    });
  }

  // 单个客户端检查方法
  checkSingleClient(seatNumber: number, actions: RestoreCardActions): void {
    // 检查客户端是否在线
    if (!this.canPerformCheck(seatNumber)) {
      console.warn(`RestoreCardCheckService: Cannot check offline client ${seatNumber}`);
      return;
    }

    console.log(`CheckRestoreCard: Check single client ${seatNumber}`, actions);
    const currentSessionId = this.serviceState.getValue().sessionId;

    this.setSingleClientLoading(seatNumber, true);
    this.settingService.restoreCardCheck("client", currentSessionId, actions, [seatNumber]).subscribe(
      (res) => {
        console.log(`CheckRestoreCard: Single client ${seatNumber} response:`, res);
        setTimeout(() => {
          this.setSingleClientLoading(seatNumber, false);
        }, 1000);
      },
      (error) => {
        console.error(`CheckRestoreCard: Single client ${seatNumber} failed:`, error);
        setTimeout(() => {
          this.setSingleClientLoading(seatNumber, false);
        }, 1000);
      }
    );
  }

  // 新增：获取单个客户端加载状态
  isSingleClientLoading(seatNumber: number): Observable<boolean> {
    if (!this.singleClientLoadingStates.has(seatNumber)) {
      this.singleClientLoadingStates.set(seatNumber, new BehaviorSubject<boolean>(false));
    }
    return this.singleClientLoadingStates.get(seatNumber)!.asObservable();
  }

  // 新增：设置单个客户端加载状态
  private setSingleClientLoading(seatNumber: number, loading: boolean): void {
    if (!this.singleClientLoadingStates.has(seatNumber)) {
      this.singleClientLoadingStates.set(seatNumber, new BehaviorSubject<boolean>(false));
    }
    this.singleClientLoadingStates.get(seatNumber)!.next(loading);
  }

  public updateClientStatus(seatNumber: number, status: RestoreCardCheckStatus): void {
    this.updateClientCheckStatus(seatNumber, {
      status,
      statusText: this.getStatusText(status),
    });

    // 检查是否需要启动超时重试
    this.checkTimeoutRetry(seatNumber, status);

    // 检查是否需要自动保存
    this.checkAutoSave();
  }

  public getClientStatus(seatNumber: number): RestoreCardCheckStatus {
    const clientState = this.getClientState(seatNumber);
    return clientState?.checkInfo.status || "NotChecked";
  }

  public resetAllStatuses(): void {
    const currentState = this.serviceState.getValue();
    const newClientStates = new Map<number, ClientState>();

    currentState.clientStates.forEach((state, seatNumber) => {
      const resetState = RestoreCardDataAdapter.mergeClientState(state, {
        checkInfo: {
          status: "NotChecked" as RestoreCardCheckStatus,
          statusText: "未检查",
        },
        lastUpdated: Date.now(),
      });
      newClientStates.set(seatNumber, resetState);

      // 停止该客户端的超时重试
      this.stopTimeoutRetry(seatNumber);
    });

    this.updateServiceState({
      clientStates: newClientStates,
      lastRefresh: Date.now(),
    });
    this.updateCountsFullRecalculation();

    // 重置保存时间，允许立即触发智能保存
    this.lastSaveTime = 0;
  }

  // WebSocket 消息处理
  public handleRestoreCardUpdate(data: RestoreCardWebSocketMessage): void {
    console.log("RestoreCardCheckService: Received status update", data);

    if (data.session_id === this.serviceState.getValue().sessionId) {
      // 使用数据适配器处理服务器更新
      this.updateClientListFromServerData(data.clients);
    }
  }

  private updateClientListFromServerData(clients: ClientCheckResult[]): void {
    try {
      const currentState = this.serviceState.getValue();
      const newClientStates = new Map(currentState.clientStates);

      const serverDataMap = new Map<number, ClientCheckResult>();
      clients.forEach((client, index) => {
        if (RestoreCardDataAdapter.validateServerData(client)) {
          const seatNumber = client.seatNumber;
          serverDataMap.set(seatNumber, client);
        } else {
          console.error(`RestoreCardCheckService: Invalid server data at index ${index}:`, {
            client,
            expectedFields: ["seatNumber", "status", "ip"],
          });
        }
      });

      // 合并现有数据和服务器数据
      serverDataMap.forEach((serverData, seatNumber) => {
        const existingState = newClientStates.get(seatNumber);
        if (existingState) {
          try {
            const serverUpdate = RestoreCardDataAdapter.fromServerUpdate(serverData);
            const updatedState = RestoreCardDataAdapter.mergeClientState(existingState, serverUpdate);
            newClientStates.set(seatNumber, updatedState);
          } catch (error) {
            console.error(`RestoreCardCheckService: Error updating client ${seatNumber}:`, error);
          }
        }
      });

      this.updateServiceState({
        clientStates: newClientStates,
        lastRefresh: Date.now(),
      });

      this.updateCountsFullRecalculation();
    } catch (error) {
      console.error("RestoreCardCheckService: Error updating client list from server data:", error);
    }
  }

  private mapStatusToDisplay(status: RestoreCardCheckStatus): string {
    const statusMap: { [key in RestoreCardCheckStatus]: string } = {
      NotChecked: "NotChecked",
      Writing: "Writing",
      Restarting: "Restarting",
      Verifying: "Verifying",
      Success: "Success",
      Failed: "Failed",
      Timeout: "Timeout",
    };
    return statusMap[status];
  }

  private getStatusText(status: RestoreCardCheckStatus): string {
    const textMap: { [key in RestoreCardCheckStatus]: string } = {
      NotChecked: "未检查",
      Writing: "检查中",
      Restarting: "重启中",
      Verifying: "验证中",
      Success: "检查成功",
      Failed: "检查失败",
      Timeout: "超时",
    };
    return textMap[status] || "未知状态";
  }

  // 新增：保存检查结果通知方法
  public triggerSaveCheckResult(reason: string = "manual"): void {
    console.log(`RestoreCardCheckService: Triggering save check result, reason: ${reason}`);
    this.saveCheckResultTrigger.next({ trigger: true, reason });
  }

  public resetSaveCheckResultTrigger(): void {
    this.saveCheckResultTrigger.next({ trigger: false });
  }

  // 自动保存相关方法
  private startAutoSave(): void {
    this.stopAutoSave(); // 防止重复启动
    this.autoSaveTimer = setInterval(() => {
      const currentState = this.serviceState.getValue();
      // 只在有客户端数据且不在加载状态时保存
      if (currentState.clientStates.size > 0 && !currentState.isLoading) {
        console.log("RestoreCardCheckService: Auto-save triggered (periodic)");
        this.triggerSaveCheckResult("auto_periodic_save");
      }
    }, this.AUTO_SAVE_INTERVAL);
    console.log("RestoreCardCheckService: Auto-save started with interval:", this.AUTO_SAVE_INTERVAL);
  }

  private stopAutoSave(): void {
    if (this.autoSaveTimer) {
      clearInterval(this.autoSaveTimer);
      this.autoSaveTimer = null;
      console.log("RestoreCardCheckService: Auto-save stopped");
    }
  }

  private checkSmartSave(): void {
    const now = Date.now();
    if (now - this.lastSaveTime > this.MIN_SAVE_INTERVAL) {
      this.lastSaveTime = now;
      console.log("RestoreCardCheckService: Smart save triggered");
      this.triggerSaveCheckResult("smart_trigger_save");
    }
  }

  // 超时重试相关方法
  private checkTimeoutRetry(seatNumber: number, status: RestoreCardCheckStatus): void {
    if (status === "Timeout") {
      console.log(`RestoreCardCheckService: Client ${seatNumber} timed out, starting retry mechanism`);
      this.startTimeoutRetry(seatNumber);
    } else if (status === "Success" || status === "Failed") {
      // 如果客户端成功或失败，停止重试
      console.log(`RestoreCardCheckService: Client ${seatNumber} reached final status ${status}, stopping retry`);
      this.stopTimeoutRetry(seatNumber);
    }
  }

  private startTimeoutRetry(seatNumber: number): void {
    // 如果已经有重试定时器在运行，先停止
    this.stopTimeoutRetry(seatNumber);

    // 初始化重试次数
    this.timeoutRetryAttempts.set(seatNumber, 0);

    console.log(`RestoreCardCheckService: Starting timeout retry for client ${seatNumber}`);

    const retryTimer = setInterval(() => {
      this.performTimeoutRetry(seatNumber);
    }, this.RETRY_INTERVAL);

    this.timeoutRetryTimers.set(seatNumber, retryTimer);
  }

  private performTimeoutRetry(seatNumber: number): void {
    const currentAttempts = this.timeoutRetryAttempts.get(seatNumber) || 0;
    const newAttempts = currentAttempts + 1;

    console.log(`RestoreCardCheckService: Performing retry attempt ${newAttempts} for client ${seatNumber}`);

    // 检查是否超过最大重试次数
    if (newAttempts > this.MAX_RETRY_ATTEMPTS) {
      console.log(`RestoreCardCheckService: Max retry attempts reached for client ${seatNumber}, stopping retry`);
      this.stopTimeoutRetry(seatNumber);
      return;
    }

    // 更新重试次数
    this.timeoutRetryAttempts.set(seatNumber, newAttempts);

    // 发送启动考试机命令
    this.sendStartClientCommand(seatNumber);
  }

  private sendStartClientCommand(seatNumber: number): void {
    console.log(`RestoreCardCheckService: Sending start command to client ${seatNumber}`);

    this.socketService.clientIO.sendMsg(M.MSG_START_CLIENT, { seats: [seatNumber] }, (res) => {
      if (res) {
        console.log(`RestoreCardCheckService: Start command sent to client ${seatNumber}:`, res);
      } else {
        console.error(`RestoreCardCheckService: Failed to send start command to client ${seatNumber}`);
      }
    });
  }

  private stopTimeoutRetry(seatNumber: number): void {
    const timer = this.timeoutRetryTimers.get(seatNumber);
    if (timer) {
      clearInterval(timer);
      this.timeoutRetryTimers.delete(seatNumber);
      this.timeoutRetryAttempts.delete(seatNumber);
      console.log(`RestoreCardCheckService: Stopped timeout retry for client ${seatNumber}`);
    }
  }

  private stopAllTimeoutRetries(): void {
    console.log("RestoreCardCheckService: Stopping all timeout retries");
    this.timeoutRetryTimers.forEach((timer, seatNumber) => {
      clearInterval(timer);
      console.log(`RestoreCardCheckService: Stopped retry timer for client ${seatNumber}`);
    });
    this.timeoutRetryTimers.clear();
    this.timeoutRetryAttempts.clear();
  }

  private checkAutoSave(): void {
    const currentState = this.serviceState.getValue();
    const clientStates = Array.from(currentState.clientStates.values());

    // 检查是否所有客户端都有了最终状态（成功或失败）
    const allClientsHaveFinalStatus =
      clientStates.length > 0 &&
      clientStates.every((clientState) => {
        const status = clientState.checkInfo.status;
        return status === "Success" || status === "Failed" || status === "Timeout";
      });

    if (allClientsHaveFinalStatus) {
      console.log("RestoreCardCheckService: All clients have final status, triggering auto-save");
      this.triggerSaveCheckResult("auto_all_clients_completed");
    } else {
      // 新增：检查是否有新的成功状态，智能触发保存
      const hasSuccessClients = clientStates.some((state) => state.checkInfo.status === "Success");
      if (hasSuccessClients) {
        this.checkSmartSave();
      }
    }
  }

  // 状态重置
  public resetClientStatus(seatNumber: number): void {
    console.log(`重置座位 ${seatNumber} 的状态`);
    this.stopTimeoutRetry(seatNumber);
    this.updateClientStatus(seatNumber, "NotChecked");
  }

  public resetAllClientStatus(): void {
    console.log("重置所有客户端状态");
    this.resetAllStatuses();
  }

  public getClientState(seatNumber: number): ClientState | undefined {
    return this.serviceState.getValue().clientStates.get(seatNumber);
  }

  public getNotCheckedClientSeats(): number[] {
    const currentState = this.serviceState.getValue();
    const notCheckedSeats: number[] = [];

    currentState.clientStates.forEach((clientState, seatNumber) => {
      if (clientState.checkInfo.status === "NotChecked") {
        notCheckedSeats.push(seatNumber);
      }
    });

    console.log(`RestoreCardCheckService: Found ${notCheckedSeats.length} not checked clients:`, notCheckedSeats);
    return notCheckedSeats;
  }

  public updateClientInfo(seatNumber: number, info: Partial<RestoreCardClientInfo>): void {
    const currentState = this.serviceState.getValue();
    const newClientStates = new Map(currentState.clientStates);

    const existing = newClientStates.get(seatNumber);
    if (existing) {
      const updatedState = RestoreCardDataAdapter.mergeClientState(existing, {
        clientInfo: { ...existing.clientInfo, ...info },
        lastUpdated: Date.now(),
      });
      newClientStates.set(seatNumber, updatedState);
    } else {
      // 创建新的客户端状态
      const clientInfo: RestoreCardClientInfo = {
        seat_number: seatNumber,
        ip: info.ip || "",
        host: info.host || info.ip || "",
        host_name: info.host_name || "",
        is_online: info.is_online || false,
        ...info,
      };
      newClientStates.set(seatNumber, RestoreCardDataAdapter.createDefaultClientState(clientInfo));

      // 新增客户端，重新计算总数
      this.updateCountsFullRecalculation();
    }

    this.updateServiceState({
      clientStates: newClientStates,
      lastRefresh: Date.now(),
    });
  }

  public updateClientCheckStatus(seatNumber: number, checkInfo: Partial<RestoreCardCheckInfo>): void {
    const currentState = this.serviceState.getValue();
    const newClientStates = new Map(currentState.clientStates);

    const existing = newClientStates.get(seatNumber);
    if (existing) {
      const oldStatus = existing.checkInfo.status;
      const newStatus = checkInfo.status || oldStatus;

      const updatedState = RestoreCardDataAdapter.mergeClientState(existing, {
        checkInfo: { ...existing.checkInfo, ...checkInfo },
        lastUpdated: Date.now(),
      });
      newClientStates.set(seatNumber, updatedState);

      this.updateServiceState({
        clientStates: newClientStates,
        lastRefresh: Date.now(),
      });

      // 重新计算计数
      if (oldStatus !== newStatus) {
        this.updateCountsFullRecalculation();
      }
    }
  }

  private updateServiceState(update: Partial<RestoreCardServiceState>): void {
    const currentState = this.serviceState.getValue();
    this.serviceState.next({
      ...currentState,
      ...update,
    });
  }

  private transformClientStateToDisplay(clientState: ClientState): RestoreCardClientDisplay {
    return {
      seat_number: clientState.clientInfo.seat_number,
      ip: clientState.clientInfo.ip,
      host: clientState.clientInfo.host,
      host_name: clientState.clientInfo.host_name,
      is_online: clientState.clientInfo.is_online,
      version: clientState.clientInfo.version,
      status: clientState.clientInfo.status,
      permit_id: clientState.clientInfo.permit_id,
      entry_id: clientState.clientInfo.entry_id,
      mac_addr: clientState.clientInfo.mac_addr,
      check: {
        status: this.mapStatusToDisplay(clientState.checkInfo.status),
        statusText: clientState.checkInfo.statusText,
      },
      // 各检查阶段的具体时间戳
      startTime: clientState.checkInfo.startTime,
      writeTime: clientState.checkInfo.writeTime,
      restartTime: clientState.checkInfo.restartTime,
      verifyTime: clientState.checkInfo.verifyTime,
      // 服务器端时间信息
      timestamp: clientState.checkInfo.timestamp,
      timeoutAt: clientState.checkInfo.timeoutAt,
    };
  }

  // 批量状态转换方法
  private transformAllClientStates(state: RestoreCardServiceState): RestoreCardClientDisplay[] {
    return Array.from(state.clientStates.values()).map((clientState) =>
      this.transformClientStateToDisplay(clientState)
    );
  }

  /**
   * 更新客户端连接状态
   */
  public updateClientConnectionStatus(
    seatNumber: number,
    isOnline: boolean,
    additionalInfo?: { host?: string; version?: string }
  ): void {
    const currentState = this.serviceState.getValue();
    const newClientStates = new Map(currentState.clientStates);

    const existing = newClientStates.get(seatNumber);
    if (existing) {
      // 更新现有客户端的连接状态
      const updatedClientInfo = {
        ...existing.clientInfo,
        is_online: isOnline,
        ...(additionalInfo?.host && { host: additionalInfo.host }),
        ...(additionalInfo?.version && { version: additionalInfo.version }),
      };

      const updatedState = RestoreCardDataAdapter.mergeClientState(existing, {
        clientInfo: updatedClientInfo,
        lastUpdated: Date.now(),
      });

      newClientStates.set(seatNumber, updatedState);

      this.updateServiceState({
        clientStates: newClientStates,
        lastRefresh: Date.now(),
      });
    } else {
      console.warn(`RestoreCardCheck: Client ${seatNumber} not found in current state`);
    }
  }

  /**
   * 检查客户端是否可以执行操作
   */
  public canPerformCheck(seatNumber: number): boolean {
    const clientState = this.serviceState.getValue().clientStates.get(seatNumber);
    return clientState?.clientInfo?.is_online ?? false;
  }
}
