{"ast": null, "code": "import { Notification } from '../Notification';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function materialize() {\n  return operate((source, subscriber) => {\n    source.subscribe(createOperatorSubscriber(subscriber, value => {\n      subscriber.next(Notification.createNext(value));\n    }, () => {\n      subscriber.next(Notification.createComplete());\n      subscriber.complete();\n    }, err => {\n      subscriber.next(Notification.createError(err));\n      subscriber.complete();\n    }));\n  });\n}", "map": {"version": 3, "names": ["Notification", "operate", "createOperatorSubscriber", "materialize", "source", "subscriber", "subscribe", "value", "next", "createNext", "createComplete", "complete", "err", "createError"], "sources": ["D:/work/joyserver/client/node_modules/rxjs/dist/esm/internal/operators/materialize.js"], "sourcesContent": ["import { Notification } from '../Notification';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function materialize() {\n    return operate((source, subscriber) => {\n        source.subscribe(createOperatorSubscriber(subscriber, (value) => {\n            subscriber.next(Notification.createNext(value));\n        }, () => {\n            subscriber.next(Notification.createComplete());\n            subscriber.complete();\n        }, (err) => {\n            subscriber.next(Notification.createError(err));\n            subscriber.complete();\n        }));\n    });\n}\n"], "mappings": "AAAA,SAASA,YAAT,QAA6B,iBAA7B;AACA,SAASC,OAAT,QAAwB,cAAxB;AACA,SAASC,wBAAT,QAAyC,sBAAzC;AACA,OAAO,SAASC,WAAT,GAAuB;EAC1B,OAAOF,OAAO,CAAC,CAACG,MAAD,EAASC,UAAT,KAAwB;IACnCD,MAAM,CAACE,SAAP,CAAiBJ,wBAAwB,CAACG,UAAD,EAAcE,KAAD,IAAW;MAC7DF,UAAU,CAACG,IAAX,CAAgBR,YAAY,CAACS,UAAb,CAAwBF,KAAxB,CAAhB;IACH,CAFwC,EAEtC,MAAM;MACLF,UAAU,CAACG,IAAX,CAAgBR,YAAY,CAACU,cAAb,EAAhB;MACAL,UAAU,CAACM,QAAX;IACH,CALwC,EAKrCC,GAAD,IAAS;MACRP,UAAU,CAACG,IAAX,CAAgBR,YAAY,CAACa,WAAb,CAAyBD,GAAzB,CAAhB;MACAP,UAAU,CAACM,QAAX;IACH,CARwC,CAAzC;EASH,CAVa,CAAd;AAWH"}, "metadata": {}, "sourceType": "module"}