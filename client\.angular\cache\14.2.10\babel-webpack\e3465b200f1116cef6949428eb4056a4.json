{"ast": null, "code": "export var hasNumericValueDocs = {\n  name: 'hasNumericValue',\n  category: 'Utils',\n  syntax: ['hasNumericValue(x)'],\n  description: 'Test whether a value is an numeric value. ' + 'In case of a string, true is returned if the string contains a numeric value.',\n  examples: ['hasNumericValue(2)', 'hasNumericValue(\"2\")', 'isNumeric(\"2\")', 'hasNumericValue(0)', 'hasNumericValue(bignumber(500))', 'hasNumericValue(fraction(0.125))', 'hasNumericValue(2 + 3i)', 'hasNumericValue([2.3, \"foo\", false])'],\n  seealso: ['isInteger', 'isZero', 'isNegative', 'isPositive', 'isNaN', 'isNumeric']\n};", "map": {"version": 3, "names": ["hasNumericValueDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["D:/work/joyserver/client/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/utils/hasNumericValue.js"], "sourcesContent": ["export var hasNumericValueDocs = {\n  name: 'hasNumericValue',\n  category: 'Utils',\n  syntax: ['hasNumericValue(x)'],\n  description: 'Test whether a value is an numeric value. ' + 'In case of a string, true is returned if the string contains a numeric value.',\n  examples: ['hasNumericValue(2)', 'hasNumericValue(\"2\")', 'isNumeric(\"2\")', 'hasNumericValue(0)', 'hasNumericValue(bignumber(500))', 'hasNumericValue(fraction(0.125))', 'hasNumericValue(2 + 3i)', 'hasNumericValue([2.3, \"foo\", false])'],\n  seealso: ['isInteger', 'isZero', 'isNegative', 'isPositive', 'isNaN', 'isNumeric']\n};"], "mappings": "AAAA,OAAO,IAAIA,mBAAmB,GAAG;EAC/BC,IAAI,EAAE,iBADyB;EAE/BC,QAAQ,EAAE,OAFqB;EAG/BC,MAAM,EAAE,CAAC,oBAAD,CAHuB;EAI/BC,WAAW,EAAE,+CAA+C,+EAJ7B;EAK/BC,QAAQ,EAAE,CAAC,oBAAD,EAAuB,sBAAvB,EAA+C,gBAA/C,EAAiE,oBAAjE,EAAuF,iCAAvF,EAA0H,kCAA1H,EAA8J,yBAA9J,EAAyL,sCAAzL,CALqB;EAM/BC,OAAO,EAAE,CAAC,WAAD,EAAc,QAAd,EAAwB,YAAxB,EAAsC,YAAtC,EAAoD,OAApD,EAA6D,WAA7D;AANsB,CAA1B"}, "metadata": {}, "sourceType": "module"}