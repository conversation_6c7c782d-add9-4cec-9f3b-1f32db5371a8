{"ast": null, "code": "export var bitNotDocs = {\n  name: 'bitNot',\n  category: 'Bitwise',\n  syntax: ['~x', 'bitNot(x)'],\n  description: 'Bitwise NOT operation. Performs a logical negation on each bit of the given value. Bits that are 0 become 1, and those that are 1 become 0.',\n  examples: ['~1', '~2', 'bitNot([2, -3, 4])'],\n  seealso: ['bitAnd', 'bitOr', 'bitXor', 'leftShift', 'rightArithShift', 'rightLogShift']\n};", "map": null, "metadata": {}, "sourceType": "module"}