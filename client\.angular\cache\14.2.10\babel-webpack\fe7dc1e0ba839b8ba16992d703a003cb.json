{"ast": null, "code": "import { isBigNumber, isMatrix } from '../../utils/is.js';\nimport { DimensionError } from '../../error/DimensionError.js';\nimport { ArgumentsError } from '../../error/ArgumentsError.js';\nimport { isInteger } from '../../utils/number.js';\nimport { format } from '../../utils/string.js';\nimport { clone } from '../../utils/object.js';\nimport { resize as arrayResize } from '../../utils/array.js';\nimport { factory } from '../../utils/factory.js';\nvar name = 'resize';\nvar dependencies = ['config', 'matrix'];\nexport var createResize = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    config,\n    matrix\n  } = _ref;\n  /**\n   * Resize a matrix\n   *\n   * Syntax:\n   *\n   *     math.resize(x, size)\n   *     math.resize(x, size, defaultValue)\n   *\n   * Examples:\n   *\n   *     math.resize([1, 2, 3, 4, 5], [3]) // returns Array  [1, 2, 3]\n   *     math.resize([1, 2, 3], [5], 0)    // returns Array  [1, 2, 3, 0, 0]\n   *     math.resize(2, [2, 3], 0)         // returns Matrix [[2, 0, 0], [0, 0, 0]]\n   *     math.resize(\"hello\", [8], \"!\")    // returns string 'hello!!!'\n   *\n   * See also:\n   *\n   *     size, squeeze, subset, reshape\n   *\n   * @param {Array | Matrix | *} x             Matrix to be resized\n   * @param {Array | Matrix} size              One dimensional array with numbers\n   * @param {number | string} [defaultValue=0] Zero by default, except in\n   *                                           case of a string, in that case\n   *                                           defaultValue = ' '\n   * @return {* | Array | Matrix} A resized clone of matrix `x`\n   */\n  // TODO: rework resize to a typed-function\n\n  return function resize(x, size, defaultValue) {\n    if (arguments.length !== 2 && arguments.length !== 3) {\n      throw new ArgumentsError('resize', arguments.length, 2, 3);\n    }\n\n    if (isMatrix(size)) {\n      size = size.valueOf(); // get Array\n    }\n\n    if (isBigNumber(size[0])) {\n      // convert bignumbers to numbers\n      size = size.map(function (value) {\n        return !isBigNumber(value) ? value : value.toNumber();\n      });\n    } // check x is a Matrix\n\n\n    if (isMatrix(x)) {\n      // use optimized matrix implementation, return copy\n      return x.resize(size, defaultValue, true);\n    }\n\n    if (typeof x === 'string') {\n      // resize string\n      return _resizeString(x, size, defaultValue);\n    } // check result should be a matrix\n\n\n    var asMatrix = Array.isArray(x) ? false : config.matrix !== 'Array';\n\n    if (size.length === 0) {\n      // output a scalar\n      while (Array.isArray(x)) {\n        x = x[0];\n      }\n\n      return clone(x);\n    } else {\n      // output an array/matrix\n      if (!Array.isArray(x)) {\n        x = [x];\n      }\n\n      x = clone(x);\n      var res = arrayResize(x, size, defaultValue);\n      return asMatrix ? matrix(res) : res;\n    }\n  };\n  /**\n   * Resize a string\n   * @param {string} str\n   * @param {number[]} size\n   * @param {string} [defaultChar=' ']\n   * @private\n   */\n\n  function _resizeString(str, size, defaultChar) {\n    if (defaultChar !== undefined) {\n      if (typeof defaultChar !== 'string' || defaultChar.length !== 1) {\n        throw new TypeError('Single character expected as defaultValue');\n      }\n    } else {\n      defaultChar = ' ';\n    }\n\n    if (size.length !== 1) {\n      throw new DimensionError(size.length, 1);\n    }\n\n    var len = size[0];\n\n    if (typeof len !== 'number' || !isInteger(len)) {\n      throw new TypeError('Invalid size, must contain positive integers ' + '(size: ' + format(size) + ')');\n    }\n\n    if (str.length > len) {\n      return str.substring(0, len);\n    } else if (str.length < len) {\n      var res = str;\n\n      for (var i = 0, ii = len - str.length; i < ii; i++) {\n        res += defaultChar;\n      }\n\n      return res;\n    } else {\n      return str;\n    }\n  }\n});", "map": null, "metadata": {}, "sourceType": "module"}