{"ast": null, "code": "/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { BigNumberDependencies } from './dependenciesBigNumberClass.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createSech } from '../../factoriesAny.js';\nexport var sechDependencies = {\n  BigNumberDependencies,\n  typedDependencies,\n  createSech\n};", "map": {"version": 3, "names": ["BigNumberDependencies", "typedDependencies", "createSech", "sechDependencies"], "sources": ["D:/work/joyserver/client/node_modules/mathjs/lib/esm/entry/dependenciesAny/dependenciesSech.generated.js"], "sourcesContent": ["/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { BigNumberDependencies } from './dependenciesBigNumberClass.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createSech } from '../../factoriesAny.js';\nexport var sechDependencies = {\n  BigNumberDependencies,\n  typedDependencies,\n  createSech\n};"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,qBAAT,QAAsC,2CAAtC;AACA,SAASC,iBAAT,QAAkC,kCAAlC;AACA,SAASC,UAAT,QAA2B,uBAA3B;AACA,OAAO,IAAIC,gBAAgB,GAAG;EAC5BH,qBAD4B;EAE5BC,iBAF4B;EAG5BC;AAH4B,CAAvB"}, "metadata": {}, "sourceType": "module"}