{"ast": null, "code": "export var sluDocs = {\n  name: 'slu',\n  category: 'Algebra',\n  syntax: ['slu(A, order, threshold)'],\n  description: 'Calculate the Matrix LU decomposition with full pivoting. Matrix A is decomposed in two matrices (L, U) and two permutation vectors (pinv, q) where P * A * Q = L * U',\n  examples: ['slu(sparse([4.5, 0, 3.2, 0; 3.1, 2.9, 0, 0.9; 0, 1.7, 3, 0; 3.5, 0.4, 0, 1]), 1, 0.001)'],\n  seealso: ['lusolve', 'lsolve', 'usolve', 'matrix', 'sparse', 'lup', 'qr']\n};", "map": {"version": 3, "names": ["sluDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["D:/work/joyserver/client/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/algebra/slu.js"], "sourcesContent": ["export var sluDocs = {\n  name: 'slu',\n  category: 'Algebra',\n  syntax: ['slu(A, order, threshold)'],\n  description: 'Calculate the Matrix LU decomposition with full pivoting. Matrix A is decomposed in two matrices (L, U) and two permutation vectors (pinv, q) where P * A * Q = L * U',\n  examples: ['slu(sparse([4.5, 0, 3.2, 0; 3.1, 2.9, 0, 0.9; 0, 1.7, 3, 0; 3.5, 0.4, 0, 1]), 1, 0.001)'],\n  seealso: ['lusolve', 'lsolve', 'usolve', 'matrix', 'sparse', 'lup', 'qr']\n};"], "mappings": "AAAA,OAAO,IAAIA,OAAO,GAAG;EACnBC,IAAI,EAAE,KADa;EAEnBC,QAAQ,EAAE,SAFS;EAGnBC,MAAM,EAAE,CAAC,0BAAD,CAHW;EAInBC,WAAW,EAAE,uKAJM;EAKnBC,QAAQ,EAAE,CAAC,yFAAD,CALS;EAMnBC,OAAO,EAAE,CAAC,SAAD,EAAY,QAAZ,EAAsB,QAAtB,EAAgC,QAAhC,EAA0C,QAA1C,EAAoD,KAApD,EAA2D,IAA3D;AANU,CAAd"}, "metadata": {}, "sourceType": "module"}