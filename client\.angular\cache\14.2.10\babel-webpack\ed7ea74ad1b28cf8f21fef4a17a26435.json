{"ast": null, "code": "export var resizeDocs = {\n  name: 'resize',\n  category: 'Matrix',\n  syntax: ['resize(x, size)', 'resize(x, size, defaultValue)'],\n  description: 'Resize a matrix.',\n  examples: ['resize([1,2,3,4,5], [3])', 'resize([1,2,3], [5])', 'resize([1,2,3], [5], -1)', 'resize(2, [2, 3])', 'resize(\"hello\", [8], \"!\")'],\n  seealso: ['size', 'subset', 'squeeze', 'reshape']\n};", "map": null, "metadata": {}, "sourceType": "module"}