{"ast": null, "code": "import { deepMap } from '../../utils/collection.js';\nimport { factory } from '../../utils/factory.js';\nimport { createEmptyMap } from '../../utils/map.js';\nvar name = 'evaluate';\nvar dependencies = ['typed', 'parse'];\nexport var createEvaluate = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed,\n    parse\n  } = _ref;\n  /**\n   * Evaluate an expression.\n   *\n   * The expression parser does not use JavaScript. Its syntax is close\n   * to JavaScript but more suited for mathematical expressions.\n   * See [https://mathjs.org/docs/expressions/syntax.html](https://mathjs.org/docs/expressions/syntax.html) to learn\n   * the syntax and get an overview of the exact differences from JavaScript.\n   *\n   * Note the evaluating arbitrary expressions may involve security risks,\n   * see [https://mathjs.org/docs/expressions/security.html](https://mathjs.org/docs/expressions/security.html) for more information.\n   *\n   * Syntax:\n   *\n   *     math.evaluate(expr)\n   *     math.evaluate(expr, scope)\n   *     math.evaluate([expr1, expr2, expr3, ...])\n   *     math.evaluate([expr1, expr2, expr3, ...], scope)\n   *\n   * Example:\n   *\n   *     math.evaluate('(2+3)/4')                // 1.25\n   *     math.evaluate('sqrt(3^2 + 4^2)')        // 5\n   *     math.evaluate('sqrt(-4)')               // 2i\n   *     math.evaluate(['a=3', 'b=4', 'a*b'])    // [3, 4, 12]\n   *\n   *     let scope = {a:3, b:4}\n   *     math.evaluate('a * b', scope)           // 12\n   *\n   * See also:\n   *\n   *    parse, compile\n   *\n   * @param {string | string[] | Matrix} expr   The expression to be evaluated\n   * @param {Object} [scope]                    Scope to read/write variables\n   * @return {*} The result of the expression\n   * @throws {Error}\n   */\n\n  return typed(name, {\n    string: function string(expr) {\n      var scope = createEmptyMap();\n      return parse(expr).compile().evaluate(scope);\n    },\n    'string, Map | Object': function string_Map__Object(expr, scope) {\n      return parse(expr).compile().evaluate(scope);\n    },\n    'Array | Matrix': function Array__Matrix(expr) {\n      var scope = createEmptyMap();\n      return deepMap(expr, function (entry) {\n        return parse(entry).compile().evaluate(scope);\n      });\n    },\n    'Array | Matrix, Map | Object': function Array__Matrix_Map__Object(expr, scope) {\n      return deepMap(expr, function (entry) {\n        return parse(entry).compile().evaluate(scope);\n      });\n    }\n  });\n});", "map": {"version": 3, "names": ["deepMap", "factory", "createEmptyMap", "name", "dependencies", "createEvaluate", "_ref", "typed", "parse", "string", "expr", "scope", "compile", "evaluate", "string_Map__Object", "Array__Matrix", "entry", "Array__Matrix_Map__Object"], "sources": ["D:/work/joyserver/client/node_modules/mathjs/lib/esm/expression/function/evaluate.js"], "sourcesContent": ["import { deepMap } from '../../utils/collection.js';\nimport { factory } from '../../utils/factory.js';\nimport { createEmptyMap } from '../../utils/map.js';\nvar name = 'evaluate';\nvar dependencies = ['typed', 'parse'];\nexport var createEvaluate = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed,\n    parse\n  } = _ref;\n  /**\n   * Evaluate an expression.\n   *\n   * The expression parser does not use JavaScript. Its syntax is close\n   * to JavaScript but more suited for mathematical expressions.\n   * See [https://mathjs.org/docs/expressions/syntax.html](https://mathjs.org/docs/expressions/syntax.html) to learn\n   * the syntax and get an overview of the exact differences from JavaScript.\n   *\n   * Note the evaluating arbitrary expressions may involve security risks,\n   * see [https://mathjs.org/docs/expressions/security.html](https://mathjs.org/docs/expressions/security.html) for more information.\n   *\n   * Syntax:\n   *\n   *     math.evaluate(expr)\n   *     math.evaluate(expr, scope)\n   *     math.evaluate([expr1, expr2, expr3, ...])\n   *     math.evaluate([expr1, expr2, expr3, ...], scope)\n   *\n   * Example:\n   *\n   *     math.evaluate('(2+3)/4')                // 1.25\n   *     math.evaluate('sqrt(3^2 + 4^2)')        // 5\n   *     math.evaluate('sqrt(-4)')               // 2i\n   *     math.evaluate(['a=3', 'b=4', 'a*b'])    // [3, 4, 12]\n   *\n   *     let scope = {a:3, b:4}\n   *     math.evaluate('a * b', scope)           // 12\n   *\n   * See also:\n   *\n   *    parse, compile\n   *\n   * @param {string | string[] | Matrix} expr   The expression to be evaluated\n   * @param {Object} [scope]                    Scope to read/write variables\n   * @return {*} The result of the expression\n   * @throws {Error}\n   */\n  return typed(name, {\n    string: function string(expr) {\n      var scope = createEmptyMap();\n      return parse(expr).compile().evaluate(scope);\n    },\n    'string, Map | Object': function string_Map__Object(expr, scope) {\n      return parse(expr).compile().evaluate(scope);\n    },\n    'Array | Matrix': function Array__Matrix(expr) {\n      var scope = createEmptyMap();\n      return deepMap(expr, function (entry) {\n        return parse(entry).compile().evaluate(scope);\n      });\n    },\n    'Array | Matrix, Map | Object': function Array__Matrix_Map__Object(expr, scope) {\n      return deepMap(expr, function (entry) {\n        return parse(entry).compile().evaluate(scope);\n      });\n    }\n  });\n});"], "mappings": "AAAA,SAASA,OAAT,QAAwB,2BAAxB;AACA,SAASC,OAAT,QAAwB,wBAAxB;AACA,SAASC,cAAT,QAA+B,oBAA/B;AACA,IAAIC,IAAI,GAAG,UAAX;AACA,IAAIC,YAAY,GAAG,CAAC,OAAD,EAAU,OAAV,CAAnB;AACA,OAAO,IAAIC,cAAc,GAAG,eAAeJ,OAAO,CAACE,IAAD,EAAOC,YAAP,EAAqBE,IAAI,IAAI;EAC7E,IAAI;IACFC,KADE;IAEFC;EAFE,IAGAF,IAHJ;EAIA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EACE,OAAOC,KAAK,CAACJ,IAAD,EAAO;IACjBM,MAAM,EAAE,SAASA,MAAT,CAAgBC,IAAhB,EAAsB;MAC5B,IAAIC,KAAK,GAAGT,cAAc,EAA1B;MACA,OAAOM,KAAK,CAACE,IAAD,CAAL,CAAYE,OAAZ,GAAsBC,QAAtB,CAA+BF,KAA/B,CAAP;IACD,CAJgB;IAKjB,wBAAwB,SAASG,kBAAT,CAA4BJ,IAA5B,EAAkCC,KAAlC,EAAyC;MAC/D,OAAOH,KAAK,CAACE,IAAD,CAAL,CAAYE,OAAZ,GAAsBC,QAAtB,CAA+BF,KAA/B,CAAP;IACD,CAPgB;IAQjB,kBAAkB,SAASI,aAAT,CAAuBL,IAAvB,EAA6B;MAC7C,IAAIC,KAAK,GAAGT,cAAc,EAA1B;MACA,OAAOF,OAAO,CAACU,IAAD,EAAO,UAAUM,KAAV,EAAiB;QACpC,OAAOR,KAAK,CAACQ,KAAD,CAAL,CAAaJ,OAAb,GAAuBC,QAAvB,CAAgCF,KAAhC,CAAP;MACD,CAFa,CAAd;IAGD,CAbgB;IAcjB,gCAAgC,SAASM,yBAAT,CAAmCP,IAAnC,EAAyCC,KAAzC,EAAgD;MAC9E,OAAOX,OAAO,CAACU,IAAD,EAAO,UAAUM,KAAV,EAAiB;QACpC,OAAOR,KAAK,CAACQ,KAAD,CAAL,CAAaJ,OAAb,GAAuBC,QAAvB,CAAgCF,KAAhC,CAAP;MACD,CAFa,CAAd;IAGD;EAlBgB,CAAP,CAAZ;AAoBD,CA9DiD,CAA3C"}, "metadata": {}, "sourceType": "module"}