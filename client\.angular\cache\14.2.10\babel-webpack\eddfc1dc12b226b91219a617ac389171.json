{"ast": null, "code": "export class Scheduler {\n  constructor(SchedulerAction, now = Scheduler.now) {\n    this.SchedulerAction = SchedulerAction;\n    this.now = now;\n  }\n\n  schedule(work, delay = 0, state) {\n    return new this.SchedulerAction(this, work).schedule(state, delay);\n  }\n\n}\n\nScheduler.now = () => Date.now();", "map": {"version": 3, "names": ["Scheduler", "constructor", "SchedulerAction", "now", "schedule", "work", "delay", "state", "Date"], "sources": ["D:/work/joyserver/client/node_modules/@angular-slider/ngx-slider/node_modules/rxjs/_esm2015/internal/Scheduler.js"], "sourcesContent": ["export class Scheduler {\n    constructor(SchedulerAction, now = Scheduler.now) {\n        this.SchedulerAction = SchedulerAction;\n        this.now = now;\n    }\n    schedule(work, delay = 0, state) {\n        return new this.SchedulerAction(this, work).schedule(state, delay);\n    }\n}\nScheduler.now = () => Date.now();\n"], "mappings": "AAAA,OAAO,MAAMA,SAAN,CAAgB;EACnBC,WAAW,CAACC,eAAD,EAAkBC,GAAG,GAAGH,SAAS,CAACG,GAAlC,EAAuC;IAC9C,KAAKD,eAAL,GAAuBA,eAAvB;IACA,KAAKC,GAAL,GAAWA,GAAX;EACH;;EACDC,QAAQ,CAACC,IAAD,EAAOC,KAAK,GAAG,CAAf,EAAkBC,KAAlB,EAAyB;IAC7B,OAAO,IAAI,KAAKL,eAAT,CAAyB,IAAzB,EAA+BG,IAA/B,EAAqCD,QAArC,CAA8CG,KAA9C,EAAqDD,KAArD,CAAP;EACH;;AAPkB;;AASvBN,SAAS,CAACG,GAAV,GAAgB,MAAMK,IAAI,CAACL,GAAL,EAAtB"}, "metadata": {}, "sourceType": "module"}