{"ast": null, "code": "import _asyncToGenerator from \"D:/work/joyserver/client/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { HttpHeaders } from \"@angular/common/http\";\nimport { Exam } from \"@core-types/exam.types\";\nimport { Subscription } from \"rxjs\";\nimport { finalize, map } from \"rxjs/operators\";\nimport { AceComponent } from \"ngx-ace-wrapper\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@core-service/exam.service\";\nimport * as i2 from \"@core-service/form.service\";\nimport * as i3 from \"@ngx-translate/core\";\nimport * as i4 from \"@core/service/auto-test.service\";\nimport * as i5 from \"@core/modal/custom-loading/custom-loading.service\";\nimport * as i6 from \"@core/service/api.service\";\nimport * as i7 from \"@angular/common/http\";\nimport * as i8 from \"@core/modal/custom-alert/custom-alert.service\";\nimport * as i9 from \"@angular/common\";\nimport * as i10 from \"@angular/forms\";\nimport * as i11 from \"../../../../core/modal/custom-confirm/custom-confirm.component\";\nimport * as i12 from \"../../../../core/directive/clickOutside.directive\";\nimport * as i13 from \"../../../../core/directive/app-motation-observer.directive\";\nimport * as i14 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i15 from \"angular-split\";\nimport * as i16 from \"ngx-ace-wrapper\";\nimport * as i17 from \"../../../../core/pipe/domSanitized.pipe\";\n\nfunction CodeComponent_span_14_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r20 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 39)(1, \"div\", 40);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementStart(4, \"i\", 41);\n    i0.ɵɵlistener(\"click\", function CodeComponent_span_14_div_3_Template_i_click_4_listener() {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r19 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r19.toggleEnvInfo(false));\n    });\n    i0.ɵɵtext(5, \"\\uE66B\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 42)(7, \"div\", 43);\n    i0.ɵɵtext(8);\n    i0.ɵɵpipe(9, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 44);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()()();\n  }\n\n  if (rf & 2) {\n    const ctx_r18 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 3, \"exam.code.lan-env\"), \" \");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(9, 5, \"exam.code.builder\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r18.showEnvInfoBlock.version);\n  }\n}\n\nfunction CodeComponent_span_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r22 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"span\", 36)(1, \"i\", 37);\n    i0.ɵɵlistener(\"click\", function CodeComponent_span_14_Template_i_click_1_listener() {\n      i0.ɵɵrestoreView(_r22);\n      const ctx_r21 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r21.toggleEnvInfo(true));\n    });\n    i0.ɵɵtext(2, \"\\uE660\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, CodeComponent_span_14_div_3_Template, 12, 7, \"div\", 38);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.showEnvInfoCont);\n  }\n}\n\nfunction CodeComponent_i_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"i\", 45);\n    i0.ɵɵtext(1, \"\\uE6FE\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction CodeComponent_ul_18_li_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r26 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"li\", 48);\n    i0.ɵɵlistener(\"click\", function CodeComponent_ul_18_li_1_Template_li_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r26);\n      const lan_r24 = restoredCtx.$implicit;\n      const ctx_r25 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r25.selectLan(lan_r24));\n    });\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const lan_r24 = ctx.$implicit;\n    i0.ɵɵproperty(\"innerHTML\", lan_r24, i0.ɵɵsanitizeHtml);\n  }\n}\n\nfunction CodeComponent_ul_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ul\", 46);\n    i0.ɵɵtemplate(1, CodeComponent_ul_18_li_1_Template, 1, 1, \"li\", 47);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.item.code_languages);\n  }\n}\n\nconst _c0 = function (a0, a1) {\n  return {\n    \"text-light\": a0,\n    \"text-secondary\": a1\n  };\n};\n\nfunction CodeComponent_i_27_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r28 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"i\", 17);\n    i0.ɵɵlistener(\"click\", function CodeComponent_i_27_Template_i_click_0_listener() {\n      i0.ɵɵrestoreView(_r28);\n      const ctx_r27 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r27.toggleItemFullScreen(true));\n    });\n    i0.ɵɵpipe(1, \"translate\");\n    i0.ɵɵtext(2, \"\\uE6CA\");\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵpropertyInterpolate(\"ngbTooltip\", i0.ɵɵpipeBind1(1, 2, \"exam.code.fullscreen\"));\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(4, _c0, ctx_r4.areaTheme !== \"white\", ctx_r4.areaTheme === \"white\"));\n  }\n}\n\nfunction CodeComponent_i_28_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r30 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"i\", 17);\n    i0.ɵɵlistener(\"click\", function CodeComponent_i_28_Template_i_click_0_listener() {\n      i0.ɵɵrestoreView(_r30);\n      const ctx_r29 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r29.toggleItemFullScreen(false));\n    });\n    i0.ɵɵpipe(1, \"translate\");\n    i0.ɵɵtext(2, \"\\uE64B\");\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵpropertyInterpolate(\"ngbTooltip\", i0.ɵɵpipeBind1(1, 2, \"exam.code.exitfullscreen\"));\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(4, _c0, ctx_r5.areaTheme !== \"white\", ctx_r5.areaTheme === \"white\"));\n  }\n}\n\nfunction CodeComponent_ace_30_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r33 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"ace\", 49, 50);\n    i0.ɵɵlistener(\"valueChange\", function CodeComponent_ace_30_Template_ace_valueChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r33);\n      const ctx_r32 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r32.itemRes.answer.value = $event);\n    })(\"valueChange\", function CodeComponent_ace_30_Template_ace_valueChange_0_listener() {\n      i0.ɵɵrestoreView(_r33);\n      const ctx_r34 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r34.updateCode());\n    });\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"config\", ctx_r6.options)(\"mode\", ctx_r6.aceMode)(\"value\", ctx_r6.itemRes.answer.value);\n  }\n}\n\nfunction CodeComponent_span_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r7.gutterCellTip);\n  }\n}\n\nfunction CodeComponent_ng_container_34_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r36 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 51)(2, \"div\", 52)(3, \"small\", 53);\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"button\", 54);\n    i0.ɵɵlistener(\"click\", function CodeComponent_ng_container_34_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r36);\n      const ctx_r35 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r35.closeFirstInTip());\n    });\n    i0.ɵɵelementStart(7, \"small\");\n    i0.ɵɵtext(8);\n    i0.ɵɵpipe(9, \"translate\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelement(10, \"div\", 55);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(5, 2, \"exam.code.submitTip\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(9, 4, \"exam.code.submitTipBtn\"));\n  }\n}\n\nfunction CodeComponent_button_39_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r38 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 56);\n    i0.ɵɵlistener(\"click\", function CodeComponent_button_39_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r38);\n      const ctx_r37 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r37.toggleBottomCont(true));\n    });\n    i0.ɵɵelementStart(1, \"div\", 57);\n    i0.ɵɵelement(2, \"div\", 58);\n    i0.ɵɵelementEnd()();\n  }\n}\n\nfunction CodeComponent_button_40_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r40 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 56);\n    i0.ɵɵlistener(\"click\", function CodeComponent_button_40_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r40);\n      const ctx_r39 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r39.toggleBottomCont(false));\n    });\n    i0.ɵɵelementStart(1, \"div\", 59);\n    i0.ɵɵelement(2, \"div\", 60);\n    i0.ɵɵelementEnd()();\n  }\n}\n\nfunction CodeComponent_li_43_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r43 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"textarea\", 61);\n    i0.ɵɵlistener(\"blur\", function CodeComponent_li_43_ng_template_4_Template_textarea_blur_0_listener() {\n      i0.ɵɵrestoreView(_r43);\n      const ctx_r42 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r42.updateStdCases());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(1, \"span\", 62);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r41 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"value\", ctx_r41.getInitCase());\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(3, 2, \"exam.code.caseTip\"));\n  }\n}\n\nfunction CodeComponent_li_43_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r45 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"li\", 30);\n    i0.ɵɵlistener(\"click\", function CodeComponent_li_43_Template_li_click_0_listener() {\n      i0.ɵɵrestoreView(_r45);\n      const ctx_r44 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r44.toggleBottomCont(true));\n    });\n    i0.ɵɵelementStart(1, \"a\", 31);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, CodeComponent_li_43_ng_template_4_Template, 4, 4, \"ng-template\", 32);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    i0.ɵɵproperty(\"ngbNavItem\", \"inputBox\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(3, 2, \"exam.code.testCase\"));\n  }\n}\n\nfunction CodeComponent_ng_template_48_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"span\", 62);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(3, 1, \"exam.code.defaultTip\"));\n  }\n}\n\nfunction CodeComponent_ng_template_48_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 63)(2, \"div\", 64);\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(5, \"pre\", 65);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r47 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(4, 2, \"exam.code.compileErr\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"innerHTML\", ctx_r47.compileError, i0.ɵɵsanitizeHtml);\n  }\n}\n\nfunction CodeComponent_ng_template_48_ng_container_2_ng_container_2_td_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(2, 1, \"exam.code.input\"));\n  }\n}\n\nfunction CodeComponent_ng_template_48_ng_container_2_ng_container_2_td_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(2, 1, \"exam.code.expectOut\"));\n  }\n}\n\nfunction CodeComponent_ng_template_48_ng_container_2_ng_container_2_td_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r56 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r56.inputCase);\n  }\n}\n\nfunction CodeComponent_ng_template_48_ng_container_2_ng_container_2_td_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r57 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r57.caseResult.key);\n  }\n}\n\nfunction CodeComponent_ng_template_48_ng_container_2_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"table\", 67)(2, \"thead\")(3, \"tr\");\n    i0.ɵɵtemplate(4, CodeComponent_ng_template_48_ng_container_2_ng_container_2_td_4_Template, 3, 3, \"td\", 23);\n    i0.ɵɵtemplate(5, CodeComponent_ng_template_48_ng_container_2_ng_container_2_td_5_Template, 3, 3, \"td\", 23);\n    i0.ɵɵelementStart(6, \"td\");\n    i0.ɵɵtext(7);\n    i0.ɵɵpipe(8, \"translate\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(9, \"tr\");\n    i0.ɵɵtemplate(10, CodeComponent_ng_template_48_ng_container_2_ng_container_2_td_10_Template, 2, 1, \"td\", 23);\n    i0.ɵɵtemplate(11, CodeComponent_ng_template_48_ng_container_2_ng_container_2_td_11_Template, 2, 1, \"td\", 23);\n    i0.ɵɵelementStart(12, \"td\");\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r49 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r49.curLan !== \"Sql\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !!(ctx_r49.caseResult == null ? null : ctx_r49.caseResult.key));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(8, 6, \"exam.code.output\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r49.curLan !== \"Sql\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !!(ctx_r49.caseResult == null ? null : ctx_r49.caseResult.key));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r49.caseResult.answer);\n  }\n}\n\nfunction CodeComponent_ng_template_48_ng_container_2_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 64);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(3, 1, \"exam.code.timeout\"));\n  }\n}\n\nfunction CodeComponent_ng_template_48_ng_container_2_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 64);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"pre\", 65);\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r51 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(3, 2, \"exam.code.runErr\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"innerHTML\", ctx_r51.caseResult.exception, i0.ɵɵsanitizeHtml);\n  }\n}\n\nfunction CodeComponent_ng_template_48_ng_container_2_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 64);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(3, 1, \"exam.code.outputLenExceed\"));\n  }\n}\n\nfunction CodeComponent_ng_template_48_ng_container_2_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 64);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(3, 1, \"exam.code.memoryLenExceed\"));\n  }\n}\n\nfunction CodeComponent_ng_template_48_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 66);\n    i0.ɵɵtemplate(2, CodeComponent_ng_template_48_ng_container_2_ng_container_2_Template, 14, 8, \"ng-container\", 23);\n    i0.ɵɵtemplate(3, CodeComponent_ng_template_48_ng_container_2_ng_container_3_Template, 4, 3, \"ng-container\", 23);\n    i0.ɵɵtemplate(4, CodeComponent_ng_template_48_ng_container_2_ng_container_4_Template, 5, 4, \"ng-container\", 23);\n    i0.ɵɵtemplate(5, CodeComponent_ng_template_48_ng_container_2_ng_container_5_Template, 4, 3, \"ng-container\", 23);\n    i0.ɵɵtemplate(6, CodeComponent_ng_template_48_ng_container_2_ng_container_6_Template, 4, 3, \"ng-container\", 23);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r48 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r48.caseResult == null ? null : ctx_r48.caseResult.verdict) === \"AC\" || (ctx_r48.caseResult == null ? null : ctx_r48.caseResult.verdict) === \"WA\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r48.caseResult == null ? null : ctx_r48.caseResult.verdict) === \"TLE\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r48.caseResult == null ? null : ctx_r48.caseResult.verdict) === \"RE\" || (ctx_r48.caseResult == null ? null : ctx_r48.caseResult.verdict) === \"RTE\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r48.caseResult == null ? null : ctx_r48.caseResult.verdict) === \"OLE\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r48.caseResult == null ? null : ctx_r48.caseResult.verdict) === \"MLE\");\n  }\n}\n\nfunction CodeComponent_ng_template_48_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, CodeComponent_ng_template_48_ng_container_0_Template, 4, 3, \"ng-container\", 23);\n    i0.ɵɵtemplate(1, CodeComponent_ng_template_48_ng_container_1_Template, 6, 4, \"ng-container\", 23);\n    i0.ɵɵtemplate(2, CodeComponent_ng_template_48_ng_container_2_Template, 7, 5, \"ng-container\", 23);\n  }\n\n  if (rf & 2) {\n    const ctx_r13 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r13.commitResult && !ctx_r13.caseResult && !ctx_r13.compileError);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !!ctx_r13.compileError);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r13.compileError && ctx_r13.caseResult);\n  }\n}\n\nfunction CodeComponent_div_49_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 68);\n  }\n\n  if (rf & 2) {\n    i0.ɵɵnextContext();\n\n    const _r11 = i0.ɵɵreference(42);\n\n    i0.ɵɵproperty(\"ngbNavOutlet\", _r11);\n  }\n}\n\nfunction CodeComponent_custom_confirm_50_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r59 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"custom-confirm\", 69);\n    i0.ɵɵlistener(\"onConfirm\", function CodeComponent_custom_confirm_50_Template_custom_confirm_onConfirm_0_listener() {\n      i0.ɵɵrestoreView(_r59);\n      const ctx_r58 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r58.confirmChangeLan());\n    })(\"onCancel\", function CodeComponent_custom_confirm_50_Template_custom_confirm_onCancel_0_listener() {\n      i0.ɵɵrestoreView(_r59);\n      const ctx_r60 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r60.cancelChangeLan());\n    });\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r15 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"bodyText\", ctx_r15.translate.instant(\"exam.code.changeLanTip\"));\n  }\n}\n\nfunction CodeComponent_custom_confirm_51_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r62 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"custom-confirm\", 69);\n    i0.ɵɵlistener(\"onConfirm\", function CodeComponent_custom_confirm_51_Template_custom_confirm_onConfirm_0_listener() {\n      i0.ɵɵrestoreView(_r62);\n      const ctx_r61 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r61.confirmResetAnswer());\n    })(\"onCancel\", function CodeComponent_custom_confirm_51_Template_custom_confirm_onCancel_0_listener() {\n      i0.ɵɵrestoreView(_r62);\n      const ctx_r63 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r63.cancelResetAnswer());\n    });\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r16 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"bodyText\", ctx_r16.translate.instant(\"exam.code.resetTip\"));\n  }\n}\n\nfunction CodeComponent_div_52_option_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const fontSize_r65 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(fontSize_r65);\n  }\n}\n\nfunction CodeComponent_div_52_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r67 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 70)(1, \"div\", 71)(2, \"div\", 72)(3, \"div\", 73);\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 74)(7, \"div\", 75)(8, \"span\", 76);\n    i0.ɵɵtext(9);\n    i0.ɵɵpipe(10, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"select\", 77);\n    i0.ɵɵlistener(\"ngModelChange\", function CodeComponent_div_52_Template_select_ngModelChange_11_listener($event) {\n      i0.ɵɵrestoreView(_r67);\n      const ctx_r66 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r66.editorConfig.fontSize = $event);\n    });\n    i0.ɵɵtemplate(12, CodeComponent_div_52_option_12_Template, 2, 1, \"option\", 78);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"div\", 79)(14, \"span\", 76);\n    i0.ɵɵtext(15);\n    i0.ɵɵpipe(16, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"label\", 80)(18, \"span\", 81)(19, \"input\", 82);\n    i0.ɵɵlistener(\"ngModelChange\", function CodeComponent_div_52_Template_input_ngModelChange_19_listener($event) {\n      i0.ɵɵrestoreView(_r67);\n      const ctx_r68 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r68.editorConfig.mode = $event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"span\");\n    i0.ɵɵtext(21);\n    i0.ɵɵpipe(22, \"translate\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(23, \"label\", 83)(24, \"span\", 81)(25, \"input\", 84);\n    i0.ɵɵlistener(\"ngModelChange\", function CodeComponent_div_52_Template_input_ngModelChange_25_listener($event) {\n      i0.ɵɵrestoreView(_r67);\n      const ctx_r69 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r69.editorConfig.mode = $event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"span\");\n    i0.ɵɵtext(27);\n    i0.ɵɵpipe(28, \"translate\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(29, \"div\", 85)(30, \"button\", 86);\n    i0.ɵɵlistener(\"click\", function CodeComponent_div_52_Template_button_click_30_listener() {\n      i0.ɵɵrestoreView(_r67);\n      const ctx_r70 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r70.confirmEditorMode(true));\n    });\n    i0.ɵɵtext(31);\n    i0.ɵɵpipe(32, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"button\", 87);\n    i0.ɵɵlistener(\"click\", function CodeComponent_div_52_Template_button_click_33_listener() {\n      i0.ɵɵrestoreView(_r67);\n      const ctx_r71 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r71.confirmEditorMode(false));\n    });\n    i0.ɵɵtext(34);\n    i0.ɵɵpipe(35, \"translate\");\n    i0.ɵɵelementEnd()()()()();\n  }\n\n  if (rf & 2) {\n    const ctx_r17 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(5, 11, \"exam.code.editorConfig\"));\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(10, 13, \"exam.code.fontSizeConfig\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngModel\", ctx_r17.editorConfig.fontSize);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r17.fontsPresets);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(16, 15, \"exam.code.styleConfig\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngModel\", ctx_r17.editorConfig.mode);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(22, 17, \"exam.code.black\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngModel\", ctx_r17.editorConfig.mode);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(28, 19, \"exam.code.light\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(32, 21, \"exam.code.confirm\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(35, 23, \"exam.code.cancel\"), \" \");\n  }\n}\n\nconst _c1 = function (a0) {\n  return {\n    \"code-fullcreen\": a0\n  };\n};\n\nconst _c2 = function (a0) {\n  return {\n    \"theme-white\": a0\n  };\n};\n\nconst _c3 = function (a0) {\n  return {\n    \"fold-up\": a0\n  };\n};\n\nexport class CodeComponent {\n  constructor(examSer, formSer, translate, autoTestSer, _loading, apiSer, http, _alert) {\n    this.examSer = examSer;\n    this.formSer = formSer;\n    this.translate = translate;\n    this.autoTestSer = autoTestSer;\n    this._loading = _loading;\n    this.apiSer = apiSer;\n    this.http = http;\n    this._alert = _alert;\n    this.section = null;\n    this.item = null;\n    this.itemRes = null;\n    this.options = {\n      showPrintMargin: false,\n      wrap: false\n    };\n    this.showLanList = false;\n    this.showEnvInfoCont = false;\n    this.itemFullScreen = false;\n    this.showBottomCont = false;\n    this.showChangeLanTip = false;\n    this.showResetAnswerTip = false;\n    this.tabActiveId = \"inputBox\";\n    this.compileError = \"\"; // 编译报错结果\n\n    this.commitResult = null;\n    this.caseResult = null;\n    this.firstErrLineIndex = -1;\n    this.casePassed = \"\"; //\n\n    this.firstIn = true; // 第一次进入试给出提交提示\n\n    this.showAceEditor = false;\n    this.ltPercentVal = 30;\n    this.composition = false;\n    this.isEditorModeModalShowed = false;\n    this.editorConfig = {\n      fontSize: \"16px\",\n      mode: \"black\" // chaos(black) | clouds(white)\n\n    };\n    this.areaTheme = \"white\"; // black、white\n\n    this.fontsPresets = [\"14px\", \"15px\", \"16px\", \"17px\", \"18px\", \"19px\", \"20px\"];\n    this.maxLinesLength = 5000;\n    this.maxCharLength = 20 * 1000;\n    this.lanDisplayed = {\n      c: \"C\",\n      cpp: \"C++\",\n      c_sharp: \"C#\",\n      java: \"Java\",\n      swift: \"Swift\",\n      javascript: \"Javascript\",\n      go: \"Go\",\n      python2: \"Python2\",\n      python3: \"Python3\",\n      sql: \"Sql\"\n    };\n    this.lanPost = {\n      C: \"c\",\n      \"C++\": \"cpp\",\n      \"C#\": \"c_sharp\",\n      Java: \"java\",\n      Swift: \"swift\",\n      Javascript: \"javascript\",\n      Go: \"go\",\n      Python2: \"python2\",\n      Python3: \"python3\",\n      Sql: \"sql\"\n    };\n    this.subscriptions = [];\n    this.showIntroTip = false;\n\n    this.compositionStart = () => {\n      this.composition = true;\n    };\n\n    this.compositionEnd = () => {\n      this.composition = false; // console.log('compositionEnd event');\n    };\n  }\n\n  ngOnInit() {}\n\n  getEditor() {\n    return this.componentRef?.directiveRef.ace();\n  }\n\n  ngOnChanges() {\n    this.initData();\n    this.showAceEditor = false;\n    setTimeout(() => {\n      this.showAceEditor = true;\n      setTimeout(() => {\n        this.initAceEditor();\n        const aceEle = document.querySelector(\"ace\");\n\n        if (aceEle) {\n          aceEle.removeEventListener(\"compositionstart\", this.compositionStart);\n          aceEle.removeEventListener(\"compositionend\", this.compositionEnd);\n          aceEle.addEventListener(\"compositionstart\", this.compositionStart);\n          aceEle.addEventListener(\"compositionend\", this.compositionEnd);\n        }\n      });\n    });\n  }\n\n  ngOnDestroy() {\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n    clearTimeout(this.timerGetResult);\n    this.timerGetResult = null;\n\n    this._loading.setValue(false);\n\n    const aceEle = document.querySelector(\"ace\");\n\n    if (aceEle) {\n      aceEle.removeEventListener(\"compositionstart\", this.compositionStart);\n      aceEle.removeEventListener(\"compositionend\", this.compositionEnd);\n    }\n  }\n\n  resetAnswer() {\n    this.showResetAnswerTip = true;\n  }\n\n  toggleBottomCont(isOpen) {\n    this.showBottomCont = isOpen;\n  }\n\n  resizeEditor() {\n    const editor = this.getEditor();\n    editor && editor.resize();\n  }\n\n  toggleItemFullScreen(isFullScreen) {\n    this.itemFullScreen = isFullScreen;\n  }\n\n  initData() {\n    this.section = this.examSer.currentSection();\n    this.item = this.formSer.getItemById(this.section, this.itemId);\n    this.itemRes = this.examSer.getItemResById(this.itemId);\n    const editorConfig = this.itemRes.editor_config;\n    this.showEnvInfoBlock = this.getEnvInfo(this.curLan);\n    this.firstIn = this.itemRes.first_in;\n    this.examSer.itemCodeChanged = false;\n    this.aceMode = this.getAceMode();\n    this.showBottomCont = false;\n    this.caseResult = null;\n    this.commitResult = null;\n    this.compileError = \"\";\n    this.editorConfig.fontSize = editorConfig ? editorConfig.fontSize : \"16px\";\n    this.editorConfig.mode = editorConfig ? editorConfig.theme : \"black\"; // reset split pos\n\n    this.setSplitPos(30);\n  }\n\n  setSplitPos(splitPos1) {\n    const splitPos2 = 100 - splitPos1;\n    setTimeout(() => {\n      window[\"$\"](\".item-code as-split-area\").eq(0).css(\"flex\", \"0 0 calc(\" + splitPos1 + \"% - \" + splitPos1 / 10 + \"px)\");\n      window[\"$\"](\".item-code as-split-area\").eq(1).css(\"flex\", \"0 0 calc(\" + splitPos2 + \"% - \" + (6 - splitPos1 / 10) + \"px)\");\n    }, 10);\n  }\n\n  initAceEditor() {\n    this.setDefaultLan();\n    const editor = this.getEditor();\n\n    if (editor) {\n      this.confirmEditorMode(true);\n      this.checkAnswerLengthLimitFnBind(editor);\n      this.examSer.itemCodeChanged = false; // 标记内容未修改，防止初始化即被认为答案变动\n\n      editor.focus();\n    }\n  }\n\n  getCompleted(value) {\n    const initAnswer = this.getInitAnswer();\n    return value !== initAnswer;\n  } // 更新代码区代码\n\n\n  updateCode() {\n    // console.log(this.itemRes.answer.value);\n    const editor = this.getEditor();\n\n    if (editor) {\n      const editorVal = editor.getValue(); // console.log(editorVal);\n\n      this.itemRes.completed = this.getCompleted(editorVal);\n    }\n\n    this.gutterCellTip = \"\";\n    this.updateHighlightTip();\n    this.gutterCellAddMarker();\n    this.examSer.itemCodeChanged = true;\n    this.examSer.examEventsFromExamSvc.next({\n      type: Exam.EventFromExamService.UpdateItemResponse\n    });\n  }\n\n  checkAnswerLengthLimitFnBind(editor) {\n    const _this = this;\n\n    const doc = editor.session.getDocument();\n    doc.applyAnyDelta = doc[\"applyAnyDelta\"] || doc[\"applyDelta\"];\n\n    doc.applyDelta = function (delta) {\n      let joinedLines = delta.lines.join(\"\\n\");\n      const charLength = this.getValue().length;\n      const lineLength = this.getLength(); // console.log(delta);\n      // 如果是中文输入法，超过字数，insert、remove都return\n\n      if (_this.composition) {\n        if (charLength + joinedLines.length > this.$maxLength) {\n          console.log(\"**** Max chars exceed [code item Ace editor]：\", charLength);\n\n          _this.examSer.alertTip(\"exam.code.maxTextLengthTip\");\n\n          return false;\n        }\n\n        if (lineLength + delta.lines.length > this.$maxLineLength + 1) {\n          console.log(\"**** Max lines exceed [code item Ace editor]：\", lineLength);\n\n          _this.examSer.alertTip(\"exam.code.maxLinesTip\");\n\n          return false;\n        }\n      }\n\n      if (delta.action == \"insert\" && this.$maxLength && this.$maxLineLength) {\n        // check char max length\n        if (charLength + joinedLines.length > this.$maxLength) {\n          if (charLength >= this.$maxLength) {\n            console.log(\"**** Max chars exceed [code item Ace editor]：\", charLength);\n\n            _this.examSer.alertTip(\"exam.code.maxTextLengthTip\");\n\n            return false;\n          }\n        } // check line max length\n\n\n        if (lineLength + delta.lines.length > this.$maxLineLength + 1) {\n          console.log(\"**** Max lines exceed [code item Ace editor]：\", lineLength);\n\n          _this.examSer.alertTip(\"exam.code.maxLinesTip\");\n\n          return false;\n        }\n      }\n\n      return this.applyAnyDelta(delta);\n    };\n\n    doc.$maxLength = this.maxCharLength;\n    doc.$maxLineLength = this.maxLinesLength;\n  } // 提交代码\n\n\n  submitCode() {\n    this.showBottomCont = true;\n    this.stdExcute();\n  } // 考生自测运行\n  // 考试机客户端版(不含单机版)可以连外网直接连外网，否则连接管理机中转；浏览器版直接连外网\n\n\n  stdExcute() {\n    var _this2 = this;\n\n    const itemRes = this.itemRes;\n    const itemData = {\n      item_code: this.item.code,\n      entry_code: itemRes.answer.value,\n      code_type: this.getCodeType(this.curLan),\n      judge_type: \"run\",\n      test_pairs: this.itemRes.answer.case\n    };\n    this.inputCase = this.getInitCase(); // 提交代码至编程题外部服务器\n\n    this._loading.setValue(true);\n\n    this.autoTestSer.codeItemPosting = true;\n    this.subscriptions.push(this.examSer._postDataObservable(this.apiSer.code, itemData).pipe(map(data => {\n      if (data[\"status\"] !== \"success\") {\n        throw data;\n      }\n\n      return data;\n    })).subscribe({\n      next: data => {\n        let getTimes = 0; // post成功之后延迟4s去get result，后端可能5s之后才有result，第一次没成功，接着每2s get 一次\n\n        this.timerGetResult = setTimeout(() => {\n          const recordData = {\n            record_id: data[\"record_id\"]\n          };\n          this.subscriptions.push(this.patchItemRes(recordData).pipe(map(data => {\n            if (data[\"status\"] !== \"success\") {\n              // error will be picked up by retryWhen\n              throw data;\n            }\n\n            return data;\n          }), this.examSer.retryOnError({\n            wait: 2000,\n            maxAttempts: 5\n          }), finalize(() => {\n            this.examSer.postNum--; // console.log(\"  this.examSer.postNum--\", this.examSer.postNum);\n\n            this.autoTestSer.codeItemPosting = false;\n          })).subscribe({\n            next: function () {\n              var _ref = _asyncToGenerator(function* (data) {\n                _this2.tabActiveId = \"result\"; // 自测运行，展示结果：输入、预期输出、实际输出；运行提交，展示结果：每个用例的状态\n\n                _this2.compileError = \"\";\n                _this2.firstErrLineIndex = -1;\n                _this2.commitResult = null;\n                _this2.caseResult = null;\n                _this2.examSer.itemCodeChanged = false; // 不论运行结果，都保存答案\n\n                _this2.updateItemRes(data.result); // 展示编译结果\n\n\n                if (data.result.compile_error) {\n                  // 编译出错\n                  _this2.compileError = data.result.compile_error;\n                  _this2.firstErrLineIndex = _this2.getFirstErrLineIndex(_this2.compileError);\n\n                  _this2.updateHighlightTip(\"ace_highlight-marker\");\n\n                  _this2.gutterCellAddMarker(_this2.compileError, \"hg-lt\");\n                } else {\n                  // 编译成功\n                  // 显示单个用例结果\n                  for (const caseKey in data.result.cases) {\n                    if (caseKey.lastIndexOf(\"\\0\", caseKey.length - 2)) {\n                      _this2.caseResult = data.result.cases[caseKey];\n                      _this2.caseResult.time_sum = Math.round(_this2.caseResult.time_sum * 1000);\n\n                      if (_this2.caseResult?.exception) {\n                        _this2.firstErrLineIndex = _this2.getFirstErrLineIndex(_this2.caseResult.exception);\n\n                        _this2.updateHighlightTip(\"ace_highlight-marker\");\n\n                        _this2.gutterCellAddMarker(_this2.compileError, \"hg-lt\");\n                      }\n                    }\n                  }\n                }\n\n                _this2._loading.setValue(false);\n              });\n\n              return function next(_x) {\n                return _ref.apply(this, arguments);\n              };\n            }(),\n            error: err => {\n              console.log(`* Get code result fail`);\n              this.handleCodeApiErr(err);\n            }\n          }));\n          clearTimeout(this.timerGetResult);\n          this.timerGetResult = null;\n        }, 4000);\n      },\n      error: err => {\n        this._loading.setValue(false);\n\n        this.autoTestSer.codeItemPosting = false;\n        console.log(`* Post code fail`);\n        this.handleCodeApiErr(err);\n      }\n    }));\n  }\n\n  handleCodeApiErr(err) {\n    this._loading.setValue(false);\n\n    console.log(`Code API error : err.status: ${err.status}, err.msg: ${err.msg}`);\n    let tipKey;\n\n    switch (err.status) {\n      case \"error\":\n      case \"judging\":\n        {\n          console.log(\"** status = error: wrong post data structure [item code]\");\n          tipKey = \"exam.code.errTip.serverCallErr\";\n          break;\n        }\n\n      case \"timeout\":\n        {\n          console.log(\"** status = timeout: server timeout\");\n          tipKey = \"exam.code.errTip.timeout\";\n          break;\n        }\n\n      case \"call-error\":\n        {\n          console.log(\"** status = call-error\");\n          tipKey = \"exam.code.errTip.managerNetOffline\";\n          break;\n        }\n\n      case 0:\n      case 502:\n        {\n          console.log(\"** Client network offline\");\n          tipKey = \"exam.code.errTip.clientNetOffline\";\n          break;\n        }\n\n      case 408:\n        {\n          console.log(\"** Manager is syncing data\");\n          tipKey = \"exam.code.errTip.dataLoading\";\n          break;\n        }\n\n      default:\n        {\n          tipKey = \"exam.code.tryAgainTip\";\n        }\n    }\n\n    this._alert.setValue({\n      status: true,\n      info: {\n        bodyText: this.translate.instant(tipKey)\n      }\n    });\n  } // 提交考生试题答案\n\n\n  patchItemRes(itemData) {\n    const headers = new HttpHeaders(); // const headers = new HttpHeaders({ \"Content-Type\": \"multipart/form-data\" });\n\n    this.examSer.postNum++;\n    return this.http.post(this.apiSer.code, itemData, {\n      headers\n    });\n  }\n\n  getFirstErrLineIndex(compileErr) {\n    const reg = /Line ((\\d)+)[:,]/;\n    const matches = reg.exec(compileErr);\n\n    if (matches && matches[1]) {\n      return Number(matches[1]);\n    }\n\n    return -1;\n  }\n\n  gutterCellAddMarker(compileErr, className) {\n    const gutterCell = window[\"$\"](\".ace_gutter-layer .ace_gutter-cell\").eq(this.firstErrLineIndex - 1);\n    const gutterTipDom = document.querySelector(\".gutter-cell-tip-wrap\");\n\n    if (className) {\n      if (this.firstErrLineIndex > -1) {\n        gutterCell.addClass(className);\n        const matches = /.*[\\n|\\r\\n]/.exec(compileErr); // 截取第一行\n\n        if (matches && matches[0]) {\n          this.gutterCellTip = matches[0];\n          gutterTipDom.style.top = gutterCell.css(\"top\");\n          gutterCell.off(\"mouseover\").off(\"mouseout\").on(\"mouseover\", () => {\n            gutterTipDom.style.display = \"block\";\n          }).on(\"mouseout\", () => {\n            gutterTipDom.style.display = \"none\";\n          });\n        }\n      }\n    } else {\n      gutterCell.removeClass(\"hg-lt\");\n      gutterCell.off(\"mouseover\").off(\"mouseout\");\n    }\n  }\n\n  switchTool() {\n    this.showIntroTip = !this.showIntroTip;\n  }\n\n  closeTool() {\n    this.showIntroTip = false;\n  }\n\n  getCommitResult(cases) {\n    const result = [];\n\n    for (const caseKey in cases) {\n      if (caseKey.lastIndexOf(\"\\0\", caseKey.length - 2)) {\n        result.push(cases[caseKey]);\n      }\n    }\n\n    return result;\n  }\n\n  updateHighlightTip(className) {\n    const Range = window[\"ace\"].acequire(\"ace/range\").Range;\n    const editor = this.getEditor();\n\n    if (className) {\n      if (this.firstErrLineIndex > -1) {\n        this.markerId = editor?.session.addMarker(new Range(this.firstErrLineIndex - 1, 0, this.firstErrLineIndex - 1, 400), className, \"fullLine\", false);\n      }\n    } else {\n      editor?.session.removeMarker(this.markerId);\n    }\n  }\n\n  updateStdCases() {\n    const stdCaseEle = document.getElementById(\"std-case\");\n    const stdCase = stdCaseEle.value.replace(/\\r\\n/g, \"\\n\").replace(/\\n+/g, \"\\n\");\n    const arr = [stdCase.split(\"\\n\")];\n    this.itemRes.answer.case = arr;\n  }\n\n  getCodeType(curLan) {\n    return this.lanPost[curLan];\n  }\n\n  allCasesPass() {\n    return this.getACCaseLength(this.commitResult) === this.commitResult.length;\n  }\n\n  getAceMode() {\n    const aceModes = {\n      \"C#\": \"csharp\",\n      Go: \"golang\",\n      Swift: \"swift\",\n      Javascript: \"javascript\",\n      Python2: \"python\",\n      Python3: \"python\",\n      C: \"c_cpp\",\n      \"C++\": \"c_cpp\",\n      Java: \"java\",\n      Sql: \"sql\"\n    };\n\n    if (aceModes[this.curLan]) {\n      return aceModes[this.curLan];\n    }\n\n    return \"java\";\n  }\n\n  getACCaseLength(cases) {\n    let n = 0;\n    cases.forEach(element => {\n      if (element.verdict === \"AC\") {\n        n++;\n      }\n    });\n    return n;\n  }\n\n  confirmChangeLan() {\n    this.showAceEditor = false;\n    setTimeout(() => {\n      this.showAceEditor = true;\n      this.showBottomCont = false;\n      this.showChangeLanTip = false;\n      this.curLan = this.lanToBeSet;\n      this.showEnvInfoBlock = this.getEnvInfo(this.curLan);\n      this.caseResult = null;\n      this.commitResult = null;\n      this.compileError = \"\";\n      this.itemRes.completed = false;\n      this.examSer.itemCodeChanged = false;\n      this.closeLanMenu();\n      setTimeout(() => {\n        const editor = this.getEditor();\n        editor.session.setMode(\"ace/mode/\" + this.getAceMode());\n        this.confirmEditorMode(true);\n        editor.setValue(this.getInitAnswer());\n        this.updateItemRes();\n        this.updateHighlightTip();\n        this.gutterCellAddMarker();\n        editor.focus();\n        this.checkAnswerLengthLimitFnBind(editor);\n      });\n    });\n  }\n\n  focusEditor() {\n    const editor = this.getEditor();\n\n    if (editor) {\n      editor.focus();\n    }\n  }\n\n  updateItemRes(result) {\n    const responseInfo = {\n      item: this.item,\n      result: result,\n      code_language: this.lanPost[this.curLan],\n      value: this.itemRes.answer.value\n    };\n    this.examSer.updateItemResponse(responseInfo);\n  }\n\n  cancelChangeLan() {\n    this.showChangeLanTip = false;\n  }\n\n  confirmResetAnswer() {\n    const editor = this.getEditor();\n    this.showResetAnswerTip = false;\n    editor.setValue(this.getInitAnswer());\n    this.focusEditor();\n  }\n\n  getInitAnswer() {\n    const initAnswer = this.item.content.init_answer;\n\n    if (initAnswer?.[this.curLan]) {\n      return initAnswer[this.curLan];\n    }\n\n    return \"\";\n  }\n\n  cancelResetAnswer() {\n    this.showResetAnswerTip = false;\n  }\n\n  setDefaultLan() {\n    this.curLan = this.lanDisplayed[this.itemRes.answer.code_language]; // this.hasInitAnswer = this.checkInitAnswer();\n\n    this.showEnvInfoBlock = this.getEnvInfo(this.curLan);\n    const editor = this.getEditor();\n    editor.session.setMode(\"ace/mode/\" + this.getAceMode());\n  }\n\n  selectLan(lan) {\n    if (lan === this.curLan) {\n      this.closeLanMenu();\n      return;\n    } // 如果没有内容，不提示\n\n\n    const editor = this.getEditor();\n    this.showChangeLanTip = editor.getValue() ? true : false;\n    this.lanToBeSet = lan;\n\n    if (!this.showChangeLanTip) {\n      this.confirmChangeLan();\n    }\n  }\n\n  toggleLanMenu() {\n    this.showLanList = !this.showLanList;\n  }\n\n  toggleEnvInfo(isShow) {\n    this.showEnvInfoCont = isShow;\n  }\n\n  closeLanMenu() {\n    this.showLanList = false;\n  }\n\n  getEnvInfo(lanSet) {\n    const details = {\n      C: {\n        version: this.translate.instant(\"exam.code.lan-c\")\n      },\n      \"C++\": {\n        version: this.translate.instant(\"exam.code.lan-cpp\")\n      },\n      \"C#\": {\n        version: this.translate.instant(\"exam.code.lan-cShar\")\n      },\n      Java: {\n        version: this.translate.instant(\"exam.code.lan-java\")\n      },\n      Swift: {\n        version: this.translate.instant(\"exam.code.lan-swift\")\n      },\n      Javascript: {\n        version: this.translate.instant(\"exam.code.lan-js\")\n      },\n      Go: {\n        version: this.translate.instant(\"exam.code.lan-go\")\n      },\n      Python2: {\n        version: this.translate.instant(\"exam.code.lan-p2\")\n      },\n      Python3: {\n        version: this.translate.instant(\"exam.code.lan-p3\")\n      },\n      Sql: {\n        version: \"\"\n      }\n    };\n    return details[lanSet] ? details[lanSet] : null;\n  }\n\n  closeFirstInTip() {\n    this.firstIn = false;\n    this.itemRes.first_in = false;\n  }\n\n  getInitCase() {\n    const initCase = this.itemRes.answer.case;\n    let initCaseStr = \"\";\n\n    if (initCase.length > 0) {\n      const caseArr = initCase[0];\n\n      for (let i = 0, len = caseArr.length; i < len; i++) {\n        if (i === caseArr.length - 1) {\n          initCaseStr += caseArr[i];\n        } else {\n          initCaseStr += caseArr[i] + \"\\n\";\n        }\n      }\n    }\n\n    return initCaseStr;\n  }\n\n  showEditorModeModal() {\n    this.isEditorModeModalShowed = true;\n  }\n\n  confirmEditorMode(ifConfirmMode) {\n    this.isEditorModeModalShowed = false;\n\n    if (ifConfirmMode) {\n      const {\n        fontSize,\n        mode\n      } = this.editorConfig;\n      const theme = mode === \"black\" ? \"ace/theme/chaos\" : \"ace/theme/clouds\"; // console.log(this.editorConfig);\n\n      const editor = this.getEditor();\n      editor.setFontSize(fontSize);\n      editor.setTheme(theme);\n      this.areaTheme = mode; // 保存编辑器风格设置到item response\n\n      this.updateEditorTheme({\n        fontSize: fontSize,\n        theme: this.areaTheme\n      });\n    }\n  }\n\n  updateEditorTheme(data) {\n    this.itemRes.editor_config = data;\n  }\n\n}\n\nCodeComponent.ɵfac = function CodeComponent_Factory(t) {\n  return new (t || CodeComponent)(i0.ɵɵdirectiveInject(i1.ExamService), i0.ɵɵdirectiveInject(i2.FormService), i0.ɵɵdirectiveInject(i3.TranslateService), i0.ɵɵdirectiveInject(i4.AutoTestService), i0.ɵɵdirectiveInject(i5.CustomLoadingService), i0.ɵɵdirectiveInject(i6.APIService), i0.ɵɵdirectiveInject(i7.HttpClient), i0.ɵɵdirectiveInject(i8.CustomAlertService));\n};\n\nCodeComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: CodeComponent,\n  selectors: [[\"app-code\"]],\n  viewQuery: function CodeComponent_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(AceComponent, 5);\n    }\n\n    if (rf & 2) {\n      let _t;\n\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.componentRef = _t.first);\n    }\n  },\n  inputs: {\n    itemId: \"itemId\",\n    showGroupInstr: \"showGroupInstr\"\n  },\n  features: [i0.ɵɵNgOnChangesFeature],\n  decls: 53,\n  vars: 56,\n  consts: [[1, \"item\", \"item-code\", 3, \"ngClass\"], [\"unit\", \"percent\", 3, \"direction\", \"gutterSize\"], [\"graphSplit\", \"\"], [3, \"size\", \"minSize\", \"maxSize\"], [1, \"wrap-item-stem\", \"p-3\"], [1, \"item-index\", \"float-left\", \"rounded\", \"bg-primary\", \"text-white\", \"px-1\", \"mr-1\"], [\"richText\", \"\", 1, \"item-stem\", \"mb-3\", 3, \"innerHTML\"], [3, \"size\"], [1, \"item-answer-area\", 3, \"ngClass\"], [1, \"area-cont\"], [1, \"top\"], [1, \"wrap-lan-set\", \"float-left\", \"ml-3\", \"pt-1\", 3, \"clickOutside\"], [\"class\", \"env-info\", 4, \"ngIf\"], [1, \"lan-set\", \"pl-2\", \"ml-2\", 3, \"click\"], [\"role\", \"button\", \"class\", \"iconfont font-min float-right mr-2\", 4, \"ngIf\"], [\"class\", \"lan-list shadow-sm\", 4, \"ngIf\"], [1, \"float-right\", \"pt-1\"], [\"role\", \"button\", \"placement\", \"left\", 1, \"iconfont\", \"mr-2\", \"p-2\", 3, \"ngClass\", \"ngbTooltip\", \"click\"], [\"appAppMotationObserver\", \"\", 3, \"innerHtmlRendered\"], [\"class\", \"iconfont mr-2 p-2\", \"role\", \"button\", \"placement\", \"left\", 3, \"ngClass\", \"ngbTooltip\", \"click\", 4, \"ngIf\"], [1, \"main\"], [\"class\", \"\", 3, \"config\", \"mode\", \"value\", \"valueChange\", 4, \"ngIf\"], [1, \"gutter-cell-tip-wrap\"], [4, \"ngIf\"], [1, \"input-output\", 3, \"ngClass\"], [1, \"btn\", \"btn-primary\", \"btn-run\", \"float-right\", 3, \"click\"], [\"class\", \"btn-expand float-right\", 3, \"click\", 4, \"ngIf\"], [\"ngbNav\", \"\", 1, \"nav-tabs\", \"font-min\", 3, \"activeId\", \"activeIdChange\"], [\"nav\", \"ngbNav\"], [\"ngbNavItem\", \"\", 3, \"ngbNavItem\", \"click\", 4, \"ngIf\"], [\"ngbNavItem\", \"\", 3, \"ngbNavItem\", \"click\"], [\"ngbNavLink\", \"\", 1, \"p-1\", \"pl-2\", \"pr-2\"], [\"ngbNavContent\", \"\"], [\"class\", \"p-2\", 3, \"ngbNavOutlet\", 4, \"ngIf\"], [3, \"bodyText\", \"onConfirm\", \"onCancel\", 4, \"ngIf\"], [\"class\", \"joy-custom-modal\", 4, \"ngIf\"], [1, \"env-info\"], [\"role\", \"button\", 1, \"iconfont\", 3, \"click\"], [\"class\", \"env-info-cont\", 4, \"ngIf\"], [1, \"env-info-cont\"], [1, \"title\", \"h6\", \"pt-3\", \"pl-4\", \"pr-4\", \"pb-3\"], [\"role\", \"button\", 1, \"iconfont\", \"float-right\", \"text-gray\", \"font-min\", 3, \"click\"], [1, \"pt-2\", \"pl-4\", \"pr-4\", \"pb-3\"], [1, \"pb-2\"], [1, \"text-gray\"], [\"role\", \"button\", 1, \"iconfont\", \"font-min\", \"float-right\", \"mr-2\"], [1, \"lan-list\", \"shadow-sm\"], [3, \"innerHTML\", \"click\", 4, \"ngFor\", \"ngForOf\"], [3, \"innerHTML\", \"click\"], [1, \"\", 3, \"config\", \"mode\", \"value\", \"valueChange\"], [\"aceEditor\", \"\"], [1, \"first-in-tip\", \"tooltip\", \"show\", \"bs-tooltip-bottom\", \"float-right\"], [1, \"tooltip-inner\", \"inner\"], [1, \"ng-star-inserted\"], [1, \"btn\", \"btn-confirm\", 3, \"click\"], [1, \"arrow\"], [1, \"btn-expand\", \"float-right\", 3, \"click\"], [1, \"box-arrow-up\"], [1, \"arrow-up\"], [1, \"box-arrow-down\"], [1, \"arrow-down\"], [\"id\", \"std-case\", 1, \"case-textarea\", \"p-2\", 3, \"value\", \"blur\"], [1, \"tip\", \"font-min\"], [1, \"compile-result\", \"error\"], [1, \"text-danger\", \"compile-err-title\"], [1, \"text-danger\", 3, \"innerHTML\"], [1, \"compile-result\"], [1, \"table-result\", \"w-100\", \"text-center\"], [1, \"p-2\", 3, \"ngbNavOutlet\"], [3, \"bodyText\", \"onConfirm\", \"onCancel\"], [1, \"joy-custom-modal\"], [1, \"wrap-joy-custom-modal-cont\", \"bg-white\"], [1, \"joy-custom-modal-head\"], [1, \"joy-custom-modal-title\"], [1, \"joy-custom-modal-cont\", \"pl-4\"], [1, \"ml-5\", \"mt-4\", \"mb-3\"], [1, \"mr-4\"], [1, \"form-control\", \"form-select\", \"d-inline-block\", \"w-50\", 3, \"ngModel\", \"ngModelChange\"], [4, \"ngFor\", \"ngForOf\"], [1, \"ml-5\", \"mb-4\", \"mode-select\"], [1, \"radio-inline\", \"mr-3\", \"mb-0\"], [1, \"input-block\"], [\"type\", \"radio\", \"name\", \"optradio\", \"value\", \"black\", 1, \"mr-2\", 3, \"ngModel\", \"ngModelChange\"], [1, \"radio-inline\", \"mb-0\"], [\"type\", \"radio\", \"name\", \"optradio\", \"value\", \"white\", 1, \"mr-2\", 3, \"ngModel\", \"ngModelChange\"], [1, \"joy-custom-modal-foot\", \"row\", \"pt-3\"], [1, \"yk-btn-primary\", \"mr-4\", 3, \"click\"], [1, \"yk-btn-bd-secondary\", 3, \"click\"]],\n  template: function CodeComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"div\", 0)(1, \"as-split\", 1, 2)(3, \"as-split-area\", 3)(4, \"div\", 4)(5, \"span\", 5);\n      i0.ɵɵtext(6);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelement(7, \"div\", 6);\n      i0.ɵɵpipe(8, \"safeHtml\");\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(9, \"as-split-area\", 7)(10, \"div\", 8)(11, \"div\", 9)(12, \"div\", 10)(13, \"div\", 11);\n      i0.ɵɵlistener(\"clickOutside\", function CodeComponent_Template_div_clickOutside_13_listener() {\n        return ctx.closeLanMenu();\n      });\n      i0.ɵɵtemplate(14, CodeComponent_span_14_Template, 4, 1, \"span\", 12);\n      i0.ɵɵelementStart(15, \"span\", 13);\n      i0.ɵɵlistener(\"click\", function CodeComponent_Template_span_click_15_listener() {\n        return ctx.toggleLanMenu();\n      });\n      i0.ɵɵtext(16);\n      i0.ɵɵtemplate(17, CodeComponent_i_17_Template, 2, 0, \"i\", 14);\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(18, CodeComponent_ul_18_Template, 2, 1, \"ul\", 15);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(19, \"div\", 16)(20, \"i\", 17);\n      i0.ɵɵlistener(\"click\", function CodeComponent_Template_i_click_20_listener() {\n        return ctx.resetAnswer();\n      });\n      i0.ɵɵpipe(21, \"translate\");\n      i0.ɵɵtext(22, \"\\uE69D\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(23, \"i\", 17);\n      i0.ɵɵlistener(\"click\", function CodeComponent_Template_i_click_23_listener() {\n        return ctx.showEditorModeModal();\n      });\n      i0.ɵɵpipe(24, \"translate\");\n      i0.ɵɵtext(25, \"\\uE62C\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(26, \"span\", 18);\n      i0.ɵɵlistener(\"innerHtmlRendered\", function CodeComponent_Template_span_innerHtmlRendered_26_listener() {\n        return ctx.resizeEditor();\n      });\n      i0.ɵɵtemplate(27, CodeComponent_i_27_Template, 3, 7, \"i\", 19);\n      i0.ɵɵtemplate(28, CodeComponent_i_28_Template, 3, 7, \"i\", 19);\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵelementStart(29, \"div\", 20);\n      i0.ɵɵtemplate(30, CodeComponent_ace_30_Template, 2, 3, \"ace\", 21);\n      i0.ɵɵelementStart(31, \"div\", 22);\n      i0.ɵɵtemplate(32, CodeComponent_span_32_Template, 2, 1, \"span\", 23);\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(33, \"div\", 24);\n      i0.ɵɵtemplate(34, CodeComponent_ng_container_34_Template, 11, 6, \"ng-container\", 23);\n      i0.ɵɵelementStart(35, \"button\", 25);\n      i0.ɵɵlistener(\"click\", function CodeComponent_Template_button_click_35_listener() {\n        return ctx.submitCode();\n      });\n      i0.ɵɵtext(36);\n      i0.ɵɵpipe(37, \"translate\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(38, \"div\", 18);\n      i0.ɵɵlistener(\"innerHtmlRendered\", function CodeComponent_Template_div_innerHtmlRendered_38_listener() {\n        return ctx.resizeEditor();\n      });\n      i0.ɵɵtemplate(39, CodeComponent_button_39_Template, 3, 0, \"button\", 26);\n      i0.ɵɵtemplate(40, CodeComponent_button_40_Template, 3, 0, \"button\", 26);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(41, \"ul\", 27, 28);\n      i0.ɵɵlistener(\"activeIdChange\", function CodeComponent_Template_ul_activeIdChange_41_listener($event) {\n        return ctx.tabActiveId = $event;\n      });\n      i0.ɵɵtemplate(43, CodeComponent_li_43_Template, 5, 4, \"li\", 29);\n      i0.ɵɵelementStart(44, \"li\", 30);\n      i0.ɵɵlistener(\"click\", function CodeComponent_Template_li_click_44_listener() {\n        return ctx.toggleBottomCont(true);\n      });\n      i0.ɵɵelementStart(45, \"a\", 31);\n      i0.ɵɵtext(46);\n      i0.ɵɵpipe(47, \"translate\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(48, CodeComponent_ng_template_48_Template, 3, 3, \"ng-template\", 32);\n      i0.ɵɵelementEnd()();\n      i0.ɵɵtemplate(49, CodeComponent_div_49_Template, 1, 1, \"div\", 33);\n      i0.ɵɵelementEnd()()()()()();\n      i0.ɵɵtemplate(50, CodeComponent_custom_confirm_50_Template, 1, 1, \"custom-confirm\", 34);\n      i0.ɵɵtemplate(51, CodeComponent_custom_confirm_51_Template, 1, 1, \"custom-confirm\", 34);\n      i0.ɵɵtemplate(52, CodeComponent_div_52_Template, 36, 25, \"div\", 35);\n    }\n\n    if (rf & 2) {\n      i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(44, _c1, ctx.itemFullScreen));\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"gutterSize\", 10);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"size\", ctx.ltPercentVal)(\"minSize\", 10)(\"maxSize\", 90);\n      i0.ɵɵadvance(3);\n      i0.ɵɵtextInterpolate(ctx.item.index);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"innerHTML\", i0.ɵɵpipeBind1(8, 34, ctx.item.content.stem), i0.ɵɵsanitizeHtml);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"size\", 100 - ctx.ltPercentVal);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(46, _c2, ctx.areaTheme === \"white\"));\n      i0.ɵɵadvance(4);\n      i0.ɵɵproperty(\"ngIf\", ctx.showEnvInfoBlock);\n      i0.ɵɵadvance(2);\n      i0.ɵɵtextInterpolate1(\"\", ctx.curLan, \" \\u00A0\\u00A0\\u00A0\\u00A0 \");\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.item.code_languages.length > 1);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.showLanList && ctx.item.code_languages.length > 1);\n      i0.ɵɵadvance(2);\n      i0.ɵɵpropertyInterpolate(\"ngbTooltip\", i0.ɵɵpipeBind1(21, 36, \"exam.code.reset\"));\n      i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(48, _c0, ctx.areaTheme !== \"white\", ctx.areaTheme === \"white\"));\n      i0.ɵɵadvance(3);\n      i0.ɵɵpropertyInterpolate(\"ngbTooltip\", i0.ɵɵpipeBind1(24, 38, \"exam.code.config\"));\n      i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(51, _c0, ctx.areaTheme !== \"white\", ctx.areaTheme === \"white\"));\n      i0.ɵɵadvance(4);\n      i0.ɵɵproperty(\"ngIf\", !ctx.itemFullScreen);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.itemFullScreen);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngIf\", ctx.showAceEditor);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngIf\", !!ctx.gutterCellTip);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(54, _c3, !ctx.showBottomCont));\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.firstIn);\n      i0.ɵɵadvance(2);\n      i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(37, 40, \"exam.code.submit\"), \" \");\n      i0.ɵɵadvance(3);\n      i0.ɵɵproperty(\"ngIf\", !ctx.showBottomCont);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.showBottomCont);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"activeId\", ctx.tabActiveId);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngIf\", ctx.curLan !== \"Sql\");\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngbNavItem\", \"result\");\n      i0.ɵɵadvance(2);\n      i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(47, 42, \"exam.code.result\"));\n      i0.ɵɵadvance(3);\n      i0.ɵɵproperty(\"ngIf\", ctx.showBottomCont);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.showChangeLanTip);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.showResetAnswerTip);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.isEditorModeModalShowed);\n    }\n  },\n  dependencies: [i9.NgClass, i9.NgForOf, i9.NgIf, i10.NgSelectOption, i10.ɵNgSelectMultipleOption, i10.DefaultValueAccessor, i10.SelectControlValueAccessor, i10.RadioControlValueAccessor, i10.NgControlStatus, i10.NgModel, i11.CustomConfirmComponent, i12.ClickOutsideDirective, i13.AppMotationObserverDirective, i14.NgbNavContent, i14.NgbNav, i14.NgbNavItem, i14.NgbNavLink, i14.NgbNavOutlet, i14.NgbTooltip, i15.SplitComponent, i15.SplitAreaDirective, i16.AceComponent, i17.SafeHtmlPipe, i3.TranslatePipe],\n  styles: [\"@charset \\\"UTF-8\\\";\\n\\n\\n\\n\\n\\n\\n\\n.item-code[_ngcontent-%COMP%] {\\n  position: absolute;\\n  height: 100%;\\n  left: 0;\\n  right: 0;\\n  \\n}\\n.item-code[_ngcontent-%COMP%]     .as-split-gutter {\\n  cursor: pointer;\\n}\\n.item-code[_ngcontent-%COMP%]     textarea {\\n  min-height: auto;\\n}\\n.item-code.code-fullcreen[_ngcontent-%COMP%] {\\n  position: fixed;\\n  left: 0;\\n  top: 0;\\n  bottom: 0;\\n  right: 0;\\n  z-index: 999;\\n  background: #fff;\\n}\\n.item-code[_ngcontent-%COMP%]   .wrap-item-stem[_ngcontent-%COMP%] {\\n  overflow: auto;\\n  height: 100%;\\n  border-right: 1px solid #eee;\\n}\\n.item-code[_ngcontent-%COMP%]   .btn-expand[_ngcontent-%COMP%] {\\n  position: absolute;\\n  width: 26px;\\n  height: 26px;\\n  border: 1px solid #ccc;\\n  border-radius: 50%;\\n  right: 50%;\\n  transform: translate(0, -13px);\\n}\\n.item-code[_ngcontent-%COMP%]   .box-arrow-up[_ngcontent-%COMP%] {\\n  transform: translate(10%, 10%);\\n}\\n.item-code[_ngcontent-%COMP%]   .box-arrow-down[_ngcontent-%COMP%] {\\n  transform: translate(5%, -10%);\\n}\\n.item-code[_ngcontent-%COMP%]   .arrow-up[_ngcontent-%COMP%] {\\n  width: 11px;\\n  height: 11px;\\n  border-top: 2px solid #000;\\n  border-right: 2px solid #000;\\n  transform: rotate(-45deg);\\n}\\n.item-code[_ngcontent-%COMP%]   .arrow-down[_ngcontent-%COMP%] {\\n  width: 11px;\\n  height: 11px;\\n  border-top: 2px solid #000;\\n  border-right: 2px solid #000;\\n  transform: rotate(135deg);\\n}\\n.item-code[_ngcontent-%COMP%]   .first-in-tip.tooltip[_ngcontent-%COMP%] {\\n  right: 8px;\\n  transform: translate(0, -42px);\\n}\\n.item-code[_ngcontent-%COMP%]   .first-in-tip.tooltip[_ngcontent-%COMP%]   .inner[_ngcontent-%COMP%] {\\n  display: inline-flex;\\n  max-width: 1000px;\\n  text-align: center;\\n  align-items: baseline;\\n  justify-content: space-around;\\n  background-color: #ff9c3e;\\n}\\n.item-code[_ngcontent-%COMP%]   .first-in-tip.tooltip[_ngcontent-%COMP%]   .inner[_ngcontent-%COMP%]   .btn-confirm[_ngcontent-%COMP%] {\\n  padding: 0.25rem 0.5rem;\\n  font-size: 0.875rem;\\n  line-height: 1;\\n  border-radius: 4px;\\n  color: #f8f9fa;\\n  background-color: transparent;\\n  background-image: none;\\n  border-color: #f8f9fa;\\n  border: 1px solid;\\n}\\n.item-code[_ngcontent-%COMP%]   .first-in-tip.tooltip[_ngcontent-%COMP%]   .arrow[_ngcontent-%COMP%] {\\n  right: 25px;\\n  top: 2.3rem;\\n  transform: rotate(180deg);\\n}\\n.item-code[_ngcontent-%COMP%]   .first-in-tip.tooltip[_ngcontent-%COMP%]   .arrow[_ngcontent-%COMP%]::before {\\n  border-bottom-color: #ff9c3e;\\n}\\n.item-code[_ngcontent-%COMP%]   .item-answer-area[_ngcontent-%COMP%] {\\n  height: 100%;\\n}\\n.item-code[_ngcontent-%COMP%]   .item-answer-area[_ngcontent-%COMP%]   .area-cont[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  flex-direction: column;\\n  height: 100%;\\n}\\n.item-code[_ngcontent-%COMP%]   .item-answer-area[_ngcontent-%COMP%]   .area-cont[_ngcontent-%COMP%]   .top[_ngcontent-%COMP%] {\\n  height: 40px;\\n  background: #2d2c31;\\n  border-bottom: 1px solid #4e4d53;\\n}\\n.item-code[_ngcontent-%COMP%]   .item-answer-area[_ngcontent-%COMP%]   .area-cont[_ngcontent-%COMP%]   .top[_ngcontent-%COMP%]   .env-info[_ngcontent-%COMP%]    > i[_ngcontent-%COMP%] {\\n  color: #666;\\n}\\n.item-code[_ngcontent-%COMP%]   .item-answer-area[_ngcontent-%COMP%]   .area-cont[_ngcontent-%COMP%]   .top[_ngcontent-%COMP%]   .env-info-cont[_ngcontent-%COMP%] {\\n  position: absolute;\\n  left: 0;\\n  top: 35px;\\n  z-index: 9;\\n  background: #2d2c31;\\n  color: #eee;\\n  width: 400px;\\n  border: 1px solid #000;\\n  border-radius: 4px;\\n}\\n.item-code[_ngcontent-%COMP%]   .item-answer-area[_ngcontent-%COMP%]   .area-cont[_ngcontent-%COMP%]   .top[_ngcontent-%COMP%]   .env-info-cont[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid #666;\\n}\\n.item-code[_ngcontent-%COMP%]   .item-answer-area[_ngcontent-%COMP%]   .area-cont[_ngcontent-%COMP%]   .top[_ngcontent-%COMP%]   .lan-set[_ngcontent-%COMP%] {\\n  border: 1px solid #64626d;\\n  border-radius: 4px;\\n  display: inline-block;\\n  height: 30px;\\n  min-width: 100px;\\n  line-height: 30px;\\n  color: #aeaeae;\\n  font-size: 16px;\\n}\\n.item-code[_ngcontent-%COMP%]   .item-answer-area[_ngcontent-%COMP%]   .area-cont[_ngcontent-%COMP%]   .top[_ngcontent-%COMP%]   .lan-list[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 40px;\\n  min-width: 200px;\\n  background-color: #2d2c31;\\n  color: #ccc;\\n  border: 1px solid #000;\\n  z-index: 8;\\n}\\n.item-code[_ngcontent-%COMP%]   .item-answer-area[_ngcontent-%COMP%]   .area-cont[_ngcontent-%COMP%]   .top[_ngcontent-%COMP%]   .lan-list[_ngcontent-%COMP%]   li[_ngcontent-%COMP%] {\\n  padding: 5px;\\n  cursor: pointer;\\n}\\n.item-code[_ngcontent-%COMP%]   .item-answer-area[_ngcontent-%COMP%]   .area-cont[_ngcontent-%COMP%]   .top[_ngcontent-%COMP%]   .lan-list[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]:hover {\\n  background-color: #666;\\n  color: #000;\\n}\\n.item-code[_ngcontent-%COMP%]   .item-answer-area[_ngcontent-%COMP%]   .area-cont[_ngcontent-%COMP%]   .top[_ngcontent-%COMP%]   .first-in-tip[_ngcontent-%COMP%] {\\n  top: 40px;\\n}\\n.item-code[_ngcontent-%COMP%]   .item-answer-area[_ngcontent-%COMP%]   .area-cont[_ngcontent-%COMP%]   .top[_ngcontent-%COMP%]   .first-in-tip[_ngcontent-%COMP%]   .arrow[_ngcontent-%COMP%] {\\n  top: 0;\\n  height: 2.82rem;\\n  transform: rotate(180deg);\\n}\\n.item-code[_ngcontent-%COMP%]   .item-answer-area[_ngcontent-%COMP%]   .area-cont[_ngcontent-%COMP%]   .main[_ngcontent-%COMP%] {\\n  flex: 1;\\n  overflow: auto;\\n  background-color: #161616;\\n  padding-top: 10px;\\n}\\n.item-code[_ngcontent-%COMP%]   .item-answer-area[_ngcontent-%COMP%]   .area-cont[_ngcontent-%COMP%]   .main[_ngcontent-%COMP%]   .ace-editor[_ngcontent-%COMP%] {\\n  min-height: 100%;\\n}\\n.item-code[_ngcontent-%COMP%]   .item-answer-area[_ngcontent-%COMP%]   .area-cont[_ngcontent-%COMP%]   .main[_ngcontent-%COMP%]   .compile-err-txt[_ngcontent-%COMP%] {\\n  background: #77535d;\\n  overflow: auto;\\n  \\n}\\n.item-code[_ngcontent-%COMP%]   .item-answer-area[_ngcontent-%COMP%]   .area-cont[_ngcontent-%COMP%]   .main[_ngcontent-%COMP%]     .ace_highlight-marker {\\n  position: absolute; \\n  background: #ff4c55; \\n  z-index: 1000; \\n}\\n.item-code[_ngcontent-%COMP%]   .item-answer-area[_ngcontent-%COMP%]   .area-cont[_ngcontent-%COMP%]   .main[_ngcontent-%COMP%]     .ace_gutter-cell.hg-lt {\\n  background: #ff4c55;\\n  color: #ff4c55;\\n  position: relative;\\n}\\n.item-code[_ngcontent-%COMP%]   .item-answer-area[_ngcontent-%COMP%]   .area-cont[_ngcontent-%COMP%]   .main[_ngcontent-%COMP%]     .ace_gutter-cell.hg-lt:hover > .gutter-cell-tip-wrap {\\n  display: block;\\n  position: absolute;\\n  right: 0;\\n}\\n.item-code[_ngcontent-%COMP%]   .item-answer-area[_ngcontent-%COMP%]   .area-cont[_ngcontent-%COMP%]   .main[_ngcontent-%COMP%]     .ace_gutter-cell.hg-lt:before {\\n  content: \\\"\\\";\\n  display: inline-block;\\n  height: 16px;\\n  width: 16px;\\n  background: url('info-icon.png') #ff4c55 no-repeat;\\n  background-size: contain;\\n  position: absolute;\\n}\\n.item-code[_ngcontent-%COMP%]   .item-answer-area[_ngcontent-%COMP%]   .area-cont[_ngcontent-%COMP%]   .main[_ngcontent-%COMP%]   .gutter-cell-tip-wrap[_ngcontent-%COMP%] {\\n  position: absolute;\\n  border: 1px solid;\\n  background: #fff;\\n  padding: 0 5px;\\n  z-index: 9;\\n  left: 42px;\\n  display: none;\\n}\\n.item-code[_ngcontent-%COMP%]   .item-answer-area[_ngcontent-%COMP%]   .area-cont[_ngcontent-%COMP%]   .input-output[_ngcontent-%COMP%] {\\n  min-height: 45px;\\n  height: 310px;\\n  background: #fff;\\n  z-index: 9;\\n  position: relative;\\n}\\n.item-code[_ngcontent-%COMP%]   .item-answer-area[_ngcontent-%COMP%]   .area-cont[_ngcontent-%COMP%]   .input-output.fold-up[_ngcontent-%COMP%] {\\n  height: auto;\\n}\\n.item-code[_ngcontent-%COMP%]   .item-answer-area[_ngcontent-%COMP%]   .area-cont[_ngcontent-%COMP%]   .input-output[_ngcontent-%COMP%]   .btn-run[_ngcontent-%COMP%] {\\n  position: absolute;\\n  right: 8px;\\n  font-size: 14px;\\n  padding: 4px;\\n  transform: translate(0, 6px);\\n}\\n.item-code[_ngcontent-%COMP%]   .item-answer-area[_ngcontent-%COMP%]   .area-cont[_ngcontent-%COMP%]   .input-output[_ngcontent-%COMP%]   .nav-tabs[_ngcontent-%COMP%] {\\n  background: #fff;\\n  border-bottom: 1px solid #bcbec3;\\n  padding-top: 5px;\\n}\\n.item-code[_ngcontent-%COMP%]   .item-answer-area[_ngcontent-%COMP%]   .area-cont[_ngcontent-%COMP%]   .input-output[_ngcontent-%COMP%]   .nav-tabs[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%] {\\n  margin-top: 5px;\\n  margin-right: 5px;\\n  margin-bottom: -2px;\\n}\\n.item-code[_ngcontent-%COMP%]   .item-answer-area[_ngcontent-%COMP%]   .area-cont[_ngcontent-%COMP%]   .input-output[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%] {\\n  color: #999;\\n}\\n.item-code[_ngcontent-%COMP%]   .item-answer-area[_ngcontent-%COMP%]   .area-cont[_ngcontent-%COMP%]   .input-output[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]:hover {\\n  color: #447dff;\\n  border-color: transparent;\\n}\\n.item-code[_ngcontent-%COMP%]   .item-answer-area[_ngcontent-%COMP%]   .area-cont[_ngcontent-%COMP%]   .input-output[_ngcontent-%COMP%]   .nav-link.active[_ngcontent-%COMP%] {\\n  color: #447dff;\\n  background-color: #fff;\\n  border: 1px solid #bcbec3;\\n  border-bottom-color: #fff;\\n}\\n.item-code[_ngcontent-%COMP%]   .item-answer-area[_ngcontent-%COMP%]   .area-cont[_ngcontent-%COMP%]   .input-output[_ngcontent-%COMP%]   .compile-result[_ngcontent-%COMP%] {\\n  overflow: auto;\\n  height: 255px;\\n}\\n.item-code[_ngcontent-%COMP%]   .item-answer-area[_ngcontent-%COMP%]   .area-cont[_ngcontent-%COMP%]   .input-output[_ngcontent-%COMP%]   .compile-result.error[_ngcontent-%COMP%] {\\n  position: relative;\\n}\\n.item-code[_ngcontent-%COMP%]   .item-answer-area[_ngcontent-%COMP%]   .area-cont[_ngcontent-%COMP%]   .input-output[_ngcontent-%COMP%]   .compile-result.error[_ngcontent-%COMP%]   pre[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 30px;\\n  bottom: 0;\\n  left: 0;\\n  right: 0;\\n  margin-bottom: 0;\\n  overflow: auto;\\n}\\n.item-code[_ngcontent-%COMP%]   .item-answer-area[_ngcontent-%COMP%]   .area-cont[_ngcontent-%COMP%]   .input-output[_ngcontent-%COMP%]   .case-textarea[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 200px;\\n  background: #fff;\\n  border: 1px solid #bcbec3;\\n  resize: none;\\n  color: #3d3c41;\\n}\\n.item-code[_ngcontent-%COMP%]   .item-answer-area[_ngcontent-%COMP%]   .area-cont[_ngcontent-%COMP%]   .input-output[_ngcontent-%COMP%]   .tip[_ngcontent-%COMP%] {\\n  color: #c1c0c2;\\n}\\n.item-code[_ngcontent-%COMP%]   .item-answer-area[_ngcontent-%COMP%]   .area-cont[_ngcontent-%COMP%]   .input-output[_ngcontent-%COMP%]   .table-result[_ngcontent-%COMP%] {\\n  border: 1px solid #bcbec3;\\n}\\n.item-code[_ngcontent-%COMP%]   .item-answer-area[_ngcontent-%COMP%]   .area-cont[_ngcontent-%COMP%]   .input-output[_ngcontent-%COMP%]   .table-result[_ngcontent-%COMP%]   thead[_ngcontent-%COMP%]    > tr[_ngcontent-%COMP%] {\\n  background-color: #a09fa1;\\n  color: #3d3c41;\\n}\\n.item-code[_ngcontent-%COMP%]   .item-answer-area[_ngcontent-%COMP%]   .area-cont[_ngcontent-%COMP%]   .input-output[_ngcontent-%COMP%]   .table-result[_ngcontent-%COMP%]   th[_ngcontent-%COMP%], .item-code[_ngcontent-%COMP%]   .item-answer-area[_ngcontent-%COMP%]   .area-cont[_ngcontent-%COMP%]   .input-output[_ngcontent-%COMP%]   .table-result[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n  border: none;\\n  border-right: 1px solid #bcbec3;\\n  padding: 8px;\\n}\\n.item-code[_ngcontent-%COMP%]   .item-answer-area[_ngcontent-%COMP%]   .area-cont[_ngcontent-%COMP%]   .input-output[_ngcontent-%COMP%]   .table-result[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n  color: #3d3c41;\\n}\\n.item-code[_ngcontent-%COMP%]   .item-answer-area[_ngcontent-%COMP%]   .area-cont[_ngcontent-%COMP%]   .input-output[_ngcontent-%COMP%]   .compile-err-title[_ngcontent-%COMP%] {\\n  background-color: #f7acac;\\n  padding-left: 8px;\\n}\\n.item-code[_ngcontent-%COMP%]   .item-answer-area.theme-white[_ngcontent-%COMP%] {\\n  background: white;\\n}\\n.item-code[_ngcontent-%COMP%]   .item-answer-area.theme-white[_ngcontent-%COMP%]   .area-cont[_ngcontent-%COMP%]   .top[_ngcontent-%COMP%] {\\n  background: white;\\n  border-bottom: 1px solid #ddd;\\n}\\n.item-code[_ngcontent-%COMP%]   .item-answer-area.theme-white[_ngcontent-%COMP%]   .area-cont[_ngcontent-%COMP%]   .top[_ngcontent-%COMP%]   .lan-list[_ngcontent-%COMP%] {\\n  background-color: #fff;\\n  color: #000;\\n  border: 1px solid #ddd;\\n  box-shadow: 2px 2px 4px 4px rgba(0, 0, 0, 0.3);\\n}\\n.item-code[_ngcontent-%COMP%]   .item-answer-area.theme-white[_ngcontent-%COMP%]   .area-cont[_ngcontent-%COMP%]   .top[_ngcontent-%COMP%]   .lan-list[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]:hover {\\n  background-color: #ddd;\\n}\\n.item-code[_ngcontent-%COMP%]   .item-answer-area.theme-white[_ngcontent-%COMP%]   .area-cont[_ngcontent-%COMP%]   .top[_ngcontent-%COMP%]   .lan-set[_ngcontent-%COMP%] {\\n  border: 1px solid #ddd;\\n  color: #666;\\n}\\n.item-code[_ngcontent-%COMP%]   .item-answer-area.theme-white[_ngcontent-%COMP%]   .area-cont[_ngcontent-%COMP%]   .main[_ngcontent-%COMP%] {\\n  background: white;\\n}\\n.item-code[_ngcontent-%COMP%]   .item-answer-area.theme-white[_ngcontent-%COMP%]   .area-cont[_ngcontent-%COMP%]   .input-output[_ngcontent-%COMP%]   .nav-tabs[_ngcontent-%COMP%] {\\n  border-top: 2px solid #ccc;\\n}\\n.form-select[_ngcontent-%COMP%] {\\n  height: auto !important;\\n}\\n.mode-select[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n}\\n.input-block[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n}\\n.radio-inline[_ngcontent-%COMP%]   input[_ngcontent-%COMP%], .radio-inline[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:hover {\\n  -webkit-appearance: auto;\\n  vertical-align: middle;\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\"]\n});", "map": {"version": 3, "mappings": ";AACA,SAAqBA,WAArB,QAAwC,sBAAxC;AAOA,SAASC,IAAT,QAAqB,wBAArB;AACA,SAASC,YAAT,QAAoC,MAApC;AACA,SAASC,QAAT,EAA6BC,GAA7B,QAAwD,gBAAxD;AACA,SAASC,YAAT,QAA6B,iBAA7B;;;;;;;;;;;;;;;;;;;;;;;;ICUgBC,gCAAmD,CAAnD,EAAmD,KAAnD,EAAmD,EAAnD;IAEIA;;IACAA;IAAiEA;MAAAA;MAAA;MAAA,OAASA,qCAAc,KAAd,EAAT;IAA6B,CAA7B;IAC9DA;IAAQA;IAGbA,gCAAiC,CAAjC,EAAiC,KAAjC,EAAiC,EAAjC;IACoBA;;IAAqCA;IACvDA;IAAuBA;IAA8BA;;;;;IAPrDA;IAAAA;IAMkBA;IAAAA;IACKA;IAAAA;;;;;;;;IAX7BA,iCAAgD,CAAhD,EAAgD,GAAhD,EAAgD,EAAhD;IACsBA;MAAAA;MAAA;MAAA,OAASA,qCAAc,IAAd,EAAT;IAA4B,CAA5B;IAA4CA;IAAQA;IACxEA;IAYFA;;;;;IAZ8BA;IAAAA;;;;;;IAiB5BA;IACGA;IAAQA;;;;;;;;IAIXA;IAA4CA;MAAA;MAAA;MAAA;MAAA,OAASA,0CAAT;IAAuB,CAAvB;IAA2CA;;;;;IAAlBA;;;;;;IADvEA;IACEA;IACFA;;;;;IADsBA;IAAAA;;;;;;;;;;;;;;;IAyBpBA;IAKEA;MAAAA;MAAA;MAAA,OAASA,4CAAqB,IAArB,EAAT;IAAmC,CAAnC;;IAGCA;IAAQA;;;;;IADTA;IAJAA;;;;;;;;IAOFA;IAKEA;MAAAA;MAAA;MAAA,OAASA,4CAAqB,KAArB,EAAT;IAAoC,CAApC;;IAGCA;IAAQA;;;;;IADTA;IAJAA;;;;;;;;IAaNA;IAMEA;MAAAA;MAAA;MAAA,OAAWA,qDAAX;IACR,CADQ,EAAgC,aAAhC,EAAgC;MAAAA;MAAA;MAAA,OACjBA,oCADiB;IACL,CAD3B;IAEDA;;;;;IAJCA,wCAAkB,MAAlB,EAAkBC,cAAlB,EAAkB,OAAlB,EAAkBA,2BAAlB;;;;;;IAOAD;IAA8BA;IAAmBA;;;;;IAAnBA;IAAAA;;;;;;;;IAOhCA;IACEA,gCAAqE,CAArE,EAAqE,KAArE,EAAqE,EAArE,EAAqE,CAArE,EAAqE,OAArE,EAAqE,EAArE;IAEoCA;;IAAuCA;IACvEA;IAAgCA;MAAAA;MAAA;MAAA,OAASA,yCAAT;IAA0B,CAA1B;IAC9BA;IAAOA;;IAA0CA;IAGrDA;IACFA;IACFA;;;;IAPsCA;IAAAA;IAEvBA;IAAAA;;;;;;;;IAabA;IAA+DA;MAAAA;MAAA;MAAA,OAASA,wCAAiB,IAAjB,EAAT;IAA+B,CAA/B;IAC7DA;IACEA;IACFA;;;;;;;;IAEFA;IAA8DA;MAAAA;MAAA;MAAA,OAASA,wCAAiB,KAAjB,EAAT;IAAgC,CAAhC;IAC5DA;IACEA;IACFA;;;;;;;;IAUEA;IAIEA;MAAAA;MAAA;MAAA,OAAQA,wCAAR;IAAwB,CAAxB;IACDA;IACDA;IAA2BA;;IAAqCA;;;;;IAH9DA;IAGyBA;IAAAA;;;;;;;;IAT/BA;IAAeA;MAAAA;MAAA;MAAA,OAASA,wCAAiB,IAAjB,EAAT;IAA+B,CAA/B;IACbA;IAAoCA;;IAAsCA;IAC1EA;IASFA;;;;IAXgDA;IACVA;IAAAA;;;;;;IAgBlCA;IACEA;IAA2BA;;IAAwCA;IACrEA;;;;IAD6BA;IAAAA;;;;;;IAG7BA;IACEA,gCAAkC,CAAlC,EAAkC,KAAlC,EAAkC,EAAlC;IAC6CA;;IAAwCA;IACnFA;IACFA;IACFA;;;;;IAH+CA;IAAAA;IAClBA;IAAAA;;;;;;IAUjBA;IAA6BA;;IAAmCA;;;;IAAnCA;IAAAA;;;;;;IAC7BA;IAA8BA;;IAAuCA;;;;IAAvCA;IAAAA;;;;;;IAKhCA;IAA6BA;IAAeA;;;;;IAAfA;IAAAA;;;;;;IAC7BA;IAA8BA;IAAoBA;;;;;IAApBA;IAAAA;;;;;;IAXpCA;IACEA,kCAA8C,CAA9C,EAA8C,OAA9C,EAA8C,CAA9C,EAA8C,IAA9C;IAGMA;IACAA;IACAA;IAAIA;;IAAoCA;IAG5CA;IACEA;IACAA;IACAA;IAAIA;IAAuBA;IAGjCA;;;;;IAXaA;IAAAA;IACAA;IAAAA;IACDA;IAAAA;IAIDA;IAAAA;IACAA;IAAAA;IACDA;IAAAA;;;;;;IAIVA;IACEA;IAA2CA;;IAAqCA;IAClFA;;;;IAD6CA;IAAAA;;;;;;IAE7CA;IACEA;IAA2CA;;IAAoCA;IAC/EA;IACFA;;;;;IAF6CA;IAAAA;IAClBA;IAAAA;;;;;;IAE3BA;IACEA;IAA2CA;;IAA6CA;IAC1FA;;;;IAD6CA;IAAAA;;;;;;IAE7CA;IACEA;IAA2CA;;IAA6CA;IAC1FA;;;;IAD6CA;IAAAA;;;;;;IA7BjDA;IACEA;IACEA;IAgBAA;IAGAA;IAIAA;IAGAA;IAGFA;IACFA;;;;;IA9BmBA;IAAAA;IAgBAA;IAAAA;IAGAA;IAAAA;IAIAA;IAAAA;IAGAA;IAAAA;;;;;;IAvCnBA;IAIAA;IAOAA;;;;;IAXeA;IAIAA;IAAAA;IAOAA;IAAAA;;;;;;IAkFrBA;;;;;;;;IAA4BA;;;;;;;;IAUxCA;IAGEA;MAAAA;MAAA;MAAA,OAAaA,0CAAb;IAA+B,CAA/B,EAAgC,UAAhC,EAAgC;MAAAA;MAAA;MAAA,OACpBA,yCADoB;IACH,CAD7B;IAGFA;;;;;IAJEA;;;;;;;;IAOFA;IAGEA;MAAAA;MAAA;MAAA,OAAaA,4CAAb;IAAiC,CAAjC,EAAkC,UAAlC,EAAkC;MAAAA;MAAA;MAAA,OACtBA,2CADsB;IACH,CAD/B;IAGFA;;;;;IAJEA;;;;;;IAgBQA;IAA8CA;IAAcA;;;;;IAAdA;IAAAA;;;;;;;;IATxDA,gCAA8D,CAA9D,EAA8D,KAA9D,EAA8D,EAA9D,EAA8D,CAA9D,EAA8D,KAA9D,EAA8D,EAA9D,EAA8D,CAA9D,EAA8D,KAA9D,EAA8D,EAA9D;IAG0CA;;IAA0CA;IAEhFA,gCAAwC,CAAxC,EAAwC,KAAxC,EAAwC,EAAxC,EAAwC,CAAxC,EAAwC,MAAxC,EAAwC,EAAxC;IAEuBA;;IAA4CA;IAC/DA;IAA6DA;MAAAA;MAAA;MAAA,OAAaA,sDAAb;IAChE,CADgE;IAC3DA;IACFA;IAEFA,iCAAmC,EAAnC,EAAmC,MAAnC,EAAmC,EAAnC;IACqBA;;IAAyCA;IAC5DA,mCACG,EADH,EACG,MADH,EACG,EADH,EACG,EADH,EACG,OADH,EACG,EADH;IAEoEA;MAAAA;MAAA;MAAA,OAAaA,kDAAb;IAAsC,CAAtC;IAA/DA;IAAiGA;IAAMA;;IAEtGA;IAGNA,mCACG,EADH,EACG,MADH,EACG,EADH,EACG,EADH,EACG,OADH,EACG,EADH;IAEoEA;MAAAA;MAAA;MAAA,OAAaA,kDAAb;IAAsC,CAAtC;IAA/DA;IAAiGA;IAAMA;;IAEtGA;IAIRA,iCAA4C,EAA5C,EAA4C,QAA5C,EAA4C,EAA5C;IACsCA;MAAAA;MAAA;MAAA,OAASA,yCAAkB,IAAlB,EAAT;IAAgC,CAAhC;IAClCA;;IACFA;IACAA;IAAoCA;MAAAA;MAAA;MAAA,OAASA,yCAAkB,KAAlB,EAAT;IAAiC,CAAjC;IAClCA;;IACFA;;;;;IAhCkCA;IAAAA;IAIfA;IAAAA;IAC0CA;IAAAA;IAC9BA;IAAAA;IAIZA;IAAAA;IAGiDA;IAAAA;IAAwCA;IAAAA;IAOxCA;IAAAA;IAAwCA;IAAAA;IAQ1GA;IAAAA;IAGAA;IAAAA;;;;;;;;;;;;;;;;;;;;;;AD1SV,OAAM,MAAOE,aAAP,CAAoB;EAyExBC,YACSC,OADT,EAESC,OAFT,EAGSC,SAHT,EAIUC,WAJV,EAKUC,QALV,EAMUC,MANV,EAOUC,IAPV,EAQUC,MARV,EAQoC;IAP3B;IACA;IACA;IACC;IACA;IACA;IACA;IACA;IAhFH,eAAwB,IAAxB;IACA,YAAkB,IAAlB;IACA,eAAU,IAAV;IAEA,eAAU;MAAEC,eAAe,EAAE,KAAnB;MAA0BC,IAAI,EAAE;IAAhC,CAAV;IACA,mBAAc,KAAd;IAEA,uBAAkB,KAAlB;IAGA,sBAAiB,KAAjB;IACA,sBAAiB,KAAjB;IACA,wBAAmB,KAAnB;IACA,0BAAqB,KAArB;IACA,mBAAqC,UAArC;IACA,oBAAe,EAAf,CAiE6B,CAjEV;;IACnB,oBAAe,IAAf;IACA,kBAAa,IAAb;IACA,yBAAoB,CAAC,CAArB;IACA,kBAAa,EAAb,CA6D6B,CA7DZ;;IAEjB,eAAU,IAAV,CA2D6B,CA3Db;;IAChB,qBAAgB,KAAhB;IACA,oBAAe,EAAf;IAEA,mBAAc,KAAd;IAEA,+BAA0B,KAA1B;IACA,oBAAe;MACpBC,QAAQ,EAAE,MADU;MAEpBC,IAAI,EAAE,OAFc,CAEL;;IAFK,CAAf;IAIA,iBAAY,OAAZ,CAgD6B,CAhDR;;IAErB,oBAAe,CAAC,MAAD,EAAS,MAAT,EAAiB,MAAjB,EAAyB,MAAzB,EAAiC,MAAjC,EAAyC,MAAzC,EAAiD,MAAjD,CAAf;IACA,sBAAiB,IAAjB;IACA,qBAAgB,KAAK,IAArB;IACA,oBAAe;MACpBC,CAAC,EAAE,GADiB;MAEpBC,GAAG,EAAE,KAFe;MAGpBC,OAAO,EAAE,IAHW;MAIpBC,IAAI,EAAE,MAJc;MAKpBC,KAAK,EAAE,OALa;MAMpBC,UAAU,EAAE,YANQ;MAOpBC,EAAE,EAAE,IAPgB;MAQpBC,OAAO,EAAE,SARW;MASpBC,OAAO,EAAE,SATW;MAUpBC,GAAG,EAAE;IAVe,CAAf;IAaA,eAAU;MACfC,CAAC,EAAE,GADY;MAEf,OAAO,KAFQ;MAGf,MAAM,SAHS;MAIfC,IAAI,EAAE,MAJS;MAKfC,KAAK,EAAE,OALQ;MAMfC,UAAU,EAAE,YANG;MAOfC,EAAE,EAAE,IAPW;MAQfC,OAAO,EAAE,SARM;MASfC,OAAO,EAAE,SATM;MAUfC,GAAG,EAAE;IAVU,CAAV;IAeC,qBAAgC,EAAhC;IACD,oBAAe,KAAf;;IA0LP,wBAAmB,MAAK;MACtB,KAAKC,WAAL,GAAmB,IAAnB;IACD,CAFD;;IAIA,sBAAiB,MAAK;MACpB,KAAKA,WAAL,GAAmB,KAAnB,CADoB,CAEpB;IACD,CAHD;EA/KI;;EAEJC,QAAQ,IAAK;;EAEbC,SAAS;IACP,OAAO,KAAKC,YAAL,EAAmBC,YAAnB,CAAgCC,GAAhC,EAAP;EACD;;EAEDC,WAAW;IACT,KAAKC,QAAL;IACA,KAAKC,aAAL,GAAqB,KAArB;IACAC,UAAU,CAAC,MAAK;MACd,KAAKD,aAAL,GAAqB,IAArB;MACAC,UAAU,CAAC,MAAK;QACd,KAAKC,aAAL;QACA,MAAMC,MAAM,GAAGC,QAAQ,CAACC,aAAT,CAAuB,KAAvB,CAAf;;QACA,IAAIF,MAAJ,EAAY;UACVA,MAAM,CAACG,mBAAP,CAA2B,kBAA3B,EAA+C,KAAKC,gBAApD;UACAJ,MAAM,CAACG,mBAAP,CAA2B,gBAA3B,EAA6C,KAAKE,cAAlD;UACAL,MAAM,CAACM,gBAAP,CAAwB,kBAAxB,EAA4C,KAAKF,gBAAjD;UACAJ,MAAM,CAACM,gBAAP,CAAwB,gBAAxB,EAA0C,KAAKD,cAA/C;QACD;MACF,CATS,CAAV;IAUD,CAZS,CAAV;EAaD;;EAEDE,WAAW;IACT,KAAKC,aAAL,CAAmBC,OAAnB,CAA4BC,GAAD,IAASA,GAAG,CAACC,WAAJ,EAApC;IACAC,YAAY,CAAC,KAAKC,cAAN,CAAZ;IACA,KAAKA,cAAL,GAAsB,IAAtB;;IACA,KAAKlD,QAAL,CAAcmD,QAAd,CAAuB,KAAvB;;IAEA,MAAMd,MAAM,GAAGC,QAAQ,CAACC,aAAT,CAAuB,KAAvB,CAAf;;IACA,IAAIF,MAAJ,EAAY;MACVA,MAAM,CAACG,mBAAP,CAA2B,kBAA3B,EAA+C,KAAKC,gBAApD;MACAJ,MAAM,CAACG,mBAAP,CAA2B,gBAA3B,EAA6C,KAAKE,cAAlD;IACD;EACF;;EAEDU,WAAW;IACT,KAAKC,kBAAL,GAA0B,IAA1B;EACD;;EAEDC,gBAAgB,CAACC,MAAD,EAAO;IACrB,KAAKC,cAAL,GAAsBD,MAAtB;EACD;;EAEDE,YAAY;IACV,MAAMC,MAAM,GAAG,KAAK9B,SAAL,EAAf;IACA8B,MAAM,IAAIA,MAAM,CAACC,MAAP,EAAV;EACD;;EAEDC,oBAAoB,CAACC,YAAD,EAAa;IAC/B,KAAKC,cAAL,GAAsBD,YAAtB;EACD;;EAED5B,QAAQ;IACN,KAAK8B,OAAL,GAAe,KAAKnE,OAAL,CAAaoE,cAAb,EAAf;IACA,KAAKC,IAAL,GAAY,KAAKpE,OAAL,CAAaqE,WAAb,CAAyB,KAAKH,OAA9B,EAAuC,KAAKI,MAA5C,CAAZ;IACA,KAAKC,OAAL,GAAe,KAAKxE,OAAL,CAAayE,cAAb,CAA4B,KAAKF,MAAjC,CAAf;IAEA,MAAMG,YAAY,GAAG,KAAKF,OAAL,CAAaG,aAAlC;IACA,KAAKC,gBAAL,GAAwB,KAAKC,UAAL,CAAgB,KAAKC,MAArB,CAAxB;IACA,KAAKC,OAAL,GAAe,KAAKP,OAAL,CAAaQ,QAA5B;IACA,KAAKhF,OAAL,CAAaiF,eAAb,GAA+B,KAA/B;IACA,KAAKC,OAAL,GAAe,KAAKC,UAAL,EAAf;IACA,KAAKvB,cAAL,GAAsB,KAAtB;IACA,KAAKwB,UAAL,GAAkB,IAAlB;IACA,KAAKC,YAAL,GAAoB,IAApB;IACA,KAAKC,YAAL,GAAoB,EAApB;IACA,KAAKZ,YAAL,CAAkBhE,QAAlB,GAA6BgE,YAAY,GAAGA,YAAY,CAAChE,QAAhB,GAA2B,MAApE;IACA,KAAKgE,YAAL,CAAkB/D,IAAlB,GAAyB+D,YAAY,GAAGA,YAAY,CAACa,KAAhB,GAAwB,OAA7D,CAfM,CAiBN;;IACA,KAAKC,WAAL,CAAiB,EAAjB;EACD;;EAEDA,WAAW,CAACC,SAAD,EAAU;IACnB,MAAMC,SAAS,GAAG,MAAMD,SAAxB;IACAlD,UAAU,CAAC,MAAK;MACdoD,MAAM,CAAC,GAAD,CAAN,CAAY,0BAAZ,EACGC,EADH,CACM,CADN,EAEGC,GAFH,CAEO,MAFP,EAEe,cAAcJ,SAAd,GAA0B,MAA1B,GAAmCA,SAAS,GAAG,EAA/C,GAAoD,KAFnE;MAGAE,MAAM,CAAC,GAAD,CAAN,CAAY,0BAAZ,EACGC,EADH,CACM,CADN,EAEGC,GAFH,CAEO,MAFP,EAEe,cAAcH,SAAd,GAA0B,MAA1B,IAAoC,IAAID,SAAS,GAAG,EAApD,IAA0D,KAFzE;IAGD,CAPS,EAOP,EAPO,CAAV;EAQD;;EAEDjD,aAAa;IACX,KAAKsD,aAAL;IACA,MAAMhC,MAAM,GAAG,KAAK9B,SAAL,EAAf;;IACA,IAAI8B,MAAJ,EAAY;MACV,KAAKiC,iBAAL,CAAuB,IAAvB;MACA,KAAKC,4BAAL,CAAkClC,MAAlC;MACA,KAAK9D,OAAL,CAAaiF,eAAb,GAA+B,KAA/B,CAHU,CAG4B;;MACtCnB,MAAM,CAACmC,KAAP;IACD;EACF;;EAEDC,YAAY,CAACC,KAAD,EAAc;IACxB,MAAMC,UAAU,GAAG,KAAKC,aAAL,EAAnB;IACA,OAAOF,KAAK,KAAKC,UAAjB;EACD,CAzLuB,CA2LxB;;;EACAE,UAAU;IACR;IACA,MAAMxC,MAAM,GAAG,KAAK9B,SAAL,EAAf;;IACA,IAAI8B,MAAJ,EAAY;MACV,MAAMyC,SAAS,GAAGzC,MAAM,CAAC0C,QAAP,EAAlB,CADU,CAEV;;MACA,KAAKhC,OAAL,CAAaiC,SAAb,GAAyB,KAAKP,YAAL,CAAkBK,SAAlB,CAAzB;IACD;;IACD,KAAKG,aAAL,GAAqB,EAArB;IACA,KAAKC,kBAAL;IACA,KAAKC,mBAAL;IACA,KAAK5G,OAAL,CAAaiF,eAAb,GAA+B,IAA/B;IAEA,KAAKjF,OAAL,CAAa6G,qBAAb,CAAmCC,IAAnC,CAAwC;MACtCC,IAAI,EAAExH,IAAI,CAACyH,oBAAL,CAA0BC;IADM,CAAxC;EAGD;;EAEDjB,4BAA4B,CAAClC,MAAD,EAAO;IACjC,MAAMoD,KAAK,GAAG,IAAd;;IACA,MAAMC,GAAG,GAAGrD,MAAM,CAACsD,OAAP,CAAeC,WAAf,EAAZ;IACAF,GAAG,CAACG,aAAJ,GAAoBH,GAAG,CAAC,eAAD,CAAH,IAAwBA,GAAG,CAAC,YAAD,CAA/C;;IACAA,GAAG,CAACI,UAAJ,GAAiB,UAAUC,KAAV,EAAoB;MACnC,IAAIC,WAAW,GAAGD,KAAK,CAACE,KAAN,CAAYC,IAAZ,CAAiB,IAAjB,CAAlB;MACA,MAAMC,UAAU,GAAG,KAAKpB,QAAL,GAAgBqB,MAAnC;MACA,MAAMC,UAAU,GAAG,KAAKC,SAAL,EAAnB,CAHmC,CAInC;MACA;;MACA,IAAIb,KAAK,CAACpF,WAAV,EAAuB;QACrB,IAAI8F,UAAU,GAAGH,WAAW,CAACI,MAAzB,GAAkC,KAAKG,UAA3C,EAAuD;UACrDC,OAAO,CAACC,GAAR,CAAY,+CAAZ,EAA6DN,UAA7D;;UACAV,KAAK,CAAClH,OAAN,CAAcmI,QAAd,CAAuB,4BAAvB;;UACA,OAAO,KAAP;QACD;;QACD,IAAIL,UAAU,GAAGN,KAAK,CAACE,KAAN,CAAYG,MAAzB,GAAkC,KAAKO,cAAL,GAAsB,CAA5D,EAA+D;UAC7DH,OAAO,CAACC,GAAR,CAAY,+CAAZ,EAA6DJ,UAA7D;;UACAZ,KAAK,CAAClH,OAAN,CAAcmI,QAAd,CAAuB,uBAAvB;;UACA,OAAO,KAAP;QACD;MACF;;MAED,IAAIX,KAAK,CAACa,MAAN,IAAgB,QAAhB,IAA4B,KAAKL,UAAjC,IAA+C,KAAKI,cAAxD,EAAwE;QACtE;QACA,IAAIR,UAAU,GAAGH,WAAW,CAACI,MAAzB,GAAkC,KAAKG,UAA3C,EAAuD;UACrD,IAAIJ,UAAU,IAAI,KAAKI,UAAvB,EAAmC;YACjCC,OAAO,CAACC,GAAR,CAAY,+CAAZ,EAA6DN,UAA7D;;YACAV,KAAK,CAAClH,OAAN,CAAcmI,QAAd,CAAuB,4BAAvB;;YACA,OAAO,KAAP;UACD;QACF,CARqE,CAUtE;;;QACA,IAAIL,UAAU,GAAGN,KAAK,CAACE,KAAN,CAAYG,MAAzB,GAAkC,KAAKO,cAAL,GAAsB,CAA5D,EAA+D;UAC7DH,OAAO,CAACC,GAAR,CAAY,+CAAZ,EAA6DJ,UAA7D;;UACAZ,KAAK,CAAClH,OAAN,CAAcmI,QAAd,CAAuB,uBAAvB;;UACA,OAAO,KAAP;QACD;MACF;;MAED,OAAO,KAAKb,aAAL,CAAmBE,KAAnB,CAAP;IACD,CAtCD;;IAuCAL,GAAG,CAACa,UAAJ,GAAiB,KAAKM,aAAtB;IACAnB,GAAG,CAACiB,cAAJ,GAAqB,KAAKG,cAA1B;EACD,CA3PuB,CAsQxB;;;EACAC,UAAU;IACR,KAAK5E,cAAL,GAAsB,IAAtB;IACA,KAAK6E,SAAL;EACD,CA1QuB,CA4QxB;EACA;;;EACAA,SAAS;IAAA;;IACP,MAAMjE,OAAO,GAAG,KAAKA,OAArB;IACA,MAAMkE,QAAQ,GAAG;MACfC,SAAS,EAAE,KAAKtE,IAAL,CAAUuE,IADN;MAEfC,UAAU,EAAErE,OAAO,CAACsE,MAAR,CAAe3C,KAFZ;MAGf4C,SAAS,EAAE,KAAKC,WAAL,CAAiB,KAAKlE,MAAtB,CAHI;MAIfmE,UAAU,EAAE,KAJG;MAKfC,UAAU,EAAE,KAAK1E,OAAL,CAAasE,MAAb,CAAoBK;IALjB,CAAjB;IAQA,KAAKC,SAAL,GAAiB,KAAKC,WAAL,EAAjB,CAVO,CAYP;;IACA,KAAKjJ,QAAL,CAAcmD,QAAd,CAAuB,IAAvB;;IACA,KAAKpD,WAAL,CAAiBmJ,eAAjB,GAAmC,IAAnC;IACA,KAAKrG,aAAL,CAAmBsG,IAAnB,CACE,KAAKvJ,OAAL,CACGwJ,mBADH,CACuB,KAAKnJ,MAAL,CAAYuI,IADnC,EACyCF,QADzC,EAEGe,IAFH,CAGI/J,GAAG,CAAEgK,IAAD,IAAS;MACX,IAAIA,IAAI,CAAC,QAAD,CAAJ,KAAmB,SAAvB,EAAkC;QAChC,MAAMA,IAAN;MACD;;MACD,OAAOA,IAAP;IACD,CALE,CAHP,EAUGC,SAVH,CAUa;MACT7C,IAAI,EAAG4C,IAAD,IAAS;QACb,IAAIE,QAAQ,GAAG,CAAf,CADa,CAEb;;QACA,KAAKtG,cAAL,GAAsBf,UAAU,CAAC,MAAK;UACpC,MAAMsH,UAAU,GAAG;YAAEC,SAAS,EAAEJ,IAAI,CAAC,WAAD;UAAjB,CAAnB;UACA,KAAKzG,aAAL,CAAmBsG,IAAnB,CACE,KAAKQ,YAAL,CAAkBF,UAAlB,EACGJ,IADH,CAEI/J,GAAG,CAAEgK,IAAD,IAAS;YACX,IAAIA,IAAI,CAAC,QAAD,CAAJ,KAAmB,SAAvB,EAAkC;cAChC;cACA,MAAMA,IAAN;YACD;;YACD,OAAOA,IAAP;UACD,CANE,CAFP,EASI,KAAK1J,OAAL,CAAagK,YAAb,CAA0B;YAAEC,IAAI,EAAE,IAAR;YAAcC,WAAW,EAAE;UAA3B,CAA1B,CATJ,EAUIzK,QAAQ,CAAC,MAAK;YACZ,KAAKO,OAAL,CAAamK,OAAb,GADY,CAEZ;;YACA,KAAKhK,WAAL,CAAiBmJ,eAAjB,GAAmC,KAAnC;UACD,CAJO,CAVZ,EAgBGK,SAhBH,CAgBa;YACT7C,IAAI;cAAA,6BAAE,WAAO4C,IAAP,EAAoB;gBACxB,MAAI,CAACU,WAAL,GAAmB,QAAnB,CADwB,CAExB;;gBACA,MAAI,CAAC9E,YAAL,GAAoB,EAApB;gBACA,MAAI,CAAC+E,iBAAL,GAAyB,CAAC,CAA1B;gBACA,MAAI,CAAChF,YAAL,GAAoB,IAApB;gBACA,MAAI,CAACD,UAAL,GAAkB,IAAlB;gBACA,MAAI,CAACpF,OAAL,CAAaiF,eAAb,GAA+B,KAA/B,CAPwB,CASxB;;gBACA,MAAI,CAACqF,aAAL,CAAmBZ,IAAI,CAACa,MAAxB,EAVwB,CAYxB;;;gBACA,IAAIb,IAAI,CAACa,MAAL,CAAYC,aAAhB,EAA+B;kBAC7B;kBACA,MAAI,CAAClF,YAAL,GAAoBoE,IAAI,CAACa,MAAL,CAAYC,aAAhC;kBACA,MAAI,CAACH,iBAAL,GAAyB,MAAI,CAACI,oBAAL,CAA0B,MAAI,CAACnF,YAA/B,CAAzB;;kBACA,MAAI,CAACqB,kBAAL,CAAwB,sBAAxB;;kBACA,MAAI,CAACC,mBAAL,CAAyB,MAAI,CAACtB,YAA9B,EAA4C,OAA5C;gBACD,CAND,MAMO;kBACL;kBACA;kBACA,KAAK,MAAMoF,OAAX,IAAsBhB,IAAI,CAACa,MAAL,CAAYI,KAAlC,EAAyC;oBACvC,IAAID,OAAO,CAACE,WAAR,CAAoB,IAApB,EAA0BF,OAAO,CAAC7C,MAAR,GAAiB,CAA3C,CAAJ,EAAmD;sBACjD,MAAI,CAACzC,UAAL,GAAkBsE,IAAI,CAACa,MAAL,CAAYI,KAAZ,CAAkBD,OAAlB,CAAlB;sBACA,MAAI,CAACtF,UAAL,CAAgByF,QAAhB,GAA2BC,IAAI,CAACC,KAAL,CAAW,MAAI,CAAC3F,UAAL,CAAgByF,QAAhB,GAA2B,IAAtC,CAA3B;;sBACA,IAAI,MAAI,CAACzF,UAAL,EAAiB4F,SAArB,EAAgC;wBAC9B,MAAI,CAACX,iBAAL,GAAyB,MAAI,CAACI,oBAAL,CAA0B,MAAI,CAACrF,UAAL,CAAgB4F,SAA1C,CAAzB;;wBACA,MAAI,CAACrE,kBAAL,CAAwB,sBAAxB;;wBACA,MAAI,CAACC,mBAAL,CAAyB,MAAI,CAACtB,YAA9B,EAA4C,OAA5C;sBACD;oBACF;kBACF;gBACF;;gBAED,MAAI,CAAClF,QAAL,CAAcmD,QAAd,CAAuB,KAAvB;cACD,CApCG;;cAAA;gBAAA;cAAA;YAAA,GADK;YAsCT0H,KAAK,EAAGC,GAAD,IAAQ;cACbjD,OAAO,CAACC,GAAR,CAAY,wBAAZ;cACA,KAAKiD,gBAAL,CAAsBD,GAAtB;YACD;UAzCQ,CAhBb,CADF;UA8DA7H,YAAY,CAAC,KAAKC,cAAN,CAAZ;UACA,KAAKA,cAAL,GAAsB,IAAtB;QACD,CAlE+B,EAkE7B,IAlE6B,CAAhC;MAmED,CAvEQ;MAwET2H,KAAK,EAAGC,GAAD,IAAQ;QACb,KAAK9K,QAAL,CAAcmD,QAAd,CAAuB,KAAvB;;QACA,KAAKpD,WAAL,CAAiBmJ,eAAjB,GAAmC,KAAnC;QAEArB,OAAO,CAACC,GAAR,CAAY,kBAAZ;QACA,KAAKiD,gBAAL,CAAsBD,GAAtB;MACD;IA9EQ,CAVb,CADF;EA4FD;;EAEDC,gBAAgB,CAACD,GAAD,EAAI;IAClB,KAAK9K,QAAL,CAAcmD,QAAd,CAAuB,KAAvB;;IACA0E,OAAO,CAACC,GAAR,CAAY,gCAAgCgD,GAAG,CAACE,MAAM,cAAcF,GAAG,CAACG,GAAG,EAA3E;IACA,IAAIC,MAAJ;;IACA,QAAQJ,GAAG,CAACE,MAAZ;MACE,KAAK,OAAL;MACA,KAAK,SAAL;QAAgB;UACdnD,OAAO,CAACC,GAAR,CAAY,0DAAZ;UACAoD,MAAM,GAAG,gCAAT;UACA;QACD;;MAED,KAAK,SAAL;QAAgB;UACdrD,OAAO,CAACC,GAAR,CAAY,qCAAZ;UACAoD,MAAM,GAAG,0BAAT;UACA;QACD;;MAED,KAAK,YAAL;QAAmB;UACjBrD,OAAO,CAACC,GAAR,CAAY,wBAAZ;UACAoD,MAAM,GAAG,oCAAT;UACA;QACD;;MAED,KAAK,CAAL;MACA,KAAK,GAAL;QAAU;UACRrD,OAAO,CAACC,GAAR,CAAY,2BAAZ;UACAoD,MAAM,GAAG,mCAAT;UACA;QACD;;MAED,KAAK,GAAL;QAAU;UACRrD,OAAO,CAACC,GAAR,CAAY,4BAAZ;UACAoD,MAAM,GAAG,8BAAT;UACA;QACD;;MAED;QAAS;UACPA,MAAM,GAAG,uBAAT;QACD;IAnCH;;IAsCA,KAAK/K,MAAL,CAAYgD,QAAZ,CAAqB;MACnB6H,MAAM,EAAE,IADW;MAEnBG,IAAI,EAAE;QAAEC,QAAQ,EAAE,KAAKtL,SAAL,CAAeuL,OAAf,CAAuBH,MAAvB;MAAZ;IAFa,CAArB;EAID,CAzauB,CA2axB;;;EACAvB,YAAY,CAACrB,QAAD,EAAS;IACnB,MAAMgD,OAAO,GAAG,IAAIpM,WAAJ,EAAhB,CADmB,CAEnB;;IACA,KAAKU,OAAL,CAAamK,OAAb;IACA,OAAO,KAAK7J,IAAL,CAAUqL,IAAV,CAAqC,KAAKtL,MAAL,CAAYuI,IAAjD,EAAuDF,QAAvD,EAAiE;MAAEgD;IAAF,CAAjE,CAAP;EACD;;EAEDjB,oBAAoB,CAACmB,UAAD,EAAW;IAC7B,MAAMC,GAAG,GAAG,kBAAZ;IACA,MAAMC,OAAO,GAAGD,GAAG,CAACE,IAAJ,CAASH,UAAT,CAAhB;;IACA,IAAIE,OAAO,IAAIA,OAAO,CAAC,CAAD,CAAtB,EAA2B;MACzB,OAAOE,MAAM,CAACF,OAAO,CAAC,CAAD,CAAR,CAAb;IACD;;IACD,OAAO,CAAC,CAAR;EACD;;EAEDlF,mBAAmB,CAACgF,UAAD,EAAcK,SAAd,EAAwB;IACzC,MAAMC,UAAU,GAAGvG,MAAM,CAAC,GAAD,CAAN,CAAY,oCAAZ,EAAkDC,EAAlD,CAAqD,KAAKyE,iBAAL,GAAyB,CAA9E,CAAnB;IACA,MAAM8B,YAAY,GAAGzJ,QAAQ,CAACC,aAAT,CAAuB,uBAAvB,CAArB;;IACA,IAAIsJ,SAAJ,EAAe;MACb,IAAI,KAAK5B,iBAAL,GAAyB,CAAC,CAA9B,EAAiC;QAC/B6B,UAAU,CAACE,QAAX,CAAoBH,SAApB;QAEA,MAAMH,OAAO,GAAG,cAAcC,IAAd,CAAmBH,UAAnB,CAAhB,CAH+B,CAGiB;;QAChD,IAAIE,OAAO,IAAIA,OAAO,CAAC,CAAD,CAAtB,EAA2B;UACzB,KAAKpF,aAAL,GAAqBoF,OAAO,CAAC,CAAD,CAA5B;UACAK,YAAY,CAACE,KAAb,CAAmBC,GAAnB,GAAyBJ,UAAU,CAACrG,GAAX,CAAe,KAAf,CAAzB;UACAqG,UAAU,CACPK,GADH,CACO,WADP,EAEGA,GAFH,CAEO,UAFP,EAGGC,EAHH,CAGM,WAHN,EAGmB,MAAK;YACpBL,YAAY,CAACE,KAAb,CAAmBI,OAAnB,GAA6B,OAA7B;UACD,CALH,EAMGD,EANH,CAMM,UANN,EAMkB,MAAK;YACnBL,YAAY,CAACE,KAAb,CAAmBI,OAAnB,GAA6B,MAA7B;UACD,CARH;QASD;MACF;IACF,CAnBD,MAmBO;MACLP,UAAU,CAACQ,WAAX,CAAuB,OAAvB;MACAR,UAAU,CAACK,GAAX,CAAe,WAAf,EAA4BA,GAA5B,CAAgC,UAAhC;IACD;EACF;;EAEDI,UAAU;IACR,KAAKC,YAAL,GAAoB,CAAC,KAAKA,YAA1B;EACD;;EAEDC,SAAS;IACP,KAAKD,YAAL,GAAoB,KAApB;EACD;;EAEDE,eAAe,CAACnC,KAAD,EAAM;IACnB,MAAMJ,MAAM,GAAG,EAAf;;IACA,KAAK,MAAMG,OAAX,IAAsBC,KAAtB,EAA6B;MAC3B,IAAID,OAAO,CAACE,WAAR,CAAoB,IAApB,EAA0BF,OAAO,CAAC7C,MAAR,GAAiB,CAA3C,CAAJ,EAAmD;QACjD0C,MAAM,CAAChB,IAAP,CAAYoB,KAAK,CAACD,OAAD,CAAjB;MACD;IACF;;IACD,OAAOH,MAAP;EACD;;EAED5D,kBAAkB,CAACsF,SAAD,EAAW;IAC3B,MAAMc,KAAK,GAAGpH,MAAM,CAAC,KAAD,CAAN,CAAcqH,QAAd,CAAuB,WAAvB,EAAoCD,KAAlD;IACA,MAAMjJ,MAAM,GAAG,KAAK9B,SAAL,EAAf;;IACA,IAAIiK,SAAJ,EAAe;MACb,IAAI,KAAK5B,iBAAL,GAAyB,CAAC,CAA9B,EAAiC;QAC/B,KAAK4C,QAAL,GAAgBnJ,MAAM,EAAEsD,OAAR,CAAgB8F,SAAhB,CACd,IAAIH,KAAJ,CAAU,KAAK1C,iBAAL,GAAyB,CAAnC,EAAsC,CAAtC,EAAyC,KAAKA,iBAAL,GAAyB,CAAlE,EAAqE,GAArE,CADc,EAEd4B,SAFc,EAGd,UAHc,EAId,KAJc,CAAhB;MAMD;IACF,CATD,MASO;MACLnI,MAAM,EAAEsD,OAAR,CAAgB+F,YAAhB,CAA6B,KAAKF,QAAlC;IACD;EACF;;EAEDG,cAAc;IACZ,MAAMC,UAAU,GAAG3K,QAAQ,CAAC4K,cAAT,CAAwB,UAAxB,CAAnB;IACA,MAAMC,OAAO,GAAGF,UAAU,CAAClH,KAAX,CAAiBqH,OAAjB,CAAyB,OAAzB,EAAkC,IAAlC,EAAwCA,OAAxC,CAAgD,MAAhD,EAAwD,IAAxD,CAAhB;IACA,MAAMC,GAAG,GAAG,CAACF,OAAO,CAACG,KAAR,CAAc,IAAd,CAAD,CAAZ;IACA,KAAKlJ,OAAL,CAAasE,MAAb,CAAoBK,IAApB,GAA2BsE,GAA3B;EACD;;EAEDzE,WAAW,CAAClE,MAAD,EAAO;IAChB,OAAO,KAAK6I,OAAL,CAAa7I,MAAb,CAAP;EACD;;EAED8I,YAAY;IACV,OAAO,KAAKC,eAAL,CAAqB,KAAKxI,YAA1B,MAA4C,KAAKA,YAAL,CAAkBwC,MAArE;EACD;;EAED1C,UAAU;IACR,MAAM2I,QAAQ,GAAG;MACf,MAAM,QADS;MAEfpM,EAAE,EAAE,QAFW;MAGfF,KAAK,EAAE,OAHQ;MAIfC,UAAU,EAAE,YAJG;MAKfE,OAAO,EAAE,QALM;MAMfC,OAAO,EAAE,QANM;MAOfN,CAAC,EAAE,OAPY;MAQf,OAAO,OARQ;MASfC,IAAI,EAAE,MATS;MAUfM,GAAG,EAAE;IAVU,CAAjB;;IAaA,IAAIiM,QAAQ,CAAC,KAAKhJ,MAAN,CAAZ,EAA2B;MACzB,OAAOgJ,QAAQ,CAAC,KAAKhJ,MAAN,CAAf;IACD;;IACD,OAAO,MAAP;EACD;;EAED+I,eAAe,CAAClD,KAAD,EAAM;IACnB,IAAIoD,CAAC,GAAG,CAAR;IACApD,KAAK,CAACzH,OAAN,CAAe8K,OAAD,IAAY;MACxB,IAAIA,OAAO,CAACC,OAAR,KAAoB,IAAxB,EAA8B;QAC5BF,CAAC;MACF;IACF,CAJD;IAKA,OAAOA,CAAP;EACD;;EAEDG,gBAAgB;IACd,KAAK5L,aAAL,GAAqB,KAArB;IACAC,UAAU,CAAC,MAAK;MACd,KAAKD,aAAL,GAAqB,IAArB;MACA,KAAKsB,cAAL,GAAsB,KAAtB;MACA,KAAKuK,gBAAL,GAAwB,KAAxB;MACA,KAAKrJ,MAAL,GAAc,KAAKsJ,UAAnB;MACA,KAAKxJ,gBAAL,GAAwB,KAAKC,UAAL,CAAgB,KAAKC,MAArB,CAAxB;MACA,KAAKM,UAAL,GAAkB,IAAlB;MACA,KAAKC,YAAL,GAAoB,IAApB;MACA,KAAKC,YAAL,GAAoB,EAApB;MACA,KAAKd,OAAL,CAAaiC,SAAb,GAAyB,KAAzB;MACA,KAAKzG,OAAL,CAAaiF,eAAb,GAA+B,KAA/B;MACA,KAAKoJ,YAAL;MACA9L,UAAU,CAAC,MAAK;QACd,MAAMuB,MAAM,GAAG,KAAK9B,SAAL,EAAf;QACA8B,MAAM,CAACsD,OAAP,CAAekH,OAAf,CAAuB,cAAc,KAAKnJ,UAAL,EAArC;QACA,KAAKY,iBAAL,CAAuB,IAAvB;QACAjC,MAAM,CAACP,QAAP,CAAgB,KAAK8C,aAAL,EAAhB;QACA,KAAKiE,aAAL;QACA,KAAK3D,kBAAL;QACA,KAAKC,mBAAL;QACA9C,MAAM,CAACmC,KAAP;QACA,KAAKD,4BAAL,CAAkClC,MAAlC;MACD,CAVS,CAAV;IAWD,CAvBS,CAAV;EAwBD;;EAEDyK,WAAW;IACT,MAAMzK,MAAM,GAAG,KAAK9B,SAAL,EAAf;;IACA,IAAI8B,MAAJ,EAAY;MACVA,MAAM,CAACmC,KAAP;IACD;EACF;;EAEDqE,aAAa,CAACC,MAAD,EAAQ;IACnB,MAAMiE,YAAY,GAAG;MACnBnK,IAAI,EAAE,KAAKA,IADQ;MAEnBkG,MAAM,EAAEA,MAFW;MAGnBkE,aAAa,EAAE,KAAKd,OAAL,CAAa,KAAK7I,MAAlB,CAHI;MAInBqB,KAAK,EAAE,KAAK3B,OAAL,CAAasE,MAAb,CAAoB3C;IAJR,CAArB;IAMA,KAAKnG,OAAL,CAAa0O,kBAAb,CAAgCF,YAAhC;EACD;;EAEDG,eAAe;IACb,KAAKR,gBAAL,GAAwB,KAAxB;EACD;;EAEDS,kBAAkB;IAChB,MAAM9K,MAAM,GAAG,KAAK9B,SAAL,EAAf;IACA,KAAKyB,kBAAL,GAA0B,KAA1B;IACAK,MAAM,CAACP,QAAP,CAAgB,KAAK8C,aAAL,EAAhB;IACA,KAAKkI,WAAL;EACD;;EAEDlI,aAAa;IACX,MAAMD,UAAU,GAAG,KAAK/B,IAAL,CAAUwK,OAAV,CAAkBC,WAArC;;IACA,IAAI1I,UAAU,GAAG,KAAKtB,MAAR,CAAd,EAA+B;MAC7B,OAAOsB,UAAU,CAAC,KAAKtB,MAAN,CAAjB;IACD;;IACD,OAAO,EAAP;EACD;;EAEDiK,iBAAiB;IACf,KAAKtL,kBAAL,GAA0B,KAA1B;EACD;;EAEDqC,aAAa;IACX,KAAKhB,MAAL,GAAc,KAAKkK,YAAL,CAAkB,KAAKxK,OAAL,CAAasE,MAAb,CAAoB2F,aAAtC,CAAd,CADW,CAEX;;IACA,KAAK7J,gBAAL,GAAwB,KAAKC,UAAL,CAAgB,KAAKC,MAArB,CAAxB;IAEA,MAAMhB,MAAM,GAAG,KAAK9B,SAAL,EAAf;IACA8B,MAAM,CAACsD,OAAP,CAAekH,OAAf,CAAuB,cAAc,KAAKnJ,UAAL,EAArC;EACD;;EAED8J,SAAS,CAACC,GAAD,EAAI;IACX,IAAIA,GAAG,KAAK,KAAKpK,MAAjB,EAAyB;MACvB,KAAKuJ,YAAL;MACA;IACD,CAJU,CAMX;;;IACA,MAAMvK,MAAM,GAAG,KAAK9B,SAAL,EAAf;IACA,KAAKmM,gBAAL,GAAwBrK,MAAM,CAAC0C,QAAP,KAAoB,IAApB,GAA2B,KAAnD;IACA,KAAK4H,UAAL,GAAkBc,GAAlB;;IAEA,IAAI,CAAC,KAAKf,gBAAV,EAA4B;MAC1B,KAAKD,gBAAL;IACD;EACF;;EAEDiB,aAAa;IACX,KAAKC,WAAL,GAAmB,CAAC,KAAKA,WAAzB;EACD;;EAEDC,aAAa,CAACC,MAAD,EAAO;IAClB,KAAKC,eAAL,GAAuBD,MAAvB;EACD;;EAEDjB,YAAY;IACV,KAAKe,WAAL,GAAmB,KAAnB;EACD;;EAEDvK,UAAU,CAAC2K,MAAD,EAAO;IACf,MAAMC,OAAO,GAAG;MACdnO,CAAC,EAAE;QACDoO,OAAO,EAAE,KAAKxP,SAAL,CAAeuL,OAAf,CAAuB,iBAAvB;MADR,CADW;MAId,OAAO;QACLiE,OAAO,EAAE,KAAKxP,SAAL,CAAeuL,OAAf,CAAuB,mBAAvB;MADJ,CAJO;MAOd,MAAM;QACJiE,OAAO,EAAE,KAAKxP,SAAL,CAAeuL,OAAf,CAAuB,qBAAvB;MADL,CAPQ;MAUdlK,IAAI,EAAE;QACJmO,OAAO,EAAE,KAAKxP,SAAL,CAAeuL,OAAf,CAAuB,oBAAvB;MADL,CAVQ;MAadjK,KAAK,EAAE;QACLkO,OAAO,EAAE,KAAKxP,SAAL,CAAeuL,OAAf,CAAuB,qBAAvB;MADJ,CAbO;MAgBdhK,UAAU,EAAE;QACViO,OAAO,EAAE,KAAKxP,SAAL,CAAeuL,OAAf,CAAuB,kBAAvB;MADC,CAhBE;MAmBd/J,EAAE,EAAE;QACFgO,OAAO,EAAE,KAAKxP,SAAL,CAAeuL,OAAf,CAAuB,kBAAvB;MADP,CAnBU;MAsBd9J,OAAO,EAAE;QACP+N,OAAO,EAAE,KAAKxP,SAAL,CAAeuL,OAAf,CAAuB,kBAAvB;MADF,CAtBK;MAyBd7J,OAAO,EAAE;QACP8N,OAAO,EAAE,KAAKxP,SAAL,CAAeuL,OAAf,CAAuB,kBAAvB;MADF,CAzBK;MA4Bd5J,GAAG,EAAE;QACH6N,OAAO,EAAE;MADN;IA5BS,CAAhB;IAgCA,OAAOD,OAAO,CAACD,MAAD,CAAP,GAAkBC,OAAO,CAACD,MAAD,CAAzB,GAAoC,IAA3C;EACD;;EAEDG,eAAe;IACb,KAAK5K,OAAL,GAAe,KAAf;IACA,KAAKP,OAAL,CAAaQ,QAAb,GAAwB,KAAxB;EACD;;EAEDqE,WAAW;IACT,MAAMuG,QAAQ,GAAG,KAAKpL,OAAL,CAAasE,MAAb,CAAoBK,IAArC;IACA,IAAI0G,WAAW,GAAG,EAAlB;;IAEA,IAAID,QAAQ,CAAC/H,MAAT,GAAkB,CAAtB,EAAyB;MACvB,MAAMiI,OAAO,GAAGF,QAAQ,CAAC,CAAD,CAAxB;;MACA,KAAK,IAAIG,CAAC,GAAG,CAAR,EAAWC,GAAG,GAAGF,OAAO,CAACjI,MAA9B,EAAsCkI,CAAC,GAAGC,GAA1C,EAA+CD,CAAC,EAAhD,EAAoD;QAClD,IAAIA,CAAC,KAAKD,OAAO,CAACjI,MAAR,GAAiB,CAA3B,EAA8B;UAC5BgI,WAAW,IAAIC,OAAO,CAACC,CAAD,CAAtB;QACD,CAFD,MAEO;UACLF,WAAW,IAAIC,OAAO,CAACC,CAAD,CAAP,GAAa,IAA5B;QACD;MACF;IACF;;IACD,OAAOF,WAAP;EACD;;EAEDI,mBAAmB;IACjB,KAAKC,uBAAL,GAA+B,IAA/B;EACD;;EAEDnK,iBAAiB,CAACoK,aAAD,EAAc;IAC7B,KAAKD,uBAAL,GAA+B,KAA/B;;IACA,IAAIC,aAAJ,EAAmB;MACjB,MAAM;QAAEzP,QAAF;QAAYC;MAAZ,IAAqB,KAAK+D,YAAhC;MACA,MAAMa,KAAK,GAAG5E,IAAI,KAAK,OAAT,GAAmB,iBAAnB,GAAuC,kBAArD,CAFiB,CAGjB;;MACA,MAAMmD,MAAM,GAAG,KAAK9B,SAAL,EAAf;MACA8B,MAAM,CAACsM,WAAP,CAAmB1P,QAAnB;MACAoD,MAAM,CAACuM,QAAP,CAAgB9K,KAAhB;MACA,KAAK+K,SAAL,GAAiB3P,IAAjB,CAPiB,CASjB;;MACA,KAAK4P,iBAAL,CAAuB;QAAE7P,QAAQ,EAAEA,QAAZ;QAAsB6E,KAAK,EAAE,KAAK+K;MAAlC,CAAvB;IACD;EACF;;EAEDC,iBAAiB,CAAC7G,IAAD,EAAK;IACpB,KAAKlF,OAAL,CAAaG,aAAb,GAA6B+E,IAA7B;EACD;;AAjuBuB;;;mBAAb5J,eAAaF;AAAA;;;QAAbE;EAAa0Q;EAAAC;IAAA;qBAuEb9Q,cAAY;;;;;;;;;;;;;;;;;;;MC1FzBC,+BAA6E,CAA7E,EAA6E,UAA7E,EAA6E,CAA7E,EAA6E,CAA7E,EAA6E,CAA7E,EAA6E,eAA7E,EAA6E,CAA7E,EAA6E,CAA7E,EAA6E,KAA7E,EAA6E,CAA7E,EAA6E,CAA7E,EAA6E,MAA7E,EAA6E,CAA7E;MAKoFA;MAAgBA;MAC5FA;;MACFA;MAGFA,yCAA2C,EAA3C,EAA2C,KAA3C,EAA2C,CAA3C,EAA2C,EAA3C,EAA2C,KAA3C,EAA2C,CAA3C,EAA2C,EAA3C,EAA2C,KAA3C,EAA2C,EAA3C,EAA2C,EAA3C,EAA2C,KAA3C,EAA2C,EAA3C;MAOuDA;QAAA,OAAgB8Q,kBAAhB;MAA8B,CAA9B;MAE7C9Q;MAiBAA;MAAgCA;QAAA,OAAS8Q,mBAAT;MAAwB,CAAxB;MAC7B9Q;MACDA;MAGFA;MACAA;MAGFA;MAGAA,iCAA8B,EAA9B,EAA8B,GAA9B,EAA8B,EAA9B;MAIIA;QAAA,OAAS8Q,iBAAT;MAAsB,CAAtB;;MAIC9Q;MAAQA;MAEXA;MAIEA;QAAA,OAAS8Q,yBAAT;MAA8B,CAA9B;;MAGC9Q;MAAQA;MAEXA;MAA6BA;QAAA,OAAqB8Q,kBAArB;MAAmC,CAAnC;MAC3B9Q;MAUAA;MAUFA;MAKJA;MACEA;MAUAA;MACEA;MACFA;MAIFA;MAEEA;MAYAA;MAAoDA;QAAA,OAAS8Q,gBAAT;MAAqB,CAArB;MAClD9Q;;MACFA;MAGAA;MAA4BA;QAAA,OAAqB8Q,kBAArB;MAAmC,CAAnC;MAC1B9Q;MAKAA;MAKFA;MAGAA;MAAmDA;QAAA;MAAA;MAEjDA;MAaAA;MAAeA;QAAA,OAAS8Q,qBAAiB,IAAjB,CAAT;MAA+B,CAA/B;MACb9Q;MAAoCA;;MAAoCA;MACxEA;MA6FFA;MAEFA;MACFA;MASVA;MASAA;MASAA;;;;MA3R4BA;MAC0BA;MAAAA;MACnCA;MAAAA,wCAAqB,SAArB,EAAqB,EAArB,EAAqB,SAArB,EAAqB,EAArB;MAGiEA;MAAAA;MAChDA;MAAAA;MAIjBA;MAAAA;MAEiBA;MAAAA;MAOEA;MAAAA;MAkBrBA;MAAAA;MAC4DA;MAAAA;MAI/BA;MAAAA;MAa9BA;MAAAA;MAJAA;MAaAA;MAAAA;MAJAA;MASGA;MAAAA;MAUAA;MAAAA;MAkBJA;MAAAA;MAQMA;MAAAA;MAKeA;MAAAA;MAETA;MAAAA;MAabA;MAAAA;MAKSA;MAAAA;MAKAA;MAAAA;MAQwCA;MAAAA;MAE0BA;MAAAA;MAa3BA;MAAAA;MACVA;MAAAA;MAgGlCA;MAAAA;MAWfA;MAAAA;MASAA;MAAAA;MAQ4BA;MAAAA", "names": ["HttpHeaders", "Exam", "Subscription", "finalize", "map", "AceComponent", "i0", "ctx_r6", "CodeComponent", "constructor", "examSer", "formSer", "translate", "autoTestSer", "_loading", "apiSer", "http", "_alert", "showPrintMargin", "wrap", "fontSize", "mode", "c", "cpp", "c_sharp", "java", "swift", "javascript", "go", "python2", "python3", "sql", "C", "Java", "Swift", "Javascript", "Go", "Python2", "Python3", "Sql", "composition", "ngOnInit", "getEditor", "componentRef", "directiveRef", "ace", "ngOnChanges", "initData", "showAceEditor", "setTimeout", "initAceEditor", "aceEle", "document", "querySelector", "removeEventListener", "compositionStart", "compositionEnd", "addEventListener", "ngOnDestroy", "subscriptions", "for<PERSON>ach", "sub", "unsubscribe", "clearTimeout", "timerGetResult", "setValue", "resetAnswer", "showResetAnswerTip", "toggleBottomCont", "isOpen", "showBottomCont", "resizeEditor", "editor", "resize", "toggleItemFullScreen", "isFullScreen", "itemFullScreen", "section", "currentSection", "item", "getItemById", "itemId", "itemRes", "getItemResById", "editorConfig", "editor_config", "showEnvInfoBlock", "getEnvInfo", "curLan", "firstIn", "first_in", "itemCodeChanged", "aceMode", "getAceMode", "caseResult", "commitResult", "compileError", "theme", "setSplitPos", "splitPos1", "splitPos2", "window", "eq", "css", "setDefaultLan", "confirmEditorMode", "checkAnswerLengthLimitFnBind", "focus", "getCompleted", "value", "initAnswer", "getInitAnswer", "updateCode", "editor<PERSON><PERSON>", "getValue", "completed", "gutterCellTip", "updateHighlightTip", "gutterCellAddMarker", "examEventsFromExamSvc", "next", "type", "EventFromExamService", "UpdateItemResponse", "_this", "doc", "session", "getDocument", "applyAnyDelta", "<PERSON><PERSON><PERSON><PERSON>", "delta", "joinedLines", "lines", "join", "char<PERSON><PERSON><PERSON>", "length", "lineLength", "<PERSON><PERSON><PERSON><PERSON>", "$maxLength", "console", "log", "alertTip", "$maxLineLength", "action", "max<PERSON><PERSON><PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON><PERSON><PERSON>", "submitCode", "stdExcute", "itemData", "item_code", "code", "entry_code", "answer", "code_type", "getCodeType", "judge_type", "test_pairs", "case", "inputCase", "getInitCase", "codeItemPosting", "push", "_postDataObservable", "pipe", "data", "subscribe", "getTimes", "recordData", "record_id", "patchItemRes", "retryOnError", "wait", "maxAttempts", "postNum", "tabActiveId", "firstErrLineIndex", "updateItemRes", "result", "compile_error", "getFirstErrLineIndex", "<PERSON><PERSON><PERSON>", "cases", "lastIndexOf", "time_sum", "Math", "round", "exception", "error", "err", "handleCodeApiErr", "status", "msg", "tipKey", "info", "bodyText", "instant", "headers", "post", "compileErr", "reg", "matches", "exec", "Number", "className", "gutterCell", "gutterTipDom", "addClass", "style", "top", "off", "on", "display", "removeClass", "switchTool", "showIntroTip", "closeTool", "getCommitResult", "Range", "acequire", "markerId", "add<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "updateStdCases", "stdCaseEle", "getElementById", "stdCase", "replace", "arr", "split", "lanPost", "allCasesPass", "getACCase<PERSON>ength", "aceModes", "n", "element", "verdict", "confirmChangeLan", "showChangeLanTip", "lanToBeSet", "closeLanMenu", "setMode", "focusEditor", "responseInfo", "code_language", "updateItemResponse", "cancelChangeLan", "confirmResetAnswer", "content", "init_answer", "cancelResetAnswer", "lanDisplayed", "selectLan", "lan", "toggleLanMenu", "showLanList", "toggleEnvInfo", "isShow", "showEnvInfoCont", "lanSet", "details", "version", "closeFirstInTip", "initCase", "initCaseStr", "caseArr", "i", "len", "showEditorModeModal", "isEditorModeModalShowed", "ifConfirmMode", "setFontSize", "setTheme", "areaTheme", "updateEditorTheme", "selectors", "viewQuery", "ctx"], "sourceRoot": "", "sources": ["D:\\work\\joyserver\\client\\src\\app\\exam\\exam-sailfish\\item\\code\\code.component.ts", "D:\\work\\joyserver\\client\\src\\app\\exam\\exam-sailfish\\item\\code\\code.component.html"], "sourcesContent": ["import { CustomAlertService } from \"@core/modal/custom-alert/custom-alert.service\";\nimport { HttpClient, HttpHeaders } from \"@angular/common/http\";\nimport { APIService } from \"@core/service/api.service\";\nimport { CustomLoadingService } from \"@core/modal/custom-loading/custom-loading.service\";\nimport { TranslateService } from \"@ngx-translate/core\";\nimport { FormService } from \"@core-service/form.service\";\nimport { ExamService } from \"@core-service/exam.service\";\nimport { Component, Input, OnInit, ViewChild } from \"@angular/core\";\nimport { Exam } from \"@core-types/exam.types\";\nimport { Subscription, timer } from \"rxjs\";\nimport { finalize, mergeMap, map, retryWhen, tap } from \"rxjs/operators\";\nimport { AceComponent } from \"ngx-ace-wrapper\";\nimport { AutoTestService } from \"@core/service/auto-test.service\";\n\n@Component({\n  selector: \"app-code\",\n  templateUrl: \"./code.component.html\",\n  styleUrls: [\"./code.component.scss\"],\n})\nexport class CodeComponent implements OnInit {\n  public section: Exam.Section = null;\n  public item: Exam.Item = null;\n  public itemRes = null;\n  public aceMode;\n  public options = { showPrintMargin: false, wrap: false };\n  public showLanList = false;\n  public showEnvInfoBlock: any;\n  public showEnvInfoCont = false;\n  public curLan: string; // 当前编程语言\n  public lanToBeSet; // 点击下拉选择编程语言后待确认语言选择\n  public itemFullScreen = false;\n  public showBottomCont = false;\n  public showChangeLanTip = false;\n  public showResetAnswerTip = false;\n  public tabActiveId: \"inputBox\" | \"result\" = \"inputBox\";\n  public compileError = \"\"; // 编译报错结果\n  public commitResult = null;\n  public caseResult = null;\n  public firstErrLineIndex = -1;\n  public casePassed = \"\"; //\n  public gutterCellTip: string;\n  public firstIn = true; // 第一次进入试给出提交提示\n  public showAceEditor = false;\n  public ltPercentVal = 30;\n  public inputCase;\n  public composition = false;\n\n  public isEditorModeModalShowed = false;\n  public editorConfig = {\n    fontSize: \"16px\",\n    mode: \"black\", // chaos(black) | clouds(white)\n  };\n  public areaTheme = \"white\"; // black、white\n\n  public fontsPresets = [\"14px\", \"15px\", \"16px\", \"17px\", \"18px\", \"19px\", \"20px\"];\n  public maxLinesLength = 5000;\n  public maxCharLength = 20 * 1000;\n  public lanDisplayed = {\n    c: \"C\",\n    cpp: \"C++\",\n    c_sharp: \"C#\",\n    java: \"Java\",\n    swift: \"Swift\",\n    javascript: \"Javascript\",\n    go: \"Go\",\n    python2: \"Python2\",\n    python3: \"Python3\",\n    sql: \"Sql\",\n  };\n\n  public lanPost = {\n    C: \"c\",\n    \"C++\": \"cpp\",\n    \"C#\": \"c_sharp\",\n    Java: \"java\",\n    Swift: \"swift\",\n    Javascript: \"javascript\",\n    Go: \"go\",\n    Python2: \"python2\",\n    Python3: \"python3\",\n    Sql: \"sql\",\n  };\n\n  private markerId;\n  private timerGetResult;\n  private subscriptions: Subscription[] = [];\n  public showIntroTip = false;\n  @Input() itemId;\n  @Input() showGroupInstr;\n\n  @ViewChild(AceComponent, { static: false }) componentRef?: AceComponent;\n\n  constructor(\n    public examSer: ExamService,\n    public formSer: FormService,\n    public translate: TranslateService,\n    private autoTestSer: AutoTestService,\n    private _loading: CustomLoadingService,\n    private apiSer: APIService,\n    private http: HttpClient,\n    private _alert: CustomAlertService\n  ) {}\n\n  ngOnInit() {}\n\n  getEditor() {\n    return this.componentRef?.directiveRef.ace();\n  }\n\n  ngOnChanges() {\n    this.initData();\n    this.showAceEditor = false;\n    setTimeout(() => {\n      this.showAceEditor = true;\n      setTimeout(() => {\n        this.initAceEditor();\n        const aceEle = document.querySelector(\"ace\") as HTMLElement;\n        if (aceEle) {\n          aceEle.removeEventListener(\"compositionstart\", this.compositionStart);\n          aceEle.removeEventListener(\"compositionend\", this.compositionEnd);\n          aceEle.addEventListener(\"compositionstart\", this.compositionStart);\n          aceEle.addEventListener(\"compositionend\", this.compositionEnd);\n        }\n      });\n    });\n  }\n\n  ngOnDestroy() {\n    this.subscriptions.forEach((sub) => sub.unsubscribe());\n    clearTimeout(this.timerGetResult);\n    this.timerGetResult = null;\n    this._loading.setValue(false);\n\n    const aceEle = document.querySelector(\"ace\") as HTMLElement;\n    if (aceEle) {\n      aceEle.removeEventListener(\"compositionstart\", this.compositionStart);\n      aceEle.removeEventListener(\"compositionend\", this.compositionEnd);\n    }\n  }\n\n  resetAnswer() {\n    this.showResetAnswerTip = true;\n  }\n\n  toggleBottomCont(isOpen) {\n    this.showBottomCont = isOpen;\n  }\n\n  resizeEditor() {\n    const editor = this.getEditor();\n    editor && editor.resize();\n  }\n\n  toggleItemFullScreen(isFullScreen) {\n    this.itemFullScreen = isFullScreen;\n  }\n\n  initData() {\n    this.section = this.examSer.currentSection();\n    this.item = this.formSer.getItemById(this.section, this.itemId) as Exam.Item;\n    this.itemRes = this.examSer.getItemResById(this.itemId);\n\n    const editorConfig = this.itemRes.editor_config;\n    this.showEnvInfoBlock = this.getEnvInfo(this.curLan);\n    this.firstIn = this.itemRes.first_in;\n    this.examSer.itemCodeChanged = false;\n    this.aceMode = this.getAceMode();\n    this.showBottomCont = false;\n    this.caseResult = null;\n    this.commitResult = null;\n    this.compileError = \"\";\n    this.editorConfig.fontSize = editorConfig ? editorConfig.fontSize : \"16px\";\n    this.editorConfig.mode = editorConfig ? editorConfig.theme : \"black\";\n\n    // reset split pos\n    this.setSplitPos(30);\n  }\n\n  setSplitPos(splitPos1) {\n    const splitPos2 = 100 - splitPos1;\n    setTimeout(() => {\n      window[\"$\"](\".item-code as-split-area\")\n        .eq(0)\n        .css(\"flex\", \"0 0 calc(\" + splitPos1 + \"% - \" + splitPos1 / 10 + \"px)\");\n      window[\"$\"](\".item-code as-split-area\")\n        .eq(1)\n        .css(\"flex\", \"0 0 calc(\" + splitPos2 + \"% - \" + (6 - splitPos1 / 10) + \"px)\");\n    }, 10);\n  }\n\n  initAceEditor() {\n    this.setDefaultLan();\n    const editor = this.getEditor();\n    if (editor) {\n      this.confirmEditorMode(true);\n      this.checkAnswerLengthLimitFnBind(editor);\n      this.examSer.itemCodeChanged = false; // 标记内容未修改，防止初始化即被认为答案变动\n      editor.focus();\n    }\n  }\n\n  getCompleted(value: string) {\n    const initAnswer = this.getInitAnswer();\n    return value !== initAnswer;\n  }\n\n  // 更新代码区代码\n  updateCode() {\n    // console.log(this.itemRes.answer.value);\n    const editor = this.getEditor();\n    if (editor) {\n      const editorVal = editor.getValue();\n      // console.log(editorVal);\n      this.itemRes.completed = this.getCompleted(editorVal);\n    }\n    this.gutterCellTip = \"\";\n    this.updateHighlightTip();\n    this.gutterCellAddMarker();\n    this.examSer.itemCodeChanged = true;\n\n    this.examSer.examEventsFromExamSvc.next({\n      type: Exam.EventFromExamService.UpdateItemResponse,\n    });\n  }\n\n  checkAnswerLengthLimitFnBind(editor) {\n    const _this = this;\n    const doc = editor.session.getDocument() as any;\n    doc.applyAnyDelta = doc[\"applyAnyDelta\"] || doc[\"applyDelta\"];\n    doc.applyDelta = function (delta: any) {\n      let joinedLines = delta.lines.join(\"\\n\");\n      const charLength = this.getValue().length;\n      const lineLength = this.getLength();\n      // console.log(delta);\n      // 如果是中文输入法，超过字数，insert、remove都return\n      if (_this.composition) {\n        if (charLength + joinedLines.length > this.$maxLength) {\n          console.log(\"**** Max chars exceed [code item Ace editor]：\", charLength);\n          _this.examSer.alertTip(\"exam.code.maxTextLengthTip\");\n          return false;\n        }\n        if (lineLength + delta.lines.length > this.$maxLineLength + 1) {\n          console.log(\"**** Max lines exceed [code item Ace editor]：\", lineLength);\n          _this.examSer.alertTip(\"exam.code.maxLinesTip\");\n          return false;\n        }\n      }\n\n      if (delta.action == \"insert\" && this.$maxLength && this.$maxLineLength) {\n        // check char max length\n        if (charLength + joinedLines.length > this.$maxLength) {\n          if (charLength >= this.$maxLength) {\n            console.log(\"**** Max chars exceed [code item Ace editor]：\", charLength);\n            _this.examSer.alertTip(\"exam.code.maxTextLengthTip\");\n            return false;\n          }\n        }\n\n        // check line max length\n        if (lineLength + delta.lines.length > this.$maxLineLength + 1) {\n          console.log(\"**** Max lines exceed [code item Ace editor]：\", lineLength);\n          _this.examSer.alertTip(\"exam.code.maxLinesTip\");\n          return false;\n        }\n      }\n\n      return this.applyAnyDelta(delta);\n    };\n    doc.$maxLength = this.maxCharLength;\n    doc.$maxLineLength = this.maxLinesLength;\n  }\n\n  compositionStart = () => {\n    this.composition = true;\n  };\n\n  compositionEnd = () => {\n    this.composition = false;\n    // console.log('compositionEnd event');\n  };\n\n  // 提交代码\n  submitCode() {\n    this.showBottomCont = true;\n    this.stdExcute();\n  }\n\n  // 考生自测运行\n  // 考试机客户端版(不含单机版)可以连外网直接连外网，否则连接管理机中转；浏览器版直接连外网\n  stdExcute() {\n    const itemRes = this.itemRes;\n    const itemData = {\n      item_code: this.item.code,\n      entry_code: itemRes.answer.value,\n      code_type: this.getCodeType(this.curLan),\n      judge_type: \"run\", // 暂固定用\"run\", 判分改为云端操作，因此不用\"commit\"\n      test_pairs: this.itemRes.answer.case,\n    };\n\n    this.inputCase = this.getInitCase();\n\n    // 提交代码至编程题外部服务器\n    this._loading.setValue(true);\n    this.autoTestSer.codeItemPosting = true;\n    this.subscriptions.push(\n      this.examSer\n        ._postDataObservable(this.apiSer.code, itemData)\n        .pipe(\n          map((data) => {\n            if (data[\"status\"] !== \"success\") {\n              throw data;\n            }\n            return data;\n          })\n        )\n        .subscribe({\n          next: (data) => {\n            let getTimes = 0;\n            // post成功之后延迟4s去get result，后端可能5s之后才有result，第一次没成功，接着每2s get 一次\n            this.timerGetResult = setTimeout(() => {\n              const recordData = { record_id: data[\"record_id\"] };\n              this.subscriptions.push(\n                this.patchItemRes(recordData)\n                  .pipe(\n                    map((data) => {\n                      if (data[\"status\"] !== \"success\") {\n                        // error will be picked up by retryWhen\n                        throw data;\n                      }\n                      return data;\n                    }),\n                    this.examSer.retryOnError({ wait: 2000, maxAttempts: 5 }),\n                    finalize(() => {\n                      this.examSer.postNum--;\n                      // console.log(\"  this.examSer.postNum--\", this.examSer.postNum);\n                      this.autoTestSer.codeItemPosting = false;\n                    })\n                  )\n                  .subscribe({\n                    next: async (data: any) => {\n                      this.tabActiveId = \"result\";\n                      // 自测运行，展示结果：输入、预期输出、实际输出；运行提交，展示结果：每个用例的状态\n                      this.compileError = \"\";\n                      this.firstErrLineIndex = -1;\n                      this.commitResult = null;\n                      this.caseResult = null;\n                      this.examSer.itemCodeChanged = false;\n\n                      // 不论运行结果，都保存答案\n                      this.updateItemRes(data.result);\n\n                      // 展示编译结果\n                      if (data.result.compile_error) {\n                        // 编译出错\n                        this.compileError = data.result.compile_error;\n                        this.firstErrLineIndex = this.getFirstErrLineIndex(this.compileError);\n                        this.updateHighlightTip(\"ace_highlight-marker\");\n                        this.gutterCellAddMarker(this.compileError, \"hg-lt\");\n                      } else {\n                        // 编译成功\n                        // 显示单个用例结果\n                        for (const caseKey in data.result.cases) {\n                          if (caseKey.lastIndexOf(\"\\0\", caseKey.length - 2)) {\n                            this.caseResult = data.result.cases[caseKey];\n                            this.caseResult.time_sum = Math.round(this.caseResult.time_sum * 1000);\n                            if (this.caseResult?.exception) {\n                              this.firstErrLineIndex = this.getFirstErrLineIndex(this.caseResult.exception);\n                              this.updateHighlightTip(\"ace_highlight-marker\");\n                              this.gutterCellAddMarker(this.compileError, \"hg-lt\");\n                            }\n                          }\n                        }\n                      }\n\n                      this._loading.setValue(false);\n                    },\n                    error: (err) => {\n                      console.log(`* Get code result fail`);\n                      this.handleCodeApiErr(err);\n                    },\n                  })\n              );\n\n              clearTimeout(this.timerGetResult);\n              this.timerGetResult = null;\n            }, 4000);\n          },\n          error: (err) => {\n            this._loading.setValue(false);\n            this.autoTestSer.codeItemPosting = false;\n\n            console.log(`* Post code fail`);\n            this.handleCodeApiErr(err);\n          },\n        })\n    );\n  }\n\n  handleCodeApiErr(err) {\n    this._loading.setValue(false);\n    console.log(`Code API error : err.status: ${err.status}, err.msg: ${err.msg}`);\n    let tipKey;\n    switch (err.status) {\n      case \"error\":\n      case \"judging\": {\n        console.log(\"** status = error: wrong post data structure [item code]\");\n        tipKey = \"exam.code.errTip.serverCallErr\";\n        break;\n      }\n\n      case \"timeout\": {\n        console.log(\"** status = timeout: server timeout\");\n        tipKey = \"exam.code.errTip.timeout\";\n        break;\n      }\n\n      case \"call-error\": {\n        console.log(\"** status = call-error\");\n        tipKey = \"exam.code.errTip.managerNetOffline\";\n        break;\n      }\n\n      case 0:\n      case 502: {\n        console.log(\"** Client network offline\");\n        tipKey = \"exam.code.errTip.clientNetOffline\";\n        break;\n      }\n\n      case 408: {\n        console.log(\"** Manager is syncing data\");\n        tipKey = \"exam.code.errTip.dataLoading\";\n        break;\n      }\n\n      default: {\n        tipKey = \"exam.code.tryAgainTip\";\n      }\n    }\n\n    this._alert.setValue({\n      status: true,\n      info: { bodyText: this.translate.instant(tipKey) },\n    });\n  }\n\n  // 提交考生试题答案\n  patchItemRes(itemData) {\n    const headers = new HttpHeaders();\n    // const headers = new HttpHeaders({ \"Content-Type\": \"multipart/form-data\" });\n    this.examSer.postNum++;\n    return this.http.post<{ filename: string }>(this.apiSer.code, itemData, { headers });\n  }\n\n  getFirstErrLineIndex(compileErr) {\n    const reg = /Line ((\\d)+)[:,]/;\n    const matches = reg.exec(compileErr);\n    if (matches && matches[1]) {\n      return Number(matches[1]);\n    }\n    return -1;\n  }\n\n  gutterCellAddMarker(compileErr?, className?) {\n    const gutterCell = window[\"$\"](\".ace_gutter-layer .ace_gutter-cell\").eq(this.firstErrLineIndex - 1);\n    const gutterTipDom = document.querySelector(\".gutter-cell-tip-wrap\") as HTMLElement;\n    if (className) {\n      if (this.firstErrLineIndex > -1) {\n        gutterCell.addClass(className);\n\n        const matches = /.*[\\n|\\r\\n]/.exec(compileErr); // 截取第一行\n        if (matches && matches[0]) {\n          this.gutterCellTip = matches[0];\n          gutterTipDom.style.top = gutterCell.css(\"top\");\n          gutterCell\n            .off(\"mouseover\")\n            .off(\"mouseout\")\n            .on(\"mouseover\", () => {\n              gutterTipDom.style.display = \"block\";\n            })\n            .on(\"mouseout\", () => {\n              gutterTipDom.style.display = \"none\";\n            });\n        }\n      }\n    } else {\n      gutterCell.removeClass(\"hg-lt\");\n      gutterCell.off(\"mouseover\").off(\"mouseout\");\n    }\n  }\n\n  switchTool() {\n    this.showIntroTip = !this.showIntroTip;\n  }\n\n  closeTool() {\n    this.showIntroTip = false;\n  }\n\n  getCommitResult(cases) {\n    const result = [];\n    for (const caseKey in cases) {\n      if (caseKey.lastIndexOf(\"\\0\", caseKey.length - 2)) {\n        result.push(cases[caseKey]);\n      }\n    }\n    return result;\n  }\n\n  updateHighlightTip(className?) {\n    const Range = window[\"ace\"].acequire(\"ace/range\").Range;\n    const editor = this.getEditor();\n    if (className) {\n      if (this.firstErrLineIndex > -1) {\n        this.markerId = editor?.session.addMarker(\n          new Range(this.firstErrLineIndex - 1, 0, this.firstErrLineIndex - 1, 400),\n          className,\n          \"fullLine\",\n          false\n        );\n      }\n    } else {\n      editor?.session.removeMarker(this.markerId);\n    }\n  }\n\n  updateStdCases() {\n    const stdCaseEle = document.getElementById(\"std-case\") as HTMLInputElement;\n    const stdCase = stdCaseEle.value.replace(/\\r\\n/g, \"\\n\").replace(/\\n+/g, \"\\n\");\n    const arr = [stdCase.split(\"\\n\")];\n    this.itemRes.answer.case = arr;\n  }\n\n  getCodeType(curLan) {\n    return this.lanPost[curLan];\n  }\n\n  allCasesPass() {\n    return this.getACCaseLength(this.commitResult) === this.commitResult.length;\n  }\n\n  getAceMode() {\n    const aceModes = {\n      \"C#\": \"csharp\",\n      Go: \"golang\",\n      Swift: \"swift\",\n      Javascript: \"javascript\",\n      Python2: \"python\",\n      Python3: \"python\",\n      C: \"c_cpp\",\n      \"C++\": \"c_cpp\",\n      Java: \"java\",\n      Sql: \"sql\",\n    };\n\n    if (aceModes[this.curLan]) {\n      return aceModes[this.curLan];\n    }\n    return \"java\";\n  }\n\n  getACCaseLength(cases) {\n    let n = 0;\n    cases.forEach((element) => {\n      if (element.verdict === \"AC\") {\n        n++;\n      }\n    });\n    return n;\n  }\n\n  confirmChangeLan() {\n    this.showAceEditor = false;\n    setTimeout(() => {\n      this.showAceEditor = true;\n      this.showBottomCont = false;\n      this.showChangeLanTip = false;\n      this.curLan = this.lanToBeSet;\n      this.showEnvInfoBlock = this.getEnvInfo(this.curLan);\n      this.caseResult = null;\n      this.commitResult = null;\n      this.compileError = \"\";\n      this.itemRes.completed = false;\n      this.examSer.itemCodeChanged = false;\n      this.closeLanMenu();\n      setTimeout(() => {\n        const editor = this.getEditor();\n        editor.session.setMode(\"ace/mode/\" + this.getAceMode());\n        this.confirmEditorMode(true);\n        editor.setValue(this.getInitAnswer());\n        this.updateItemRes();\n        this.updateHighlightTip();\n        this.gutterCellAddMarker();\n        editor.focus();\n        this.checkAnswerLengthLimitFnBind(editor);\n      });\n    });\n  }\n\n  focusEditor() {\n    const editor = this.getEditor();\n    if (editor) {\n      editor.focus();\n    }\n  }\n\n  updateItemRes(result?) {\n    const responseInfo = {\n      item: this.item,\n      result: result,\n      code_language: this.lanPost[this.curLan],\n      value: this.itemRes.answer.value,\n    };\n    this.examSer.updateItemResponse(responseInfo);\n  }\n\n  cancelChangeLan() {\n    this.showChangeLanTip = false;\n  }\n\n  confirmResetAnswer() {\n    const editor = this.getEditor();\n    this.showResetAnswerTip = false;\n    editor.setValue(this.getInitAnswer());\n    this.focusEditor();\n  }\n\n  getInitAnswer() {\n    const initAnswer = this.item.content.init_answer;\n    if (initAnswer?.[this.curLan]) {\n      return initAnswer[this.curLan];\n    }\n    return \"\";\n  }\n\n  cancelResetAnswer() {\n    this.showResetAnswerTip = false;\n  }\n\n  setDefaultLan() {\n    this.curLan = this.lanDisplayed[this.itemRes.answer.code_language];\n    // this.hasInitAnswer = this.checkInitAnswer();\n    this.showEnvInfoBlock = this.getEnvInfo(this.curLan);\n\n    const editor = this.getEditor();\n    editor.session.setMode(\"ace/mode/\" + this.getAceMode());\n  }\n\n  selectLan(lan) {\n    if (lan === this.curLan) {\n      this.closeLanMenu();\n      return;\n    }\n\n    // 如果没有内容，不提示\n    const editor = this.getEditor();\n    this.showChangeLanTip = editor.getValue() ? true : false;\n    this.lanToBeSet = lan;\n\n    if (!this.showChangeLanTip) {\n      this.confirmChangeLan();\n    }\n  }\n\n  toggleLanMenu() {\n    this.showLanList = !this.showLanList;\n  }\n\n  toggleEnvInfo(isShow) {\n    this.showEnvInfoCont = isShow;\n  }\n\n  closeLanMenu() {\n    this.showLanList = false;\n  }\n\n  getEnvInfo(lanSet) {\n    const details = {\n      C: {\n        version: this.translate.instant(\"exam.code.lan-c\"),\n      },\n      \"C++\": {\n        version: this.translate.instant(\"exam.code.lan-cpp\"),\n      },\n      \"C#\": {\n        version: this.translate.instant(\"exam.code.lan-cShar\"),\n      },\n      Java: {\n        version: this.translate.instant(\"exam.code.lan-java\"),\n      },\n      Swift: {\n        version: this.translate.instant(\"exam.code.lan-swift\"),\n      },\n      Javascript: {\n        version: this.translate.instant(\"exam.code.lan-js\"),\n      },\n      Go: {\n        version: this.translate.instant(\"exam.code.lan-go\"),\n      },\n      Python2: {\n        version: this.translate.instant(\"exam.code.lan-p2\"),\n      },\n      Python3: {\n        version: this.translate.instant(\"exam.code.lan-p3\"),\n      },\n      Sql: {\n        version: \"\",\n      },\n    };\n    return details[lanSet] ? details[lanSet] : null;\n  }\n\n  closeFirstInTip() {\n    this.firstIn = false;\n    this.itemRes.first_in = false;\n  }\n\n  getInitCase() {\n    const initCase = this.itemRes.answer.case;\n    let initCaseStr = \"\";\n\n    if (initCase.length > 0) {\n      const caseArr = initCase[0];\n      for (let i = 0, len = caseArr.length; i < len; i++) {\n        if (i === caseArr.length - 1) {\n          initCaseStr += caseArr[i];\n        } else {\n          initCaseStr += caseArr[i] + \"\\n\";\n        }\n      }\n    }\n    return initCaseStr;\n  }\n\n  showEditorModeModal() {\n    this.isEditorModeModalShowed = true;\n  }\n\n  confirmEditorMode(ifConfirmMode) {\n    this.isEditorModeModalShowed = false;\n    if (ifConfirmMode) {\n      const { fontSize, mode } = this.editorConfig;\n      const theme = mode === \"black\" ? \"ace/theme/chaos\" : \"ace/theme/clouds\";\n      // console.log(this.editorConfig);\n      const editor = this.getEditor();\n      editor.setFontSize(fontSize);\n      editor.setTheme(theme);\n      this.areaTheme = mode;\n\n      // 保存编辑器风格设置到item response\n      this.updateEditorTheme({ fontSize: fontSize, theme: this.areaTheme });\n    }\n  }\n\n  updateEditorTheme(data) {\n    this.itemRes.editor_config = data;\n  }\n}\n", "<div class=\"item item-code\" [ngClass]=\"{ 'code-fullcreen': itemFullScreen }\">\n  <as-split #graphSplit unit=\"percent\" [direction]=\"\" [gutterSize]=\"10\">\n    <as-split-area [size]=\"ltPercentVal\" [minSize]=\"10\" [maxSize]=\"90\">\n      <!--题干区-->\n      <div class=\"wrap-item-stem p-3\">\n        <span class=\"item-index float-left rounded bg-primary text-white px-1 mr-1\">{{ item.index }}</span>\n        <div class=\"item-stem mb-3\" [innerHTML]=\"item.content.stem | safeHtml\" richText></div>\n      </div>\n    </as-split-area>\n\n    <as-split-area [size]=\"100 - ltPercentVal\">\n      <!--答题区-->\n      <div class=\"item-answer-area\" [ngClass]=\"{ 'theme-white': areaTheme === 'white' }\">\n        <div class=\"area-cont\">\n          <!--top-->\n          <div class=\"top\">\n            <!--编程语言选择-->\n            <div class=\"wrap-lan-set float-left ml-3 pt-1\" (clickOutside)=\"closeLanMenu()\">\n              <!--展示语言详细信息：版本、描述等-->\n              <span class=\"env-info\" *ngIf=\"showEnvInfoBlock\">\n                <i class=\"iconfont\" (click)=\"toggleEnvInfo(true)\" role=\"button\">&#xe660;</i>\n                <div class=\"env-info-cont\" *ngIf=\"showEnvInfoCont\">\n                  <div class=\"title h6 pt-3 pl-4 pr-4 pb-3\">\n                    {{ \"exam.code.lan-env\" | translate }}\n                    <i role=\"button\" class=\"iconfont float-right text-gray font-min\" (click)=\"toggleEnvInfo(false)\"\n                      >&#xe66b;</i\n                    >\n                  </div>\n                  <div class=\"pt-2 pl-4 pr-4 pb-3\">\n                    <div class=\"pb-2\">{{ \"exam.code.builder\" | translate }}</div>\n                    <div class=\"text-gray\">{{ showEnvInfoBlock.version }}</div>\n                  </div>\n                </div>\n              </span>\n\n              <!--编程语言选择-->\n              <span class=\"lan-set pl-2 ml-2\" (click)=\"toggleLanMenu()\"\n                >{{ curLan }} &nbsp;&nbsp;&nbsp;&nbsp;\n                <i role=\"button\" class=\"iconfont font-min float-right mr-2\" *ngIf=\"item.code_languages.length > 1\"\n                  >&#xe6fe;</i\n                >\n              </span>\n              <ul class=\"lan-list shadow-sm\" *ngIf=\"showLanList && item.code_languages.length > 1\">\n                <li *ngFor=\"let lan of item.code_languages\" (click)=\"selectLan(lan)\" [innerHTML]=\"lan\"></li>\n              </ul>\n            </div>\n\n            <!--顶部右侧按钮：reset、fullscreen-->\n            <div class=\"float-right pt-1\">\n              <i\n                class=\"iconfont mr-2 p-2\"\n                [ngClass]=\"{ 'text-light': areaTheme !== 'white', 'text-secondary': areaTheme === 'white' }\"\n                (click)=\"resetAnswer()\"\n                role=\"button\"\n                placement=\"left\"\n                ngbTooltip=\"{{ 'exam.code.reset' | translate }}\"\n                >&#xe69d;</i\n              >\n              <i\n                class=\"iconfont mr-2 p-2\"\n                [ngClass]=\"{ 'text-light': areaTheme !== 'white', 'text-secondary': areaTheme === 'white' }\"\n                role=\"button\"\n                (click)=\"showEditorModeModal()\"\n                placement=\"left\"\n                ngbTooltip=\"{{ 'exam.code.config' | translate }}\"\n                >&#xe62c;</i\n              >\n              <span appAppMotationObserver (innerHtmlRendered)=\"resizeEditor()\">\n                <i\n                  *ngIf=\"!itemFullScreen\"\n                  class=\"iconfont mr-2 p-2\"\n                  [ngClass]=\"{ 'text-light': areaTheme !== 'white', 'text-secondary': areaTheme === 'white' }\"\n                  role=\"button\"\n                  (click)=\"toggleItemFullScreen(true)\"\n                  placement=\"left\"\n                  ngbTooltip=\"{{ 'exam.code.fullscreen' | translate }}\"\n                  >&#xe6ca;</i\n                >\n                <i\n                  *ngIf=\"itemFullScreen\"\n                  class=\"iconfont mr-2 p-2\"\n                  [ngClass]=\"{ 'text-light': areaTheme !== 'white', 'text-secondary': areaTheme === 'white' }\"\n                  role=\"button\"\n                  (click)=\"toggleItemFullScreen(false)\"\n                  placement=\"left\"\n                  ngbTooltip=\"{{ 'exam.code.exitfullscreen' | translate }}\"\n                  >&#xe64b;</i\n                >\n              </span>\n            </div>\n          </div>\n\n          <!--代码区-->\n          <div class=\"main\">\n            <ace\n              class=\"\"\n              #aceEditor\n              *ngIf=\"showAceEditor\"\n              [config]=\"options\"\n              [mode]=\"aceMode\"\n              [(value)]=\"itemRes.answer.value\"\n              (valueChange)=\"updateCode()\"\n            ></ace>\n            <!--gutter cell tip-->\n            <div class=\"gutter-cell-tip-wrap\">\n              <span *ngIf=\"!!gutterCellTip\">{{ gutterCellTip }}</span>\n            </div>\n          </div>\n\n          <!--结果区-->\n          <div class=\"input-output\" [ngClass]=\"{ 'fold-up': !showBottomCont }\">\n            <!--第一次进入提示-->\n            <ng-container *ngIf=\"firstIn\">\n              <div class=\"first-in-tip tooltip show bs-tooltip-bottom float-right\">\n                <div class=\"tooltip-inner inner\">\n                  <small class=\"ng-star-inserted\">{{ \"exam.code.submitTip\" | translate }}</small>\n                  <button class=\"btn btn-confirm\" (click)=\"closeFirstInTip()\">\n                    <small>{{ \"exam.code.submitTipBtn\" | translate }}</small>\n                  </button>\n                </div>\n                <div class=\"arrow\"></div>\n              </div>\n            </ng-container>\n            <!--运行测试按钮-->\n            <button class=\"btn btn-primary btn-run float-right\" (click)=\"submitCode()\">\n              {{ \"exam.code.submit\" | translate }}\n            </button>\n\n            <!--隐藏-显示按钮-->\n            <div appAppMotationObserver (innerHtmlRendered)=\"resizeEditor()\">\n              <button *ngIf=\"!showBottomCont\" class=\"btn-expand float-right\" (click)=\"toggleBottomCont(true)\">\n                <div class=\"box-arrow-up\">\n                  <div class=\"arrow-up\"></div>\n                </div>\n              </button>\n              <button *ngIf=\"showBottomCont\" class=\"btn-expand float-right\" (click)=\"toggleBottomCont(false)\">\n                <div class=\"box-arrow-down\">\n                  <div class=\"arrow-down\"></div>\n                </div>\n              </button>\n            </div>\n\n            <!--nav-->\n            <ul ngbNav #nav=\"ngbNav\" class=\"nav-tabs font-min\" [(activeId)]=\"tabActiveId\">\n              <!--测试用例: sql 不需要自测输入用例-->\n              <li ngbNavItem (click)=\"toggleBottomCont(true)\" [ngbNavItem]=\"'inputBox'\" *ngIf=\"curLan !== 'Sql'\">\n                <a ngbNavLink class=\"p-1 pl-2 pr-2\">{{ \"exam.code.testCase\" | translate }}</a>\n                <ng-template ngbNavContent>\n                  <textarea\n                    class=\"case-textarea p-2\"\n                    id=\"std-case\"\n                    [value]=\"getInitCase()\"\n                    (blur)=\"updateStdCases()\"\n                  ></textarea>\n                  <span class=\"tip font-min\">{{ \"exam.code.caseTip\" | translate }}</span>\n                </ng-template>\n              </li>\n              <!--代码运行结果-->\n              <li ngbNavItem (click)=\"toggleBottomCont(true)\" [ngbNavItem]=\"'result'\">\n                <a ngbNavLink class=\"p-1 pl-2 pr-2\">{{ \"exam.code.result\" | translate }}</a>\n                <ng-template ngbNavContent>\n                  <!--default tip: 您的运行结果将显示在这里-->\n                  <ng-container *ngIf=\"!commitResult && !caseResult && !compileError\">\n                    <span class=\"tip font-min\">{{ \"exam.code.defaultTip\" | translate }}</span>\n                  </ng-container>\n                  <!--编译报错提示，同时highlight 对应行-->\n                  <ng-container *ngIf=\"!!compileError\">\n                    <div class=\"compile-result error\">\n                      <div class=\"text-danger compile-err-title\">{{ \"exam.code.compileErr\" | translate }}</div>\n                      <pre class=\"text-danger\" [innerHTML]=\"compileError\"></pre>\n                    </div>\n                  </ng-container>\n                  <!--运行测试成功，结果展示-->\n                  <ng-container *ngIf=\"!compileError && caseResult\">\n                    <div class=\"compile-result\">\n                      <ng-container *ngIf=\"caseResult?.verdict === 'AC' || caseResult?.verdict === 'WA'\">\n                        <table class=\"table-result w-100 text-center\">\n                          <thead>\n                            <tr>\n                              <td *ngIf=\"curLan !== 'Sql'\">{{ \"exam.code.input\" | translate }}</td>\n                              <td *ngIf=\"!!caseResult?.key\">{{ \"exam.code.expectOut\" | translate }}</td>\n                              <td>{{ \"exam.code.output\" | translate }}</td>\n                            </tr>\n                          </thead>\n                          <tr>\n                            <td *ngIf=\"curLan !== 'Sql'\">{{ inputCase }}</td>\n                            <td *ngIf=\"!!caseResult?.key\">{{ caseResult.key }}</td>\n                            <td>{{ caseResult.answer }}</td>\n                          </tr>\n                        </table>\n                      </ng-container>\n                      <ng-container *ngIf=\"caseResult?.verdict === 'TLE'\">\n                        <div class=\"text-danger compile-err-title\">{{ \"exam.code.timeout\" | translate }}</div>\n                      </ng-container>\n                      <ng-container *ngIf=\"caseResult?.verdict === 'RE' || caseResult?.verdict === 'RTE'\">\n                        <div class=\"text-danger compile-err-title\">{{ \"exam.code.runErr\" | translate }}</div>\n                        <pre class=\"text-danger\" [innerHTML]=\"caseResult.exception\"></pre>\n                      </ng-container>\n                      <ng-container *ngIf=\"caseResult?.verdict === 'OLE'\">\n                        <div class=\"text-danger compile-err-title\">{{ \"exam.code.outputLenExceed\" | translate }}</div>\n                      </ng-container>\n                      <ng-container *ngIf=\"caseResult?.verdict === 'MLE'\">\n                        <div class=\"text-danger compile-err-title\">{{ \"exam.code.memoryLenExceed\" | translate }}</div>\n                      </ng-container>\n                    </div>\n                  </ng-container>\n\n                  <!--运行并提交成功，结果展示-->\n                  <!--只有\"run\"类型，show_commit_result 属性无效\n                  <ng-container *ngIf=\"item.content.show_commit_result\">\n                    <div class=\"compile-result\">\n                      <div class=\"mb-2\" [ngClass]=\"{ 'text-danger': !allCasesPass(), 'text-success': allCasesPass() }\">\n                        <i *ngIf=\"allCasesPass()\" class=\"ez-icon-font\">&#xe663;</i>\n                        <i *ngIf=\"!allCasesPass()\" class=\"ez-icon-font\">&#xe662;</i>\n                        {{ casePassed }}\n                      </div>\n                      <table class=\"table-result w-100 text-center\">\n                        <thead>\n                          <tr>\n                            <td>{{ \"exam.code.caseTxt\" | translate }}</td>\n                            <td *ngFor=\"let case of commitResult; let caseIndex = index\">\n                              {{ caseIndex + 1 }}\n                            </td>\n                          </tr>\n                        </thead>\n                        <tr>\n                          <td>\n                            <span class=\"result-txt\" (clickOutside)=\"closeTool()\"\n                              >{{ \"exam.code.resultTxt\" | translate }}\n                              <i class=\"icon-tip iconfont\" (click)=\"switchTool()\">&#xe660;</i>\n                              <div class=\"intro-tip tooltip show bs-tooltip-top\" *ngIf=\"showIntroTip\">\n                                <div class=\"arrow\"></div>\n                                <div class=\"tooltip-inner\">\n                                  <small>{{ \"exam.code.introAC\" | translate }} </small>\n                                  <small>{{ \"exam.code.introWA\" | translate }} </small>\n                                  <small>{{ \"exam.code.introRE\" | translate }} </small>\n                                  <small>{{ \"exam.code.introTLE\" | translate }} </small>\n                                </div>\n                              </div>\n                            </span>\n                          </td>\n                          <td *ngFor=\"let case of commitResult\">\n                            <span\n                              [ngClass]=\"{ 'text-success': case.verdict === 'AC', 'text-danger': case.verdict !== 'AC' }\"\n                              >{{ case.verdict }}</span\n                            >\n                          </td>\n                        </tr>\n                      </table>\n                    </div>\n                  </ng-container>\n                  -->\n                </ng-template>\n              </li>\n            </ul>\n            <div *ngIf=\"showBottomCont\" [ngbNavOutlet]=\"nav\" class=\"p-2\"></div>\n          </div>\n        </div>\n      </div>\n    </as-split-area>\n  </as-split>\n</div>\n\n<!--弹出框-->\n<!--change code language-->\n<custom-confirm\n  *ngIf=\"showChangeLanTip\"\n  [bodyText]=\"translate.instant('exam.code.changeLanTip')\"\n  (onConfirm)=\"confirmChangeLan()\"\n  (onCancel)=\"cancelChangeLan()\"\n>\n</custom-confirm>\n\n<!--reset code-->\n<custom-confirm\n  *ngIf=\"showResetAnswerTip\"\n  [bodyText]=\"translate.instant('exam.code.resetTip')\"\n  (onConfirm)=\"confirmResetAnswer()\"\n  (onCancel)=\"cancelResetAnswer()\"\n>\n</custom-confirm>\n\n<!-- 编辑器设置黑白风格及字号弹框 -->\n<div class=\"joy-custom-modal\" *ngIf=\"isEditorModeModalShowed\">\n  <div class=\"wrap-joy-custom-modal-cont bg-white\">\n    <div class=\"joy-custom-modal-head\">\n      <div class=\"joy-custom-modal-title\">{{ \"exam.code.editorConfig\" | translate }}</div>\n    </div>\n    <div class=\"joy-custom-modal-cont pl-4\">\n      <div class=\"ml-5 mt-4 mb-3\">\n        <span class=\"mr-4\">{{ \"exam.code.fontSizeConfig\" | translate }}</span>\n        <select class=\"form-control form-select d-inline-block w-50\" [(ngModel)]=\"editorConfig.fontSize\">\n          <option *ngFor=\"let fontSize of fontsPresets\">{{ fontSize }}</option>\n        </select>\n      </div>\n      <div class=\"ml-5 mb-4 mode-select\">\n        <span class=\"mr-4\">{{ \"exam.code.styleConfig\" | translate }}</span>\n        <label class=\"radio-inline mr-3 mb-0\"\n          ><span class=\"input-block\"\n            ><input type=\"radio\" name=\"optradio\" value=\"black\" class=\"mr-2\" [(ngModel)]=\"editorConfig.mode\" /><span>{{\n              \"exam.code.black\" | translate\n            }}</span></span\n          ></label\n        >\n        <label class=\"radio-inline mb-0\"\n          ><span class=\"input-block\"\n            ><input type=\"radio\" name=\"optradio\" value=\"white\" class=\"mr-2\" [(ngModel)]=\"editorConfig.mode\" /><span>{{\n              \"exam.code.light\" | translate\n            }}</span></span\n          ></label\n        >\n      </div>\n      <div class=\"joy-custom-modal-foot row pt-3\">\n        <button class=\"yk-btn-primary mr-4\" (click)=\"confirmEditorMode(true)\">\n          {{ \"exam.code.confirm\" | translate }}\n        </button>\n        <button class=\"yk-btn-bd-secondary\" (click)=\"confirmEditorMode(false)\">\n          {{ \"exam.code.cancel\" | translate }}\n        </button>\n      </div>\n    </div>\n  </div>\n</div>\n"]}, "metadata": {}, "sourceType": "module"}