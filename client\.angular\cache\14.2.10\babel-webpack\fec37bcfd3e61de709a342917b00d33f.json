{"ast": null, "code": "export var smallerEqDocs = {\n  name: 'smallerEq',\n  category: 'Relational',\n  syntax: ['x <= y', 'smallerEq(x, y)'],\n  description: 'Check if value x is smaller or equal to value y. Returns true if x is smaller than y, and false if not.',\n  examples: ['2 <= 1+1', '2 < 1+1', 'a = 3.2', 'b = 6-2.8', '(a <= b)'],\n  seealso: ['equal', 'unequal', 'larger', 'smaller', 'largerEq', 'compare']\n};", "map": null, "metadata": {}, "sourceType": "module"}