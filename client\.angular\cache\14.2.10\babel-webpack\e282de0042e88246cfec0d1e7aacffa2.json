{"ast": null, "code": "import { factory } from '../../utils/factory.js';\nimport { gammaG, gammaNumber, gammaP } from '../../plain/number/index.js';\nvar name = 'gamma';\nvar dependencies = ['typed', 'config', 'multiplyScalar', 'pow', 'BigNumber', 'Complex'];\nexport var createGamma = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed,\n    config,\n    multiplyScalar,\n    pow,\n    BigNumber: _BigNumber,\n    Complex\n  } = _ref;\n  /**\n   * Compute the gamma function of a value using <PERSON>nczos approximation for\n   * small values, and an extended Stirling approximation for large values.\n   *\n   * To avoid confusion with the matrix Gamma function, this function does\n   * not apply to matrices.\n   *\n   * Syntax:\n   *\n   *    math.gamma(n)\n   *\n   * Examples:\n   *\n   *    math.gamma(5)       // returns 24\n   *    math.gamma(-0.5)    // returns -3.5449077018110335\n   *    math.gamma(math.i)  // returns -0.15494982830180973 - 0.49801566811835596i\n   *\n   * See also:\n   *\n   *    combinations, factorial, permutations\n   *\n   * @param {number | BigNumber | Complex} n   A real or complex number\n   * @return {number | BigNumber | Complex}    The gamma of `n`\n   */\n\n  function gammaComplex(n) {\n    if (n.im === 0) {\n      return gammaNumber(n.re);\n    } // Lanczos approximation doesn't work well with real part lower than 0.5\n    // So reflection formula is required\n\n\n    if (n.re < 0.5) {\n      // Euler's reflection formula\n      // gamma(1-z) * gamma(z) = PI / sin(PI * z)\n      // real part of Z should not be integer [sin(PI) == 0 -> 1/0 - undefined]\n      // thanks to imperfect sin implementation sin(PI * n) != 0\n      // we can safely use it anyway\n      var _t = new Complex(1 - n.re, -n.im);\n\n      var r = new Complex(Math.PI * n.re, Math.PI * n.im);\n      return new Complex(Math.PI).div(r.sin()).div(gammaComplex(_t));\n    } // Lanczos approximation\n    // z -= 1\n\n\n    n = new Complex(n.re - 1, n.im); // x = gammaPval[0]\n\n    var x = new Complex(gammaP[0], 0); // for (i, gammaPval) in enumerate(gammaP):\n\n    for (var i = 1; i < gammaP.length; ++i) {\n      // x += gammaPval / (z + i)\n      var gammaPval = new Complex(gammaP[i], 0);\n      x = x.add(gammaPval.div(n.add(i)));\n    } // t = z + gammaG + 0.5\n\n\n    var t = new Complex(n.re + gammaG + 0.5, n.im); // y = sqrt(2 * pi) * t ** (z + 0.5) * exp(-t) * x\n\n    var twoPiSqrt = Math.sqrt(2 * Math.PI);\n    var tpow = t.pow(n.add(0.5));\n    var expt = t.neg().exp(); // y = [x] * [sqrt(2 * pi)] * [t ** (z + 0.5)] * [exp(-t)]\n\n    return x.mul(twoPiSqrt).mul(tpow).mul(expt);\n  }\n\n  return typed(name, {\n    number: gammaNumber,\n    Complex: gammaComplex,\n    BigNumber: function BigNumber(n) {\n      if (n.isInteger()) {\n        return n.isNegative() || n.isZero() ? new _BigNumber(Infinity) : bigFactorial(n.minus(1));\n      }\n\n      if (!n.isFinite()) {\n        return new _BigNumber(n.isNegative() ? NaN : Infinity);\n      }\n\n      throw new Error('Integer BigNumber expected');\n    }\n  });\n  /**\n   * Calculate factorial for a BigNumber\n   * @param {BigNumber} n\n   * @returns {BigNumber} Returns the factorial of n\n   */\n\n  function bigFactorial(n) {\n    if (n < 8) {\n      return new _BigNumber([1, 1, 2, 6, 24, 120, 720, 5040][n]);\n    }\n\n    var precision = config.precision + (Math.log(n.toNumber()) | 0);\n\n    var Big = _BigNumber.clone({\n      precision\n    });\n\n    if (n % 2 === 1) {\n      return n.times(bigFactorial(new _BigNumber(n - 1)));\n    }\n\n    var p = n;\n    var prod = new Big(n);\n    var sum = n.toNumber();\n\n    while (p > 2) {\n      p -= 2;\n      sum += p;\n      prod = prod.times(sum);\n    }\n\n    return new _BigNumber(prod.toPrecision(_BigNumber.precision));\n  }\n});", "map": null, "metadata": {}, "sourceType": "module"}