{"ast": null, "code": "export var expmDocs = {\n  name: 'expm',\n  category: 'Arithmetic',\n  syntax: ['exp(x)'],\n  description: 'Compute the matrix exponential, expm(A) = e^A. ' + 'The matrix must be square. ' + 'Not to be confused with exp(a), which performs element-wise exponentiation.',\n  examples: ['expm([[0,2],[0,0]])'],\n  seealso: ['exp']\n};", "map": null, "metadata": {}, "sourceType": "module"}