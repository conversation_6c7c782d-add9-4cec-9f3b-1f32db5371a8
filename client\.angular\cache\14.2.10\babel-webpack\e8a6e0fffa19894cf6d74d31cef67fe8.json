{"ast": null, "code": "import { isBigNumber } from '../../utils/is.js';\nimport { resize } from '../../utils/array.js';\nimport { isInteger } from '../../utils/number.js';\nimport { factory } from '../../utils/factory.js';\nvar name = 'identity';\nvar dependencies = ['typed', 'config', 'matrix', 'BigNumber', 'DenseMatrix', 'SparseMatrix'];\nexport var createIdentity = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed,\n    config,\n    matrix,\n    BigNumber,\n    DenseMatrix,\n    SparseMatrix\n  } = _ref;\n  /**\n   * Create a 2-dimensional identity matrix with size m x n or n x n.\n   * The matrix has ones on the diagonal and zeros elsewhere.\n   *\n   * Syntax:\n   *\n   *    math.identity(n)\n   *    math.identity(n, format)\n   *    math.identity(m, n)\n   *    math.identity(m, n, format)\n   *    math.identity([m, n])\n   *    math.identity([m, n], format)\n   *\n   * Examples:\n   *\n   *    math.identity(3)                    // returns [[1, 0, 0], [0, 1, 0], [0, 0, 1]]\n   *    math.identity(3, 2)                 // returns [[1, 0], [0, 1], [0, 0]]\n   *\n   *    const A = [[1, 2, 3], [4, 5, 6]]\n   *    math.identity(math.size(A))         // returns [[1, 0, 0], [0, 1, 0]]\n   *\n   * See also:\n   *\n   *    diag, ones, zeros, size, range\n   *\n   * @param {...number | Matrix | Array} size   The size for the matrix\n   * @param {string} [format]                   The Matrix storage format\n   *\n   * @return {Matrix | Array | number} A matrix with ones on the diagonal.\n   */\n\n  return typed(name, {\n    '': function _() {\n      return config.matrix === 'Matrix' ? matrix([]) : [];\n    },\n    string: function string(format) {\n      return matrix(format);\n    },\n    'number | BigNumber': function number__BigNumber(rows) {\n      return _identity(rows, rows, config.matrix === 'Matrix' ? 'dense' : undefined);\n    },\n    'number | BigNumber, string': function number__BigNumber_string(rows, format) {\n      return _identity(rows, rows, format);\n    },\n    'number | BigNumber, number | BigNumber': function number__BigNumber_number__BigNumber(rows, cols) {\n      return _identity(rows, cols, config.matrix === 'Matrix' ? 'dense' : undefined);\n    },\n    'number | BigNumber, number | BigNumber, string': function number__BigNumber_number__BigNumber_string(rows, cols, format) {\n      return _identity(rows, cols, format);\n    },\n    Array: function Array(size) {\n      return _identityVector(size);\n    },\n    'Array, string': function Array_string(size, format) {\n      return _identityVector(size, format);\n    },\n    Matrix: function Matrix(size) {\n      return _identityVector(size.valueOf(), size.storage());\n    },\n    'Matrix, string': function Matrix_string(size, format) {\n      return _identityVector(size.valueOf(), format);\n    }\n  });\n\n  function _identityVector(size, format) {\n    switch (size.length) {\n      case 0:\n        return format ? matrix(format) : [];\n\n      case 1:\n        return _identity(size[0], size[0], format);\n\n      case 2:\n        return _identity(size[0], size[1], format);\n\n      default:\n        throw new Error('Vector containing two values expected');\n    }\n  }\n  /**\n   * Create an identity matrix\n   * @param {number | BigNumber} rows\n   * @param {number | BigNumber} cols\n   * @param {string} [format]\n   * @returns {Matrix}\n   * @private\n   */\n\n\n  function _identity(rows, cols, format) {\n    // BigNumber constructor with the right precision\n    var Big = isBigNumber(rows) || isBigNumber(cols) ? BigNumber : null;\n    if (isBigNumber(rows)) rows = rows.toNumber();\n    if (isBigNumber(cols)) cols = cols.toNumber();\n\n    if (!isInteger(rows) || rows < 1) {\n      throw new Error('Parameters in function identity must be positive integers');\n    }\n\n    if (!isInteger(cols) || cols < 1) {\n      throw new Error('Parameters in function identity must be positive integers');\n    }\n\n    var one = Big ? new BigNumber(1) : 1;\n    var defaultValue = Big ? new Big(0) : 0;\n    var size = [rows, cols]; // check we need to return a matrix\n\n    if (format) {\n      // create diagonal matrix (use optimized implementation for storage format)\n      if (format === 'sparse') {\n        return SparseMatrix.diagonal(size, one, 0, defaultValue);\n      }\n\n      if (format === 'dense') {\n        return DenseMatrix.diagonal(size, one, 0, defaultValue);\n      }\n\n      throw new TypeError(\"Unknown matrix type \\\"\".concat(format, \"\\\"\"));\n    } // create and resize array\n\n\n    var res = resize([], size, defaultValue); // fill in ones on the diagonal\n\n    var minimum = rows < cols ? rows : cols; // fill diagonal\n\n    for (var d = 0; d < minimum; d++) {\n      res[d][d] = one;\n    }\n\n    return res;\n  }\n});", "map": null, "metadata": {}, "sourceType": "module"}