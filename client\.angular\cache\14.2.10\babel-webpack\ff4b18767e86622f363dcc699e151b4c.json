{"ast": null, "code": "import _asyncToGenerator from \"D:/work/joyserver/client/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\n\nvar __decorate = this && this.__decorate || function (decorators, target, key, desc) {\n  var c = arguments.length,\n      r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc,\n      d;\n  if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n  return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\n\nimport { NgZone } from \"@angular/core\";\nimport { BehaviorSubject, Observable } from \"rxjs\";\nimport * as M from \"@core-io/io.message.consts\";\nimport { IOClient } from \"@core-io/io.client\";\nimport { distinctUntilChanged } from \"rxjs/operators\";\nimport serverTime from \"../utils/time.service\";\nimport { OnlineExamWSLogin } from \"../online/decorator/wsLogin.decorator\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../utils/util.service\";\nimport * as i3 from \"../localStorage.service\";\nimport * as i4 from \"../url.service\";\n\nclass PromiseHelper {}\n\nexport var ConnectState;\n\n(function (ConnectState) {\n  ConnectState[ConnectState[\"notConnected\"] = 0] = \"notConnected\";\n  ConnectState[ConnectState[\"connecting\"] = 1] = \"connecting\";\n  ConnectState[ConnectState[\"connected\"] = 2] = \"connected\";\n  ConnectState[ConnectState[\"logining\"] = 3] = \"logining\";\n  ConnectState[ConnectState[\"logined\"] = 4] = \"logined\";\n})(ConnectState || (ConnectState = {}));\n\nexport class SocketService {\n  constructor(router, zone, utilSer, localStorageService, urlService) {\n    this.router = router;\n    this.zone = zone;\n    this.utilSer = utilSer;\n    this.localStorageService = localStorageService;\n    this.urlService = urlService;\n    this.clientIO = new IOClient();\n    this.entry = null;\n    this.loginReqs = [];\n    this.isLogined = new BehaviorSubject(false);\n    this.enable_register = new BehaviorSubject(false);\n    this.connectSubject = new BehaviorSubject(ConnectState.notConnected);\n    this.autoLogin = true;\n    this.disableWs = window.is_demo; // 浏览器版demo不使用websocket\n\n    this.WS_TOKEN$ = new BehaviorSubject(\"\"); // 在线考试ws token\n\n    if (this.localStorageService.isDebugEnabled) {\n      this.clientIO.verbose = true;\n    }\n\n    if (this.disableWs) {\n      this.isLogined.next(true);\n      return;\n    }\n\n    this.setupSocket();\n    this.localStorageService.changed.subscribe(() => {\n      this.connect();\n    });\n    this.WS_TOKEN$.subscribe(token => {\n      if (token) {\n        console.log(\"WS_TOKEN$\", token);\n        this.connect();\n      }\n    });\n  }\n\n  get connectState() {\n    return this.connectSubject.pipe(distinctUntilChanged());\n  }\n\n  loginObs() {\n    return new Observable(observer => {\n      this.connect().then(res => {\n        observer.next(res);\n        observer.complete();\n      }).catch(e => {\n        observer.error(e);\n      });\n    });\n  } // 连接服务器\n\n\n  connect() {\n    var _this = this;\n\n    return _asyncToGenerator(function* () {\n      if (_this.disableWs) {\n        return Promise.resolve({\n          errcode: M.ERR_OK\n        });\n      }\n\n      const url = _this.urlService.getWsUrl();\n\n      const seat = _this.localStorageService.getRegInfo().seat;\n\n      if (url !== _this.url || seat !== _this.seatNumber) {\n        _this.clientIO.terminate();\n\n        _this.setAsLogout();\n\n        _this.connectSubject.next(ConnectState.notConnected);\n      }\n\n      _this.url = url;\n      _this.seatNumber = seat;\n      _this.autoLogin = true;\n\n      if (_this.isLogined.getValue()) {\n        return Promise.resolve({\n          errcode: M.ERR_OK\n        });\n      }\n\n      return new Promise((resolve, reject) => {\n        _this.loginReqs.push({\n          resolve,\n          reject\n        });\n\n        if (_this.connectSubject.value === ConnectState.connecting || _this.connectSubject.value === ConnectState.logining) {\n          return;\n        }\n\n        if (_this.connectSubject.value === ConnectState.notConnected) {\n          _this.connectSubject.next(ConnectState.connecting);\n\n          _this.zone.run(() => {\n            _this.clientIO.autoConnect = true;\n\n            _this.clientIO.connect(_this.url);\n          });\n\n          return;\n        }\n\n        if (!_this.isLogined.value) {\n          _this.relogin();\n        }\n      });\n    })();\n  } // 断开服务器\n\n\n  disconnect() {\n    if (this.disableWs) {\n      return;\n    }\n\n    this.autoLogin = false;\n    this.clientIO.disconnect();\n  } // 登出考试机\n\n\n  logout() {\n    if (this.disableWs) {\n      return;\n    }\n\n    this.clientIO.sendMsg(M.MSG_CLIENT_LOGOUT, {}, res => {\n      this.autoLogin = false;\n      console.log(\"- client logout: \" + JSON.stringify(res));\n    });\n  } // 修改状态为未登录\n\n\n  setAsLogout() {\n    if (this.disableWs) {\n      return;\n    }\n\n    if (this.isLogined.getValue()) {\n      this.isLogined.next(false);\n      this.connectSubject.next(ConnectState.connected);\n      console.log(\"* Logout\");\n    }\n  } // 登录\n\n\n  doLogin(mac_addr, seat, code, permit) {\n    var _this2 = this;\n\n    return _asyncToGenerator(function* () {\n      const version = _this2.localStorageService.appVersion();\n\n      _this2.connectSubject.next(ConnectState.logining);\n\n      return new Promise((resolve, reject) => {\n        _this2.clientIO.sendMsg(M.MSG_CLIENT_LOGIN, {\n          mac_addr,\n          seat,\n          code,\n          version,\n          client: true,\n          permit,\n          timeout: 5000\n        }, res => {\n          if (!res) {\n            // disconnected\n            res = {\n              errcode: M.ERR_NET_ERR,\n              errmsg: \"network error\"\n            };\n            reject(res);\n\n            _this2.clientIO.emit(\"login_error\", res);\n\n            _this2.loginReqs.forEach(rq => {\n              rq.reject(res);\n            });\n\n            _this2.loginReqs = [];\n            return;\n          }\n\n          if (res.errcode === 0) {\n            // 登录成功\n            console.log(\"- Client logined，Seat number：\", _this2.seatNumber); // update room info\n\n            if (res.room) {\n              // room: {room, room_code, center_name, center_address}\n              _this2.localStorageService.updateRegInfo(res.room);\n            }\n\n            _this2.isLogined.next(true);\n\n            _this2.clientIO.emit(\"logined\", _this2.seatNumber);\n\n            _this2.loginReqs.forEach(rq => {\n              rq.resolve({\n                errcode: M.ERR_OK,\n                errmsg: \"OK\"\n              });\n            });\n\n            _this2.loginReqs = [];\n            resolve(res);\n\n            _this2.connectSubject.next(ConnectState.logined);\n          } else {\n            _this2.clientIO.emit(\"login_error\", res);\n\n            _this2.loginReqs.forEach(rq => {\n              rq.reject(res);\n            });\n\n            _this2.loginReqs = [];\n            reject(res);\n          }\n        });\n      });\n    })();\n  }\n\n  relogin() {\n    var _this3 = this;\n\n    return _asyncToGenerator(function* () {\n      const info = _this3.localStorageService.getRegInfo();\n\n      _this3.clientIO.emit(\"logining\", _this3.seatNumber);\n\n      const mac_addr = yield _this3.utilSer.getPhysAddr();\n\n      _this3.doLogin(mac_addr, _this3.seatNumber, info.code, _this3.entry?.permit).catch(res => {\n        if (res && res.errcode !== 0) {\n          setTimeout(() => {\n            if (_this3.autoLogin) {\n              _this3.relogin();\n            }\n          }, 5000);\n        }\n      });\n    })();\n  }\n\n  setupSocket() {\n    this.clientIO.on(\"connect\", () => {\n      this.connectSubject.next(ConnectState.connected);\n\n      if (this.autoLogin) {\n        this.relogin();\n      }\n    });\n    this.clientIO.on(\"connect_error\", () => {\n      this.loginReqs.forEach(rq => {\n        rq.reject({\n          errcode: M.ERR_NET_ERR,\n          errmsg: \"connect error\"\n        });\n      });\n      this.loginReqs = [];\n    });\n    this.clientIO.on(\"connect_timeout\", () => {\n      this.loginReqs.forEach(rq => {\n        rq.reject({\n          errcode: M.ERR_NET_ERR,\n          errmsg: \"connect timeout\"\n        });\n      });\n      this.loginReqs = [];\n    });\n    this.clientIO.on(\"disconnect\", () => {\n      this.connectSubject.next(ConnectState.notConnected);\n      this.isLogined.next(false);\n      this.loginReqs.forEach(rq => {\n        rq.reject({\n          errcode: M.ERR_NET_ERR,\n          errmsg: \"disconnected\"\n        });\n      });\n      this.loginReqs = [];\n    });\n    this.clientIO.onMsg(M.MSG_RELOGIN, () => {\n      this.setAsLogout();\n      this.relogin();\n      return true;\n    });\n    this.clientIO.onMsg(M.MSG_ENABLE_REGISTER, req => {\n      this.enable_register.next(req.params.enabled);\n      return true;\n    });\n    this.clientIO.onMsg(M.MSG_UNREGISTER, () => {\n      this.localStorageService.clearRegInfo();\n      this.setAsLogout();\n      console.log(\"---- Enter page: register\");\n      this.router.navigate([\"/register\", {\n        by_manager: true\n      }]);\n      return true;\n    });\n    this.clientIO.onMsg(M.MSG_CHANGE_SEAT_NUMBER, req => {\n      const reg_info = this.localStorageService.getRegInfo();\n\n      if (reg_info) {\n        reg_info.seat = req.params.new_seat_number;\n        reg_info.code = req.params.code;\n        this.localStorageService.saveRegInfo(reg_info);\n      }\n\n      return true;\n    });\n    this.clientIO.onMsg(M.MSG_GET_STATUS, () => {\n      return true;\n    });\n    this.clientIO.onMsg(M.MSG_TIME, req => {\n      serverTime.sync(req.params.time);\n      return true;\n    });\n    this.clientIO.onMsg(M.MSG_SETTING_UPDATE, req => {\n      if (joyshell) {\n        joyshell.UpdateSettings(req.params);\n\n        if (typeof req.params.CONTENT_PROTECTION !== \"undefined\") {\n          joyshell.ToggleProtection(req.params.CONTENT_PROTECTION);\n        }\n\n        if (typeof req.params.REJECT_APPS_EXT !== \"undefined\") {\n          joyshell.StartRuntimeCheck(\"\");\n        }\n      }\n\n      return true;\n    });\n    this.clientIO.onMsg(M.MSG_GET_PROCESSES, /*#__PURE__*/function () {\n      var _ref = _asyncToGenerator(function* (req, res) {\n        const processes = yield joyshell.GetProcessList();\n        res[\"platform\"] = joyshell.Platform;\n        res[\"processes\"] = typeof processes === \"string\" ? JSON.parse(processes) : processes;\n        return true;\n      });\n\n      return function (_x, _x2) {\n        return _ref.apply(this, arguments);\n      };\n    }());\n    this.clientIO.onMsg(M.MSG_KILL_PROCESS, /*#__PURE__*/function () {\n      var _ref2 = _asyncToGenerator(function* (req, res) {\n        const result = yield joyshell.KillProcess(req.params.name);\n        res[\"result\"] = result;\n        return true;\n      });\n\n      return function (_x3, _x4) {\n        return _ref2.apply(this, arguments);\n      };\n    }());\n    this.clientIO.onMsg(M.MSG_EVAL_JS, /*#__PURE__*/function () {\n      var _ref3 = _asyncToGenerator(function* (req, res) {\n        res[\"result\"] = yield joyshell.EvalScript(req.params.js, req.params.type);\n        return true;\n      });\n\n      return function (_x5, _x6) {\n        return _ref3.apply(this, arguments);\n      };\n    }());\n  }\n\n}\n\nSocketService.ɵfac = function SocketService_Factory(t) {\n  return new (t || SocketService)(i0.ɵɵinject(i1.Router), i0.ɵɵinject(i0.NgZone), i0.ɵɵinject(i2.UtilService), i0.ɵɵinject(i3.LocalStorageService), i0.ɵɵinject(i4.URLService));\n};\n\nSocketService.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n  token: SocketService,\n  factory: SocketService.ɵfac,\n  providedIn: \"root\"\n});\n\n__decorate([OnlineExamWSLogin()], SocketService.prototype, \"doLogin\", null);", "map": {"version": 3, "mappings": ";;;;;;;;;;AAAA,SAAqBA,MAArB,QAAmC,eAAnC;AAEA,SAASC,eAAT,EAA0BC,UAA1B,QAA4C,MAA5C;AAGA,OAAO,KAAKC,CAAZ,MAAmB,4BAAnB;AACA,SAASC,QAAT,QAAyB,oBAAzB;AACA,SAASC,oBAAT,QAAqC,gBAArC;AACA,OAAOC,UAAP,MAAuB,uBAAvB;AAEA,SAASC,iBAAT,QAAkC,uCAAlC;;;;;;;AAEA,MAAMC,aAAN,CAAmB;;AAKnB,WAAYC,YAAZ;;AAAA,WAAYA,YAAZ,EAAwB;EACtBA;EACAA;EACAA;EACAA;EACAA;AACD,CAND,EAAYA,YAAY,KAAZA,YAAY,MAAxB;;AASA,OAAM,MAAOC,aAAP,CAAoB;EAcxBC,YACSC,MADT,EAEUC,IAFV,EAGUC,OAHV,EAISC,mBAJT,EAKUC,UALV,EAKgC;IAJvB;IACC;IACA;IACD;IACC;IAjBV,gBAAW,IAAIZ,QAAJ,EAAX;IAEA,aAAqD,IAArD;IACQ,iBAA6B,EAA7B;IACR,iBAAY,IAAIH,eAAJ,CAA6B,KAA7B,CAAZ;IACA,uBAAkB,IAAIA,eAAJ,CAA6B,KAA7B,CAAlB;IACQ,sBAAiB,IAAIA,eAAJ,CAAkCQ,YAAY,CAACQ,YAA/C,CAAjB;IACA,iBAAY,IAAZ;IACA,iBAAYC,MAAM,CAACC,OAAnB,CASwB,CATI;;IAEpC,iBAAY,IAAIlB,eAAJ,CAA4B,EAA5B,CAAZ,CAOgC,CAPa;;IAS3C,IAAI,KAAKc,mBAAL,CAAyBK,cAA7B,EAA6C;MAC3C,KAAKC,QAAL,CAAcC,OAAd,GAAwB,IAAxB;IACD;;IAED,IAAI,KAAKC,SAAT,EAAoB;MAClB,KAAKC,SAAL,CAAeC,IAAf,CAAoB,IAApB;MACA;IACD;;IAED,KAAKC,WAAL;IAEA,KAAKX,mBAAL,CAAyBY,OAAzB,CAAiCC,SAAjC,CAA2C,MAAK;MAC9C,KAAKC,OAAL;IACD,CAFD;IAIA,KAAKC,SAAL,CAAeF,SAAf,CAA0BG,KAAD,IAAU;MACjC,IAAIA,KAAJ,EAAW;QACTC,OAAO,CAACC,GAAR,CAAY,WAAZ,EAAyBF,KAAzB;QACA,KAAKF,OAAL;MACD;IACF,CALD;EAMD;;EAEe,IAAZK,YAAY;IACd,OAAO,KAAKC,cAAL,CAAoBC,IAApB,CAAyB/B,oBAAoB,EAA7C,CAAP;EACD;;EAEDgC,QAAQ;IACN,OAAO,IAAInC,UAAJ,CAAgBoC,QAAD,IAAa;MACjC,KAAKT,OAAL,GACGU,IADH,CACSC,GAAD,IAAQ;QACZF,QAAQ,CAACb,IAAT,CAAce,GAAd;QACAF,QAAQ,CAACG,QAAT;MACD,CAJH,EAKGC,KALH,CAKUC,CAAD,IAAM;QACXL,QAAQ,CAACM,KAAT,CAAeD,CAAf;MACD,CAPH;IAQD,CATM,CAAP;EAUD,CA3DuB,CA6DxB;;;EACMd,OAAO;IAAA;;IAAA;MACX,IAAI,KAAI,CAACN,SAAT,EAAoB;QAClB,OAAOsB,OAAO,CAACC,OAAR,CAAgB;UAAEC,OAAO,EAAE5C,CAAC,CAAC6C;QAAb,CAAhB,CAAP;MACD;;MAED,MAAMC,GAAG,GAAG,KAAI,CAACjC,UAAL,CAAgBkC,QAAhB,EAAZ;;MACA,MAAMC,IAAI,GAAG,KAAI,CAACpC,mBAAL,CAAyBqC,UAAzB,GAAsCD,IAAnD;;MACA,IAAIF,GAAG,KAAK,KAAI,CAACA,GAAb,IAAoBE,IAAI,KAAK,KAAI,CAACE,UAAtC,EAAkD;QAChD,KAAI,CAAChC,QAAL,CAAciC,SAAd;;QACA,KAAI,CAACC,WAAL;;QACA,KAAI,CAACpB,cAAL,CAAoBV,IAApB,CAAyBhB,YAAY,CAACQ,YAAtC;MACD;;MAED,KAAI,CAACgC,GAAL,GAAWA,GAAX;MACA,KAAI,CAACI,UAAL,GAAkBF,IAAlB;MACA,KAAI,CAACK,SAAL,GAAiB,IAAjB;;MAEA,IAAI,KAAI,CAAChC,SAAL,CAAeiC,QAAf,EAAJ,EAA+B;QAC7B,OAAOZ,OAAO,CAACC,OAAR,CAAgB;UAAEC,OAAO,EAAE5C,CAAC,CAAC6C;QAAb,CAAhB,CAAP;MACD;;MAED,OAAO,IAAIH,OAAJ,CAAY,CAACC,OAAD,EAAUY,MAAV,KAAoB;QACrC,KAAI,CAACC,SAAL,CAAeC,IAAf,CAAoB;UAAEd,OAAF;UAAWY;QAAX,CAApB;;QAEA,IACE,KAAI,CAACvB,cAAL,CAAoB0B,KAApB,KAA8BpD,YAAY,CAACqD,UAA3C,IACA,KAAI,CAAC3B,cAAL,CAAoB0B,KAApB,KAA8BpD,YAAY,CAACsD,QAF7C,EAGE;UACA;QACD;;QAED,IAAI,KAAI,CAAC5B,cAAL,CAAoB0B,KAApB,KAA8BpD,YAAY,CAACQ,YAA/C,EAA6D;UAC3D,KAAI,CAACkB,cAAL,CAAoBV,IAApB,CAAyBhB,YAAY,CAACqD,UAAtC;;UACA,KAAI,CAACjD,IAAL,CAAUmD,GAAV,CAAc,MAAK;YACjB,KAAI,CAAC3C,QAAL,CAAc4C,WAAd,GAA4B,IAA5B;;YACA,KAAI,CAAC5C,QAAL,CAAcQ,OAAd,CAAsB,KAAI,CAACoB,GAA3B;UACD,CAHD;;UAIA;QACD;;QAED,IAAI,CAAC,KAAI,CAACzB,SAAL,CAAeqC,KAApB,EAA2B;UACzB,KAAI,CAACK,OAAL;QACD;MACF,CAtBM,CAAP;IArBW;EA4CZ,CA1GuB,CA4GxB;;;EACAC,UAAU;IACR,IAAI,KAAK5C,SAAT,EAAoB;MAClB;IACD;;IAED,KAAKiC,SAAL,GAAiB,KAAjB;IACA,KAAKnC,QAAL,CAAc8C,UAAd;EACD,CApHuB,CAsHxB;;;EACAC,MAAM;IACJ,IAAI,KAAK7C,SAAT,EAAoB;MAClB;IACD;;IAED,KAAKF,QAAL,CAAcgD,OAAd,CAAsBlE,CAAC,CAACmE,iBAAxB,EAA2C,EAA3C,EAAgD9B,GAAD,IAAQ;MACrD,KAAKgB,SAAL,GAAiB,KAAjB;MACAxB,OAAO,CAACC,GAAR,CAAY,sBAAsBsC,IAAI,CAACC,SAAL,CAAehC,GAAf,CAAlC;IACD,CAHD;EAID,CAhIuB,CAkIxB;;;EACAe,WAAW;IACT,IAAI,KAAKhC,SAAT,EAAoB;MAClB;IACD;;IAED,IAAI,KAAKC,SAAL,CAAeiC,QAAf,EAAJ,EAA+B;MAC7B,KAAKjC,SAAL,CAAeC,IAAf,CAAoB,KAApB;MACA,KAAKU,cAAL,CAAoBV,IAApB,CAAyBhB,YAAY,CAACgE,SAAtC;MACAzC,OAAO,CAACC,GAAR,CAAY,UAAZ;IACD;EACF,CA7IuB,CA+IxB;;;EAEcyC,OAAO,CAACC,QAAD,EAAmBxB,IAAnB,EAAiCyB,IAAjC,EAA+CC,MAA/C,EAA6D;IAAA;;IAAA;MAChF,MAAMC,OAAO,GAAG,MAAI,CAAC/D,mBAAL,CAAyBgE,UAAzB,EAAhB;;MAEA,MAAI,CAAC5C,cAAL,CAAoBV,IAApB,CAAyBhB,YAAY,CAACsD,QAAtC;;MACA,OAAO,IAAIlB,OAAJ,CAAY,CAACC,OAAD,EAAUY,MAAV,KAAoB;QACrC,MAAI,CAACrC,QAAL,CAAcgD,OAAd,CACElE,CAAC,CAAC6E,gBADJ,EAEE;UAAEL,QAAF;UAAYxB,IAAZ;UAAkByB,IAAlB;UAAwBE,OAAxB;UAAiCG,MAAM,EAAE,IAAzC;UAA+CJ,MAA/C;UAAuDK,OAAO,EAAE;QAAhE,CAFF,EAGG1C,GAAD,IAAQ;UACN,IAAI,CAACA,GAAL,EAAU;YACR;YACAA,GAAG,GAAG;cAAEO,OAAO,EAAE5C,CAAC,CAACgF,WAAb;cAA0BC,MAAM,EAAE;YAAlC,CAAN;YACA1B,MAAM,CAAClB,GAAD,CAAN;;YACA,MAAI,CAACnB,QAAL,CAAcgE,IAAd,CAAmB,aAAnB,EAAkC7C,GAAlC;;YACA,MAAI,CAACmB,SAAL,CAAe2B,OAAf,CAAwBC,EAAD,IAAO;cAC5BA,EAAE,CAAC7B,MAAH,CAAUlB,GAAV;YACD,CAFD;;YAGA,MAAI,CAACmB,SAAL,GAAiB,EAAjB;YACA;UACD;;UACD,IAAInB,GAAG,CAACO,OAAJ,KAAgB,CAApB,EAAuB;YACrB;YACAf,OAAO,CAACC,GAAR,CAAY,+BAAZ,EAA6C,MAAI,CAACoB,UAAlD,EAFqB,CAIrB;;YACA,IAAIb,GAAG,CAACgD,IAAR,EAAc;cACZ;cACA,MAAI,CAACzE,mBAAL,CAAyB0E,aAAzB,CAAuCjD,GAAG,CAACgD,IAA3C;YACD;;YAED,MAAI,CAAChE,SAAL,CAAeC,IAAf,CAAoB,IAApB;;YAEA,MAAI,CAACJ,QAAL,CAAcgE,IAAd,CAAmB,SAAnB,EAA8B,MAAI,CAAChC,UAAnC;;YACA,MAAI,CAACM,SAAL,CAAe2B,OAAf,CAAwBC,EAAD,IAAO;cAC5BA,EAAE,CAACzC,OAAH,CAAW;gBAAEC,OAAO,EAAE5C,CAAC,CAAC6C,MAAb;gBAAqBoC,MAAM,EAAE;cAA7B,CAAX;YACD,CAFD;;YAGA,MAAI,CAACzB,SAAL,GAAiB,EAAjB;YACAb,OAAO,CAACN,GAAD,CAAP;;YAEA,MAAI,CAACL,cAAL,CAAoBV,IAApB,CAAyBhB,YAAY,CAACiF,OAAtC;UACD,CApBD,MAoBO;YACL,MAAI,CAACrE,QAAL,CAAcgE,IAAd,CAAmB,aAAnB,EAAkC7C,GAAlC;;YACA,MAAI,CAACmB,SAAL,CAAe2B,OAAf,CAAwBC,EAAD,IAAO;cAC5BA,EAAE,CAAC7B,MAAH,CAAUlB,GAAV;YACD,CAFD;;YAGA,MAAI,CAACmB,SAAL,GAAiB,EAAjB;YACAD,MAAM,CAAClB,GAAD,CAAN;UACD;QACF,CA3CH;MA6CD,CA9CM,CAAP;IAJgF;EAmDjF;;EAEa0B,OAAO;IAAA;;IAAA;MACnB,MAAMyB,IAAI,GAAG,MAAI,CAAC5E,mBAAL,CAAyBqC,UAAzB,EAAb;;MACA,MAAI,CAAC/B,QAAL,CAAcgE,IAAd,CAAmB,UAAnB,EAA+B,MAAI,CAAChC,UAApC;;MACA,MAAMsB,QAAQ,SAAS,MAAI,CAAC7D,OAAL,CAAa8E,WAAb,EAAvB;;MACA,MAAI,CAAClB,OAAL,CAAaC,QAAb,EAAuB,MAAI,CAACtB,UAA5B,EAAwCsC,IAAI,CAACf,IAA7C,EAAmD,MAAI,CAACiB,KAAL,EAAYhB,MAA/D,EAAuEnC,KAAvE,CAA8EF,GAAD,IAAQ;QACnF,IAAIA,GAAG,IAAIA,GAAG,CAACO,OAAJ,KAAgB,CAA3B,EAA8B;UAC5B+C,UAAU,CAAC,MAAK;YACd,IAAI,MAAI,CAACtC,SAAT,EAAoB;cAClB,MAAI,CAACU,OAAL;YACD;UACF,CAJS,EAIP,IAJO,CAAV;QAKD;MACF,CARD;IAJmB;EAapB;;EAEOxC,WAAW;IACjB,KAAKL,QAAL,CAAc0E,EAAd,CAAiB,SAAjB,EAA4B,MAAK;MAC/B,KAAK5D,cAAL,CAAoBV,IAApB,CAAyBhB,YAAY,CAACgE,SAAtC;;MACA,IAAI,KAAKjB,SAAT,EAAoB;QAClB,KAAKU,OAAL;MACD;IACF,CALD;IAOA,KAAK7C,QAAL,CAAc0E,EAAd,CAAiB,eAAjB,EAAkC,MAAK;MACrC,KAAKpC,SAAL,CAAe2B,OAAf,CAAwBC,EAAD,IAAO;QAC5BA,EAAE,CAAC7B,MAAH,CAAU;UAAEX,OAAO,EAAE5C,CAAC,CAACgF,WAAb;UAA0BC,MAAM,EAAE;QAAlC,CAAV;MACD,CAFD;MAGA,KAAKzB,SAAL,GAAiB,EAAjB;IACD,CALD;IAOA,KAAKtC,QAAL,CAAc0E,EAAd,CAAiB,iBAAjB,EAAoC,MAAK;MACvC,KAAKpC,SAAL,CAAe2B,OAAf,CAAwBC,EAAD,IAAO;QAC5BA,EAAE,CAAC7B,MAAH,CAAU;UAAEX,OAAO,EAAE5C,CAAC,CAACgF,WAAb;UAA0BC,MAAM,EAAE;QAAlC,CAAV;MACD,CAFD;MAGA,KAAKzB,SAAL,GAAiB,EAAjB;IACD,CALD;IAOA,KAAKtC,QAAL,CAAc0E,EAAd,CAAiB,YAAjB,EAA+B,MAAK;MAClC,KAAK5D,cAAL,CAAoBV,IAApB,CAAyBhB,YAAY,CAACQ,YAAtC;MACA,KAAKO,SAAL,CAAeC,IAAf,CAAoB,KAApB;MACA,KAAKkC,SAAL,CAAe2B,OAAf,CAAwBC,EAAD,IAAO;QAC5BA,EAAE,CAAC7B,MAAH,CAAU;UAAEX,OAAO,EAAE5C,CAAC,CAACgF,WAAb;UAA0BC,MAAM,EAAE;QAAlC,CAAV;MACD,CAFD;MAGA,KAAKzB,SAAL,GAAiB,EAAjB;IACD,CAPD;IASA,KAAKtC,QAAL,CAAc2E,KAAd,CAAoB7F,CAAC,CAAC8F,WAAtB,EAAmC,MAAK;MACtC,KAAK1C,WAAL;MACA,KAAKW,OAAL;MACA,OAAO,IAAP;IACD,CAJD;IAMA,KAAK7C,QAAL,CAAc2E,KAAd,CAAoB7F,CAAC,CAAC+F,mBAAtB,EAA4CC,GAAD,IAAQ;MACjD,KAAKC,eAAL,CAAqB3E,IAArB,CAA0B0E,GAAG,CAACE,MAAJ,CAAWC,OAArC;MACA,OAAO,IAAP;IACD,CAHD;IAKA,KAAKjF,QAAL,CAAc2E,KAAd,CAAoB7F,CAAC,CAACoG,cAAtB,EAAsC,MAAK;MACzC,KAAKxF,mBAAL,CAAyByF,YAAzB;MACA,KAAKjD,WAAL;MACAvB,OAAO,CAACC,GAAR,CAAY,2BAAZ;MACA,KAAKrB,MAAL,CAAY6F,QAAZ,CAAqB,CAAC,WAAD,EAAc;QAAEC,UAAU,EAAE;MAAd,CAAd,CAArB;MACA,OAAO,IAAP;IACD,CAND;IAQA,KAAKrF,QAAL,CAAc2E,KAAd,CAAoB7F,CAAC,CAACwG,sBAAtB,EAA+CR,GAAD,IAAQ;MACpD,MAAMS,QAAQ,GAAG,KAAK7F,mBAAL,CAAyBqC,UAAzB,EAAjB;;MACA,IAAIwD,QAAJ,EAAc;QACZA,QAAQ,CAACzD,IAAT,GAAgBgD,GAAG,CAACE,MAAJ,CAAWQ,eAA3B;QACAD,QAAQ,CAAChC,IAAT,GAAgBuB,GAAG,CAACE,MAAJ,CAAWzB,IAA3B;QACA,KAAK7D,mBAAL,CAAyB+F,WAAzB,CAAqCF,QAArC;MACD;;MACD,OAAO,IAAP;IACD,CARD;IAUA,KAAKvF,QAAL,CAAc2E,KAAd,CAAoB7F,CAAC,CAAC4G,cAAtB,EAAsC,MAAK;MACzC,OAAO,IAAP;IACD,CAFD;IAIA,KAAK1F,QAAL,CAAc2E,KAAd,CAAoB7F,CAAC,CAAC6G,QAAtB,EAAiCb,GAAD,IAAQ;MACtC7F,UAAU,CAAC2G,IAAX,CAAgBd,GAAG,CAACE,MAAJ,CAAWa,IAA3B;MACA,OAAO,IAAP;IACD,CAHD;IAKA,KAAK7F,QAAL,CAAc2E,KAAd,CAAoB7F,CAAC,CAACgH,kBAAtB,EAA2ChB,GAAD,IAAQ;MAChD,IAAIiB,QAAJ,EAAc;QACZA,QAAQ,CAACC,cAAT,CAAwBlB,GAAG,CAACE,MAA5B;;QACA,IAAI,OAAOF,GAAG,CAACE,MAAJ,CAAWiB,kBAAlB,KAAyC,WAA7C,EAA0D;UACxDF,QAAQ,CAACG,gBAAT,CAA0BpB,GAAG,CAACE,MAAJ,CAAWiB,kBAArC;QACD;;QACD,IAAI,OAAOnB,GAAG,CAACE,MAAJ,CAAWmB,eAAlB,KAAsC,WAA1C,EAAuD;UACrDJ,QAAQ,CAACK,iBAAT,CAA2B,EAA3B;QACD;MACF;;MAED,OAAO,IAAP;IACD,CAZD;IAcA,KAAKpG,QAAL,CAAc2E,KAAd,CAAoB7F,CAAC,CAACuH,iBAAtB;MAAA,6BAAyC,WAAOvB,GAAP,EAAY3D,GAAZ,EAAmB;QAC1D,MAAMmF,SAAS,SAASP,QAAQ,CAACQ,cAAT,EAAxB;QACApF,GAAG,CAAC,UAAD,CAAH,GAAkB4E,QAAQ,CAACS,QAA3B;QACArF,GAAG,CAAC,WAAD,CAAH,GAAmB,OAAOmF,SAAP,KAAqB,QAArB,GAAgCpD,IAAI,CAACuD,KAAL,CAAWH,SAAX,CAAhC,GAAwDA,SAA3E;QACA,OAAO,IAAP;MACD,CALD;;MAAA;QAAA;MAAA;IAAA;IAOA,KAAKtG,QAAL,CAAc2E,KAAd,CAAoB7F,CAAC,CAAC4H,gBAAtB;MAAA,8BAAwC,WAAO5B,GAAP,EAAY3D,GAAZ,EAAmB;QACzD,MAAMwF,MAAM,SAASZ,QAAQ,CAACa,WAAT,CAAqB9B,GAAG,CAACE,MAAJ,CAAW6B,IAAhC,CAArB;QACA1F,GAAG,CAAC,QAAD,CAAH,GAAgBwF,MAAhB;QACA,OAAO,IAAP;MACD,CAJD;;MAAA;QAAA;MAAA;IAAA;IAMA,KAAK3G,QAAL,CAAc2E,KAAd,CAAoB7F,CAAC,CAACgI,WAAtB;MAAA,8BAAmC,WAAOhC,GAAP,EAAY3D,GAAZ,EAAmB;QACpDA,GAAG,CAAC,QAAD,CAAH,SAAsB4E,QAAQ,CAACgB,UAAT,CAAoBjC,GAAG,CAACE,MAAJ,CAAWgC,EAA/B,EAAmClC,GAAG,CAACE,MAAJ,CAAWiC,IAA9C,CAAtB;QACA,OAAO,IAAP;MACD,CAHD;;MAAA;QAAA;MAAA;IAAA;EAID;;AAzTuB;;;mBAAb5H,eAAa6H;AAAA;;;SAAb7H;EAAa8H,SAAb9H,aAAa;EAAA+H,YADA;;;YAiJvBlI,iBAAiB", "names": ["NgZone", "BehaviorSubject", "Observable", "M", "IOClient", "distinctUntilChanged", "serverTime", "OnlineExamWSLogin", "PromiseHelper", "ConnectState", "SocketService", "constructor", "router", "zone", "utilSer", "localStorageService", "urlService", "notConnected", "window", "is_demo", "isDebugEnabled", "clientIO", "verbose", "disableWs", "isLogined", "next", "setupSocket", "changed", "subscribe", "connect", "WS_TOKEN$", "token", "console", "log", "connectState", "connectSubject", "pipe", "loginObs", "observer", "then", "res", "complete", "catch", "e", "error", "Promise", "resolve", "<PERSON><PERSON><PERSON>", "ERR_OK", "url", "getWsUrl", "seat", "getRegInfo", "seatNumber", "terminate", "setAsLogout", "autoLogin", "getValue", "reject", "loginReqs", "push", "value", "connecting", "logining", "run", "autoConnect", "relogin", "disconnect", "logout", "sendMsg", "MSG_CLIENT_LOGOUT", "JSON", "stringify", "connected", "do<PERSON><PERSON><PERSON>", "mac_addr", "code", "permit", "version", "appVersion", "MSG_CLIENT_LOGIN", "client", "timeout", "ERR_NET_ERR", "errmsg", "emit", "for<PERSON>ach", "rq", "room", "updateRegInfo", "logined", "info", "getPhysAddr", "entry", "setTimeout", "on", "onMsg", "MSG_RELOGIN", "MSG_ENABLE_REGISTER", "req", "enable_register", "params", "enabled", "MSG_UNREGISTER", "clearRegInfo", "navigate", "by_manager", "MSG_CHANGE_SEAT_NUMBER", "reg_info", "new_seat_number", "saveRegInfo", "MSG_GET_STATUS", "MSG_TIME", "sync", "time", "MSG_SETTING_UPDATE", "joyshell", "UpdateSettings", "CONTENT_PROTECTION", "ToggleProtection", "REJECT_APPS_EXT", "StartRuntimeCheck", "MSG_GET_PROCESSES", "processes", "GetProcessList", "Platform", "parse", "MSG_KILL_PROCESS", "result", "KillProcess", "name", "MSG_EVAL_JS", "EvalScript", "js", "type", "i0", "factory", "providedIn"], "sourceRoot": "", "sources": ["D:\\work\\joyserver\\client\\src\\app\\core\\service\\socket\\socket.service.ts"], "sourcesContent": ["import { Injectable, NgZone } from \"@angular/core\";\nimport { Router } from \"@angular/router\";\nimport { BehaviorSubject, Observable } from \"rxjs\";\nimport { LocalStorageService } from \"../localStorage.service\";\nimport { URLService } from \"../url.service\";\nimport * as M from \"@core-io/io.message.consts\";\nimport { IOClient } from \"@core-io/io.client\";\nimport { distinctUntilChanged } from \"rxjs/operators\";\nimport serverTime from \"../utils/time.service\";\nimport { UtilService } from \"../utils/util.service\";\nimport { OnlineExamWSLogin } from \"../online/decorator/wsLogin.decorator\";\n\nclass PromiseHelper {\n  resolve: (res: { errcode?: number; errmsg?: string }) => void;\n  reject: (res: { errcode?: number; errmsg?: string }) => void;\n}\n\nexport enum ConnectState {\n  notConnected = 0,\n  connecting,\n  connected,\n  logining,\n  logined,\n}\n\n@Injectable({ providedIn: \"root\" })\nexport class SocketService {\n  private url: string;\n  clientIO = new IOClient();\n  seatNumber: number;\n  entry: { entry_id: string; permit: string } | null = null;\n  private loginReqs: PromiseHelper[] = [];\n  isLogined = new BehaviorSubject<boolean>(false);\n  enable_register = new BehaviorSubject<boolean>(false);\n  private connectSubject = new BehaviorSubject<ConnectState>(ConnectState.notConnected);\n  private autoLogin = true;\n  private disableWs = window.is_demo; // 浏览器版demo不使用websocket\n\n  WS_TOKEN$ = new BehaviorSubject<string>(\"\"); // 在线考试ws token\n\n  constructor(\n    public router: Router,\n    private zone: NgZone,\n    private utilSer: UtilService,\n    public localStorageService: LocalStorageService,\n    private urlService: URLService\n  ) {\n    if (this.localStorageService.isDebugEnabled) {\n      this.clientIO.verbose = true;\n    }\n\n    if (this.disableWs) {\n      this.isLogined.next(true);\n      return;\n    }\n\n    this.setupSocket();\n\n    this.localStorageService.changed.subscribe(() => {\n      this.connect();\n    });\n\n    this.WS_TOKEN$.subscribe((token) => {\n      if (token) {\n        console.log(\"WS_TOKEN$\", token);\n        this.connect();\n      }\n    });\n  }\n\n  get connectState(): Observable<ConnectState> {\n    return this.connectSubject.pipe(distinctUntilChanged());\n  }\n\n  loginObs(): Observable<any> {\n    return new Observable((observer) => {\n      this.connect()\n        .then((res) => {\n          observer.next(res);\n          observer.complete();\n        })\n        .catch((e) => {\n          observer.error(e);\n        });\n    });\n  }\n\n  // 连接服务器\n  async connect() {\n    if (this.disableWs) {\n      return Promise.resolve({ errcode: M.ERR_OK });\n    }\n\n    const url = this.urlService.getWsUrl();\n    const seat = this.localStorageService.getRegInfo().seat;\n    if (url !== this.url || seat !== this.seatNumber) {\n      this.clientIO.terminate();\n      this.setAsLogout();\n      this.connectSubject.next(ConnectState.notConnected);\n    }\n\n    this.url = url;\n    this.seatNumber = seat;\n    this.autoLogin = true;\n\n    if (this.isLogined.getValue()) {\n      return Promise.resolve({ errcode: M.ERR_OK });\n    }\n\n    return new Promise((resolve, reject) => {\n      this.loginReqs.push({ resolve, reject });\n\n      if (\n        this.connectSubject.value === ConnectState.connecting ||\n        this.connectSubject.value === ConnectState.logining\n      ) {\n        return;\n      }\n\n      if (this.connectSubject.value === ConnectState.notConnected) {\n        this.connectSubject.next(ConnectState.connecting);\n        this.zone.run(() => {\n          this.clientIO.autoConnect = true;\n          this.clientIO.connect(this.url);\n        });\n        return;\n      }\n\n      if (!this.isLogined.value) {\n        this.relogin();\n      }\n    });\n  }\n\n  // 断开服务器\n  disconnect() {\n    if (this.disableWs) {\n      return;\n    }\n\n    this.autoLogin = false;\n    this.clientIO.disconnect();\n  }\n\n  // 登出考试机\n  logout() {\n    if (this.disableWs) {\n      return;\n    }\n\n    this.clientIO.sendMsg(M.MSG_CLIENT_LOGOUT, {}, (res) => {\n      this.autoLogin = false;\n      console.log(\"- client logout: \" + JSON.stringify(res));\n    });\n  }\n\n  // 修改状态为未登录\n  setAsLogout() {\n    if (this.disableWs) {\n      return;\n    }\n\n    if (this.isLogined.getValue()) {\n      this.isLogined.next(false);\n      this.connectSubject.next(ConnectState.connected);\n      console.log(\"* Logout\");\n    }\n  }\n\n  // 登录\n  @OnlineExamWSLogin()\n  private async doLogin(mac_addr: string, seat: number, code: string, permit: string) {\n    const version = this.localStorageService.appVersion();\n\n    this.connectSubject.next(ConnectState.logining);\n    return new Promise((resolve, reject) => {\n      this.clientIO.sendMsg(\n        M.MSG_CLIENT_LOGIN,\n        { mac_addr, seat, code, version, client: true, permit, timeout: 5000 },\n        (res) => {\n          if (!res) {\n            // disconnected\n            res = { errcode: M.ERR_NET_ERR, errmsg: \"network error\" };\n            reject(res);\n            this.clientIO.emit(\"login_error\", res);\n            this.loginReqs.forEach((rq) => {\n              rq.reject(res);\n            });\n            this.loginReqs = [];\n            return;\n          }\n          if (res.errcode === 0) {\n            // 登录成功\n            console.log(\"- Client logined，Seat number：\", this.seatNumber);\n\n            // update room info\n            if (res.room) {\n              // room: {room, room_code, center_name, center_address}\n              this.localStorageService.updateRegInfo(res.room);\n            }\n\n            this.isLogined.next(true);\n\n            this.clientIO.emit(\"logined\", this.seatNumber);\n            this.loginReqs.forEach((rq) => {\n              rq.resolve({ errcode: M.ERR_OK, errmsg: \"OK\" });\n            });\n            this.loginReqs = [];\n            resolve(res);\n\n            this.connectSubject.next(ConnectState.logined);\n          } else {\n            this.clientIO.emit(\"login_error\", res);\n            this.loginReqs.forEach((rq) => {\n              rq.reject(res);\n            });\n            this.loginReqs = [];\n            reject(res);\n          }\n        }\n      );\n    });\n  }\n\n  private async relogin() {\n    const info = this.localStorageService.getRegInfo();\n    this.clientIO.emit(\"logining\", this.seatNumber);\n    const mac_addr = await this.utilSer.getPhysAddr();\n    this.doLogin(mac_addr, this.seatNumber, info.code, this.entry?.permit).catch((res) => {\n      if (res && res.errcode !== 0) {\n        setTimeout(() => {\n          if (this.autoLogin) {\n            this.relogin();\n          }\n        }, 5000);\n      }\n    });\n  }\n\n  private setupSocket() {\n    this.clientIO.on(\"connect\", () => {\n      this.connectSubject.next(ConnectState.connected);\n      if (this.autoLogin) {\n        this.relogin();\n      }\n    });\n\n    this.clientIO.on(\"connect_error\", () => {\n      this.loginReqs.forEach((rq) => {\n        rq.reject({ errcode: M.ERR_NET_ERR, errmsg: \"connect error\" });\n      });\n      this.loginReqs = [];\n    });\n\n    this.clientIO.on(\"connect_timeout\", () => {\n      this.loginReqs.forEach((rq) => {\n        rq.reject({ errcode: M.ERR_NET_ERR, errmsg: \"connect timeout\" });\n      });\n      this.loginReqs = [];\n    });\n\n    this.clientIO.on(\"disconnect\", () => {\n      this.connectSubject.next(ConnectState.notConnected);\n      this.isLogined.next(false);\n      this.loginReqs.forEach((rq) => {\n        rq.reject({ errcode: M.ERR_NET_ERR, errmsg: \"disconnected\" });\n      });\n      this.loginReqs = [];\n    });\n\n    this.clientIO.onMsg(M.MSG_RELOGIN, () => {\n      this.setAsLogout();\n      this.relogin();\n      return true;\n    });\n\n    this.clientIO.onMsg(M.MSG_ENABLE_REGISTER, (req) => {\n      this.enable_register.next(req.params.enabled);\n      return true;\n    });\n\n    this.clientIO.onMsg(M.MSG_UNREGISTER, () => {\n      this.localStorageService.clearRegInfo();\n      this.setAsLogout();\n      console.log(\"---- Enter page: register\");\n      this.router.navigate([\"/register\", { by_manager: true }]);\n      return true;\n    });\n\n    this.clientIO.onMsg(M.MSG_CHANGE_SEAT_NUMBER, (req) => {\n      const reg_info = this.localStorageService.getRegInfo();\n      if (reg_info) {\n        reg_info.seat = req.params.new_seat_number;\n        reg_info.code = req.params.code;\n        this.localStorageService.saveRegInfo(reg_info);\n      }\n      return true;\n    });\n\n    this.clientIO.onMsg(M.MSG_GET_STATUS, () => {\n      return true;\n    });\n\n    this.clientIO.onMsg(M.MSG_TIME, (req) => {\n      serverTime.sync(req.params.time);\n      return true;\n    });\n\n    this.clientIO.onMsg(M.MSG_SETTING_UPDATE, (req) => {\n      if (joyshell) {\n        joyshell.UpdateSettings(req.params);\n        if (typeof req.params.CONTENT_PROTECTION !== \"undefined\") {\n          joyshell.ToggleProtection(req.params.CONTENT_PROTECTION);\n        }\n        if (typeof req.params.REJECT_APPS_EXT !== \"undefined\") {\n          joyshell.StartRuntimeCheck(\"\");\n        }\n      }\n\n      return true;\n    });\n\n    this.clientIO.onMsg(M.MSG_GET_PROCESSES, async (req, res) => {\n      const processes = await joyshell.GetProcessList();\n      res[\"platform\"] = joyshell.Platform;\n      res[\"processes\"] = typeof processes === \"string\" ? JSON.parse(processes) : processes;\n      return true;\n    });\n\n    this.clientIO.onMsg(M.MSG_KILL_PROCESS, async (req, res) => {\n      const result = await joyshell.KillProcess(req.params.name);\n      res[\"result\"] = result;\n      return true;\n    });\n\n    this.clientIO.onMsg(M.MSG_EVAL_JS, async (req, res) => {\n      res[\"result\"] = await joyshell.EvalScript(req.params.js, req.params.type);\n      return true;\n    });\n  }\n}\n"]}, "metadata": {}, "sourceType": "module"}