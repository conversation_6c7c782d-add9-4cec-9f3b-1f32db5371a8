{"ast": null, "code": "import { mergeMap } from './mergeMap';\nimport { identity } from '../util/identity';\nexport function mergeAll(concurrent = Infinity) {\n  return mergeMap(identity, concurrent);\n}", "map": {"version": 3, "names": ["mergeMap", "identity", "mergeAll", "concurrent", "Infinity"], "sources": ["D:/work/joyserver/client/node_modules/rxjs/dist/esm/internal/operators/mergeAll.js"], "sourcesContent": ["import { mergeMap } from './mergeMap';\nimport { identity } from '../util/identity';\nexport function mergeAll(concurrent = Infinity) {\n    return mergeMap(identity, concurrent);\n}\n"], "mappings": "AAAA,SAASA,QAAT,QAAyB,YAAzB;AACA,SAASC,QAAT,QAAyB,kBAAzB;AACA,OAAO,SAASC,QAAT,CAAkBC,UAAU,GAAGC,QAA/B,EAAyC;EAC5C,OAAOJ,QAAQ,CAACC,QAAD,EAAWE,UAAX,CAAf;AACH"}, "metadata": {}, "sourceType": "module"}