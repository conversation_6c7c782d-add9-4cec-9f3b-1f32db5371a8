{"ast": null, "code": "export var schurDocs = {\n  name: 'schur',\n  category: 'Algebra',\n  syntax: ['schur(A)'],\n  description: 'Performs a real Schur decomposition of the real matrix A = UTU\\'',\n  examples: ['schur([[1, 0], [-4, 3]])', 'A = [[1, 0], [-4, 3]]', 'schur(A)'],\n  seealso: ['lyap', 'sylvester']\n};", "map": {"version": 3, "names": ["schur<PERSON><PERSON><PERSON>", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["D:/work/joyserver/client/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/algebra/schur.js"], "sourcesContent": ["export var schurDocs = {\n  name: 'schur',\n  category: 'Algebra',\n  syntax: ['schur(A)'],\n  description: 'Performs a real Schur decomposition of the real matrix A = UTU\\'',\n  examples: ['schur([[1, 0], [-4, 3]])', 'A = [[1, 0], [-4, 3]]', 'schur(A)'],\n  seealso: ['lyap', 'sylvester']\n};"], "mappings": "AAAA,OAAO,IAAIA,SAAS,GAAG;EACrBC,IAAI,EAAE,OADe;EAErBC,QAAQ,EAAE,SAFW;EAGrBC,MAAM,EAAE,CAAC,UAAD,CAHa;EAIrBC,WAAW,EAAE,kEAJQ;EAKrBC,QAAQ,EAAE,CAAC,0BAAD,EAA6B,uBAA7B,EAAsD,UAAtD,CALW;EAMrBC,OAAO,EAAE,CAAC,MAAD,EAAS,WAAT;AANY,CAAhB"}, "metadata": {}, "sourceType": "module"}