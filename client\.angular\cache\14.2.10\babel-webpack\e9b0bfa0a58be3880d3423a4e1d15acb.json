{"ast": null, "code": "import { factory } from '../../utils/factory.js';\nimport { atanhNumber } from '../../plain/number/index.js';\nvar name = 'atanh';\nvar dependencies = ['typed', 'config', 'Complex'];\nexport var createAtanh = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed,\n    config,\n    Complex\n  } = _ref;\n  /**\n   * Calculate the hyperbolic arctangent of a value,\n   * defined as `atanh(x) = ln((1 + x)/(1 - x)) / 2`.\n   *\n   * To avoid confusion with the matrix hyperbolic arctangent, this function\n   * does not apply to matrices.\n   *\n   * Syntax:\n   *\n   *    math.atanh(x)\n   *\n   * Examples:\n   *\n   *    math.atanh(0.5)       // returns 0.5493061443340549\n   *\n   * See also:\n   *\n   *    acosh, asinh\n   *\n   * @param {number | BigNumber | Complex} x  Function input\n   * @return {number | BigNumber | Complex} Hyperbolic arctangent of x\n   */\n\n  return typed(name, {\n    number: function number(x) {\n      if (x <= 1 && x >= -1 || config.predictable) {\n        return atanhNumber(x);\n      }\n\n      return new Complex(x, 0).atanh();\n    },\n    Complex: function Complex(x) {\n      return x.atanh();\n    },\n    BigNumber: function BigNumber(x) {\n      return x.atanh();\n    }\n  });\n});", "map": {"version": 3, "names": ["factory", "atanhNumber", "name", "dependencies", "createAtanh", "_ref", "typed", "config", "Complex", "number", "x", "predictable", "atanh", "BigNumber"], "sources": ["D:/work/joyserver/client/node_modules/mathjs/lib/esm/function/trigonometry/atanh.js"], "sourcesContent": ["import { factory } from '../../utils/factory.js';\nimport { atanhNumber } from '../../plain/number/index.js';\nvar name = 'atanh';\nvar dependencies = ['typed', 'config', 'Complex'];\nexport var createAtanh = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed,\n    config,\n    Complex\n  } = _ref;\n  /**\n   * Calculate the hyperbolic arctangent of a value,\n   * defined as `atanh(x) = ln((1 + x)/(1 - x)) / 2`.\n   *\n   * To avoid confusion with the matrix hyperbolic arctangent, this function\n   * does not apply to matrices.\n   *\n   * Syntax:\n   *\n   *    math.atanh(x)\n   *\n   * Examples:\n   *\n   *    math.atanh(0.5)       // returns 0.5493061443340549\n   *\n   * See also:\n   *\n   *    acosh, asinh\n   *\n   * @param {number | BigNumber | Complex} x  Function input\n   * @return {number | BigNumber | Complex} Hyperbolic arctangent of x\n   */\n  return typed(name, {\n    number: function number(x) {\n      if (x <= 1 && x >= -1 || config.predictable) {\n        return atanhNumber(x);\n      }\n      return new Complex(x, 0).atanh();\n    },\n    Complex: function Complex(x) {\n      return x.atanh();\n    },\n    BigNumber: function BigNumber(x) {\n      return x.atanh();\n    }\n  });\n});"], "mappings": "AAAA,SAASA,OAAT,QAAwB,wBAAxB;AACA,SAASC,WAAT,QAA4B,6BAA5B;AACA,IAAIC,IAAI,GAAG,OAAX;AACA,IAAIC,YAAY,GAAG,CAAC,OAAD,EAAU,QAAV,EAAoB,SAApB,CAAnB;AACA,OAAO,IAAIC,WAAW,GAAG,eAAeJ,OAAO,CAACE,IAAD,EAAOC,YAAP,EAAqBE,IAAI,IAAI;EAC1E,IAAI;IACFC,KADE;IAEFC,MAFE;IAGFC;EAHE,IAIAH,IAJJ;EAKA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EACE,OAAOC,KAAK,CAACJ,IAAD,EAAO;IACjBO,MAAM,EAAE,SAASA,MAAT,CAAgBC,CAAhB,EAAmB;MACzB,IAAIA,CAAC,IAAI,CAAL,IAAUA,CAAC,IAAI,CAAC,CAAhB,IAAqBH,MAAM,CAACI,WAAhC,EAA6C;QAC3C,OAAOV,WAAW,CAACS,CAAD,CAAlB;MACD;;MACD,OAAO,IAAIF,OAAJ,CAAYE,CAAZ,EAAe,CAAf,EAAkBE,KAAlB,EAAP;IACD,CANgB;IAOjBJ,OAAO,EAAE,SAASA,OAAT,CAAiBE,CAAjB,EAAoB;MAC3B,OAAOA,CAAC,CAACE,KAAF,EAAP;IACD,CATgB;IAUjBC,SAAS,EAAE,SAASA,SAAT,CAAmBH,CAAnB,EAAsB;MAC/B,OAAOA,CAAC,CAACE,KAAF,EAAP;IACD;EAZgB,CAAP,CAAZ;AAcD,CA1C8C,CAAxC"}, "metadata": {}, "sourceType": "module"}