{"ast": null, "code": "import getCompositeRect from \"./dom-utils/getCompositeRect.js\";\nimport getLayoutRect from \"./dom-utils/getLayoutRect.js\";\nimport listScrollParents from \"./dom-utils/listScrollParents.js\";\nimport getOffsetParent from \"./dom-utils/getOffsetParent.js\";\nimport getComputedStyle from \"./dom-utils/getComputedStyle.js\";\nimport orderModifiers from \"./utils/orderModifiers.js\";\nimport debounce from \"./utils/debounce.js\";\nimport validateModifiers from \"./utils/validateModifiers.js\";\nimport uniqueBy from \"./utils/uniqueBy.js\";\nimport getBasePlacement from \"./utils/getBasePlacement.js\";\nimport mergeByName from \"./utils/mergeByName.js\";\nimport detectOverflow from \"./utils/detectOverflow.js\";\nimport { isElement } from \"./dom-utils/instanceOf.js\";\nimport { auto } from \"./enums.js\";\nvar INVALID_ELEMENT_ERROR = 'Popper: Invalid reference or popper argument provided. They must be either a DOM element or virtual element.';\nvar INFINITE_LOOP_ERROR = 'Popper: An infinite loop in the modifiers cycle has been detected! The cycle has been interrupted to prevent a browser crash.';\nvar DEFAULT_OPTIONS = {\n  placement: 'bottom',\n  modifiers: [],\n  strategy: 'absolute'\n};\n\nfunction areValidElements() {\n  for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n    args[_key] = arguments[_key];\n  }\n\n  return !args.some(function (element) {\n    return !(element && typeof element.getBoundingClientRect === 'function');\n  });\n}\n\nexport function popperGenerator(generatorOptions) {\n  if (generatorOptions === void 0) {\n    generatorOptions = {};\n  }\n\n  var _generatorOptions = generatorOptions,\n      _generatorOptions$def = _generatorOptions.defaultModifiers,\n      defaultModifiers = _generatorOptions$def === void 0 ? [] : _generatorOptions$def,\n      _generatorOptions$def2 = _generatorOptions.defaultOptions,\n      defaultOptions = _generatorOptions$def2 === void 0 ? DEFAULT_OPTIONS : _generatorOptions$def2;\n  return function createPopper(reference, popper, options) {\n    if (options === void 0) {\n      options = defaultOptions;\n    }\n\n    var state = {\n      placement: 'bottom',\n      orderedModifiers: [],\n      options: Object.assign({}, DEFAULT_OPTIONS, defaultOptions),\n      modifiersData: {},\n      elements: {\n        reference: reference,\n        popper: popper\n      },\n      attributes: {},\n      styles: {}\n    };\n    var effectCleanupFns = [];\n    var isDestroyed = false;\n    var instance = {\n      state: state,\n      setOptions: function setOptions(setOptionsAction) {\n        var options = typeof setOptionsAction === 'function' ? setOptionsAction(state.options) : setOptionsAction;\n        cleanupModifierEffects();\n        state.options = Object.assign({}, defaultOptions, state.options, options);\n        state.scrollParents = {\n          reference: isElement(reference) ? listScrollParents(reference) : reference.contextElement ? listScrollParents(reference.contextElement) : [],\n          popper: listScrollParents(popper)\n        }; // Orders the modifiers based on their dependencies and `phase`\n        // properties\n\n        var orderedModifiers = orderModifiers(mergeByName([].concat(defaultModifiers, state.options.modifiers))); // Strip out disabled modifiers\n\n        state.orderedModifiers = orderedModifiers.filter(function (m) {\n          return m.enabled;\n        }); // Validate the provided modifiers so that the consumer will get warned\n        // if one of the modifiers is invalid for any reason\n\n        if (process.env.NODE_ENV !== \"production\") {\n          var modifiers = uniqueBy([].concat(orderedModifiers, state.options.modifiers), function (_ref) {\n            var name = _ref.name;\n            return name;\n          });\n          validateModifiers(modifiers);\n\n          if (getBasePlacement(state.options.placement) === auto) {\n            var flipModifier = state.orderedModifiers.find(function (_ref2) {\n              var name = _ref2.name;\n              return name === 'flip';\n            });\n\n            if (!flipModifier) {\n              console.error(['Popper: \"auto\" placements require the \"flip\" modifier be', 'present and enabled to work.'].join(' '));\n            }\n          }\n\n          var _getComputedStyle = getComputedStyle(popper),\n              marginTop = _getComputedStyle.marginTop,\n              marginRight = _getComputedStyle.marginRight,\n              marginBottom = _getComputedStyle.marginBottom,\n              marginLeft = _getComputedStyle.marginLeft; // We no longer take into account `margins` on the popper, and it can\n          // cause bugs with positioning, so we'll warn the consumer\n\n\n          if ([marginTop, marginRight, marginBottom, marginLeft].some(function (margin) {\n            return parseFloat(margin);\n          })) {\n            console.warn(['Popper: CSS \"margin\" styles cannot be used to apply padding', 'between the popper and its reference element or boundary.', 'To replicate margin, use the `offset` modifier, as well as', 'the `padding` option in the `preventOverflow` and `flip`', 'modifiers.'].join(' '));\n          }\n        }\n\n        runModifierEffects();\n        return instance.update();\n      },\n      // Sync update – it will always be executed, even if not necessary. This\n      // is useful for low frequency updates where sync behavior simplifies the\n      // logic.\n      // For high frequency updates (e.g. `resize` and `scroll` events), always\n      // prefer the async Popper#update method\n      forceUpdate: function forceUpdate() {\n        if (isDestroyed) {\n          return;\n        }\n\n        var _state$elements = state.elements,\n            reference = _state$elements.reference,\n            popper = _state$elements.popper; // Don't proceed if `reference` or `popper` are not valid elements\n        // anymore\n\n        if (!areValidElements(reference, popper)) {\n          if (process.env.NODE_ENV !== \"production\") {\n            console.error(INVALID_ELEMENT_ERROR);\n          }\n\n          return;\n        } // Store the reference and popper rects to be read by modifiers\n\n\n        state.rects = {\n          reference: getCompositeRect(reference, getOffsetParent(popper), state.options.strategy === 'fixed'),\n          popper: getLayoutRect(popper)\n        }; // Modifiers have the ability to reset the current update cycle. The\n        // most common use case for this is the `flip` modifier changing the\n        // placement, which then needs to re-run all the modifiers, because the\n        // logic was previously ran for the previous placement and is therefore\n        // stale/incorrect\n\n        state.reset = false;\n        state.placement = state.options.placement; // On each update cycle, the `modifiersData` property for each modifier\n        // is filled with the initial data specified by the modifier. This means\n        // it doesn't persist and is fresh on each update.\n        // To ensure persistent data, use `${name}#persistent`\n\n        state.orderedModifiers.forEach(function (modifier) {\n          return state.modifiersData[modifier.name] = Object.assign({}, modifier.data);\n        });\n        var __debug_loops__ = 0;\n\n        for (var index = 0; index < state.orderedModifiers.length; index++) {\n          if (process.env.NODE_ENV !== \"production\") {\n            __debug_loops__ += 1;\n\n            if (__debug_loops__ > 100) {\n              console.error(INFINITE_LOOP_ERROR);\n              break;\n            }\n          }\n\n          if (state.reset === true) {\n            state.reset = false;\n            index = -1;\n            continue;\n          }\n\n          var _state$orderedModifie = state.orderedModifiers[index],\n              fn = _state$orderedModifie.fn,\n              _state$orderedModifie2 = _state$orderedModifie.options,\n              _options = _state$orderedModifie2 === void 0 ? {} : _state$orderedModifie2,\n              name = _state$orderedModifie.name;\n\n          if (typeof fn === 'function') {\n            state = fn({\n              state: state,\n              options: _options,\n              name: name,\n              instance: instance\n            }) || state;\n          }\n        }\n      },\n      // Async and optimistically optimized update – it will not be executed if\n      // not necessary (debounced to run at most once-per-tick)\n      update: debounce(function () {\n        return new Promise(function (resolve) {\n          instance.forceUpdate();\n          resolve(state);\n        });\n      }),\n      destroy: function destroy() {\n        cleanupModifierEffects();\n        isDestroyed = true;\n      }\n    };\n\n    if (!areValidElements(reference, popper)) {\n      if (process.env.NODE_ENV !== \"production\") {\n        console.error(INVALID_ELEMENT_ERROR);\n      }\n\n      return instance;\n    }\n\n    instance.setOptions(options).then(function (state) {\n      if (!isDestroyed && options.onFirstUpdate) {\n        options.onFirstUpdate(state);\n      }\n    }); // Modifiers have the ability to execute arbitrary code before the first\n    // update cycle runs. They will be executed in the same order as the update\n    // cycle. This is useful when a modifier adds some persistent data that\n    // other modifiers need to use, but the modifier is run after the dependent\n    // one.\n\n    function runModifierEffects() {\n      state.orderedModifiers.forEach(function (_ref3) {\n        var name = _ref3.name,\n            _ref3$options = _ref3.options,\n            options = _ref3$options === void 0 ? {} : _ref3$options,\n            effect = _ref3.effect;\n\n        if (typeof effect === 'function') {\n          var cleanupFn = effect({\n            state: state,\n            name: name,\n            instance: instance,\n            options: options\n          });\n\n          var noopFn = function noopFn() {};\n\n          effectCleanupFns.push(cleanupFn || noopFn);\n        }\n      });\n    }\n\n    function cleanupModifierEffects() {\n      effectCleanupFns.forEach(function (fn) {\n        return fn();\n      });\n      effectCleanupFns = [];\n    }\n\n    return instance;\n  };\n}\nexport var createPopper = /*#__PURE__*/popperGenerator(); // eslint-disable-next-line import/no-unused-modules\n\nexport { detectOverflow };", "map": {"version": 3, "names": ["getCompositeRect", "getLayoutRect", "listScrollParents", "getOffsetParent", "getComputedStyle", "orderModifiers", "debounce", "validateModifiers", "uniqueBy", "getBasePlacement", "mergeByName", "detectOverflow", "isElement", "auto", "INVALID_ELEMENT_ERROR", "INFINITE_LOOP_ERROR", "DEFAULT_OPTIONS", "placement", "modifiers", "strategy", "areValidElements", "_len", "arguments", "length", "args", "Array", "_key", "some", "element", "getBoundingClientRect", "popperGenerator", "generatorOptions", "_generatorOptions", "_generatorOptions$def", "defaultModifiers", "_generatorOptions$def2", "defaultOptions", "createPopper", "reference", "popper", "options", "state", "orderedModifiers", "Object", "assign", "modifiersData", "elements", "attributes", "styles", "effectCleanupFns", "isDestroyed", "instance", "setOptions", "setOptionsAction", "cleanupModifierEffects", "scrollParents", "contextElement", "concat", "filter", "m", "enabled", "process", "env", "NODE_ENV", "_ref", "name", "flipModifier", "find", "_ref2", "console", "error", "join", "_getComputedStyle", "marginTop", "marginRight", "marginBottom", "marginLeft", "margin", "parseFloat", "warn", "runModifierEffects", "update", "forceUpdate", "_state$elements", "rects", "reset", "for<PERSON>ach", "modifier", "data", "__debug_loops__", "index", "_state$orderedModifie", "fn", "_state$orderedModifie2", "_options", "Promise", "resolve", "destroy", "then", "onFirstUpdate", "_ref3", "_ref3$options", "effect", "cleanupFn", "noopFn", "push"], "sources": ["D:/work/joyserver/client/node_modules/@popperjs/core/lib/createPopper.js"], "sourcesContent": ["import getCompositeRect from \"./dom-utils/getCompositeRect.js\";\nimport getLayoutRect from \"./dom-utils/getLayoutRect.js\";\nimport listScrollParents from \"./dom-utils/listScrollParents.js\";\nimport getOffsetParent from \"./dom-utils/getOffsetParent.js\";\nimport getComputedStyle from \"./dom-utils/getComputedStyle.js\";\nimport orderModifiers from \"./utils/orderModifiers.js\";\nimport debounce from \"./utils/debounce.js\";\nimport validateModifiers from \"./utils/validateModifiers.js\";\nimport uniqueBy from \"./utils/uniqueBy.js\";\nimport getBasePlacement from \"./utils/getBasePlacement.js\";\nimport mergeByName from \"./utils/mergeByName.js\";\nimport detectOverflow from \"./utils/detectOverflow.js\";\nimport { isElement } from \"./dom-utils/instanceOf.js\";\nimport { auto } from \"./enums.js\";\nvar INVALID_ELEMENT_ERROR = 'Popper: Invalid reference or popper argument provided. They must be either a DOM element or virtual element.';\nvar INFINITE_LOOP_ERROR = 'Popper: An infinite loop in the modifiers cycle has been detected! The cycle has been interrupted to prevent a browser crash.';\nvar DEFAULT_OPTIONS = {\n  placement: 'bottom',\n  modifiers: [],\n  strategy: 'absolute'\n};\n\nfunction areValidElements() {\n  for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n    args[_key] = arguments[_key];\n  }\n\n  return !args.some(function (element) {\n    return !(element && typeof element.getBoundingClientRect === 'function');\n  });\n}\n\nexport function popperGenerator(generatorOptions) {\n  if (generatorOptions === void 0) {\n    generatorOptions = {};\n  }\n\n  var _generatorOptions = generatorOptions,\n      _generatorOptions$def = _generatorOptions.defaultModifiers,\n      defaultModifiers = _generatorOptions$def === void 0 ? [] : _generatorOptions$def,\n      _generatorOptions$def2 = _generatorOptions.defaultOptions,\n      defaultOptions = _generatorOptions$def2 === void 0 ? DEFAULT_OPTIONS : _generatorOptions$def2;\n  return function createPopper(reference, popper, options) {\n    if (options === void 0) {\n      options = defaultOptions;\n    }\n\n    var state = {\n      placement: 'bottom',\n      orderedModifiers: [],\n      options: Object.assign({}, DEFAULT_OPTIONS, defaultOptions),\n      modifiersData: {},\n      elements: {\n        reference: reference,\n        popper: popper\n      },\n      attributes: {},\n      styles: {}\n    };\n    var effectCleanupFns = [];\n    var isDestroyed = false;\n    var instance = {\n      state: state,\n      setOptions: function setOptions(setOptionsAction) {\n        var options = typeof setOptionsAction === 'function' ? setOptionsAction(state.options) : setOptionsAction;\n        cleanupModifierEffects();\n        state.options = Object.assign({}, defaultOptions, state.options, options);\n        state.scrollParents = {\n          reference: isElement(reference) ? listScrollParents(reference) : reference.contextElement ? listScrollParents(reference.contextElement) : [],\n          popper: listScrollParents(popper)\n        }; // Orders the modifiers based on their dependencies and `phase`\n        // properties\n\n        var orderedModifiers = orderModifiers(mergeByName([].concat(defaultModifiers, state.options.modifiers))); // Strip out disabled modifiers\n\n        state.orderedModifiers = orderedModifiers.filter(function (m) {\n          return m.enabled;\n        }); // Validate the provided modifiers so that the consumer will get warned\n        // if one of the modifiers is invalid for any reason\n\n        if (process.env.NODE_ENV !== \"production\") {\n          var modifiers = uniqueBy([].concat(orderedModifiers, state.options.modifiers), function (_ref) {\n            var name = _ref.name;\n            return name;\n          });\n          validateModifiers(modifiers);\n\n          if (getBasePlacement(state.options.placement) === auto) {\n            var flipModifier = state.orderedModifiers.find(function (_ref2) {\n              var name = _ref2.name;\n              return name === 'flip';\n            });\n\n            if (!flipModifier) {\n              console.error(['Popper: \"auto\" placements require the \"flip\" modifier be', 'present and enabled to work.'].join(' '));\n            }\n          }\n\n          var _getComputedStyle = getComputedStyle(popper),\n              marginTop = _getComputedStyle.marginTop,\n              marginRight = _getComputedStyle.marginRight,\n              marginBottom = _getComputedStyle.marginBottom,\n              marginLeft = _getComputedStyle.marginLeft; // We no longer take into account `margins` on the popper, and it can\n          // cause bugs with positioning, so we'll warn the consumer\n\n\n          if ([marginTop, marginRight, marginBottom, marginLeft].some(function (margin) {\n            return parseFloat(margin);\n          })) {\n            console.warn(['Popper: CSS \"margin\" styles cannot be used to apply padding', 'between the popper and its reference element or boundary.', 'To replicate margin, use the `offset` modifier, as well as', 'the `padding` option in the `preventOverflow` and `flip`', 'modifiers.'].join(' '));\n          }\n        }\n\n        runModifierEffects();\n        return instance.update();\n      },\n      // Sync update – it will always be executed, even if not necessary. This\n      // is useful for low frequency updates where sync behavior simplifies the\n      // logic.\n      // For high frequency updates (e.g. `resize` and `scroll` events), always\n      // prefer the async Popper#update method\n      forceUpdate: function forceUpdate() {\n        if (isDestroyed) {\n          return;\n        }\n\n        var _state$elements = state.elements,\n            reference = _state$elements.reference,\n            popper = _state$elements.popper; // Don't proceed if `reference` or `popper` are not valid elements\n        // anymore\n\n        if (!areValidElements(reference, popper)) {\n          if (process.env.NODE_ENV !== \"production\") {\n            console.error(INVALID_ELEMENT_ERROR);\n          }\n\n          return;\n        } // Store the reference and popper rects to be read by modifiers\n\n\n        state.rects = {\n          reference: getCompositeRect(reference, getOffsetParent(popper), state.options.strategy === 'fixed'),\n          popper: getLayoutRect(popper)\n        }; // Modifiers have the ability to reset the current update cycle. The\n        // most common use case for this is the `flip` modifier changing the\n        // placement, which then needs to re-run all the modifiers, because the\n        // logic was previously ran for the previous placement and is therefore\n        // stale/incorrect\n\n        state.reset = false;\n        state.placement = state.options.placement; // On each update cycle, the `modifiersData` property for each modifier\n        // is filled with the initial data specified by the modifier. This means\n        // it doesn't persist and is fresh on each update.\n        // To ensure persistent data, use `${name}#persistent`\n\n        state.orderedModifiers.forEach(function (modifier) {\n          return state.modifiersData[modifier.name] = Object.assign({}, modifier.data);\n        });\n        var __debug_loops__ = 0;\n\n        for (var index = 0; index < state.orderedModifiers.length; index++) {\n          if (process.env.NODE_ENV !== \"production\") {\n            __debug_loops__ += 1;\n\n            if (__debug_loops__ > 100) {\n              console.error(INFINITE_LOOP_ERROR);\n              break;\n            }\n          }\n\n          if (state.reset === true) {\n            state.reset = false;\n            index = -1;\n            continue;\n          }\n\n          var _state$orderedModifie = state.orderedModifiers[index],\n              fn = _state$orderedModifie.fn,\n              _state$orderedModifie2 = _state$orderedModifie.options,\n              _options = _state$orderedModifie2 === void 0 ? {} : _state$orderedModifie2,\n              name = _state$orderedModifie.name;\n\n          if (typeof fn === 'function') {\n            state = fn({\n              state: state,\n              options: _options,\n              name: name,\n              instance: instance\n            }) || state;\n          }\n        }\n      },\n      // Async and optimistically optimized update – it will not be executed if\n      // not necessary (debounced to run at most once-per-tick)\n      update: debounce(function () {\n        return new Promise(function (resolve) {\n          instance.forceUpdate();\n          resolve(state);\n        });\n      }),\n      destroy: function destroy() {\n        cleanupModifierEffects();\n        isDestroyed = true;\n      }\n    };\n\n    if (!areValidElements(reference, popper)) {\n      if (process.env.NODE_ENV !== \"production\") {\n        console.error(INVALID_ELEMENT_ERROR);\n      }\n\n      return instance;\n    }\n\n    instance.setOptions(options).then(function (state) {\n      if (!isDestroyed && options.onFirstUpdate) {\n        options.onFirstUpdate(state);\n      }\n    }); // Modifiers have the ability to execute arbitrary code before the first\n    // update cycle runs. They will be executed in the same order as the update\n    // cycle. This is useful when a modifier adds some persistent data that\n    // other modifiers need to use, but the modifier is run after the dependent\n    // one.\n\n    function runModifierEffects() {\n      state.orderedModifiers.forEach(function (_ref3) {\n        var name = _ref3.name,\n            _ref3$options = _ref3.options,\n            options = _ref3$options === void 0 ? {} : _ref3$options,\n            effect = _ref3.effect;\n\n        if (typeof effect === 'function') {\n          var cleanupFn = effect({\n            state: state,\n            name: name,\n            instance: instance,\n            options: options\n          });\n\n          var noopFn = function noopFn() {};\n\n          effectCleanupFns.push(cleanupFn || noopFn);\n        }\n      });\n    }\n\n    function cleanupModifierEffects() {\n      effectCleanupFns.forEach(function (fn) {\n        return fn();\n      });\n      effectCleanupFns = [];\n    }\n\n    return instance;\n  };\n}\nexport var createPopper = /*#__PURE__*/popperGenerator(); // eslint-disable-next-line import/no-unused-modules\n\nexport { detectOverflow };"], "mappings": "AAAA,OAAOA,gBAAP,MAA6B,iCAA7B;AACA,OAAOC,aAAP,MAA0B,8BAA1B;AACA,OAAOC,iBAAP,MAA8B,kCAA9B;AACA,OAAOC,eAAP,MAA4B,gCAA5B;AACA,OAAOC,gBAAP,MAA6B,iCAA7B;AACA,OAAOC,cAAP,MAA2B,2BAA3B;AACA,OAAOC,QAAP,MAAqB,qBAArB;AACA,OAAOC,iBAAP,MAA8B,8BAA9B;AACA,OAAOC,QAAP,MAAqB,qBAArB;AACA,OAAOC,gBAAP,MAA6B,6BAA7B;AACA,OAAOC,WAAP,MAAwB,wBAAxB;AACA,OAAOC,cAAP,MAA2B,2BAA3B;AACA,SAASC,SAAT,QAA0B,2BAA1B;AACA,SAASC,IAAT,QAAqB,YAArB;AACA,IAAIC,qBAAqB,GAAG,8GAA5B;AACA,IAAIC,mBAAmB,GAAG,+HAA1B;AACA,IAAIC,eAAe,GAAG;EACpBC,SAAS,EAAE,QADS;EAEpBC,SAAS,EAAE,EAFS;EAGpBC,QAAQ,EAAE;AAHU,CAAtB;;AAMA,SAASC,gBAAT,GAA4B;EAC1B,KAAK,IAAIC,IAAI,GAAGC,SAAS,CAACC,MAArB,EAA6BC,IAAI,GAAG,IAAIC,KAAJ,CAAUJ,IAAV,CAApC,EAAqDK,IAAI,GAAG,CAAjE,EAAoEA,IAAI,GAAGL,IAA3E,EAAiFK,IAAI,EAArF,EAAyF;IACvFF,IAAI,CAACE,IAAD,CAAJ,GAAaJ,SAAS,CAACI,IAAD,CAAtB;EACD;;EAED,OAAO,CAACF,IAAI,CAACG,IAAL,CAAU,UAAUC,OAAV,EAAmB;IACnC,OAAO,EAAEA,OAAO,IAAI,OAAOA,OAAO,CAACC,qBAAf,KAAyC,UAAtD,CAAP;EACD,CAFO,CAAR;AAGD;;AAED,OAAO,SAASC,eAAT,CAAyBC,gBAAzB,EAA2C;EAChD,IAAIA,gBAAgB,KAAK,KAAK,CAA9B,EAAiC;IAC/BA,gBAAgB,GAAG,EAAnB;EACD;;EAED,IAAIC,iBAAiB,GAAGD,gBAAxB;EAAA,IACIE,qBAAqB,GAAGD,iBAAiB,CAACE,gBAD9C;EAAA,IAEIA,gBAAgB,GAAGD,qBAAqB,KAAK,KAAK,CAA/B,GAAmC,EAAnC,GAAwCA,qBAF/D;EAAA,IAGIE,sBAAsB,GAAGH,iBAAiB,CAACI,cAH/C;EAAA,IAIIA,cAAc,GAAGD,sBAAsB,KAAK,KAAK,CAAhC,GAAoCnB,eAApC,GAAsDmB,sBAJ3E;EAKA,OAAO,SAASE,YAAT,CAAsBC,SAAtB,EAAiCC,MAAjC,EAAyCC,OAAzC,EAAkD;IACvD,IAAIA,OAAO,KAAK,KAAK,CAArB,EAAwB;MACtBA,OAAO,GAAGJ,cAAV;IACD;;IAED,IAAIK,KAAK,GAAG;MACVxB,SAAS,EAAE,QADD;MAEVyB,gBAAgB,EAAE,EAFR;MAGVF,OAAO,EAAEG,MAAM,CAACC,MAAP,CAAc,EAAd,EAAkB5B,eAAlB,EAAmCoB,cAAnC,CAHC;MAIVS,aAAa,EAAE,EAJL;MAKVC,QAAQ,EAAE;QACRR,SAAS,EAAEA,SADH;QAERC,MAAM,EAAEA;MAFA,CALA;MASVQ,UAAU,EAAE,EATF;MAUVC,MAAM,EAAE;IAVE,CAAZ;IAYA,IAAIC,gBAAgB,GAAG,EAAvB;IACA,IAAIC,WAAW,GAAG,KAAlB;IACA,IAAIC,QAAQ,GAAG;MACbV,KAAK,EAAEA,KADM;MAEbW,UAAU,EAAE,SAASA,UAAT,CAAoBC,gBAApB,EAAsC;QAChD,IAAIb,OAAO,GAAG,OAAOa,gBAAP,KAA4B,UAA5B,GAAyCA,gBAAgB,CAACZ,KAAK,CAACD,OAAP,CAAzD,GAA2Ea,gBAAzF;QACAC,sBAAsB;QACtBb,KAAK,CAACD,OAAN,GAAgBG,MAAM,CAACC,MAAP,CAAc,EAAd,EAAkBR,cAAlB,EAAkCK,KAAK,CAACD,OAAxC,EAAiDA,OAAjD,CAAhB;QACAC,KAAK,CAACc,aAAN,GAAsB;UACpBjB,SAAS,EAAE1B,SAAS,CAAC0B,SAAD,CAAT,GAAuBpC,iBAAiB,CAACoC,SAAD,CAAxC,GAAsDA,SAAS,CAACkB,cAAV,GAA2BtD,iBAAiB,CAACoC,SAAS,CAACkB,cAAX,CAA5C,GAAyE,EADtH;UAEpBjB,MAAM,EAAErC,iBAAiB,CAACqC,MAAD;QAFL,CAAtB,CAJgD,CAO7C;QACH;;QAEA,IAAIG,gBAAgB,GAAGrC,cAAc,CAACK,WAAW,CAAC,GAAG+C,MAAH,CAAUvB,gBAAV,EAA4BO,KAAK,CAACD,OAAN,CAActB,SAA1C,CAAD,CAAZ,CAArC,CAVgD,CAU0D;;QAE1GuB,KAAK,CAACC,gBAAN,GAAyBA,gBAAgB,CAACgB,MAAjB,CAAwB,UAAUC,CAAV,EAAa;UAC5D,OAAOA,CAAC,CAACC,OAAT;QACD,CAFwB,CAAzB,CAZgD,CAc5C;QACJ;;QAEA,IAAIC,OAAO,CAACC,GAAR,CAAYC,QAAZ,KAAyB,YAA7B,EAA2C;UACzC,IAAI7C,SAAS,GAAGV,QAAQ,CAAC,GAAGiD,MAAH,CAAUf,gBAAV,EAA4BD,KAAK,CAACD,OAAN,CAActB,SAA1C,CAAD,EAAuD,UAAU8C,IAAV,EAAgB;YAC7F,IAAIC,IAAI,GAAGD,IAAI,CAACC,IAAhB;YACA,OAAOA,IAAP;UACD,CAHuB,CAAxB;UAIA1D,iBAAiB,CAACW,SAAD,CAAjB;;UAEA,IAAIT,gBAAgB,CAACgC,KAAK,CAACD,OAAN,CAAcvB,SAAf,CAAhB,KAA8CJ,IAAlD,EAAwD;YACtD,IAAIqD,YAAY,GAAGzB,KAAK,CAACC,gBAAN,CAAuByB,IAAvB,CAA4B,UAAUC,KAAV,EAAiB;cAC9D,IAAIH,IAAI,GAAGG,KAAK,CAACH,IAAjB;cACA,OAAOA,IAAI,KAAK,MAAhB;YACD,CAHkB,CAAnB;;YAKA,IAAI,CAACC,YAAL,EAAmB;cACjBG,OAAO,CAACC,KAAR,CAAc,CAAC,0DAAD,EAA6D,8BAA7D,EAA6FC,IAA7F,CAAkG,GAAlG,CAAd;YACD;UACF;;UAED,IAAIC,iBAAiB,GAAGpE,gBAAgB,CAACmC,MAAD,CAAxC;UAAA,IACIkC,SAAS,GAAGD,iBAAiB,CAACC,SADlC;UAAA,IAEIC,WAAW,GAAGF,iBAAiB,CAACE,WAFpC;UAAA,IAGIC,YAAY,GAAGH,iBAAiB,CAACG,YAHrC;UAAA,IAIIC,UAAU,GAAGJ,iBAAiB,CAACI,UAJnC,CAlByC,CAsBM;UAC/C;;;UAGA,IAAI,CAACH,SAAD,EAAYC,WAAZ,EAAyBC,YAAzB,EAAuCC,UAAvC,EAAmDjD,IAAnD,CAAwD,UAAUkD,MAAV,EAAkB;YAC5E,OAAOC,UAAU,CAACD,MAAD,CAAjB;UACD,CAFG,CAAJ,EAEI;YACFR,OAAO,CAACU,IAAR,CAAa,CAAC,6DAAD,EAAgE,2DAAhE,EAA6H,4DAA7H,EAA2L,0DAA3L,EAAuP,YAAvP,EAAqQR,IAArQ,CAA0Q,GAA1Q,CAAb;UACD;QACF;;QAEDS,kBAAkB;QAClB,OAAO7B,QAAQ,CAAC8B,MAAT,EAAP;MACD,CAtDY;MAuDb;MACA;MACA;MACA;MACA;MACAC,WAAW,EAAE,SAASA,WAAT,GAAuB;QAClC,IAAIhC,WAAJ,EAAiB;UACf;QACD;;QAED,IAAIiC,eAAe,GAAG1C,KAAK,CAACK,QAA5B;QAAA,IACIR,SAAS,GAAG6C,eAAe,CAAC7C,SADhC;QAAA,IAEIC,MAAM,GAAG4C,eAAe,CAAC5C,MAF7B,CALkC,CAOG;QACrC;;QAEA,IAAI,CAACnB,gBAAgB,CAACkB,SAAD,EAAYC,MAAZ,CAArB,EAA0C;UACxC,IAAIsB,OAAO,CAACC,GAAR,CAAYC,QAAZ,KAAyB,YAA7B,EAA2C;YACzCM,OAAO,CAACC,KAAR,CAAcxD,qBAAd;UACD;;UAED;QACD,CAhBiC,CAgBhC;;;QAGF2B,KAAK,CAAC2C,KAAN,GAAc;UACZ9C,SAAS,EAAEtC,gBAAgB,CAACsC,SAAD,EAAYnC,eAAe,CAACoC,MAAD,CAA3B,EAAqCE,KAAK,CAACD,OAAN,CAAcrB,QAAd,KAA2B,OAAhE,CADf;UAEZoB,MAAM,EAAEtC,aAAa,CAACsC,MAAD;QAFT,CAAd,CAnBkC,CAsB/B;QACH;QACA;QACA;QACA;;QAEAE,KAAK,CAAC4C,KAAN,GAAc,KAAd;QACA5C,KAAK,CAACxB,SAAN,GAAkBwB,KAAK,CAACD,OAAN,CAAcvB,SAAhC,CA7BkC,CA6BS;QAC3C;QACA;QACA;;QAEAwB,KAAK,CAACC,gBAAN,CAAuB4C,OAAvB,CAA+B,UAAUC,QAAV,EAAoB;UACjD,OAAO9C,KAAK,CAACI,aAAN,CAAoB0C,QAAQ,CAACtB,IAA7B,IAAqCtB,MAAM,CAACC,MAAP,CAAc,EAAd,EAAkB2C,QAAQ,CAACC,IAA3B,CAA5C;QACD,CAFD;QAGA,IAAIC,eAAe,GAAG,CAAtB;;QAEA,KAAK,IAAIC,KAAK,GAAG,CAAjB,EAAoBA,KAAK,GAAGjD,KAAK,CAACC,gBAAN,CAAuBnB,MAAnD,EAA2DmE,KAAK,EAAhE,EAAoE;UAClE,IAAI7B,OAAO,CAACC,GAAR,CAAYC,QAAZ,KAAyB,YAA7B,EAA2C;YACzC0B,eAAe,IAAI,CAAnB;;YAEA,IAAIA,eAAe,GAAG,GAAtB,EAA2B;cACzBpB,OAAO,CAACC,KAAR,CAAcvD,mBAAd;cACA;YACD;UACF;;UAED,IAAI0B,KAAK,CAAC4C,KAAN,KAAgB,IAApB,EAA0B;YACxB5C,KAAK,CAAC4C,KAAN,GAAc,KAAd;YACAK,KAAK,GAAG,CAAC,CAAT;YACA;UACD;;UAED,IAAIC,qBAAqB,GAAGlD,KAAK,CAACC,gBAAN,CAAuBgD,KAAvB,CAA5B;UAAA,IACIE,EAAE,GAAGD,qBAAqB,CAACC,EAD/B;UAAA,IAEIC,sBAAsB,GAAGF,qBAAqB,CAACnD,OAFnD;UAAA,IAGIsD,QAAQ,GAAGD,sBAAsB,KAAK,KAAK,CAAhC,GAAoC,EAApC,GAAyCA,sBAHxD;UAAA,IAII5B,IAAI,GAAG0B,qBAAqB,CAAC1B,IAJjC;;UAMA,IAAI,OAAO2B,EAAP,KAAc,UAAlB,EAA8B;YAC5BnD,KAAK,GAAGmD,EAAE,CAAC;cACTnD,KAAK,EAAEA,KADE;cAETD,OAAO,EAAEsD,QAFA;cAGT7B,IAAI,EAAEA,IAHG;cAITd,QAAQ,EAAEA;YAJD,CAAD,CAAF,IAKFV,KALN;UAMD;QACF;MACF,CAlIY;MAmIb;MACA;MACAwC,MAAM,EAAE3E,QAAQ,CAAC,YAAY;QAC3B,OAAO,IAAIyF,OAAJ,CAAY,UAAUC,OAAV,EAAmB;UACpC7C,QAAQ,CAAC+B,WAAT;UACAc,OAAO,CAACvD,KAAD,CAAP;QACD,CAHM,CAAP;MAID,CALe,CArIH;MA2IbwD,OAAO,EAAE,SAASA,OAAT,GAAmB;QAC1B3C,sBAAsB;QACtBJ,WAAW,GAAG,IAAd;MACD;IA9IY,CAAf;;IAiJA,IAAI,CAAC9B,gBAAgB,CAACkB,SAAD,EAAYC,MAAZ,CAArB,EAA0C;MACxC,IAAIsB,OAAO,CAACC,GAAR,CAAYC,QAAZ,KAAyB,YAA7B,EAA2C;QACzCM,OAAO,CAACC,KAAR,CAAcxD,qBAAd;MACD;;MAED,OAAOqC,QAAP;IACD;;IAEDA,QAAQ,CAACC,UAAT,CAAoBZ,OAApB,EAA6B0D,IAA7B,CAAkC,UAAUzD,KAAV,EAAiB;MACjD,IAAI,CAACS,WAAD,IAAgBV,OAAO,CAAC2D,aAA5B,EAA2C;QACzC3D,OAAO,CAAC2D,aAAR,CAAsB1D,KAAtB;MACD;IACF,CAJD,EA5KuD,CAgLnD;IACJ;IACA;IACA;IACA;;IAEA,SAASuC,kBAAT,GAA8B;MAC5BvC,KAAK,CAACC,gBAAN,CAAuB4C,OAAvB,CAA+B,UAAUc,KAAV,EAAiB;QAC9C,IAAInC,IAAI,GAAGmC,KAAK,CAACnC,IAAjB;QAAA,IACIoC,aAAa,GAAGD,KAAK,CAAC5D,OAD1B;QAAA,IAEIA,OAAO,GAAG6D,aAAa,KAAK,KAAK,CAAvB,GAA2B,EAA3B,GAAgCA,aAF9C;QAAA,IAGIC,MAAM,GAAGF,KAAK,CAACE,MAHnB;;QAKA,IAAI,OAAOA,MAAP,KAAkB,UAAtB,EAAkC;UAChC,IAAIC,SAAS,GAAGD,MAAM,CAAC;YACrB7D,KAAK,EAAEA,KADc;YAErBwB,IAAI,EAAEA,IAFe;YAGrBd,QAAQ,EAAEA,QAHW;YAIrBX,OAAO,EAAEA;UAJY,CAAD,CAAtB;;UAOA,IAAIgE,MAAM,GAAG,SAASA,MAAT,GAAkB,CAAE,CAAjC;;UAEAvD,gBAAgB,CAACwD,IAAjB,CAAsBF,SAAS,IAAIC,MAAnC;QACD;MACF,CAlBD;IAmBD;;IAED,SAASlD,sBAAT,GAAkC;MAChCL,gBAAgB,CAACqC,OAAjB,CAAyB,UAAUM,EAAV,EAAc;QACrC,OAAOA,EAAE,EAAT;MACD,CAFD;MAGA3C,gBAAgB,GAAG,EAAnB;IACD;;IAED,OAAOE,QAAP;EACD,CApND;AAqND;AACD,OAAO,IAAId,YAAY,GAAG,aAAaP,eAAe,EAA/C,C,CAAmD;;AAE1D,SAASnB,cAAT"}, "metadata": {}, "sourceType": "module"}