{"ast": null, "code": "import { createMatAlgo02xDS0 } from '../../type/matrix/utils/matAlgo02xDS0.js';\nimport { createMatAlgo11xS0s } from '../../type/matrix/utils/matAlgo11xS0s.js';\nimport { createMatAlgo14xDs } from '../../type/matrix/utils/matAlgo14xDs.js';\nimport { createMatAlgo01xDSid } from '../../type/matrix/utils/matAlgo01xDSid.js';\nimport { createMatAlgo10xSids } from '../../type/matrix/utils/matAlgo10xSids.js';\nimport { createMatAlgo08xS0Sid } from '../../type/matrix/utils/matAlgo08xS0Sid.js';\nimport { factory } from '../../utils/factory.js';\nimport { createMatrixAlgorithmSuite } from '../../type/matrix/utils/matrixAlgorithmSuite.js';\nimport { createUseMatrixForArrayScalar } from './useMatrixForArrayScalar.js';\nimport { leftShiftNumber } from '../../plain/number/index.js';\nimport { leftShiftBigNumber } from '../../utils/bignumber/bitwise.js';\nvar name = 'leftShift';\nvar dependencies = ['typed', 'matrix', 'equalScalar', 'zeros', 'DenseMatrix', 'concat'];\nexport var createLeftShift = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed,\n    matrix,\n    equalScalar,\n    zeros,\n    DenseMatrix,\n    concat\n  } = _ref;\n  var matAlgo01xDSid = createMatAlgo01xDSid({\n    typed\n  });\n  var matAlgo02xDS0 = createMatAlgo02xDS0({\n    typed,\n    equalScalar\n  });\n  var matAlgo08xS0Sid = createMatAlgo08xS0Sid({\n    typed,\n    equalScalar\n  });\n  var matAlgo10xSids = createMatAlgo10xSids({\n    typed,\n    DenseMatrix\n  });\n  var matAlgo11xS0s = createMatAlgo11xS0s({\n    typed,\n    equalScalar\n  });\n  var matAlgo14xDs = createMatAlgo14xDs({\n    typed\n  });\n  var matrixAlgorithmSuite = createMatrixAlgorithmSuite({\n    typed,\n    matrix,\n    concat\n  });\n  var useMatrixForArrayScalar = createUseMatrixForArrayScalar({\n    typed,\n    matrix\n  });\n  /**\n   * Bitwise left logical shift of a value x by y number of bits, `x << y`.\n   * For matrices, the function is evaluated element wise.\n   * For units, the function is evaluated on the best prefix base.\n   *\n   * Syntax:\n   *\n   *    math.leftShift(x, y)\n   *\n   * Examples:\n   *\n   *    math.leftShift(1, 2)               // returns number 4\n   *\n   *    math.leftShift([1, 2, 4], 4)       // returns Array [16, 32, 64]\n   *\n   * See also:\n   *\n   *    leftShift, bitNot, bitOr, bitXor, rightArithShift, rightLogShift\n   *\n   * @param  {number | BigNumber | bigint | Array | Matrix} x Value to be shifted\n   * @param  {number | BigNumber | bigint} y Amount of shifts\n   * @return {number | BigNumber | bigint | Array | Matrix} `x` shifted left `y` times\n   */\n\n  return typed(name, {\n    'number, number': leftShiftNumber,\n    'BigNumber, BigNumber': leftShiftBigNumber,\n    'bigint, bigint': (x, y) => x << y,\n    'SparseMatrix, number | BigNumber': typed.referToSelf(self => (x, y) => {\n      // check scalar\n      if (equalScalar(y, 0)) {\n        return x.clone();\n      }\n\n      return matAlgo11xS0s(x, y, self, false);\n    }),\n    'DenseMatrix, number | BigNumber': typed.referToSelf(self => (x, y) => {\n      // check scalar\n      if (equalScalar(y, 0)) {\n        return x.clone();\n      }\n\n      return matAlgo14xDs(x, y, self, false);\n    }),\n    'number | BigNumber, SparseMatrix': typed.referToSelf(self => (x, y) => {\n      // check scalar\n      if (equalScalar(x, 0)) {\n        return zeros(y.size(), y.storage());\n      }\n\n      return matAlgo10xSids(y, x, self, true);\n    }),\n    'number | BigNumber, DenseMatrix': typed.referToSelf(self => (x, y) => {\n      // check scalar\n      if (equalScalar(x, 0)) {\n        return zeros(y.size(), y.storage());\n      }\n\n      return matAlgo14xDs(y, x, self, true);\n    })\n  }, useMatrixForArrayScalar, matrixAlgorithmSuite({\n    SS: matAlgo08xS0Sid,\n    DS: matAlgo01xDSid,\n    SD: matAlgo02xDS0\n  }));\n});", "map": null, "metadata": {}, "sourceType": "module"}