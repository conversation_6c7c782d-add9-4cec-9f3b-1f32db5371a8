{"ast": null, "code": "import { arraySize as size } from '../../utils/array.js';\nimport { factory } from '../../utils/factory.js';\nvar name = 'sort';\nvar dependencies = ['typed', 'matrix', 'compare', 'compareNatural'];\nexport var createSort = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed,\n    matrix,\n    compare,\n    compareNatural\n  } = _ref;\n  var compareAsc = compare;\n\n  var compareDesc = (a, b) => -compare(a, b);\n  /**\n   * Sort the items in a matrix.\n   *\n   * Syntax:\n   *\n   *    math.sort(x)\n   *    math.sort(x, compare)\n   *\n   * Examples:\n   *\n   *    math.sort([5, 10, 1]) // returns [1, 5, 10]\n   *    math.sort(['C', 'B', 'A', 'D'], math.compareNatural)\n   *    // returns ['A', 'B', 'C', 'D']\n   *\n   *    function sortByLength (a, b) {\n   *      return a.length - b.length\n   *    }\n   *    math.sort(['<PERSON>', '<PERSON>', '<PERSON>'], sortByLength)\n   *    // returns ['<PERSON>', '<PERSON>', '<PERSON>']\n   *\n   * See also:\n   *\n   *    filter, forEach, map, compare, compareNatural\n   *\n   * @param {Matrix | Array} x    A one dimensional matrix or array to sort\n   * @param {Function | 'asc' | 'desc' | 'natural'} [compare='asc']\n   *        An optional _comparator function or name. The function is called as\n   *        `compare(a, b)`, and must return 1 when a > b, -1 when a < b,\n   *        and 0 when a == b.\n   * @return {Matrix | Array} Returns the sorted matrix.\n   */\n\n\n  return typed(name, {\n    Array: function Array(x) {\n      _arrayIsVector(x);\n\n      return x.sort(compareAsc);\n    },\n    Matrix: function Matrix(x) {\n      _matrixIsVector(x);\n\n      return matrix(x.toArray().sort(compareAsc), x.storage());\n    },\n    'Array, function': function Array_function(x, _comparator) {\n      _arrayIsVector(x);\n\n      return x.sort(_comparator);\n    },\n    'Matrix, function': function Matrix_function(x, _comparator) {\n      _matrixIsVector(x);\n\n      return matrix(x.toArray().sort(_comparator), x.storage());\n    },\n    'Array, string': function Array_string(x, order) {\n      _arrayIsVector(x);\n\n      return x.sort(_comparator(order));\n    },\n    'Matrix, string': function Matrix_string(x, order) {\n      _matrixIsVector(x);\n\n      return matrix(x.toArray().sort(_comparator(order)), x.storage());\n    }\n  });\n  /**\n   * Get the comparator for given order ('asc', 'desc', 'natural')\n   * @param {'asc' | 'desc' | 'natural'} order\n   * @return {Function} Returns a _comparator function\n   */\n\n  function _comparator(order) {\n    if (order === 'asc') {\n      return compareAsc;\n    } else if (order === 'desc') {\n      return compareDesc;\n    } else if (order === 'natural') {\n      return compareNatural;\n    } else {\n      throw new Error('String \"asc\", \"desc\", or \"natural\" expected');\n    }\n  }\n  /**\n   * Validate whether an array is one dimensional\n   * Throws an error when this is not the case\n   * @param {Array} array\n   * @private\n   */\n\n\n  function _arrayIsVector(array) {\n    if (size(array).length !== 1) {\n      throw new Error('One dimensional array expected');\n    }\n  }\n  /**\n   * Validate whether a matrix is one dimensional\n   * Throws an error when this is not the case\n   * @param {Matrix} matrix\n   * @private\n   */\n\n\n  function _matrixIsVector(matrix) {\n    if (matrix.size().length !== 1) {\n      throw new Error('One dimensional matrix expected');\n    }\n  }\n});", "map": null, "metadata": {}, "sourceType": "module"}