{"ast": null, "code": "import Fraction from 'fraction.js';\nimport { factory } from '../../utils/factory.js';\nvar name = 'Fraction';\nvar dependencies = [];\nexport var createFractionClass = /* #__PURE__ */factory(name, dependencies, () => {\n  /**\n   * Attach type information\n   */\n  Object.defineProperty(Fraction, 'name', {\n    value: 'Fraction'\n  });\n  Fraction.prototype.constructor = Fraction;\n  Fraction.prototype.type = 'Fraction';\n  Fraction.prototype.isFraction = true;\n  /**\n   * Get a JSON representation of a Fraction containing type information\n   * @returns {Object} Returns a JSON object structured as:\n   *                   `{\"mathjs\": \"Fraction\", \"n\": \"3\", \"d\": \"8\"}`\n   */\n\n  Fraction.prototype.toJSON = function () {\n    return {\n      mathjs: 'Fraction',\n      n: String(this.s * this.n),\n      d: String(this.d)\n    };\n  };\n  /**\n   * Instantiate a Fraction from a JSON object\n   * @param {Object} json  a JSON object structured as:\n   *                       `{\"mathjs\": \"Fraction\", \"n\": \"3\", \"d\": \"8\"}`\n   * @return {BigNumber}\n   */\n\n\n  Fraction.fromJSON = function (json) {\n    return new Fraction(json);\n  };\n\n  return Fraction;\n}, {\n  isClass: true\n});", "map": {"version": 3, "names": ["Fraction", "factory", "name", "dependencies", "createFractionClass", "Object", "defineProperty", "value", "prototype", "constructor", "type", "isFraction", "toJSON", "mathjs", "n", "String", "s", "d", "fromJSON", "json", "isClass"], "sources": ["D:/work/joyserver/client/node_modules/mathjs/lib/esm/type/fraction/Fraction.js"], "sourcesContent": ["import Fraction from 'fraction.js';\nimport { factory } from '../../utils/factory.js';\nvar name = 'Fraction';\nvar dependencies = [];\nexport var createFractionClass = /* #__PURE__ */factory(name, dependencies, () => {\n  /**\n   * Attach type information\n   */\n  Object.defineProperty(Fraction, 'name', {\n    value: 'Fraction'\n  });\n  Fraction.prototype.constructor = Fraction;\n  Fraction.prototype.type = 'Fraction';\n  Fraction.prototype.isFraction = true;\n\n  /**\n   * Get a JSON representation of a Fraction containing type information\n   * @returns {Object} Returns a JSON object structured as:\n   *                   `{\"mathjs\": \"Fraction\", \"n\": \"3\", \"d\": \"8\"}`\n   */\n  Fraction.prototype.toJSON = function () {\n    return {\n      mathjs: 'Fraction',\n      n: String(this.s * this.n),\n      d: String(this.d)\n    };\n  };\n\n  /**\n   * Instantiate a Fraction from a JSON object\n   * @param {Object} json  a JSON object structured as:\n   *                       `{\"mathjs\": \"Fraction\", \"n\": \"3\", \"d\": \"8\"}`\n   * @return {BigNumber}\n   */\n  Fraction.fromJSON = function (json) {\n    return new Fraction(json);\n  };\n  return Fraction;\n}, {\n  isClass: true\n});"], "mappings": "AAAA,OAAOA,QAAP,MAAqB,aAArB;AACA,SAASC,OAAT,QAAwB,wBAAxB;AACA,IAAIC,IAAI,GAAG,UAAX;AACA,IAAIC,YAAY,GAAG,EAAnB;AACA,OAAO,IAAIC,mBAAmB,GAAG,eAAeH,OAAO,CAACC,IAAD,EAAOC,YAAP,EAAqB,MAAM;EAChF;AACF;AACA;EACEE,MAAM,CAACC,cAAP,CAAsBN,QAAtB,EAAgC,MAAhC,EAAwC;IACtCO,KAAK,EAAE;EAD+B,CAAxC;EAGAP,QAAQ,CAACQ,SAAT,CAAmBC,WAAnB,GAAiCT,QAAjC;EACAA,QAAQ,CAACQ,SAAT,CAAmBE,IAAnB,GAA0B,UAA1B;EACAV,QAAQ,CAACQ,SAAT,CAAmBG,UAAnB,GAAgC,IAAhC;EAEA;AACF;AACA;AACA;AACA;;EACEX,QAAQ,CAACQ,SAAT,CAAmBI,MAAnB,GAA4B,YAAY;IACtC,OAAO;MACLC,MAAM,EAAE,UADH;MAELC,CAAC,EAAEC,MAAM,CAAC,KAAKC,CAAL,GAAS,KAAKF,CAAf,CAFJ;MAGLG,CAAC,EAAEF,MAAM,CAAC,KAAKE,CAAN;IAHJ,CAAP;EAKD,CAND;EAQA;AACF;AACA;AACA;AACA;AACA;;;EACEjB,QAAQ,CAACkB,QAAT,GAAoB,UAAUC,IAAV,EAAgB;IAClC,OAAO,IAAInB,QAAJ,CAAamB,IAAb,CAAP;EACD,CAFD;;EAGA,OAAOnB,QAAP;AACD,CAlCsD,EAkCpD;EACDoB,OAAO,EAAE;AADR,CAlCoD,CAAhD"}, "metadata": {}, "sourceType": "module"}