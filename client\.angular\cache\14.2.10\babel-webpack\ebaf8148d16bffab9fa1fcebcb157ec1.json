{"ast": null, "code": "import { isFunction } from './util/isFunction';\nimport { empty as emptyObserver } from './Observer';\nimport { Subscription } from './Subscription';\nimport { rxSubscriber as rxSubscriberSymbol } from '../internal/symbol/rxSubscriber';\nimport { config } from './config';\nimport { hostReportError } from './util/hostReportError';\nexport class Subscriber extends Subscription {\n  constructor(destinationOrNext, error, complete) {\n    super();\n    this.syncErrorValue = null;\n    this.syncErrorThrown = false;\n    this.syncErrorThrowable = false;\n    this.isStopped = false;\n\n    switch (arguments.length) {\n      case 0:\n        this.destination = emptyObserver;\n        break;\n\n      case 1:\n        if (!destinationOrNext) {\n          this.destination = emptyObserver;\n          break;\n        }\n\n        if (typeof destinationOrNext === 'object') {\n          if (destinationOrNext instanceof Subscriber) {\n            this.syncErrorThrowable = destinationOrNext.syncErrorThrowable;\n            this.destination = destinationOrNext;\n            destinationOrNext.add(this);\n          } else {\n            this.syncErrorThrowable = true;\n            this.destination = new SafeSubscriber(this, destinationOrNext);\n          }\n\n          break;\n        }\n\n      default:\n        this.syncErrorThrowable = true;\n        this.destination = new SafeSubscriber(this, destinationOrNext, error, complete);\n        break;\n    }\n  }\n\n  [rxSubscriberSymbol]() {\n    return this;\n  }\n\n  static create(next, error, complete) {\n    const subscriber = new Subscriber(next, error, complete);\n    subscriber.syncErrorThrowable = false;\n    return subscriber;\n  }\n\n  next(value) {\n    if (!this.isStopped) {\n      this._next(value);\n    }\n  }\n\n  error(err) {\n    if (!this.isStopped) {\n      this.isStopped = true;\n\n      this._error(err);\n    }\n  }\n\n  complete() {\n    if (!this.isStopped) {\n      this.isStopped = true;\n\n      this._complete();\n    }\n  }\n\n  unsubscribe() {\n    if (this.closed) {\n      return;\n    }\n\n    this.isStopped = true;\n    super.unsubscribe();\n  }\n\n  _next(value) {\n    this.destination.next(value);\n  }\n\n  _error(err) {\n    this.destination.error(err);\n    this.unsubscribe();\n  }\n\n  _complete() {\n    this.destination.complete();\n    this.unsubscribe();\n  }\n\n  _unsubscribeAndRecycle() {\n    const {\n      _parentOrParents\n    } = this;\n    this._parentOrParents = null;\n    this.unsubscribe();\n    this.closed = false;\n    this.isStopped = false;\n    this._parentOrParents = _parentOrParents;\n    return this;\n  }\n\n}\nexport class SafeSubscriber extends Subscriber {\n  constructor(_parentSubscriber, observerOrNext, error, complete) {\n    super();\n    this._parentSubscriber = _parentSubscriber;\n    let next;\n    let context = this;\n\n    if (isFunction(observerOrNext)) {\n      next = observerOrNext;\n    } else if (observerOrNext) {\n      next = observerOrNext.next;\n      error = observerOrNext.error;\n      complete = observerOrNext.complete;\n\n      if (observerOrNext !== emptyObserver) {\n        context = Object.create(observerOrNext);\n\n        if (isFunction(context.unsubscribe)) {\n          this.add(context.unsubscribe.bind(context));\n        }\n\n        context.unsubscribe = this.unsubscribe.bind(this);\n      }\n    }\n\n    this._context = context;\n    this._next = next;\n    this._error = error;\n    this._complete = complete;\n  }\n\n  next(value) {\n    if (!this.isStopped && this._next) {\n      const {\n        _parentSubscriber\n      } = this;\n\n      if (!config.useDeprecatedSynchronousErrorHandling || !_parentSubscriber.syncErrorThrowable) {\n        this.__tryOrUnsub(this._next, value);\n      } else if (this.__tryOrSetError(_parentSubscriber, this._next, value)) {\n        this.unsubscribe();\n      }\n    }\n  }\n\n  error(err) {\n    if (!this.isStopped) {\n      const {\n        _parentSubscriber\n      } = this;\n      const {\n        useDeprecatedSynchronousErrorHandling\n      } = config;\n\n      if (this._error) {\n        if (!useDeprecatedSynchronousErrorHandling || !_parentSubscriber.syncErrorThrowable) {\n          this.__tryOrUnsub(this._error, err);\n\n          this.unsubscribe();\n        } else {\n          this.__tryOrSetError(_parentSubscriber, this._error, err);\n\n          this.unsubscribe();\n        }\n      } else if (!_parentSubscriber.syncErrorThrowable) {\n        this.unsubscribe();\n\n        if (useDeprecatedSynchronousErrorHandling) {\n          throw err;\n        }\n\n        hostReportError(err);\n      } else {\n        if (useDeprecatedSynchronousErrorHandling) {\n          _parentSubscriber.syncErrorValue = err;\n          _parentSubscriber.syncErrorThrown = true;\n        } else {\n          hostReportError(err);\n        }\n\n        this.unsubscribe();\n      }\n    }\n  }\n\n  complete() {\n    if (!this.isStopped) {\n      const {\n        _parentSubscriber\n      } = this;\n\n      if (this._complete) {\n        const wrappedComplete = () => this._complete.call(this._context);\n\n        if (!config.useDeprecatedSynchronousErrorHandling || !_parentSubscriber.syncErrorThrowable) {\n          this.__tryOrUnsub(wrappedComplete);\n\n          this.unsubscribe();\n        } else {\n          this.__tryOrSetError(_parentSubscriber, wrappedComplete);\n\n          this.unsubscribe();\n        }\n      } else {\n        this.unsubscribe();\n      }\n    }\n  }\n\n  __tryOrUnsub(fn, value) {\n    try {\n      fn.call(this._context, value);\n    } catch (err) {\n      this.unsubscribe();\n\n      if (config.useDeprecatedSynchronousErrorHandling) {\n        throw err;\n      } else {\n        hostReportError(err);\n      }\n    }\n  }\n\n  __tryOrSetError(parent, fn, value) {\n    if (!config.useDeprecatedSynchronousErrorHandling) {\n      throw new Error('bad call');\n    }\n\n    try {\n      fn.call(this._context, value);\n    } catch (err) {\n      if (config.useDeprecatedSynchronousErrorHandling) {\n        parent.syncErrorValue = err;\n        parent.syncErrorThrown = true;\n        return true;\n      } else {\n        hostReportError(err);\n        return true;\n      }\n    }\n\n    return false;\n  }\n\n  _unsubscribe() {\n    const {\n      _parentSubscriber\n    } = this;\n    this._context = null;\n    this._parentSubscriber = null;\n\n    _parentSubscriber.unsubscribe();\n  }\n\n} //# sourceMappingURL=Subscriber.js.map", "map": null, "metadata": {}, "sourceType": "module"}