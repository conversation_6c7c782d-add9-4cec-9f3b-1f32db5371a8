{"ast": null, "code": "import { EMPTY } from '../observable/empty';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function takeLast(count) {\n  return count <= 0 ? () => EMPTY : operate((source, subscriber) => {\n    let buffer = [];\n    source.subscribe(createOperatorSubscriber(subscriber, value => {\n      buffer.push(value);\n      count < buffer.length && buffer.shift();\n    }, () => {\n      for (const value of buffer) {\n        subscriber.next(value);\n      }\n\n      subscriber.complete();\n    }, undefined, () => {\n      buffer = null;\n    }));\n  });\n}", "map": {"version": 3, "names": ["EMPTY", "operate", "createOperatorSubscriber", "takeLast", "count", "source", "subscriber", "buffer", "subscribe", "value", "push", "length", "shift", "next", "complete", "undefined"], "sources": ["D:/work/joyserver/client/node_modules/rxjs/dist/esm/internal/operators/takeLast.js"], "sourcesContent": ["import { EMPTY } from '../observable/empty';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function takeLast(count) {\n    return count <= 0\n        ? () => EMPTY\n        : operate((source, subscriber) => {\n            let buffer = [];\n            source.subscribe(createOperatorSubscriber(subscriber, (value) => {\n                buffer.push(value);\n                count < buffer.length && buffer.shift();\n            }, () => {\n                for (const value of buffer) {\n                    subscriber.next(value);\n                }\n                subscriber.complete();\n            }, undefined, () => {\n                buffer = null;\n            }));\n        });\n}\n"], "mappings": "AAAA,SAASA,KAAT,QAAsB,qBAAtB;AACA,SAASC,OAAT,QAAwB,cAAxB;AACA,SAASC,wBAAT,QAAyC,sBAAzC;AACA,OAAO,SAASC,QAAT,CAAkBC,KAAlB,EAAyB;EAC5B,OAAOA,KAAK,IAAI,CAAT,GACD,MAAMJ,KADL,GAEDC,OAAO,CAAC,CAACI,MAAD,EAASC,UAAT,KAAwB;IAC9B,IAAIC,MAAM,GAAG,EAAb;IACAF,MAAM,CAACG,SAAP,CAAiBN,wBAAwB,CAACI,UAAD,EAAcG,KAAD,IAAW;MAC7DF,MAAM,CAACG,IAAP,CAAYD,KAAZ;MACAL,KAAK,GAAGG,MAAM,CAACI,MAAf,IAAyBJ,MAAM,CAACK,KAAP,EAAzB;IACH,CAHwC,EAGtC,MAAM;MACL,KAAK,MAAMH,KAAX,IAAoBF,MAApB,EAA4B;QACxBD,UAAU,CAACO,IAAX,CAAgBJ,KAAhB;MACH;;MACDH,UAAU,CAACQ,QAAX;IACH,CARwC,EAQtCC,SARsC,EAQ3B,MAAM;MAChBR,MAAM,GAAG,IAAT;IACH,CAVwC,CAAzC;EAWH,CAbQ,CAFb;AAgBH"}, "metadata": {}, "sourceType": "module"}