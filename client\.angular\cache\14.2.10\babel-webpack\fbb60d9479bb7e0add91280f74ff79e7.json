{"ast": null, "code": "import { clone, deepExtend } from '../../utils/object.js';\nimport { DEFAULT_CONFIG } from '../config.js';\nexport var MATRIX_OPTIONS = ['Matrix', 'Array']; // valid values for option matrix\n\nexport var NUMBER_OPTIONS = ['number', 'BigNumber', 'Fraction']; // valid values for option number\n\nexport function configFactory(config, emit) {\n  /**\n   * Set configuration options for math.js, and get current options.\n   * Will emit a 'config' event, with arguments (curr, prev, changes).\n   *\n   * This function is only available on a mathjs instance created using `create`.\n   *\n   * Syntax:\n   *\n   *     math.config(config: Object): Object\n   *\n   * Examples:\n   *\n   *\n   *     import { create, all } from 'mathjs'\n   *\n   *     // create a mathjs instance\n   *     const math = create(all)\n   *\n   *     math.config().number                // outputs 'number'\n   *     math.evaluate('0.4')                // outputs number 0.4\n   *     math.config({number: 'Fraction'})\n   *     math.evaluate('0.4')                // outputs Fraction 2/5\n   *\n   * @param {Object} [options] Available options:\n   *                            {number} relTol\n   *                              Minimum relative difference between two\n   *                              compared values, used by all comparison functions.\n   *                            {number} absTol\n   *                              Minimum absolute difference between two\n   *                              compared values, used by all comparison functions.\n   *                            {string} matrix\n   *                              A string 'Matrix' (default) or 'Array'.\n   *                            {string} number\n   *                              A string 'number' (default), 'BigNumber', 'bigint', or 'Fraction'\n   *                            {number} precision\n   *                              The number of significant digits for BigNumbers.\n   *                              Not applicable for Numbers.\n   *                            {string} parenthesis\n   *                              How to display parentheses in LaTeX and string\n   *                              output.\n   *                            {string} randomSeed\n   *                              Random seed for seeded pseudo random number generator.\n   *                              Set to null to randomly seed.\n   * @return {Object} Returns the current configuration\n   */\n  function _config(options) {\n    if (options) {\n      if (options.epsilon !== undefined) {\n        // this if is only for backwards compatibility, it can be removed in the future.\n        console.warn('Warning: The configuration option \"epsilon\" is deprecated. Use \"relTol\" and \"absTol\" instead.');\n        var optionsFix = clone(options);\n        optionsFix.relTol = options.epsilon;\n        optionsFix.absTol = options.epsilon * 1e-3;\n        delete optionsFix.epsilon;\n        return _config(optionsFix);\n      }\n\n      var prev = clone(config); // validate some of the options\n\n      validateOption(options, 'matrix', MATRIX_OPTIONS);\n      validateOption(options, 'number', NUMBER_OPTIONS); // merge options\n\n      deepExtend(config, options);\n      var curr = clone(config);\n      var changes = clone(options); // emit 'config' event\n\n      emit('config', curr, prev, changes);\n      return curr;\n    } else {\n      return clone(config);\n    }\n  } // attach the valid options to the function so they can be extended\n\n\n  _config.MATRIX_OPTIONS = MATRIX_OPTIONS;\n  _config.NUMBER_OPTIONS = NUMBER_OPTIONS; // attach the config properties as readonly properties to the config function\n\n  Object.keys(DEFAULT_CONFIG).forEach(key => {\n    Object.defineProperty(_config, key, {\n      get: () => config[key],\n      enumerable: true,\n      configurable: true\n    });\n  });\n  return _config;\n}\n/**\n * Validate an option\n * @param {Object} options         Object with options\n * @param {string} name            Name of the option to validate\n * @param {Array.<string>} values  Array with valid values for this option\n */\n\nfunction validateOption(options, name, values) {\n  if (options[name] !== undefined && !values.includes(options[name])) {\n    // unknown value\n    console.warn('Warning: Unknown value \"' + options[name] + '\" for configuration option \"' + name + '\". ' + 'Available options: ' + values.map(value => JSON.stringify(value)).join(', ') + '.');\n  }\n}", "map": {"version": 3, "names": ["clone", "deepExtend", "DEFAULT_CONFIG", "MATRIX_OPTIONS", "NUMBER_OPTIONS", "configFactory", "config", "emit", "_config", "options", "epsilon", "undefined", "console", "warn", "optionsFix", "relTol", "absTol", "prev", "validateOption", "curr", "changes", "Object", "keys", "for<PERSON>ach", "key", "defineProperty", "get", "enumerable", "configurable", "name", "values", "includes", "map", "value", "JSON", "stringify", "join"], "sources": ["D:/work/joyserver/client/node_modules/mathjs/lib/esm/core/function/config.js"], "sourcesContent": ["import { clone, deepExtend } from '../../utils/object.js';\nimport { DEFAULT_CONFIG } from '../config.js';\nexport var MATRIX_OPTIONS = ['Matrix', 'Array']; // valid values for option matrix\nexport var NUMBER_OPTIONS = ['number', 'BigNumber', 'Fraction']; // valid values for option number\n\nexport function configFactory(config, emit) {\n  /**\n   * Set configuration options for math.js, and get current options.\n   * Will emit a 'config' event, with arguments (curr, prev, changes).\n   *\n   * This function is only available on a mathjs instance created using `create`.\n   *\n   * Syntax:\n   *\n   *     math.config(config: Object): Object\n   *\n   * Examples:\n   *\n   *\n   *     import { create, all } from 'mathjs'\n   *\n   *     // create a mathjs instance\n   *     const math = create(all)\n   *\n   *     math.config().number                // outputs 'number'\n   *     math.evaluate('0.4')                // outputs number 0.4\n   *     math.config({number: 'Fraction'})\n   *     math.evaluate('0.4')                // outputs Fraction 2/5\n   *\n   * @param {Object} [options] Available options:\n   *                            {number} relTol\n   *                              Minimum relative difference between two\n   *                              compared values, used by all comparison functions.\n   *                            {number} absTol\n   *                              Minimum absolute difference between two\n   *                              compared values, used by all comparison functions.\n   *                            {string} matrix\n   *                              A string 'Matrix' (default) or 'Array'.\n   *                            {string} number\n   *                              A string 'number' (default), 'BigNumber', 'bigint', or 'Fraction'\n   *                            {number} precision\n   *                              The number of significant digits for BigNumbers.\n   *                              Not applicable for Numbers.\n   *                            {string} parenthesis\n   *                              How to display parentheses in LaTeX and string\n   *                              output.\n   *                            {string} randomSeed\n   *                              Random seed for seeded pseudo random number generator.\n   *                              Set to null to randomly seed.\n   * @return {Object} Returns the current configuration\n   */\n  function _config(options) {\n    if (options) {\n      if (options.epsilon !== undefined) {\n        // this if is only for backwards compatibility, it can be removed in the future.\n        console.warn('Warning: The configuration option \"epsilon\" is deprecated. Use \"relTol\" and \"absTol\" instead.');\n        var optionsFix = clone(options);\n        optionsFix.relTol = options.epsilon;\n        optionsFix.absTol = options.epsilon * 1e-3;\n        delete optionsFix.epsilon;\n        return _config(optionsFix);\n      }\n      var prev = clone(config);\n\n      // validate some of the options\n      validateOption(options, 'matrix', MATRIX_OPTIONS);\n      validateOption(options, 'number', NUMBER_OPTIONS);\n\n      // merge options\n      deepExtend(config, options);\n      var curr = clone(config);\n      var changes = clone(options);\n\n      // emit 'config' event\n      emit('config', curr, prev, changes);\n      return curr;\n    } else {\n      return clone(config);\n    }\n  }\n\n  // attach the valid options to the function so they can be extended\n  _config.MATRIX_OPTIONS = MATRIX_OPTIONS;\n  _config.NUMBER_OPTIONS = NUMBER_OPTIONS;\n\n  // attach the config properties as readonly properties to the config function\n  Object.keys(DEFAULT_CONFIG).forEach(key => {\n    Object.defineProperty(_config, key, {\n      get: () => config[key],\n      enumerable: true,\n      configurable: true\n    });\n  });\n  return _config;\n}\n\n/**\n * Validate an option\n * @param {Object} options         Object with options\n * @param {string} name            Name of the option to validate\n * @param {Array.<string>} values  Array with valid values for this option\n */\nfunction validateOption(options, name, values) {\n  if (options[name] !== undefined && !values.includes(options[name])) {\n    // unknown value\n    console.warn('Warning: Unknown value \"' + options[name] + '\" for configuration option \"' + name + '\". ' + 'Available options: ' + values.map(value => JSON.stringify(value)).join(', ') + '.');\n  }\n}"], "mappings": "AAAA,SAASA,KAAT,EAAgBC,UAAhB,QAAkC,uBAAlC;AACA,SAASC,cAAT,QAA+B,cAA/B;AACA,OAAO,IAAIC,cAAc,GAAG,CAAC,QAAD,EAAW,OAAX,CAArB,C,CAA0C;;AACjD,OAAO,IAAIC,cAAc,GAAG,CAAC,QAAD,EAAW,WAAX,EAAwB,UAAxB,CAArB,C,CAA0D;;AAEjE,OAAO,SAASC,aAAT,CAAuBC,MAAvB,EAA+BC,IAA/B,EAAqC;EAC1C;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASC,OAAT,CAAiBC,OAAjB,EAA0B;IACxB,IAAIA,OAAJ,EAAa;MACX,IAAIA,OAAO,CAACC,OAAR,KAAoBC,SAAxB,EAAmC;QACjC;QACAC,OAAO,CAACC,IAAR,CAAa,+FAAb;QACA,IAAIC,UAAU,GAAGd,KAAK,CAACS,OAAD,CAAtB;QACAK,UAAU,CAACC,MAAX,GAAoBN,OAAO,CAACC,OAA5B;QACAI,UAAU,CAACE,MAAX,GAAoBP,OAAO,CAACC,OAAR,GAAkB,IAAtC;QACA,OAAOI,UAAU,CAACJ,OAAlB;QACA,OAAOF,OAAO,CAACM,UAAD,CAAd;MACD;;MACD,IAAIG,IAAI,GAAGjB,KAAK,CAACM,MAAD,CAAhB,CAVW,CAYX;;MACAY,cAAc,CAACT,OAAD,EAAU,QAAV,EAAoBN,cAApB,CAAd;MACAe,cAAc,CAACT,OAAD,EAAU,QAAV,EAAoBL,cAApB,CAAd,CAdW,CAgBX;;MACAH,UAAU,CAACK,MAAD,EAASG,OAAT,CAAV;MACA,IAAIU,IAAI,GAAGnB,KAAK,CAACM,MAAD,CAAhB;MACA,IAAIc,OAAO,GAAGpB,KAAK,CAACS,OAAD,CAAnB,CAnBW,CAqBX;;MACAF,IAAI,CAAC,QAAD,EAAWY,IAAX,EAAiBF,IAAjB,EAAuBG,OAAvB,CAAJ;MACA,OAAOD,IAAP;IACD,CAxBD,MAwBO;MACL,OAAOnB,KAAK,CAACM,MAAD,CAAZ;IACD;EACF,CA1EyC,CA4E1C;;;EACAE,OAAO,CAACL,cAAR,GAAyBA,cAAzB;EACAK,OAAO,CAACJ,cAAR,GAAyBA,cAAzB,CA9E0C,CAgF1C;;EACAiB,MAAM,CAACC,IAAP,CAAYpB,cAAZ,EAA4BqB,OAA5B,CAAoCC,GAAG,IAAI;IACzCH,MAAM,CAACI,cAAP,CAAsBjB,OAAtB,EAA+BgB,GAA/B,EAAoC;MAClCE,GAAG,EAAE,MAAMpB,MAAM,CAACkB,GAAD,CADiB;MAElCG,UAAU,EAAE,IAFsB;MAGlCC,YAAY,EAAE;IAHoB,CAApC;EAKD,CAND;EAOA,OAAOpB,OAAP;AACD;AAED;AACA;AACA;AACA;AACA;AACA;;AACA,SAASU,cAAT,CAAwBT,OAAxB,EAAiCoB,IAAjC,EAAuCC,MAAvC,EAA+C;EAC7C,IAAIrB,OAAO,CAACoB,IAAD,CAAP,KAAkBlB,SAAlB,IAA+B,CAACmB,MAAM,CAACC,QAAP,CAAgBtB,OAAO,CAACoB,IAAD,CAAvB,CAApC,EAAoE;IAClE;IACAjB,OAAO,CAACC,IAAR,CAAa,6BAA6BJ,OAAO,CAACoB,IAAD,CAApC,GAA6C,8BAA7C,GAA8EA,IAA9E,GAAqF,KAArF,GAA6F,qBAA7F,GAAqHC,MAAM,CAACE,GAAP,CAAWC,KAAK,IAAIC,IAAI,CAACC,SAAL,CAAeF,KAAf,CAApB,EAA2CG,IAA3C,CAAgD,IAAhD,CAArH,GAA6K,GAA1L;EACD;AACF"}, "metadata": {}, "sourceType": "module"}