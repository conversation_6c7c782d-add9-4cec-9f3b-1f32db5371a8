{"ast": null, "code": "import { isArray, isBig<PERSON><PERSON>ber, isCollection, isIndex, isMatrix, isNumber, isString, typeOf } from '../../utils/is.js';\nimport { isInteger } from '../../utils/number.js';\nimport { format } from '../../utils/string.js';\nimport { clone, deepStrictEqual } from '../../utils/object.js';\nimport { arraySize, getArrayDataType, processSizesWildcard, unsqueeze, validateIndex } from '../../utils/array.js';\nimport { factory } from '../../utils/factory.js';\nimport { DimensionError } from '../../error/DimensionError.js';\nimport { optimizeCallback } from '../../utils/optimizeCallback.js';\nvar name = 'SparseMatrix';\nvar dependencies = ['typed', 'equalScalar', 'Matrix'];\nexport var createSparseMatrixClass = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed,\n    equalScalar,\n    Matrix\n  } = _ref;\n  /**\n   * Sparse Matrix implementation. This type implements\n   * a [Compressed Column Storage](https://en.wikipedia.org/wiki/Sparse_matrix#Compressed_sparse_column_(CSC_or_CCS))\n   * format for two-dimensional sparse matrices.\n   * @class SparseMatrix\n   */\n\n  function SparseMatrix(data, datatype) {\n    if (!(this instanceof SparseMatrix)) {\n      throw new SyntaxError('Constructor must be called with the new operator');\n    }\n\n    if (datatype && !isString(datatype)) {\n      throw new Error('Invalid datatype: ' + datatype);\n    }\n\n    if (isMatrix(data)) {\n      // create from matrix\n      _createFromMatrix(this, data, datatype);\n    } else if (data && isArray(data.index) && isArray(data.ptr) && isArray(data.size)) {\n      // initialize fields\n      this._values = data.values;\n      this._index = data.index;\n      this._ptr = data.ptr;\n      this._size = data.size;\n      this._datatype = datatype || data.datatype;\n    } else if (isArray(data)) {\n      // create from array\n      _createFromArray(this, data, datatype);\n    } else if (data) {\n      // unsupported type\n      throw new TypeError('Unsupported type of data (' + typeOf(data) + ')');\n    } else {\n      // nothing provided\n      this._values = [];\n      this._index = [];\n      this._ptr = [0];\n      this._size = [0, 0];\n      this._datatype = datatype;\n    }\n  }\n\n  function _createFromMatrix(matrix, source, datatype) {\n    // check matrix type\n    if (source.type === 'SparseMatrix') {\n      // clone arrays\n      matrix._values = source._values ? clone(source._values) : undefined;\n      matrix._index = clone(source._index);\n      matrix._ptr = clone(source._ptr);\n      matrix._size = clone(source._size);\n      matrix._datatype = datatype || source._datatype;\n    } else {\n      // build from matrix data\n      _createFromArray(matrix, source.valueOf(), datatype || source._datatype);\n    }\n  }\n\n  function _createFromArray(matrix, data, datatype) {\n    // initialize fields\n    matrix._values = [];\n    matrix._index = [];\n    matrix._ptr = [];\n    matrix._datatype = datatype; // discover rows & columns, do not use math.size() to avoid looping array twice\n\n    var rows = data.length;\n    var columns = 0; // equal signature to use\n\n    var eq = equalScalar; // zero value\n\n    var zero = 0;\n\n    if (isString(datatype)) {\n      // find signature that matches (datatype, datatype)\n      eq = typed.find(equalScalar, [datatype, datatype]) || equalScalar; // convert 0 to the same datatype\n\n      zero = typed.convert(0, datatype);\n    } // check we have rows (empty array)\n\n\n    if (rows > 0) {\n      // column index\n      var j = 0;\n\n      do {\n        // store pointer to values index\n        matrix._ptr.push(matrix._index.length); // loop rows\n\n\n        for (var i = 0; i < rows; i++) {\n          // current row\n          var row = data[i]; // check row is an array\n\n          if (isArray(row)) {\n            // update columns if needed (only on first column)\n            if (j === 0 && columns < row.length) {\n              columns = row.length;\n            } // check row has column\n\n\n            if (j < row.length) {\n              // value\n              var v = row[j]; // check value != 0\n\n              if (!eq(v, zero)) {\n                // store value\n                matrix._values.push(v); // index\n\n\n                matrix._index.push(i);\n              }\n            }\n          } else {\n            // update columns if needed (only on first column)\n            if (j === 0 && columns < 1) {\n              columns = 1;\n            } // check value != 0 (row is a scalar)\n\n\n            if (!eq(row, zero)) {\n              // store value\n              matrix._values.push(row); // index\n\n\n              matrix._index.push(i);\n            }\n          }\n        } // increment index\n\n\n        j++;\n      } while (j < columns);\n    } // store number of values in ptr\n\n\n    matrix._ptr.push(matrix._index.length); // size\n\n\n    matrix._size = [rows, columns];\n  }\n\n  SparseMatrix.prototype = new Matrix();\n  /**\n   * Create a new SparseMatrix\n   */\n\n  SparseMatrix.prototype.createSparseMatrix = function (data, datatype) {\n    return new SparseMatrix(data, datatype);\n  };\n  /**\n   * Attach type information\n   */\n\n\n  Object.defineProperty(SparseMatrix, 'name', {\n    value: 'SparseMatrix'\n  });\n  SparseMatrix.prototype.constructor = SparseMatrix;\n  SparseMatrix.prototype.type = 'SparseMatrix';\n  SparseMatrix.prototype.isSparseMatrix = true;\n  /**\n   * Get the matrix type\n   *\n   * Usage:\n   *    const matrixType = matrix.getDataType()  // retrieves the matrix type\n   *\n   * @memberOf SparseMatrix\n   * @return {string}   type information; if multiple types are found from the Matrix, it will return \"mixed\"\n   */\n\n  SparseMatrix.prototype.getDataType = function () {\n    return getArrayDataType(this._values, typeOf);\n  };\n  /**\n   * Get the storage format used by the matrix.\n   *\n   * Usage:\n   *     const format = matrix.storage()   // retrieve storage format\n   *\n   * @memberof SparseMatrix\n   * @return {string}           The storage format.\n   */\n\n\n  SparseMatrix.prototype.storage = function () {\n    return 'sparse';\n  };\n  /**\n   * Get the datatype of the data stored in the matrix.\n   *\n   * Usage:\n   *     const format = matrix.datatype()    // retrieve matrix datatype\n   *\n   * @memberof SparseMatrix\n   * @return {string}           The datatype.\n   */\n\n\n  SparseMatrix.prototype.datatype = function () {\n    return this._datatype;\n  };\n  /**\n   * Create a new SparseMatrix\n   * @memberof SparseMatrix\n   * @param {Array} data\n   * @param {string} [datatype]\n   */\n\n\n  SparseMatrix.prototype.create = function (data, datatype) {\n    return new SparseMatrix(data, datatype);\n  };\n  /**\n   * Get the matrix density.\n   *\n   * Usage:\n   *     const density = matrix.density()                   // retrieve matrix density\n   *\n   * @memberof SparseMatrix\n   * @return {number}           The matrix density.\n   */\n\n\n  SparseMatrix.prototype.density = function () {\n    // rows & columns\n    var rows = this._size[0];\n    var columns = this._size[1]; // calculate density\n\n    return rows !== 0 && columns !== 0 ? this._index.length / (rows * columns) : 0;\n  };\n  /**\n   * Get a subset of the matrix, or replace a subset of the matrix.\n   *\n   * Usage:\n   *     const subset = matrix.subset(index)               // retrieve subset\n   *     const value = matrix.subset(index, replacement)   // replace subset\n   *\n   * @memberof SparseMatrix\n   * @param {Index} index\n   * @param {Array | Matrix | *} [replacement]\n   * @param {*} [defaultValue=0]      Default value, filled in on new entries when\n   *                                  the matrix is resized. If not provided,\n   *                                  new matrix elements will be filled with zeros.\n   */\n\n\n  SparseMatrix.prototype.subset = function (index, replacement, defaultValue) {\n    // check it is a pattern matrix\n    if (!this._values) {\n      throw new Error('Cannot invoke subset on a Pattern only matrix');\n    } // check arguments\n\n\n    switch (arguments.length) {\n      case 1:\n        return _getsubset(this, index);\n      // intentional fall through\n\n      case 2:\n      case 3:\n        return _setsubset(this, index, replacement, defaultValue);\n\n      default:\n        throw new SyntaxError('Wrong number of arguments');\n    }\n  };\n\n  function _getsubset(matrix, idx) {\n    // check idx\n    if (!isIndex(idx)) {\n      throw new TypeError('Invalid index');\n    }\n\n    var isScalar = idx.isScalar();\n\n    if (isScalar) {\n      // return a scalar\n      return matrix.get(idx.min());\n    } // validate dimensions\n\n\n    var size = idx.size();\n\n    if (size.length !== matrix._size.length) {\n      throw new DimensionError(size.length, matrix._size.length);\n    } // vars\n\n\n    var i, ii, k, kk; // validate if any of the ranges in the index is out of range\n\n    var min = idx.min();\n    var max = idx.max();\n\n    for (i = 0, ii = matrix._size.length; i < ii; i++) {\n      validateIndex(min[i], matrix._size[i]);\n      validateIndex(max[i], matrix._size[i]);\n    } // matrix arrays\n\n\n    var mvalues = matrix._values;\n    var mindex = matrix._index;\n    var mptr = matrix._ptr; // rows & columns dimensions for result matrix\n\n    var rows = idx.dimension(0);\n    var columns = idx.dimension(1); // workspace & permutation vector\n\n    var w = [];\n    var pv = []; // loop rows in resulting matrix\n\n    rows.forEach(function (i, r) {\n      // update permutation vector\n      pv[i] = r[0]; // mark i in workspace\n\n      w[i] = true;\n    }); // result matrix arrays\n\n    var values = mvalues ? [] : undefined;\n    var index = [];\n    var ptr = []; // loop columns in result matrix\n\n    columns.forEach(function (j) {\n      // update ptr\n      ptr.push(index.length); // loop values in column j\n\n      for (k = mptr[j], kk = mptr[j + 1]; k < kk; k++) {\n        // row\n        i = mindex[k]; // check row is in result matrix\n\n        if (w[i] === true) {\n          // push index\n          index.push(pv[i]); // check we need to process values\n\n          if (values) {\n            values.push(mvalues[k]);\n          }\n        }\n      }\n    }); // update ptr\n\n    ptr.push(index.length); // return matrix\n\n    return new SparseMatrix({\n      values,\n      index,\n      ptr,\n      size,\n      datatype: matrix._datatype\n    });\n  }\n\n  function _setsubset(matrix, index, submatrix, defaultValue) {\n    // check index\n    if (!index || index.isIndex !== true) {\n      throw new TypeError('Invalid index');\n    } // get index size and check whether the index contains a single value\n\n\n    var iSize = index.size();\n    var isScalar = index.isScalar(); // calculate the size of the submatrix, and convert it into an Array if needed\n\n    var sSize;\n\n    if (isMatrix(submatrix)) {\n      // submatrix size\n      sSize = submatrix.size(); // use array representation\n\n      submatrix = submatrix.toArray();\n    } else {\n      // get submatrix size (array, scalar)\n      sSize = arraySize(submatrix);\n    } // check index is a scalar\n\n\n    if (isScalar) {\n      // verify submatrix is a scalar\n      if (sSize.length !== 0) {\n        throw new TypeError('Scalar expected');\n      } // set value\n\n\n      matrix.set(index.min(), submatrix, defaultValue);\n    } else {\n      // validate dimensions, index size must be one or two dimensions\n      if (iSize.length !== 1 && iSize.length !== 2) {\n        throw new DimensionError(iSize.length, matrix._size.length, '<');\n      } // check submatrix and index have the same dimensions\n\n\n      if (sSize.length < iSize.length) {\n        // calculate number of missing outer dimensions\n        var i = 0;\n        var outer = 0;\n\n        while (iSize[i] === 1 && sSize[i] === 1) {\n          i++;\n        }\n\n        while (iSize[i] === 1) {\n          outer++;\n          i++;\n        } // unsqueeze both outer and inner dimensions\n\n\n        submatrix = unsqueeze(submatrix, iSize.length, outer, sSize);\n      } // check whether the size of the submatrix matches the index size\n\n\n      if (!deepStrictEqual(iSize, sSize)) {\n        throw new DimensionError(iSize, sSize, '>');\n      } // insert the sub matrix\n\n\n      if (iSize.length === 1) {\n        // if the replacement index only has 1 dimension, go trough each one and set its value\n        var range = index.dimension(0);\n        range.forEach(function (dataIndex, subIndex) {\n          validateIndex(dataIndex);\n          matrix.set([dataIndex, 0], submatrix[subIndex[0]], defaultValue);\n        });\n      } else {\n        // if the replacement index has 2 dimensions, go through each one and set the value in the correct index\n        var firstDimensionRange = index.dimension(0);\n        var secondDimensionRange = index.dimension(1);\n        firstDimensionRange.forEach(function (firstDataIndex, firstSubIndex) {\n          validateIndex(firstDataIndex);\n          secondDimensionRange.forEach(function (secondDataIndex, secondSubIndex) {\n            validateIndex(secondDataIndex);\n            matrix.set([firstDataIndex, secondDataIndex], submatrix[firstSubIndex[0]][secondSubIndex[0]], defaultValue);\n          });\n        });\n      }\n    }\n\n    return matrix;\n  }\n  /**\n   * Get a single element from the matrix.\n   * @memberof SparseMatrix\n   * @param {number[]} index   Zero-based index\n   * @return {*} value\n   */\n\n\n  SparseMatrix.prototype.get = function (index) {\n    if (!isArray(index)) {\n      throw new TypeError('Array expected');\n    }\n\n    if (index.length !== this._size.length) {\n      throw new DimensionError(index.length, this._size.length);\n    } // check it is a pattern matrix\n\n\n    if (!this._values) {\n      throw new Error('Cannot invoke get on a Pattern only matrix');\n    } // row and column\n\n\n    var i = index[0];\n    var j = index[1]; // check i, j are valid\n\n    validateIndex(i, this._size[0]);\n    validateIndex(j, this._size[1]); // find value index\n\n    var k = _getValueIndex(i, this._ptr[j], this._ptr[j + 1], this._index); // check k is prior to next column k and it is in the correct row\n\n\n    if (k < this._ptr[j + 1] && this._index[k] === i) {\n      return this._values[k];\n    }\n\n    return 0;\n  };\n  /**\n   * Replace a single element in the matrix.\n   * @memberof SparseMatrix\n   * @param {number[]} index   Zero-based index\n   * @param {*} v\n   * @param {*} [defaultValue]        Default value, filled in on new entries when\n   *                                  the matrix is resized. If not provided,\n   *                                  new matrix elements will be set to zero.\n   * @return {SparseMatrix} self\n   */\n\n\n  SparseMatrix.prototype.set = function (index, v, defaultValue) {\n    if (!isArray(index)) {\n      throw new TypeError('Array expected');\n    }\n\n    if (index.length !== this._size.length) {\n      throw new DimensionError(index.length, this._size.length);\n    } // check it is a pattern matrix\n\n\n    if (!this._values) {\n      throw new Error('Cannot invoke set on a Pattern only matrix');\n    } // row and column\n\n\n    var i = index[0];\n    var j = index[1]; // rows & columns\n\n    var rows = this._size[0];\n    var columns = this._size[1]; // equal signature to use\n\n    var eq = equalScalar; // zero value\n\n    var zero = 0;\n\n    if (isString(this._datatype)) {\n      // find signature that matches (datatype, datatype)\n      eq = typed.find(equalScalar, [this._datatype, this._datatype]) || equalScalar; // convert 0 to the same datatype\n\n      zero = typed.convert(0, this._datatype);\n    } // check we need to resize matrix\n\n\n    if (i > rows - 1 || j > columns - 1) {\n      // resize matrix\n      _resize(this, Math.max(i + 1, rows), Math.max(j + 1, columns), defaultValue); // update rows & columns\n\n\n      rows = this._size[0];\n      columns = this._size[1];\n    } // check i, j are valid\n\n\n    validateIndex(i, rows);\n    validateIndex(j, columns); // find value index\n\n    var k = _getValueIndex(i, this._ptr[j], this._ptr[j + 1], this._index); // check k is prior to next column k and it is in the correct row\n\n\n    if (k < this._ptr[j + 1] && this._index[k] === i) {\n      // check value != 0\n      if (!eq(v, zero)) {\n        // update value\n        this._values[k] = v;\n      } else {\n        // remove value from matrix\n        _remove(k, j, this._values, this._index, this._ptr);\n      }\n    } else {\n      if (!eq(v, zero)) {\n        // insert value @ (i, j)\n        _insert(k, i, j, v, this._values, this._index, this._ptr);\n      }\n    }\n\n    return this;\n  };\n\n  function _getValueIndex(i, top, bottom, index) {\n    // check row is on the bottom side\n    if (bottom - top === 0) {\n      return bottom;\n    } // loop rows [top, bottom[\n\n\n    for (var r = top; r < bottom; r++) {\n      // check we found value index\n      if (index[r] === i) {\n        return r;\n      }\n    } // we did not find row\n\n\n    return top;\n  }\n\n  function _remove(k, j, values, index, ptr) {\n    // remove value @ k\n    values.splice(k, 1);\n    index.splice(k, 1); // update pointers\n\n    for (var x = j + 1; x < ptr.length; x++) {\n      ptr[x]--;\n    }\n  }\n\n  function _insert(k, i, j, v, values, index, ptr) {\n    // insert value\n    values.splice(k, 0, v); // update row for k\n\n    index.splice(k, 0, i); // update column pointers\n\n    for (var x = j + 1; x < ptr.length; x++) {\n      ptr[x]++;\n    }\n  }\n  /**\n   * Resize the matrix to the given size. Returns a copy of the matrix when\n   * `copy=true`, otherwise return the matrix itself (resize in place).\n   *\n   * @memberof SparseMatrix\n   * @param {number[] | Matrix} size  The new size the matrix should have.\n   *                                  Since sparse matrices are always two-dimensional,\n   *                                  size must be two numbers in either an array or a matrix\n   * @param {*} [defaultValue=0]      Default value, filled in on new entries.\n   *                                  If not provided, the matrix elements will\n   *                                  be filled with zeros.\n   * @param {boolean} [copy]          Return a resized copy of the matrix\n   *\n   * @return {Matrix}                 The resized matrix\n   */\n\n\n  SparseMatrix.prototype.resize = function (size, defaultValue, copy) {\n    // validate arguments\n    if (!isCollection(size)) {\n      throw new TypeError('Array or Matrix expected');\n    } // SparseMatrix input is always 2d, flatten this into 1d if it's indeed a vector\n\n\n    var sizeArray = size.valueOf().map(value => {\n      return Array.isArray(value) && value.length === 1 ? value[0] : value;\n    });\n\n    if (sizeArray.length !== 2) {\n      throw new Error('Only two dimensions matrix are supported');\n    } // check sizes\n\n\n    sizeArray.forEach(function (value) {\n      if (!isNumber(value) || !isInteger(value) || value < 0) {\n        throw new TypeError('Invalid size, must contain positive integers ' + '(size: ' + format(sizeArray) + ')');\n      }\n    }); // matrix to resize\n\n    var m = copy ? this.clone() : this; // resize matrix\n\n    return _resize(m, sizeArray[0], sizeArray[1], defaultValue);\n  };\n\n  function _resize(matrix, rows, columns, defaultValue) {\n    // value to insert at the time of growing matrix\n    var value = defaultValue || 0; // equal signature to use\n\n    var eq = equalScalar; // zero value\n\n    var zero = 0;\n\n    if (isString(matrix._datatype)) {\n      // find signature that matches (datatype, datatype)\n      eq = typed.find(equalScalar, [matrix._datatype, matrix._datatype]) || equalScalar; // convert 0 to the same datatype\n\n      zero = typed.convert(0, matrix._datatype); // convert value to the same datatype\n\n      value = typed.convert(value, matrix._datatype);\n    } // should we insert the value?\n\n\n    var ins = !eq(value, zero); // old columns and rows\n\n    var r = matrix._size[0];\n    var c = matrix._size[1];\n    var i, j, k; // check we need to increase columns\n\n    if (columns > c) {\n      // loop new columns\n      for (j = c; j < columns; j++) {\n        // update matrix._ptr for current column\n        matrix._ptr[j] = matrix._values.length; // check we need to insert matrix._values\n\n        if (ins) {\n          // loop rows\n          for (i = 0; i < r; i++) {\n            // add new matrix._values\n            matrix._values.push(value); // update matrix._index\n\n\n            matrix._index.push(i);\n          }\n        }\n      } // store number of matrix._values in matrix._ptr\n\n\n      matrix._ptr[columns] = matrix._values.length;\n    } else if (columns < c) {\n      // truncate matrix._ptr\n      matrix._ptr.splice(columns + 1, c - columns); // truncate matrix._values and matrix._index\n\n\n      matrix._values.splice(matrix._ptr[columns], matrix._values.length);\n\n      matrix._index.splice(matrix._ptr[columns], matrix._index.length);\n    } // update columns\n\n\n    c = columns; // check we need to increase rows\n\n    if (rows > r) {\n      // check we have to insert values\n      if (ins) {\n        // inserts\n        var n = 0; // loop columns\n\n        for (j = 0; j < c; j++) {\n          // update matrix._ptr for current column\n          matrix._ptr[j] = matrix._ptr[j] + n; // where to insert matrix._values\n\n          k = matrix._ptr[j + 1] + n; // pointer\n\n          var p = 0; // loop new rows, initialize pointer\n\n          for (i = r; i < rows; i++, p++) {\n            // add value\n            matrix._values.splice(k + p, 0, value); // update matrix._index\n\n\n            matrix._index.splice(k + p, 0, i); // increment inserts\n\n\n            n++;\n          }\n        } // store number of matrix._values in matrix._ptr\n\n\n        matrix._ptr[c] = matrix._values.length;\n      }\n    } else if (rows < r) {\n      // deletes\n      var d = 0; // loop columns\n\n      for (j = 0; j < c; j++) {\n        // update matrix._ptr for current column\n        matrix._ptr[j] = matrix._ptr[j] - d; // where matrix._values start for next column\n\n        var k0 = matrix._ptr[j];\n        var k1 = matrix._ptr[j + 1] - d; // loop matrix._index\n\n        for (k = k0; k < k1; k++) {\n          // row\n          i = matrix._index[k]; // check we need to delete value and matrix._index\n\n          if (i > rows - 1) {\n            // remove value\n            matrix._values.splice(k, 1); // remove item from matrix._index\n\n\n            matrix._index.splice(k, 1); // increase deletes\n\n\n            d++;\n          }\n        }\n      } // update matrix._ptr for current column\n\n\n      matrix._ptr[j] = matrix._values.length;\n    } // update matrix._size\n\n\n    matrix._size[0] = rows;\n    matrix._size[1] = columns; // return matrix\n\n    return matrix;\n  }\n  /**\n   * Reshape the matrix to the given size. Returns a copy of the matrix when\n   * `copy=true`, otherwise return the matrix itself (reshape in place).\n   *\n   * NOTE: This might be better suited to copy by default, instead of modifying\n   *       in place. For now, it operates in place to remain consistent with\n   *       resize().\n   *\n   * @memberof SparseMatrix\n   * @param {number[]} sizes          The new size the matrix should have.\n   *                                  Since sparse matrices are always two-dimensional,\n   *                                  size must be two numbers in either an array or a matrix\n   * @param {boolean} [copy]          Return a reshaped copy of the matrix\n   *\n   * @return {Matrix}                 The reshaped matrix\n   */\n\n\n  SparseMatrix.prototype.reshape = function (sizes, copy) {\n    // validate arguments\n    if (!isArray(sizes)) {\n      throw new TypeError('Array expected');\n    }\n\n    if (sizes.length !== 2) {\n      throw new Error('Sparse matrices can only be reshaped in two dimensions');\n    } // check sizes\n\n\n    sizes.forEach(function (value) {\n      if (!isNumber(value) || !isInteger(value) || value <= -2 || value === 0) {\n        throw new TypeError('Invalid size, must contain positive integers or -1 ' + '(size: ' + format(sizes) + ')');\n      }\n    });\n    var currentLength = this._size[0] * this._size[1];\n    sizes = processSizesWildcard(sizes, currentLength);\n    var newLength = sizes[0] * sizes[1]; // m * n must not change\n\n    if (currentLength !== newLength) {\n      throw new Error('Reshaping sparse matrix will result in the wrong number of elements');\n    } // matrix to reshape\n\n\n    var m = copy ? this.clone() : this; // return unchanged if the same shape\n\n    if (this._size[0] === sizes[0] && this._size[1] === sizes[1]) {\n      return m;\n    } // Convert to COO format (generate a column index)\n\n\n    var colIndex = [];\n\n    for (var i = 0; i < m._ptr.length; i++) {\n      for (var j = 0; j < m._ptr[i + 1] - m._ptr[i]; j++) {\n        colIndex.push(i);\n      }\n    } // Clone the values array\n\n\n    var values = m._values.slice(); // Clone the row index array\n\n\n    var rowIndex = m._index.slice(); // Transform the (row, column) indices\n\n\n    for (var _i = 0; _i < m._index.length; _i++) {\n      var r1 = rowIndex[_i];\n      var c1 = colIndex[_i];\n      var flat = r1 * m._size[1] + c1;\n      colIndex[_i] = flat % sizes[1];\n      rowIndex[_i] = Math.floor(flat / sizes[1]);\n    } // Now reshaping is supposed to preserve the row-major order, BUT these sparse matrices are stored\n    // in column-major order, so we have to reorder the value array now. One option is to use a multisort,\n    // sorting several arrays based on some other array.\n    // OR, we could easily just:\n    // 1. Remove all values from the matrix\n\n\n    m._values.length = 0;\n    m._index.length = 0;\n    m._ptr.length = sizes[1] + 1;\n    m._size = sizes.slice();\n\n    for (var _i2 = 0; _i2 < m._ptr.length; _i2++) {\n      m._ptr[_i2] = 0;\n    } // 2. Re-insert all elements in the proper order (simplified code from SparseMatrix.prototype.set)\n    // This step is probably the most time-consuming\n\n\n    for (var h = 0; h < values.length; h++) {\n      var _i3 = rowIndex[h];\n      var _j = colIndex[h];\n      var v = values[h];\n\n      var k = _getValueIndex(_i3, m._ptr[_j], m._ptr[_j + 1], m._index);\n\n      _insert(k, _i3, _j, v, m._values, m._index, m._ptr);\n    } // The value indices are inserted out of order, but apparently that's... still OK?\n\n\n    return m;\n  };\n  /**\n   * Create a clone of the matrix\n   * @memberof SparseMatrix\n   * @return {SparseMatrix} clone\n   */\n\n\n  SparseMatrix.prototype.clone = function () {\n    var m = new SparseMatrix({\n      values: this._values ? clone(this._values) : undefined,\n      index: clone(this._index),\n      ptr: clone(this._ptr),\n      size: clone(this._size),\n      datatype: this._datatype\n    });\n    return m;\n  };\n  /**\n   * Retrieve the size of the matrix.\n   * @memberof SparseMatrix\n   * @returns {number[]} size\n   */\n\n\n  SparseMatrix.prototype.size = function () {\n    return this._size.slice(0); // copy the Array\n  };\n  /**\n   * Create a new matrix with the results of the callback function executed on\n   * each entry of the matrix.\n   * @memberof SparseMatrix\n   * @param {Function} callback   The callback function is invoked with three\n   *                              parameters: the value of the element, the index\n   *                              of the element, and the Matrix being traversed.\n   * @param {boolean} [skipZeros] Invoke callback function for non-zero values only.\n   *\n   * @return {SparseMatrix} matrix\n   */\n\n\n  SparseMatrix.prototype.map = function (callback, skipZeros) {\n    // check it is a pattern matrix\n    if (!this._values) {\n      throw new Error('Cannot invoke map on a Pattern only matrix');\n    } // matrix instance\n\n\n    var me = this; // rows and columns\n\n    var rows = this._size[0];\n    var columns = this._size[1];\n    var fastCallback = optimizeCallback(callback, me, 'map'); // invoke callback\n\n    var invoke = function invoke(v, i, j) {\n      // invoke callback\n      return fastCallback(v, [i, j], me);\n    }; // invoke _map\n\n\n    return _map(this, 0, rows - 1, 0, columns - 1, invoke, skipZeros);\n  };\n  /**\n   * Create a new matrix with the results of the callback function executed on the interval\n   * [minRow..maxRow, minColumn..maxColumn].\n   */\n\n\n  function _map(matrix, minRow, maxRow, minColumn, maxColumn, callback, skipZeros) {\n    // result arrays\n    var values = [];\n    var index = [];\n    var ptr = []; // equal signature to use\n\n    var eq = equalScalar; // zero value\n\n    var zero = 0;\n\n    if (isString(matrix._datatype)) {\n      // find signature that matches (datatype, datatype)\n      eq = typed.find(equalScalar, [matrix._datatype, matrix._datatype]) || equalScalar; // convert 0 to the same datatype\n\n      zero = typed.convert(0, matrix._datatype);\n    } // invoke callback\n\n\n    var invoke = function invoke(v, x, y) {\n      // invoke callback\n      var value = callback(v, x, y); // check value != 0\n\n      if (!eq(value, zero)) {\n        // store value\n        values.push(value); // index\n\n        index.push(x);\n      }\n    }; // loop columns\n\n\n    for (var j = minColumn; j <= maxColumn; j++) {\n      // store pointer to values index\n      ptr.push(values.length); // k0 <= k < k1 where k0 = _ptr[j] && k1 = _ptr[j+1]\n\n      var k0 = matrix._ptr[j];\n      var k1 = matrix._ptr[j + 1];\n\n      if (skipZeros) {\n        // loop k within [k0, k1[\n        for (var k = k0; k < k1; k++) {\n          // row index\n          var i = matrix._index[k]; // check i is in range\n\n          if (i >= minRow && i <= maxRow) {\n            // value @ k\n            invoke(matrix._values[k], i - minRow, j - minColumn);\n          }\n        }\n      } else {\n        // create a cache holding all defined values\n        var _values = {};\n\n        for (var _k = k0; _k < k1; _k++) {\n          var _i4 = matrix._index[_k];\n          _values[_i4] = matrix._values[_k];\n        } // loop over all rows (indexes can be unordered so we can't use that),\n        // and either read the value or zero\n\n\n        for (var _i5 = minRow; _i5 <= maxRow; _i5++) {\n          var value = _i5 in _values ? _values[_i5] : 0;\n          invoke(value, _i5 - minRow, j - minColumn);\n        }\n      }\n    } // store number of values in ptr\n\n\n    ptr.push(values.length); // return sparse matrix\n\n    return new SparseMatrix({\n      values,\n      index,\n      ptr,\n      size: [maxRow - minRow + 1, maxColumn - minColumn + 1]\n    });\n  }\n  /**\n   * Execute a callback function on each entry of the matrix.\n   * @memberof SparseMatrix\n   * @param {Function} callback   The callback function is invoked with three\n   *                              parameters: the value of the element, the index\n   *                              of the element, and the Matrix being traversed.\n   * @param {boolean} [skipZeros] Invoke callback function for non-zero values only.\n   *                              If false, the indices are guaranteed to be in order,\n   *                              if true, the indices can be unordered.\n   */\n\n\n  SparseMatrix.prototype.forEach = function (callback, skipZeros) {\n    // check it is a pattern matrix\n    if (!this._values) {\n      throw new Error('Cannot invoke forEach on a Pattern only matrix');\n    } // matrix instance\n\n\n    var me = this; // rows and columns\n\n    var rows = this._size[0];\n    var columns = this._size[1];\n    var fastCallback = optimizeCallback(callback, me, 'forEach'); // loop columns\n\n    for (var j = 0; j < columns; j++) {\n      // k0 <= k < k1 where k0 = _ptr[j] && k1 = _ptr[j+1]\n      var k0 = this._ptr[j];\n      var k1 = this._ptr[j + 1];\n\n      if (skipZeros) {\n        // loop k within [k0, k1[\n        for (var k = k0; k < k1; k++) {\n          // row index\n          var i = this._index[k]; // value @ k\n\n          fastCallback(this._values[k], [i, j], me);\n        }\n      } else {\n        // create a cache holding all defined values\n        var values = {};\n\n        for (var _k2 = k0; _k2 < k1; _k2++) {\n          var _i6 = this._index[_k2];\n          values[_i6] = this._values[_k2];\n        } // loop over all rows (indexes can be unordered so we can't use that),\n        // and either read the value or zero\n\n\n        for (var _i7 = 0; _i7 < rows; _i7++) {\n          var value = _i7 in values ? values[_i7] : 0;\n          fastCallback(value, [_i7, j], me);\n        }\n      }\n    }\n  };\n  /**\n   * Iterate over the matrix elements, skipping zeros\n   * @return {Iterable<{ value, index: number[] }>}\n   */\n\n\n  SparseMatrix.prototype[Symbol.iterator] = function* () {\n    if (!this._values) {\n      throw new Error('Cannot iterate a Pattern only matrix');\n    }\n\n    var columns = this._size[1];\n\n    for (var j = 0; j < columns; j++) {\n      var k0 = this._ptr[j];\n      var k1 = this._ptr[j + 1];\n\n      for (var k = k0; k < k1; k++) {\n        // row index\n        var i = this._index[k];\n        yield {\n          value: this._values[k],\n          index: [i, j]\n        };\n      }\n    }\n  };\n  /**\n   * Create an Array with a copy of the data of the SparseMatrix\n   * @memberof SparseMatrix\n   * @returns {Array} array\n   */\n\n\n  SparseMatrix.prototype.toArray = function () {\n    return _toArray(this._values, this._index, this._ptr, this._size, true);\n  };\n  /**\n   * Get the primitive value of the SparseMatrix: a two dimensions array\n   * @memberof SparseMatrix\n   * @returns {Array} array\n   */\n\n\n  SparseMatrix.prototype.valueOf = function () {\n    return _toArray(this._values, this._index, this._ptr, this._size, false);\n  };\n\n  function _toArray(values, index, ptr, size, copy) {\n    // rows and columns\n    var rows = size[0];\n    var columns = size[1]; // result\n\n    var a = []; // vars\n\n    var i, j; // initialize array\n\n    for (i = 0; i < rows; i++) {\n      a[i] = [];\n\n      for (j = 0; j < columns; j++) {\n        a[i][j] = 0;\n      }\n    } // loop columns\n\n\n    for (j = 0; j < columns; j++) {\n      // k0 <= k < k1 where k0 = _ptr[j] && k1 = _ptr[j+1]\n      var k0 = ptr[j];\n      var k1 = ptr[j + 1]; // loop k within [k0, k1[\n\n      for (var k = k0; k < k1; k++) {\n        // row index\n        i = index[k]; // set value (use one for pattern matrix)\n\n        a[i][j] = values ? copy ? clone(values[k]) : values[k] : 1;\n      }\n    }\n\n    return a;\n  }\n  /**\n   * Get a string representation of the matrix, with optional formatting options.\n   * @memberof SparseMatrix\n   * @param {Object | number | Function} [options]  Formatting options. See\n   *                                                lib/utils/number:format for a\n   *                                                description of the available\n   *                                                options.\n   * @returns {string} str\n   */\n\n\n  SparseMatrix.prototype.format = function (options) {\n    // rows and columns\n    var rows = this._size[0];\n    var columns = this._size[1]; // density\n\n    var density = this.density(); // rows & columns\n\n    var str = 'Sparse Matrix [' + format(rows, options) + ' x ' + format(columns, options) + '] density: ' + format(density, options) + '\\n'; // loop columns\n\n    for (var j = 0; j < columns; j++) {\n      // k0 <= k < k1 where k0 = _ptr[j] && k1 = _ptr[j+1]\n      var k0 = this._ptr[j];\n      var k1 = this._ptr[j + 1]; // loop k within [k0, k1[\n\n      for (var k = k0; k < k1; k++) {\n        // row index\n        var i = this._index[k]; // append value\n\n        str += '\\n    (' + format(i, options) + ', ' + format(j, options) + ') ==> ' + (this._values ? format(this._values[k], options) : 'X');\n      }\n    }\n\n    return str;\n  };\n  /**\n   * Get a string representation of the matrix\n   * @memberof SparseMatrix\n   * @returns {string} str\n   */\n\n\n  SparseMatrix.prototype.toString = function () {\n    return format(this.toArray());\n  };\n  /**\n   * Get a JSON representation of the matrix\n   * @memberof SparseMatrix\n   * @returns {Object}\n   */\n\n\n  SparseMatrix.prototype.toJSON = function () {\n    return {\n      mathjs: 'SparseMatrix',\n      values: this._values,\n      index: this._index,\n      ptr: this._ptr,\n      size: this._size,\n      datatype: this._datatype\n    };\n  };\n  /**\n   * Get the kth Matrix diagonal.\n   *\n   * @memberof SparseMatrix\n   * @param {number | BigNumber} [k=0]     The kth diagonal where the vector will retrieved.\n   *\n   * @returns {Matrix}                     The matrix vector with the diagonal values.\n   */\n\n\n  SparseMatrix.prototype.diagonal = function (k) {\n    // validate k if any\n    if (k) {\n      // convert BigNumber to a number\n      if (isBigNumber(k)) {\n        k = k.toNumber();\n      } // is must be an integer\n\n\n      if (!isNumber(k) || !isInteger(k)) {\n        throw new TypeError('The parameter k must be an integer number');\n      }\n    } else {\n      // default value\n      k = 0;\n    }\n\n    var kSuper = k > 0 ? k : 0;\n    var kSub = k < 0 ? -k : 0; // rows & columns\n\n    var rows = this._size[0];\n    var columns = this._size[1]; // number diagonal values\n\n    var n = Math.min(rows - kSub, columns - kSuper); // diagonal arrays\n\n    var values = [];\n    var index = [];\n    var ptr = []; // initial ptr value\n\n    ptr[0] = 0; // loop columns\n\n    for (var j = kSuper; j < columns && values.length < n; j++) {\n      // k0 <= k < k1 where k0 = _ptr[j] && k1 = _ptr[j+1]\n      var k0 = this._ptr[j];\n      var k1 = this._ptr[j + 1]; // loop x within [k0, k1[\n\n      for (var x = k0; x < k1; x++) {\n        // row index\n        var i = this._index[x]; // check row\n\n        if (i === j - kSuper + kSub) {\n          // value on this column\n          values.push(this._values[x]); // store row\n\n          index[values.length - 1] = i - kSub; // exit loop\n\n          break;\n        }\n      }\n    } // close ptr\n\n\n    ptr.push(values.length); // return matrix\n\n    return new SparseMatrix({\n      values,\n      index,\n      ptr,\n      size: [n, 1]\n    });\n  };\n  /**\n   * Generate a matrix from a JSON object\n   * @memberof SparseMatrix\n   * @param {Object} json  An object structured like\n   *                       `{\"mathjs\": \"SparseMatrix\", \"values\": [], \"index\": [], \"ptr\": [], \"size\": []}`,\n   *                       where mathjs is optional\n   * @returns {SparseMatrix}\n   */\n\n\n  SparseMatrix.fromJSON = function (json) {\n    return new SparseMatrix(json);\n  };\n  /**\n   * Create a diagonal matrix.\n   *\n   * @memberof SparseMatrix\n   * @param {Array} size                       The matrix size.\n   * @param {number | Array | Matrix } value   The values for the diagonal.\n   * @param {number | BigNumber} [k=0]         The kth diagonal where the vector will be filled in.\n   * @param {number} [defaultValue]            The default value for non-diagonal\n   * @param {string} [datatype]                The Matrix datatype, values must be of this datatype.\n   *\n   * @returns {SparseMatrix}\n   */\n\n\n  SparseMatrix.diagonal = function (size, value, k, defaultValue, datatype) {\n    if (!isArray(size)) {\n      throw new TypeError('Array expected, size parameter');\n    }\n\n    if (size.length !== 2) {\n      throw new Error('Only two dimensions matrix are supported');\n    } // map size & validate\n\n\n    size = size.map(function (s) {\n      // check it is a big number\n      if (isBigNumber(s)) {\n        // convert it\n        s = s.toNumber();\n      } // validate arguments\n\n\n      if (!isNumber(s) || !isInteger(s) || s < 1) {\n        throw new Error('Size values must be positive integers');\n      }\n\n      return s;\n    }); // validate k if any\n\n    if (k) {\n      // convert BigNumber to a number\n      if (isBigNumber(k)) {\n        k = k.toNumber();\n      } // is must be an integer\n\n\n      if (!isNumber(k) || !isInteger(k)) {\n        throw new TypeError('The parameter k must be an integer number');\n      }\n    } else {\n      // default value\n      k = 0;\n    } // equal signature to use\n\n\n    var eq = equalScalar; // zero value\n\n    var zero = 0;\n\n    if (isString(datatype)) {\n      // find signature that matches (datatype, datatype)\n      eq = typed.find(equalScalar, [datatype, datatype]) || equalScalar; // convert 0 to the same datatype\n\n      zero = typed.convert(0, datatype);\n    }\n\n    var kSuper = k > 0 ? k : 0;\n    var kSub = k < 0 ? -k : 0; // rows and columns\n\n    var rows = size[0];\n    var columns = size[1]; // number of non-zero items\n\n    var n = Math.min(rows - kSub, columns - kSuper); // value extraction function\n\n    var _value; // check value\n\n\n    if (isArray(value)) {\n      // validate array\n      if (value.length !== n) {\n        // number of values in array must be n\n        throw new Error('Invalid value array length');\n      } // define function\n\n\n      _value = function _value(i) {\n        // return value @ i\n        return value[i];\n      };\n    } else if (isMatrix(value)) {\n      // matrix size\n      var ms = value.size(); // validate matrix\n\n      if (ms.length !== 1 || ms[0] !== n) {\n        // number of values in array must be n\n        throw new Error('Invalid matrix length');\n      } // define function\n\n\n      _value = function _value(i) {\n        // return value @ i\n        return value.get([i]);\n      };\n    } else {\n      // define function\n      _value = function _value() {\n        // return value\n        return value;\n      };\n    } // create arrays\n\n\n    var values = [];\n    var index = [];\n    var ptr = []; // loop items\n\n    for (var j = 0; j < columns; j++) {\n      // number of rows with value\n      ptr.push(values.length); // diagonal index\n\n      var i = j - kSuper; // check we need to set diagonal value\n\n      if (i >= 0 && i < n) {\n        // get value @ i\n        var v = _value(i); // check for zero\n\n\n        if (!eq(v, zero)) {\n          // column\n          index.push(i + kSub); // add value\n\n          values.push(v);\n        }\n      }\n    } // last value should be number of values\n\n\n    ptr.push(values.length); // create SparseMatrix\n\n    return new SparseMatrix({\n      values,\n      index,\n      ptr,\n      size: [rows, columns]\n    });\n  };\n  /**\n   * Swap rows i and j in Matrix.\n   *\n   * @memberof SparseMatrix\n   * @param {number} i       Matrix row index 1\n   * @param {number} j       Matrix row index 2\n   *\n   * @return {Matrix}        The matrix reference\n   */\n\n\n  SparseMatrix.prototype.swapRows = function (i, j) {\n    // check index\n    if (!isNumber(i) || !isInteger(i) || !isNumber(j) || !isInteger(j)) {\n      throw new Error('Row index must be positive integers');\n    } // check dimensions\n\n\n    if (this._size.length !== 2) {\n      throw new Error('Only two dimensional matrix is supported');\n    } // validate index\n\n\n    validateIndex(i, this._size[0]);\n    validateIndex(j, this._size[0]); // swap rows\n\n    SparseMatrix._swapRows(i, j, this._size[1], this._values, this._index, this._ptr); // return current instance\n\n\n    return this;\n  };\n  /**\n   * Loop rows with data in column j.\n   *\n   * @param {number} j            Column\n   * @param {Array} values        Matrix values\n   * @param {Array} index         Matrix row indeces\n   * @param {Array} ptr           Matrix column pointers\n   * @param {Function} callback   Callback function invoked for every row in column j\n   */\n\n\n  SparseMatrix._forEachRow = function (j, values, index, ptr, callback) {\n    // indeces for column j\n    var k0 = ptr[j];\n    var k1 = ptr[j + 1]; // loop\n\n    for (var k = k0; k < k1; k++) {\n      // invoke callback\n      callback(index[k], values[k]);\n    }\n  };\n  /**\n   * Swap rows x and y in Sparse Matrix data structures.\n   *\n   * @param {number} x         Matrix row index 1\n   * @param {number} y         Matrix row index 2\n   * @param {number} columns   Number of columns in matrix\n   * @param {Array} values     Matrix values\n   * @param {Array} index      Matrix row indeces\n   * @param {Array} ptr        Matrix column pointers\n   */\n\n\n  SparseMatrix._swapRows = function (x, y, columns, values, index, ptr) {\n    // loop columns\n    for (var j = 0; j < columns; j++) {\n      // k0 <= k < k1 where k0 = _ptr[j] && k1 = _ptr[j+1]\n      var k0 = ptr[j];\n      var k1 = ptr[j + 1]; // find value index @ x\n\n      var kx = _getValueIndex(x, k0, k1, index); // find value index @ x\n\n\n      var ky = _getValueIndex(y, k0, k1, index); // check both rows exist in matrix\n\n\n      if (kx < k1 && ky < k1 && index[kx] === x && index[ky] === y) {\n        // swap values (check for pattern matrix)\n        if (values) {\n          var v = values[kx];\n          values[kx] = values[ky];\n          values[ky] = v;\n        } // next column\n\n\n        continue;\n      } // check x row exist & no y row\n\n\n      if (kx < k1 && index[kx] === x && (ky >= k1 || index[ky] !== y)) {\n        // value @ x (check for pattern matrix)\n        var vx = values ? values[kx] : undefined; // insert value @ y\n\n        index.splice(ky, 0, y);\n\n        if (values) {\n          values.splice(ky, 0, vx);\n        } // remove value @ x (adjust array index if needed)\n\n\n        index.splice(ky <= kx ? kx + 1 : kx, 1);\n\n        if (values) {\n          values.splice(ky <= kx ? kx + 1 : kx, 1);\n        } // next column\n\n\n        continue;\n      } // check y row exist & no x row\n\n\n      if (ky < k1 && index[ky] === y && (kx >= k1 || index[kx] !== x)) {\n        // value @ y (check for pattern matrix)\n        var vy = values ? values[ky] : undefined; // insert value @ x\n\n        index.splice(kx, 0, x);\n\n        if (values) {\n          values.splice(kx, 0, vy);\n        } // remove value @ y (adjust array index if needed)\n\n\n        index.splice(kx <= ky ? ky + 1 : ky, 1);\n\n        if (values) {\n          values.splice(kx <= ky ? ky + 1 : ky, 1);\n        }\n      }\n    }\n  };\n\n  return SparseMatrix;\n}, {\n  isClass: true\n});", "map": null, "metadata": {}, "sourceType": "module"}