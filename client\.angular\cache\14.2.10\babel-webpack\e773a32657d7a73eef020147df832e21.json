{"ast": null, "code": "import { compareText as _compareText } from '../../utils/string.js';\nimport { factory } from '../../utils/factory.js';\nimport { createMatrixAlgorithmSuite } from '../../type/matrix/utils/matrixAlgorithmSuite.js';\nvar name = 'compareText';\nvar dependencies = ['typed', 'matrix', 'concat'];\n_compareText.signature = 'any, any';\nexport var createCompareText = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed,\n    matrix,\n    concat\n  } = _ref;\n  var matrixAlgorithmSuite = createMatrixAlgorithmSuite({\n    typed,\n    matrix,\n    concat\n  });\n  /**\n   * Compare two strings lexically. Comparison is case sensitive.\n   * Returns 1 when x > y, -1 when x < y, and 0 when x == y.\n   *\n   * For matrices, the function is evaluated element wise.\n   *\n   * Syntax:\n   *\n   *    math.compareText(x, y)\n   *\n   * Examples:\n   *\n   *    math.compareText('B', 'A')     // returns 1\n   *    math.compareText('2', '10')    // returns 1\n   *    math.compare('2', '10')        // returns -1\n   *    math.compareNatural('2', '10') // returns -1\n   *\n   *    math.compareText('B', ['A', 'B', 'C']) // returns [1, 0, -1]\n   *\n   * See also:\n   *\n   *    equal, equalText, compare, compareNatural\n   *\n   * @param  {string | Array | DenseMatrix} x First string to compare\n   * @param  {string | Array | DenseMatrix} y Second string to compare\n   * @return {number | Array | DenseMatrix} Returns the result of the comparison:\n   *                                        1 when x > y, -1 when x < y, and 0 when x == y.\n   */\n\n  return typed(name, _compareText, matrixAlgorithmSuite({\n    elop: _compareText,\n    Ds: true\n  }));\n});\nexport var createCompareTextNumber = /* #__PURE__ */factory(name, ['typed'], _ref2 => {\n  var {\n    typed\n  } = _ref2;\n  return typed(name, _compareText);\n});", "map": null, "metadata": {}, "sourceType": "module"}