{"ast": null, "code": "import { factory } from '../../utils/factory.js';\nimport { cubeNumber } from '../../plain/number/index.js';\nvar name = 'cube';\nvar dependencies = ['typed'];\nexport var createCube = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed\n  } = _ref;\n  /**\n   * Compute the cube of a value, `x * x * x`.\n   * To avoid confusion with `pow(M,3)`, this function does not apply to matrices.\n   * If you wish to cube every entry of a matrix, see the examples.\n   *\n   * Syntax:\n   *\n   *    math.cube(x)\n   *\n   * Examples:\n   *\n   *    math.cube(2)            // returns number 8\n   *    math.pow(2, 3)          // returns number 8\n   *    math.cube(4)            // returns number 64\n   *    4 * 4 * 4               // returns number 64\n   *\n   *    math.map([1, 2, 3, 4], math.cube) // returns Array [1, 8, 27, 64]\n   *\n   * See also:\n   *\n   *    multiply, square, pow, cbrt\n   *\n   * @param  {number | BigNumber | bigint | Fraction | Complex | Unit} x  Number for which to calculate the cube\n   * @return {number | BigNumber | bigint | Fraction | Complex | Unit} Cube of x\n   */\n\n  return typed(name, {\n    number: cubeNumber,\n    Complex: function Complex(x) {\n      return x.mul(x).mul(x); // Is faster than pow(x, 3)\n    },\n    BigNumber: function BigNumber(x) {\n      return x.times(x).times(x);\n    },\n    bigint: function bigint(x) {\n      return x * x * x;\n    },\n    Fraction: function Fraction(x) {\n      return x.pow(3); // Is faster than mul()mul()mul()\n    },\n    Unit: function Unit(x) {\n      return x.pow(3);\n    }\n  });\n});", "map": {"version": 3, "names": ["factory", "cubeNumber", "name", "dependencies", "createCube", "_ref", "typed", "number", "Complex", "x", "mul", "BigNumber", "times", "bigint", "Fraction", "pow", "Unit"], "sources": ["D:/work/joyserver/client/node_modules/mathjs/lib/esm/function/arithmetic/cube.js"], "sourcesContent": ["import { factory } from '../../utils/factory.js';\nimport { cubeNumber } from '../../plain/number/index.js';\nvar name = 'cube';\nvar dependencies = ['typed'];\nexport var createCube = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed\n  } = _ref;\n  /**\n   * Compute the cube of a value, `x * x * x`.\n   * To avoid confusion with `pow(M,3)`, this function does not apply to matrices.\n   * If you wish to cube every entry of a matrix, see the examples.\n   *\n   * Syntax:\n   *\n   *    math.cube(x)\n   *\n   * Examples:\n   *\n   *    math.cube(2)            // returns number 8\n   *    math.pow(2, 3)          // returns number 8\n   *    math.cube(4)            // returns number 64\n   *    4 * 4 * 4               // returns number 64\n   *\n   *    math.map([1, 2, 3, 4], math.cube) // returns Array [1, 8, 27, 64]\n   *\n   * See also:\n   *\n   *    multiply, square, pow, cbrt\n   *\n   * @param  {number | BigNumber | bigint | Fraction | Complex | Unit} x  Number for which to calculate the cube\n   * @return {number | BigNumber | bigint | Fraction | Complex | Unit} Cube of x\n   */\n  return typed(name, {\n    number: cubeNumber,\n    Complex: function Complex(x) {\n      return x.mul(x).mul(x); // Is faster than pow(x, 3)\n    },\n    BigNumber: function BigNumber(x) {\n      return x.times(x).times(x);\n    },\n    bigint: function bigint(x) {\n      return x * x * x;\n    },\n    Fraction: function Fraction(x) {\n      return x.pow(3); // Is faster than mul()mul()mul()\n    },\n    Unit: function Unit(x) {\n      return x.pow(3);\n    }\n  });\n});"], "mappings": "AAAA,SAASA,OAAT,QAAwB,wBAAxB;AACA,SAASC,UAAT,QAA2B,6BAA3B;AACA,IAAIC,IAAI,GAAG,MAAX;AACA,IAAIC,YAAY,GAAG,CAAC,OAAD,CAAnB;AACA,OAAO,IAAIC,UAAU,GAAG,eAAeJ,OAAO,CAACE,IAAD,EAAOC,YAAP,EAAqBE,IAAI,IAAI;EACzE,IAAI;IACFC;EADE,IAEAD,IAFJ;EAGA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EACE,OAAOC,KAAK,CAACJ,IAAD,EAAO;IACjBK,MAAM,EAAEN,UADS;IAEjBO,OAAO,EAAE,SAASA,OAAT,CAAiBC,CAAjB,EAAoB;MAC3B,OAAOA,CAAC,CAACC,GAAF,CAAMD,CAAN,EAASC,GAAT,CAAaD,CAAb,CAAP,CAD2B,CACH;IACzB,CAJgB;IAKjBE,SAAS,EAAE,SAASA,SAAT,CAAmBF,CAAnB,EAAsB;MAC/B,OAAOA,CAAC,CAACG,KAAF,CAAQH,CAAR,EAAWG,KAAX,CAAiBH,CAAjB,CAAP;IACD,CAPgB;IAQjBI,MAAM,EAAE,SAASA,MAAT,CAAgBJ,CAAhB,EAAmB;MACzB,OAAOA,CAAC,GAAGA,CAAJ,GAAQA,CAAf;IACD,CAVgB;IAWjBK,QAAQ,EAAE,SAASA,QAAT,CAAkBL,CAAlB,EAAqB;MAC7B,OAAOA,CAAC,CAACM,GAAF,CAAM,CAAN,CAAP,CAD6B,CACZ;IAClB,CAbgB;IAcjBC,IAAI,EAAE,SAASA,IAAT,CAAcP,CAAd,EAAiB;MACrB,OAAOA,CAAC,CAACM,GAAF,CAAM,CAAN,CAAP;IACD;EAhBgB,CAAP,CAAZ;AAkBD,CA/C6C,CAAvC"}, "metadata": {}, "sourceType": "module"}