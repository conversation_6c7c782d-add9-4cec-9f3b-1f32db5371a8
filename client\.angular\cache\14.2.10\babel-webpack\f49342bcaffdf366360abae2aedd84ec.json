{"ast": null, "code": "/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { BigNumberDependencies } from './dependenciesBigNumberClass.generated.js';\nimport { UnitDependencies } from './dependenciesUnitClass.generated.js';\nimport { createRydberg } from '../../factoriesAny.js';\nexport var rydbergDependencies = {\n  BigNumberDependencies,\n  UnitDependencies,\n  createRydberg\n};", "map": {"version": 3, "names": ["BigNumberDependencies", "UnitDependencies", "createRydberg", "rydbergDependencies"], "sources": ["D:/work/joyserver/client/node_modules/mathjs/lib/esm/entry/dependenciesAny/dependenciesRydberg.generated.js"], "sourcesContent": ["/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { BigNumberDependencies } from './dependenciesBigNumberClass.generated.js';\nimport { UnitDependencies } from './dependenciesUnitClass.generated.js';\nimport { createRydberg } from '../../factoriesAny.js';\nexport var rydbergDependencies = {\n  BigNumberDependencies,\n  UnitDependencies,\n  createRydberg\n};"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,qBAAT,QAAsC,2CAAtC;AACA,SAASC,gBAAT,QAAiC,sCAAjC;AACA,SAASC,aAAT,QAA8B,uBAA9B;AACA,OAAO,IAAIC,mBAAmB,GAAG;EAC/BH,qBAD+B;EAE/BC,gBAF+B;EAG/BC;AAH+B,CAA1B"}, "metadata": {}, "sourceType": "module"}