{"ast": null, "code": "import Decimal from 'decimal.js';\nimport { factory } from '../../utils/factory.js';\nvar name = 'BigNumber';\nvar dependencies = ['?on', 'config'];\nexport var createBigNumberClass = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    on,\n    config\n  } = _ref;\n  var BigNumber = Decimal.clone({\n    precision: config.precision,\n    modulo: Decimal.EUCLID\n  });\n  BigNumber.prototype = Object.create(BigNumber.prototype);\n  /**\n   * Attach type information\n   */\n\n  BigNumber.prototype.type = 'BigNumber';\n  BigNumber.prototype.isBigNumber = true;\n  /**\n   * Get a JSON representation of a BigNumber containing\n   * type information\n   * @returns {Object} Returns a JSON object structured as:\n   *                   `{\"mathjs\": \"BigNumber\", \"value\": \"0.2\"}`\n   */\n\n  BigNumber.prototype.toJSON = function () {\n    return {\n      mathjs: 'BigNumber',\n      value: this.toString()\n    };\n  };\n  /**\n   * Instantiate a BigNumber from a JSON object\n   * @param {Object} json  a JSON object structured as:\n   *                       `{\"mathjs\": \"BigNumber\", \"value\": \"0.2\"}`\n   * @return {BigNumber}\n   */\n\n\n  BigNumber.fromJSON = function (json) {\n    return new BigNumber(json.value);\n  };\n\n  if (on) {\n    // listen for changed in the configuration, automatically apply changed precision\n    on('config', function (curr, prev) {\n      if (curr.precision !== prev.precision) {\n        BigNumber.config({\n          precision: curr.precision\n        });\n      }\n    });\n  }\n\n  return BigNumber;\n}, {\n  isClass: true\n});", "map": null, "metadata": {}, "sourceType": "module"}