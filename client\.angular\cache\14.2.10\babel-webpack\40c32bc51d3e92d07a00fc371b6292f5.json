{"ast": null, "code": "import { factory } from '../../../utils/factory.js';\nimport { createSolveValidation } from './utils/solveValidation.js';\nvar name = 'usolveAll';\nvar dependencies = ['typed', 'matrix', 'divideScalar', 'multiplyScalar', 'subtractScalar', 'equalScalar', 'DenseMatrix'];\nexport var createUsolveAll = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed,\n    matrix,\n    divideScalar,\n    multiplyScalar,\n    subtractScalar,\n    equalScalar,\n    DenseMatrix\n  } = _ref;\n  var solveValidation = createSolveValidation({\n    DenseMatrix\n  });\n  /**\n   * Finds all solutions of a linear equation system by backward substitution. Matrix must be an upper triangular matrix.\n   *\n   * `U * x = b`\n   *\n   * Syntax:\n   *\n   *    math.usolveAll(U, b)\n   *\n   * Examples:\n   *\n   *    const a = [[-2, 3], [2, 1]]\n   *    const b = [11, 9]\n   *    const x = usolveAll(a, b)  // [ [[8], [9]] ]\n   *\n   * See also:\n   *\n   *    usolve, lup, slu, usolve, lusolve\n   *\n   * @param {Matrix, Array} U       A N x N matrix or array (U)\n   * @param {Matrix, Array} b       A column vector with the b values\n   *\n   * @return {DenseMatrix[] | Array[]}  An array of affine-independent column vectors (x) that solve the linear system\n   */\n\n  return typed(name, {\n    'SparseMatrix, Array | Matrix': function SparseMatrix_Array__Matrix(m, b) {\n      return _sparseBackwardSubstitution(m, b);\n    },\n    'DenseMatrix, Array | Matrix': function DenseMatrix_Array__Matrix(m, b) {\n      return _denseBackwardSubstitution(m, b);\n    },\n    'Array, Array | Matrix': function Array_Array__Matrix(a, b) {\n      var m = matrix(a);\n\n      var R = _denseBackwardSubstitution(m, b);\n\n      return R.map(r => r.valueOf());\n    }\n  });\n\n  function _denseBackwardSubstitution(m, b_) {\n    // the algorithm is derived from\n    // https://www.overleaf.com/read/csvgqdxggyjv\n    // array of right-hand sides\n    var B = [solveValidation(m, b_, true)._data.map(e => e[0])];\n    var M = m._data;\n    var rows = m._size[0];\n    var columns = m._size[1]; // loop columns backwards\n\n    for (var i = columns - 1; i >= 0; i--) {\n      var L = B.length; // loop right-hand sides\n\n      for (var k = 0; k < L; k++) {\n        var b = B[k];\n\n        if (!equalScalar(M[i][i], 0)) {\n          // non-singular row\n          b[i] = divideScalar(b[i], M[i][i]);\n\n          for (var j = i - 1; j >= 0; j--) {\n            // b[j] -= b[i] * M[j,i]\n            b[j] = subtractScalar(b[j], multiplyScalar(b[i], M[j][i]));\n          }\n        } else if (!equalScalar(b[i], 0)) {\n          // singular row, nonzero RHS\n          if (k === 0) {\n            // There is no valid solution\n            return [];\n          } else {\n            // This RHS is invalid but other solutions may still exist\n            B.splice(k, 1);\n            k -= 1;\n            L -= 1;\n          }\n        } else if (k === 0) {\n          // singular row, RHS is zero\n          var bNew = [...b];\n          bNew[i] = 1;\n\n          for (var _j = i - 1; _j >= 0; _j--) {\n            bNew[_j] = subtractScalar(bNew[_j], M[_j][i]);\n          }\n\n          B.push(bNew);\n        }\n      }\n    }\n\n    return B.map(x => new DenseMatrix({\n      data: x.map(e => [e]),\n      size: [rows, 1]\n    }));\n  }\n\n  function _sparseBackwardSubstitution(m, b_) {\n    // array of right-hand sides\n    var B = [solveValidation(m, b_, true)._data.map(e => e[0])];\n    var rows = m._size[0];\n    var columns = m._size[1];\n    var values = m._values;\n    var index = m._index;\n    var ptr = m._ptr; // loop columns backwards\n\n    for (var i = columns - 1; i >= 0; i--) {\n      var L = B.length; // loop right-hand sides\n\n      for (var k = 0; k < L; k++) {\n        var b = B[k]; // values & indices (column i)\n\n        var iValues = [];\n        var iIndices = []; // first & last indeces in column\n\n        var firstIndex = ptr[i];\n        var lastIndex = ptr[i + 1]; // find the value at [i, i]\n\n        var Mii = 0;\n\n        for (var j = lastIndex - 1; j >= firstIndex; j--) {\n          var J = index[j]; // check row\n\n          if (J === i) {\n            Mii = values[j];\n          } else if (J < i) {\n            // store upper triangular\n            iValues.push(values[j]);\n            iIndices.push(J);\n          }\n        }\n\n        if (!equalScalar(Mii, 0)) {\n          // non-singular row\n          b[i] = divideScalar(b[i], Mii); // loop upper triangular\n\n          for (var _j2 = 0, _lastIndex = iIndices.length; _j2 < _lastIndex; _j2++) {\n            var _J = iIndices[_j2];\n            b[_J] = subtractScalar(b[_J], multiplyScalar(b[i], iValues[_j2]));\n          }\n        } else if (!equalScalar(b[i], 0)) {\n          // singular row, nonzero RHS\n          if (k === 0) {\n            // There is no valid solution\n            return [];\n          } else {\n            // This RHS is invalid but other solutions may still exist\n            B.splice(k, 1);\n            k -= 1;\n            L -= 1;\n          }\n        } else if (k === 0) {\n          // singular row, RHS is zero\n          var bNew = [...b];\n          bNew[i] = 1; // loop upper triangular\n\n          for (var _j3 = 0, _lastIndex2 = iIndices.length; _j3 < _lastIndex2; _j3++) {\n            var _J2 = iIndices[_j3];\n            bNew[_J2] = subtractScalar(bNew[_J2], iValues[_j3]);\n          }\n\n          B.push(bNew);\n        }\n      }\n    }\n\n    return B.map(x => new DenseMatrix({\n      data: x.map(e => [e]),\n      size: [rows, 1]\n    }));\n  }\n});", "map": null, "metadata": {}, "sourceType": "module"}