{"ast": null, "code": "export var gammaDocs = {\n  name: 'gamma',\n  category: 'Probability',\n  syntax: ['gamma(n)'],\n  description: 'Compute the gamma function. For small values, the <PERSON><PERSON><PERSON><PERSON> approximation is used, and for large values the extended <PERSON> approximation.',\n  examples: ['gamma(4)', '3!', 'gamma(1/2)', 'sqrt(pi)'],\n  seealso: ['factorial']\n};", "map": null, "metadata": {}, "sourceType": "module"}