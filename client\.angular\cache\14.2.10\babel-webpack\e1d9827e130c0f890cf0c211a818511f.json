{"ast": null, "code": "import { AsyncScheduler } from './AsyncScheduler';\nexport class AsapScheduler extends AsyncScheduler {\n  flush(action) {\n    this.active = true;\n    this.scheduled = undefined;\n    const {\n      actions\n    } = this;\n    let error;\n    let index = -1;\n    let count = actions.length;\n    action = action || actions.shift();\n\n    do {\n      if (error = action.execute(action.state, action.delay)) {\n        break;\n      }\n    } while (++index < count && (action = actions.shift()));\n\n    this.active = false;\n\n    if (error) {\n      while (++index < count && (action = actions.shift())) {\n        action.unsubscribe();\n      }\n\n      throw error;\n    }\n  }\n\n}", "map": {"version": 3, "names": ["AsyncScheduler", "AsapScheduler", "flush", "action", "active", "scheduled", "undefined", "actions", "error", "index", "count", "length", "shift", "execute", "state", "delay", "unsubscribe"], "sources": ["D:/work/joyserver/client/node_modules/@angular-slider/ngx-slider/node_modules/rxjs/_esm2015/internal/scheduler/AsapScheduler.js"], "sourcesContent": ["import { AsyncScheduler } from './AsyncScheduler';\nexport class AsapScheduler extends AsyncScheduler {\n    flush(action) {\n        this.active = true;\n        this.scheduled = undefined;\n        const { actions } = this;\n        let error;\n        let index = -1;\n        let count = actions.length;\n        action = action || actions.shift();\n        do {\n            if (error = action.execute(action.state, action.delay)) {\n                break;\n            }\n        } while (++index < count && (action = actions.shift()));\n        this.active = false;\n        if (error) {\n            while (++index < count && (action = actions.shift())) {\n                action.unsubscribe();\n            }\n            throw error;\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,cAAT,QAA+B,kBAA/B;AACA,OAAO,MAAMC,aAAN,SAA4BD,cAA5B,CAA2C;EAC9CE,KAAK,CAACC,MAAD,EAAS;IACV,KAAKC,MAAL,GAAc,IAAd;IACA,KAAKC,SAAL,GAAiBC,SAAjB;IACA,MAAM;MAAEC;IAAF,IAAc,IAApB;IACA,IAAIC,KAAJ;IACA,IAAIC,KAAK,GAAG,CAAC,CAAb;IACA,IAAIC,KAAK,GAAGH,OAAO,CAACI,MAApB;IACAR,MAAM,GAAGA,MAAM,IAAII,OAAO,CAACK,KAAR,EAAnB;;IACA,GAAG;MACC,IAAIJ,KAAK,GAAGL,MAAM,CAACU,OAAP,CAAeV,MAAM,CAACW,KAAtB,EAA6BX,MAAM,CAACY,KAApC,CAAZ,EAAwD;QACpD;MACH;IACJ,CAJD,QAIS,EAAEN,KAAF,GAAUC,KAAV,KAAoBP,MAAM,GAAGI,OAAO,CAACK,KAAR,EAA7B,CAJT;;IAKA,KAAKR,MAAL,GAAc,KAAd;;IACA,IAAII,KAAJ,EAAW;MACP,OAAO,EAAEC,KAAF,GAAUC,KAAV,KAAoBP,MAAM,GAAGI,OAAO,CAACK,KAAR,EAA7B,CAAP,EAAsD;QAClDT,MAAM,CAACa,WAAP;MACH;;MACD,MAAMR,KAAN;IACH;EACJ;;AArB6C"}, "metadata": {}, "sourceType": "module"}