{"ast": null, "code": "export var LOG10EDocs = {\n  name: 'LOG10E',\n  category: 'Constants',\n  syntax: ['LOG10E'],\n  description: 'Returns the base-10 logarithm of E, approximately equal to 0.434',\n  examples: ['LOG10E', 'log(e, 10)'],\n  seealso: []\n};", "map": {"version": 3, "names": ["LOG10EDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["D:/work/joyserver/client/node_modules/mathjs/lib/esm/expression/embeddedDocs/constants/LOG10E.js"], "sourcesContent": ["export var LOG10EDocs = {\n  name: 'LOG10E',\n  category: 'Constants',\n  syntax: ['LOG10E'],\n  description: 'Returns the base-10 logarithm of E, approximately equal to 0.434',\n  examples: ['LOG10E', 'log(e, 10)'],\n  seealso: []\n};"], "mappings": "AAAA,OAAO,IAAIA,UAAU,GAAG;EACtBC,IAAI,EAAE,QADgB;EAEtBC,QAAQ,EAAE,WAFY;EAGtBC,MAAM,EAAE,CAAC,QAAD,CAHc;EAItBC,WAAW,EAAE,kEAJS;EAKtBC,QAAQ,EAAE,CAAC,QAAD,EAAW,YAAX,CALY;EAMtBC,OAAO,EAAE;AANa,CAAjB"}, "metadata": {}, "sourceType": "module"}