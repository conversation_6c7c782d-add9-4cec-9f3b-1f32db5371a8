{"ast": null, "code": "import getComputedStyle from \"./getComputedStyle.js\";\nexport default function isScrollParent(element) {\n  // Firefox wants us to check `-x` and `-y` variations as well\n  var _getComputedStyle = getComputedStyle(element),\n      overflow = _getComputedStyle.overflow,\n      overflowX = _getComputedStyle.overflowX,\n      overflowY = _getComputedStyle.overflowY;\n\n  return /auto|scroll|overlay|hidden/.test(overflow + overflowY + overflowX);\n}", "map": {"version": 3, "names": ["getComputedStyle", "isScrollParent", "element", "_getComputedStyle", "overflow", "overflowX", "overflowY", "test"], "sources": ["D:/work/joyserver/client/node_modules/@popperjs/core/lib/dom-utils/isScrollParent.js"], "sourcesContent": ["import getComputedStyle from \"./getComputedStyle.js\";\nexport default function isScrollParent(element) {\n  // Firefox wants us to check `-x` and `-y` variations as well\n  var _getComputedStyle = getComputedStyle(element),\n      overflow = _getComputedStyle.overflow,\n      overflowX = _getComputedStyle.overflowX,\n      overflowY = _getComputedStyle.overflowY;\n\n  return /auto|scroll|overlay|hidden/.test(overflow + overflowY + overflowX);\n}"], "mappings": "AAAA,OAAOA,gBAAP,MAA6B,uBAA7B;AACA,eAAe,SAASC,cAAT,CAAwBC,OAAxB,EAAiC;EAC9C;EACA,IAAIC,iBAAiB,GAAGH,gBAAgB,CAACE,OAAD,CAAxC;EAAA,IACIE,QAAQ,GAAGD,iBAAiB,CAACC,QADjC;EAAA,IAEIC,SAAS,GAAGF,iBAAiB,CAACE,SAFlC;EAAA,IAGIC,SAAS,GAAGH,iBAAiB,CAACG,SAHlC;;EAKA,OAAO,6BAA6BC,IAA7B,CAAkCH,QAAQ,GAAGE,SAAX,GAAuBD,SAAzD,CAAP;AACD"}, "metadata": {}, "sourceType": "module"}