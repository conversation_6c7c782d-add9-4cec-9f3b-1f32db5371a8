{"ast": null, "code": "import { Subscriber } from '../Subscriber';\nexport function mapTo(value) {\n  return source => source.lift(new MapToOperator(value));\n}\n\nclass MapToOperator {\n  constructor(value) {\n    this.value = value;\n  }\n\n  call(subscriber, source) {\n    return source.subscribe(new MapToSubscriber(subscriber, this.value));\n  }\n\n}\n\nclass MapToSubscriber extends Subscriber {\n  constructor(destination, value) {\n    super(destination);\n    this.value = value;\n  }\n\n  _next(x) {\n    this.destination.next(this.value);\n  }\n\n} //# sourceMappingURL=mapTo.js.map", "map": null, "metadata": {}, "sourceType": "module"}