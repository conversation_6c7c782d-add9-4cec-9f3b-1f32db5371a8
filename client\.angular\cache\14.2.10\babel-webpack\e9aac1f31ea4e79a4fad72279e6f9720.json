{"ast": null, "code": "import { clone } from '../../utils/object.js';\nimport { format } from '../../utils/string.js';\nimport { factory } from '../../utils/factory.js';\nvar name = 'transpose';\nvar dependencies = ['typed', 'matrix'];\nexport var createTranspose = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed,\n    matrix\n  } = _ref;\n  /**\n   * Transpose a matrix. All values of the matrix are reflected over its\n   * main diagonal. Only applicable to two dimensional matrices containing\n   * a vector (i.e. having size `[1,n]` or `[n,1]`). One dimensional\n   * vectors and scalars return the input unchanged.\n   *\n   * Syntax:\n   *\n   *     math.transpose(x)\n   *\n   * Examples:\n   *\n   *     const A = [[1, 2, 3], [4, 5, 6]]\n   *     math.transpose(A)               // returns [[1, 4], [2, 5], [3, 6]]\n   *\n   * See also:\n   *\n   *     diag, inv, subset, squeeze\n   *\n   * @param {Array | Matrix} x  Matrix to be transposed\n   * @return {Array | Matrix}   The transposed matrix\n   */\n\n  return typed(name, {\n    Array: x => transposeMatrix(matrix(x)).valueOf(),\n    Matrix: transposeMatrix,\n    any: clone // scalars\n\n  });\n\n  function transposeMatrix(x) {\n    // matrix size\n    var size = x.size(); // result\n\n    var c; // process dimensions\n\n    switch (size.length) {\n      case 1:\n        // vector\n        c = x.clone();\n        break;\n\n      case 2:\n        {\n          // rows and columns\n          var rows = size[0];\n          var columns = size[1]; // check columns\n\n          if (columns === 0) {\n            // throw exception\n            throw new RangeError('Cannot transpose a 2D matrix with no columns (size: ' + format(size) + ')');\n          } // process storage format\n\n\n          switch (x.storage()) {\n            case 'dense':\n              c = _denseTranspose(x, rows, columns);\n              break;\n\n            case 'sparse':\n              c = _sparseTranspose(x, rows, columns);\n              break;\n          }\n        }\n        break;\n\n      default:\n        // multi dimensional\n        throw new RangeError('Matrix must be a vector or two dimensional (size: ' + format(size) + ')');\n    }\n\n    return c;\n  }\n\n  function _denseTranspose(m, rows, columns) {\n    // matrix array\n    var data = m._data; // transposed matrix data\n\n    var transposed = [];\n    var transposedRow; // loop columns\n\n    for (var j = 0; j < columns; j++) {\n      // initialize row\n      transposedRow = transposed[j] = []; // loop rows\n\n      for (var i = 0; i < rows; i++) {\n        // set data\n        transposedRow[i] = clone(data[i][j]);\n      }\n    } // return matrix\n\n\n    return m.createDenseMatrix({\n      data: transposed,\n      size: [columns, rows],\n      datatype: m._datatype\n    });\n  }\n\n  function _sparseTranspose(m, rows, columns) {\n    // matrix arrays\n    var values = m._values;\n    var index = m._index;\n    var ptr = m._ptr; // result matrices\n\n    var cvalues = values ? [] : undefined;\n    var cindex = [];\n    var cptr = []; // row counts\n\n    var w = [];\n\n    for (var x = 0; x < rows; x++) {\n      w[x] = 0;\n    } // vars\n\n\n    var p, l, j; // loop values in matrix\n\n    for (p = 0, l = index.length; p < l; p++) {\n      // number of values in row\n      w[index[p]]++;\n    } // cumulative sum\n\n\n    var sum = 0; // initialize cptr with the cummulative sum of row counts\n\n    for (var i = 0; i < rows; i++) {\n      // update cptr\n      cptr.push(sum); // update sum\n\n      sum += w[i]; // update w\n\n      w[i] = cptr[i];\n    } // update cptr\n\n\n    cptr.push(sum); // loop columns\n\n    for (j = 0; j < columns; j++) {\n      // values & index in column\n      for (var k0 = ptr[j], k1 = ptr[j + 1], k = k0; k < k1; k++) {\n        // C values & index\n        var q = w[index[k]]++; // C[j, i] = A[i, j]\n\n        cindex[q] = j; // check we need to process values (pattern matrix)\n\n        if (values) {\n          cvalues[q] = clone(values[k]);\n        }\n      }\n    } // return matrix\n\n\n    return m.createSparseMatrix({\n      values: cvalues,\n      index: cindex,\n      ptr: cptr,\n      size: [columns, rows],\n      datatype: m._datatype\n    });\n  }\n});", "map": {"version": 3, "names": ["clone", "format", "factory", "name", "dependencies", "createTranspose", "_ref", "typed", "matrix", "Array", "x", "transposeMatrix", "valueOf", "Matrix", "any", "size", "c", "length", "rows", "columns", "RangeError", "storage", "_denseTranspose", "_sparseTranspose", "m", "data", "_data", "transposed", "transposedRow", "j", "i", "createDenseMatrix", "datatype", "_datatype", "values", "_values", "index", "_index", "ptr", "_ptr", "cvalues", "undefined", "cindex", "cptr", "w", "p", "l", "sum", "push", "k0", "k1", "k", "q", "createSparseMatrix"], "sources": ["D:/work/joyserver/client/node_modules/mathjs/lib/esm/function/matrix/transpose.js"], "sourcesContent": ["import { clone } from '../../utils/object.js';\nimport { format } from '../../utils/string.js';\nimport { factory } from '../../utils/factory.js';\nvar name = 'transpose';\nvar dependencies = ['typed', 'matrix'];\nexport var createTranspose = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed,\n    matrix\n  } = _ref;\n  /**\n   * Transpose a matrix. All values of the matrix are reflected over its\n   * main diagonal. Only applicable to two dimensional matrices containing\n   * a vector (i.e. having size `[1,n]` or `[n,1]`). One dimensional\n   * vectors and scalars return the input unchanged.\n   *\n   * Syntax:\n   *\n   *     math.transpose(x)\n   *\n   * Examples:\n   *\n   *     const A = [[1, 2, 3], [4, 5, 6]]\n   *     math.transpose(A)               // returns [[1, 4], [2, 5], [3, 6]]\n   *\n   * See also:\n   *\n   *     diag, inv, subset, squeeze\n   *\n   * @param {Array | Matrix} x  Matrix to be transposed\n   * @return {Array | Matrix}   The transposed matrix\n   */\n  return typed(name, {\n    Array: x => transposeMatrix(matrix(x)).valueOf(),\n    Matrix: transposeMatrix,\n    any: clone // scalars\n  });\n  function transposeMatrix(x) {\n    // matrix size\n    var size = x.size();\n\n    // result\n    var c;\n\n    // process dimensions\n    switch (size.length) {\n      case 1:\n        // vector\n        c = x.clone();\n        break;\n      case 2:\n        {\n          // rows and columns\n          var rows = size[0];\n          var columns = size[1];\n\n          // check columns\n          if (columns === 0) {\n            // throw exception\n            throw new RangeError('Cannot transpose a 2D matrix with no columns (size: ' + format(size) + ')');\n          }\n\n          // process storage format\n          switch (x.storage()) {\n            case 'dense':\n              c = _denseTranspose(x, rows, columns);\n              break;\n            case 'sparse':\n              c = _sparseTranspose(x, rows, columns);\n              break;\n          }\n        }\n        break;\n      default:\n        // multi dimensional\n        throw new RangeError('Matrix must be a vector or two dimensional (size: ' + format(size) + ')');\n    }\n    return c;\n  }\n  function _denseTranspose(m, rows, columns) {\n    // matrix array\n    var data = m._data;\n    // transposed matrix data\n    var transposed = [];\n    var transposedRow;\n    // loop columns\n    for (var j = 0; j < columns; j++) {\n      // initialize row\n      transposedRow = transposed[j] = [];\n      // loop rows\n      for (var i = 0; i < rows; i++) {\n        // set data\n        transposedRow[i] = clone(data[i][j]);\n      }\n    }\n    // return matrix\n    return m.createDenseMatrix({\n      data: transposed,\n      size: [columns, rows],\n      datatype: m._datatype\n    });\n  }\n  function _sparseTranspose(m, rows, columns) {\n    // matrix arrays\n    var values = m._values;\n    var index = m._index;\n    var ptr = m._ptr;\n    // result matrices\n    var cvalues = values ? [] : undefined;\n    var cindex = [];\n    var cptr = [];\n    // row counts\n    var w = [];\n    for (var x = 0; x < rows; x++) {\n      w[x] = 0;\n    }\n    // vars\n    var p, l, j;\n    // loop values in matrix\n    for (p = 0, l = index.length; p < l; p++) {\n      // number of values in row\n      w[index[p]]++;\n    }\n    // cumulative sum\n    var sum = 0;\n    // initialize cptr with the cummulative sum of row counts\n    for (var i = 0; i < rows; i++) {\n      // update cptr\n      cptr.push(sum);\n      // update sum\n      sum += w[i];\n      // update w\n      w[i] = cptr[i];\n    }\n    // update cptr\n    cptr.push(sum);\n    // loop columns\n    for (j = 0; j < columns; j++) {\n      // values & index in column\n      for (var k0 = ptr[j], k1 = ptr[j + 1], k = k0; k < k1; k++) {\n        // C values & index\n        var q = w[index[k]]++;\n        // C[j, i] = A[i, j]\n        cindex[q] = j;\n        // check we need to process values (pattern matrix)\n        if (values) {\n          cvalues[q] = clone(values[k]);\n        }\n      }\n    }\n    // return matrix\n    return m.createSparseMatrix({\n      values: cvalues,\n      index: cindex,\n      ptr: cptr,\n      size: [columns, rows],\n      datatype: m._datatype\n    });\n  }\n});"], "mappings": "AAAA,SAASA,KAAT,QAAsB,uBAAtB;AACA,SAASC,MAAT,QAAuB,uBAAvB;AACA,SAASC,OAAT,QAAwB,wBAAxB;AACA,IAAIC,IAAI,GAAG,WAAX;AACA,IAAIC,YAAY,GAAG,CAAC,OAAD,EAAU,QAAV,CAAnB;AACA,OAAO,IAAIC,eAAe,GAAG,eAAeH,OAAO,CAACC,IAAD,EAAOC,YAAP,EAAqBE,IAAI,IAAI;EAC9E,IAAI;IACFC,KADE;IAEFC;EAFE,IAGAF,IAHJ;EAIA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EACE,OAAOC,KAAK,CAACJ,IAAD,EAAO;IACjBM,KAAK,EAAEC,CAAC,IAAIC,eAAe,CAACH,MAAM,CAACE,CAAD,CAAP,CAAf,CAA2BE,OAA3B,EADK;IAEjBC,MAAM,EAAEF,eAFS;IAGjBG,GAAG,EAAEd,KAHY,CAGN;;EAHM,CAAP,CAAZ;;EAKA,SAASW,eAAT,CAAyBD,CAAzB,EAA4B;IAC1B;IACA,IAAIK,IAAI,GAAGL,CAAC,CAACK,IAAF,EAAX,CAF0B,CAI1B;;IACA,IAAIC,CAAJ,CAL0B,CAO1B;;IACA,QAAQD,IAAI,CAACE,MAAb;MACE,KAAK,CAAL;QACE;QACAD,CAAC,GAAGN,CAAC,CAACV,KAAF,EAAJ;QACA;;MACF,KAAK,CAAL;QACE;UACE;UACA,IAAIkB,IAAI,GAAGH,IAAI,CAAC,CAAD,CAAf;UACA,IAAII,OAAO,GAAGJ,IAAI,CAAC,CAAD,CAAlB,CAHF,CAKE;;UACA,IAAII,OAAO,KAAK,CAAhB,EAAmB;YACjB;YACA,MAAM,IAAIC,UAAJ,CAAe,yDAAyDnB,MAAM,CAACc,IAAD,CAA/D,GAAwE,GAAvF,CAAN;UACD,CATH,CAWE;;;UACA,QAAQL,CAAC,CAACW,OAAF,EAAR;YACE,KAAK,OAAL;cACEL,CAAC,GAAGM,eAAe,CAACZ,CAAD,EAAIQ,IAAJ,EAAUC,OAAV,CAAnB;cACA;;YACF,KAAK,QAAL;cACEH,CAAC,GAAGO,gBAAgB,CAACb,CAAD,EAAIQ,IAAJ,EAAUC,OAAV,CAApB;cACA;UANJ;QAQD;QACD;;MACF;QACE;QACA,MAAM,IAAIC,UAAJ,CAAe,uDAAuDnB,MAAM,CAACc,IAAD,CAA7D,GAAsE,GAArF,CAAN;IA9BJ;;IAgCA,OAAOC,CAAP;EACD;;EACD,SAASM,eAAT,CAAyBE,CAAzB,EAA4BN,IAA5B,EAAkCC,OAAlC,EAA2C;IACzC;IACA,IAAIM,IAAI,GAAGD,CAAC,CAACE,KAAb,CAFyC,CAGzC;;IACA,IAAIC,UAAU,GAAG,EAAjB;IACA,IAAIC,aAAJ,CALyC,CAMzC;;IACA,KAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGV,OAApB,EAA6BU,CAAC,EAA9B,EAAkC;MAChC;MACAD,aAAa,GAAGD,UAAU,CAACE,CAAD,CAAV,GAAgB,EAAhC,CAFgC,CAGhC;;MACA,KAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGZ,IAApB,EAA0BY,CAAC,EAA3B,EAA+B;QAC7B;QACAF,aAAa,CAACE,CAAD,CAAb,GAAmB9B,KAAK,CAACyB,IAAI,CAACK,CAAD,CAAJ,CAAQD,CAAR,CAAD,CAAxB;MACD;IACF,CAfwC,CAgBzC;;;IACA,OAAOL,CAAC,CAACO,iBAAF,CAAoB;MACzBN,IAAI,EAAEE,UADmB;MAEzBZ,IAAI,EAAE,CAACI,OAAD,EAAUD,IAAV,CAFmB;MAGzBc,QAAQ,EAAER,CAAC,CAACS;IAHa,CAApB,CAAP;EAKD;;EACD,SAASV,gBAAT,CAA0BC,CAA1B,EAA6BN,IAA7B,EAAmCC,OAAnC,EAA4C;IAC1C;IACA,IAAIe,MAAM,GAAGV,CAAC,CAACW,OAAf;IACA,IAAIC,KAAK,GAAGZ,CAAC,CAACa,MAAd;IACA,IAAIC,GAAG,GAAGd,CAAC,CAACe,IAAZ,CAJ0C,CAK1C;;IACA,IAAIC,OAAO,GAAGN,MAAM,GAAG,EAAH,GAAQO,SAA5B;IACA,IAAIC,MAAM,GAAG,EAAb;IACA,IAAIC,IAAI,GAAG,EAAX,CAR0C,CAS1C;;IACA,IAAIC,CAAC,GAAG,EAAR;;IACA,KAAK,IAAIlC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGQ,IAApB,EAA0BR,CAAC,EAA3B,EAA+B;MAC7BkC,CAAC,CAAClC,CAAD,CAAD,GAAO,CAAP;IACD,CAbyC,CAc1C;;;IACA,IAAImC,CAAJ,EAAOC,CAAP,EAAUjB,CAAV,CAf0C,CAgB1C;;IACA,KAAKgB,CAAC,GAAG,CAAJ,EAAOC,CAAC,GAAGV,KAAK,CAACnB,MAAtB,EAA8B4B,CAAC,GAAGC,CAAlC,EAAqCD,CAAC,EAAtC,EAA0C;MACxC;MACAD,CAAC,CAACR,KAAK,CAACS,CAAD,CAAN,CAAD;IACD,CApByC,CAqB1C;;;IACA,IAAIE,GAAG,GAAG,CAAV,CAtB0C,CAuB1C;;IACA,KAAK,IAAIjB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGZ,IAApB,EAA0BY,CAAC,EAA3B,EAA+B;MAC7B;MACAa,IAAI,CAACK,IAAL,CAAUD,GAAV,EAF6B,CAG7B;;MACAA,GAAG,IAAIH,CAAC,CAACd,CAAD,CAAR,CAJ6B,CAK7B;;MACAc,CAAC,CAACd,CAAD,CAAD,GAAOa,IAAI,CAACb,CAAD,CAAX;IACD,CA/ByC,CAgC1C;;;IACAa,IAAI,CAACK,IAAL,CAAUD,GAAV,EAjC0C,CAkC1C;;IACA,KAAKlB,CAAC,GAAG,CAAT,EAAYA,CAAC,GAAGV,OAAhB,EAAyBU,CAAC,EAA1B,EAA8B;MAC5B;MACA,KAAK,IAAIoB,EAAE,GAAGX,GAAG,CAACT,CAAD,CAAZ,EAAiBqB,EAAE,GAAGZ,GAAG,CAACT,CAAC,GAAG,CAAL,CAAzB,EAAkCsB,CAAC,GAAGF,EAA3C,EAA+CE,CAAC,GAAGD,EAAnD,EAAuDC,CAAC,EAAxD,EAA4D;QAC1D;QACA,IAAIC,CAAC,GAAGR,CAAC,CAACR,KAAK,CAACe,CAAD,CAAN,CAAD,EAAR,CAF0D,CAG1D;;QACAT,MAAM,CAACU,CAAD,CAAN,GAAYvB,CAAZ,CAJ0D,CAK1D;;QACA,IAAIK,MAAJ,EAAY;UACVM,OAAO,CAACY,CAAD,CAAP,GAAapD,KAAK,CAACkC,MAAM,CAACiB,CAAD,CAAP,CAAlB;QACD;MACF;IACF,CA/CyC,CAgD1C;;;IACA,OAAO3B,CAAC,CAAC6B,kBAAF,CAAqB;MAC1BnB,MAAM,EAAEM,OADkB;MAE1BJ,KAAK,EAAEM,MAFmB;MAG1BJ,GAAG,EAAEK,IAHqB;MAI1B5B,IAAI,EAAE,CAACI,OAAD,EAAUD,IAAV,CAJoB;MAK1Bc,QAAQ,EAAER,CAAC,CAACS;IALc,CAArB,CAAP;EAOD;AACF,CA1JkD,CAA5C"}, "metadata": {}, "sourceType": "module"}