{"ast": null, "code": "import { factory } from '../../utils/factory.js';\nimport { isCollection } from '../../utils/is.js';\nvar name = 'std';\nvar dependencies = ['typed', 'map', 'sqrt', 'variance'];\nexport var createStd = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed,\n    map,\n    sqrt,\n    variance\n  } = _ref;\n  /**\n   * Compute the standard deviation of a matrix or a  list with values.\n   * The standard deviations is defined as the square root of the variance:\n   * `std(A) = sqrt(variance(A))`.\n   * In case of a (multi dimensional) array or matrix, the standard deviation\n   * over all elements will be calculated by default, unless an axis is specified\n   * in which case the standard deviation will be computed along that axis.\n   *\n   * Additionally, it is possible to compute the standard deviation along the rows\n   * or columns of a matrix by specifying the dimension as the second argument.\n   *\n   * Optionally, the type of normalization can be specified as the final\n   * parameter. The parameter `normalization` can be one of the following values:\n   *\n   * - 'unbiased' (default) The sum of squared errors is divided by (n - 1)\n   * - 'uncorrected'        The sum of squared errors is divided by n\n   * - 'biased'             The sum of squared errors is divided by (n + 1)\n   *\n   *\n   * Syntax:\n   *\n   *     math.std(a, b, c, ...)\n   *     math.std(A)\n   *     math.std(A, normalization)\n   *     math.std(A, dimension)\n   *     math.std(A, dimension, normalization)\n   *\n   * Examples:\n   *\n   *     math.std(2, 4, 6)                     // returns 2\n   *     math.std([2, 4, 6, 8])                // returns 2.581988897471611\n   *     math.std([2, 4, 6, 8], 'uncorrected') // returns 2.23606797749979\n   *     math.std([2, 4, 6, 8], 'biased')      // returns 2\n   *\n   *     math.std([[1, 2, 3], [4, 5, 6]])      // returns 1.8708286933869707\n   *     math.std([[1, 2, 3], [4, 6, 8]], 0)    // returns [2.1213203435596424, 2.8284271247461903, 3.5355339059327378]\n   *     math.std([[1, 2, 3], [4, 6, 8]], 1)    // returns [1, 2]\n   *     math.std([[1, 2, 3], [4, 6, 8]], 1, 'biased') // returns [0.7071067811865476, 1.4142135623730951]\n   *\n   * See also:\n   *\n   *    mean, median, max, min, prod, sum, variance\n   *\n   * @param {Array | Matrix} array\n   *                        A single matrix or or multiple scalar values\n   * @param {string} [normalization='unbiased']\n   *                        Determines how to normalize the variance.\n   *                        Choose 'unbiased' (default), 'uncorrected', or 'biased'.\n   * @param dimension {number | BigNumber}\n   *                        Determines the axis to compute the standard deviation for a matrix\n   * @return {*} The standard deviation\n   */\n\n  return typed(name, {\n    // std([a, b, c, d, ...])\n    'Array | Matrix': _std,\n    // std([a, b, c, d, ...], normalization)\n    'Array | Matrix, string': _std,\n    // std([a, b, c, c, ...], dim)\n    'Array | Matrix, number | BigNumber': _std,\n    // std([a, b, c, c, ...], dim, normalization)\n    'Array | Matrix, number | BigNumber, string': _std,\n    // std(a, b, c, d, ...)\n    '...': function _(args) {\n      return _std(args);\n    }\n  });\n\n  function _std(array, normalization) {\n    if (array.length === 0) {\n      throw new SyntaxError('Function std requires one or more parameters (0 provided)');\n    }\n\n    try {\n      var v = variance.apply(null, arguments);\n\n      if (isCollection(v)) {\n        return map(v, sqrt);\n      } else {\n        return sqrt(v);\n      }\n    } catch (err) {\n      if (err instanceof TypeError && err.message.includes(' variance')) {\n        throw new TypeError(err.message.replace(' variance', ' std'));\n      } else {\n        throw err;\n      }\n    }\n  }\n});", "map": {"version": 3, "names": ["factory", "isCollection", "name", "dependencies", "createStd", "_ref", "typed", "map", "sqrt", "variance", "_std", "_", "args", "array", "normalization", "length", "SyntaxError", "v", "apply", "arguments", "err", "TypeError", "message", "includes", "replace"], "sources": ["D:/work/joyserver/client/node_modules/mathjs/lib/esm/function/statistics/std.js"], "sourcesContent": ["import { factory } from '../../utils/factory.js';\nimport { isCollection } from '../../utils/is.js';\nvar name = 'std';\nvar dependencies = ['typed', 'map', 'sqrt', 'variance'];\nexport var createStd = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed,\n    map,\n    sqrt,\n    variance\n  } = _ref;\n  /**\n   * Compute the standard deviation of a matrix or a  list with values.\n   * The standard deviations is defined as the square root of the variance:\n   * `std(A) = sqrt(variance(A))`.\n   * In case of a (multi dimensional) array or matrix, the standard deviation\n   * over all elements will be calculated by default, unless an axis is specified\n   * in which case the standard deviation will be computed along that axis.\n   *\n   * Additionally, it is possible to compute the standard deviation along the rows\n   * or columns of a matrix by specifying the dimension as the second argument.\n   *\n   * Optionally, the type of normalization can be specified as the final\n   * parameter. The parameter `normalization` can be one of the following values:\n   *\n   * - 'unbiased' (default) The sum of squared errors is divided by (n - 1)\n   * - 'uncorrected'        The sum of squared errors is divided by n\n   * - 'biased'             The sum of squared errors is divided by (n + 1)\n   *\n   *\n   * Syntax:\n   *\n   *     math.std(a, b, c, ...)\n   *     math.std(A)\n   *     math.std(A, normalization)\n   *     math.std(A, dimension)\n   *     math.std(A, dimension, normalization)\n   *\n   * Examples:\n   *\n   *     math.std(2, 4, 6)                     // returns 2\n   *     math.std([2, 4, 6, 8])                // returns 2.581988897471611\n   *     math.std([2, 4, 6, 8], 'uncorrected') // returns 2.23606797749979\n   *     math.std([2, 4, 6, 8], 'biased')      // returns 2\n   *\n   *     math.std([[1, 2, 3], [4, 5, 6]])      // returns 1.8708286933869707\n   *     math.std([[1, 2, 3], [4, 6, 8]], 0)    // returns [2.1213203435596424, 2.8284271247461903, 3.5355339059327378]\n   *     math.std([[1, 2, 3], [4, 6, 8]], 1)    // returns [1, 2]\n   *     math.std([[1, 2, 3], [4, 6, 8]], 1, 'biased') // returns [0.7071067811865476, 1.4142135623730951]\n   *\n   * See also:\n   *\n   *    mean, median, max, min, prod, sum, variance\n   *\n   * @param {Array | Matrix} array\n   *                        A single matrix or or multiple scalar values\n   * @param {string} [normalization='unbiased']\n   *                        Determines how to normalize the variance.\n   *                        Choose 'unbiased' (default), 'uncorrected', or 'biased'.\n   * @param dimension {number | BigNumber}\n   *                        Determines the axis to compute the standard deviation for a matrix\n   * @return {*} The standard deviation\n   */\n  return typed(name, {\n    // std([a, b, c, d, ...])\n    'Array | Matrix': _std,\n    // std([a, b, c, d, ...], normalization)\n    'Array | Matrix, string': _std,\n    // std([a, b, c, c, ...], dim)\n    'Array | Matrix, number | BigNumber': _std,\n    // std([a, b, c, c, ...], dim, normalization)\n    'Array | Matrix, number | BigNumber, string': _std,\n    // std(a, b, c, d, ...)\n    '...': function _(args) {\n      return _std(args);\n    }\n  });\n  function _std(array, normalization) {\n    if (array.length === 0) {\n      throw new SyntaxError('Function std requires one or more parameters (0 provided)');\n    }\n    try {\n      var v = variance.apply(null, arguments);\n      if (isCollection(v)) {\n        return map(v, sqrt);\n      } else {\n        return sqrt(v);\n      }\n    } catch (err) {\n      if (err instanceof TypeError && err.message.includes(' variance')) {\n        throw new TypeError(err.message.replace(' variance', ' std'));\n      } else {\n        throw err;\n      }\n    }\n  }\n});"], "mappings": "AAAA,SAASA,OAAT,QAAwB,wBAAxB;AACA,SAASC,YAAT,QAA6B,mBAA7B;AACA,IAAIC,IAAI,GAAG,KAAX;AACA,IAAIC,YAAY,GAAG,CAAC,OAAD,EAAU,KAAV,EAAiB,MAAjB,EAAyB,UAAzB,CAAnB;AACA,OAAO,IAAIC,SAAS,GAAG,eAAeJ,OAAO,CAACE,IAAD,EAAOC,YAAP,EAAqBE,IAAI,IAAI;EACxE,IAAI;IACFC,KADE;IAEFC,GAFE;IAGFC,IAHE;IAIFC;EAJE,IAKAJ,IALJ;EAMA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EACE,OAAOC,KAAK,CAACJ,IAAD,EAAO;IACjB;IACA,kBAAkBQ,IAFD;IAGjB;IACA,0BAA0BA,IAJT;IAKjB;IACA,sCAAsCA,IANrB;IAOjB;IACA,8CAA8CA,IAR7B;IASjB;IACA,OAAO,SAASC,CAAT,CAAWC,IAAX,EAAiB;MACtB,OAAOF,IAAI,CAACE,IAAD,CAAX;IACD;EAZgB,CAAP,CAAZ;;EAcA,SAASF,IAAT,CAAcG,KAAd,EAAqBC,aAArB,EAAoC;IAClC,IAAID,KAAK,CAACE,MAAN,KAAiB,CAArB,EAAwB;MACtB,MAAM,IAAIC,WAAJ,CAAgB,2DAAhB,CAAN;IACD;;IACD,IAAI;MACF,IAAIC,CAAC,GAAGR,QAAQ,CAACS,KAAT,CAAe,IAAf,EAAqBC,SAArB,CAAR;;MACA,IAAIlB,YAAY,CAACgB,CAAD,CAAhB,EAAqB;QACnB,OAAOV,GAAG,CAACU,CAAD,EAAIT,IAAJ,CAAV;MACD,CAFD,MAEO;QACL,OAAOA,IAAI,CAACS,CAAD,CAAX;MACD;IACF,CAPD,CAOE,OAAOG,GAAP,EAAY;MACZ,IAAIA,GAAG,YAAYC,SAAf,IAA4BD,GAAG,CAACE,OAAJ,CAAYC,QAAZ,CAAqB,WAArB,CAAhC,EAAmE;QACjE,MAAM,IAAIF,SAAJ,CAAcD,GAAG,CAACE,OAAJ,CAAYE,OAAZ,CAAoB,WAApB,EAAiC,MAAjC,CAAd,CAAN;MACD,CAFD,MAEO;QACL,MAAMJ,GAAN;MACD;IACF;EACF;AACF,CA5F4C,CAAtC"}, "metadata": {}, "sourceType": "module"}