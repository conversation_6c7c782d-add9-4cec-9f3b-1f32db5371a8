{"ast": null, "code": "import { SimpleOuterSubscriber, innerSubscribe, SimpleInnerSubscriber } from '../innerSubscribe';\nexport function debounce(durationSelector) {\n  return source => source.lift(new DebounceOperator(durationSelector));\n}\n\nclass DebounceOperator {\n  constructor(durationSelector) {\n    this.durationSelector = durationSelector;\n  }\n\n  call(subscriber, source) {\n    return source.subscribe(new DebounceSubscriber(subscriber, this.durationSelector));\n  }\n\n}\n\nclass DebounceSubscriber extends SimpleOuterSubscriber {\n  constructor(destination, durationSelector) {\n    super(destination);\n    this.durationSelector = durationSelector;\n    this.hasValue = false;\n  }\n\n  _next(value) {\n    try {\n      const result = this.durationSelector.call(this, value);\n\n      if (result) {\n        this._tryNext(value, result);\n      }\n    } catch (err) {\n      this.destination.error(err);\n    }\n  }\n\n  _complete() {\n    this.emitValue();\n    this.destination.complete();\n  }\n\n  _tryNext(value, duration) {\n    let subscription = this.durationSubscription;\n    this.value = value;\n    this.hasValue = true;\n\n    if (subscription) {\n      subscription.unsubscribe();\n      this.remove(subscription);\n    }\n\n    subscription = innerSubscribe(duration, new SimpleInnerSubscriber(this));\n\n    if (subscription && !subscription.closed) {\n      this.add(this.durationSubscription = subscription);\n    }\n  }\n\n  notifyNext() {\n    this.emitValue();\n  }\n\n  notifyComplete() {\n    this.emitValue();\n  }\n\n  emitValue() {\n    if (this.hasValue) {\n      const value = this.value;\n      const subscription = this.durationSubscription;\n\n      if (subscription) {\n        this.durationSubscription = undefined;\n        subscription.unsubscribe();\n        this.remove(subscription);\n      }\n\n      this.value = undefined;\n      this.hasValue = false;\n\n      super._next(value);\n    }\n  }\n\n} //# sourceMappingURL=debounce.js.map", "map": null, "metadata": {}, "sourceType": "module"}