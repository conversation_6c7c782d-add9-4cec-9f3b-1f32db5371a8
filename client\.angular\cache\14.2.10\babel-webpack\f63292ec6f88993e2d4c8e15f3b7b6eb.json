{"ast": null, "code": "export var rotationMatrixDocs = {\n  name: 'rotationMatrix',\n  category: 'Matrix',\n  syntax: ['rotationMatrix(theta)', 'rotationMatrix(theta, v)', 'rotationMatrix(theta, v, format)'],\n  description: 'Returns a 2-D rotation matrix (2x2) for a given angle (in radians). ' + 'Returns a 2-D rotation matrix (3x3) of a given angle (in radians) around given axis.',\n  examples: ['rotationMatrix(pi / 2)', 'rotationMatrix(unit(\"45deg\"), [0, 0, 1])', 'rotationMatrix(1, matrix([0, 0, 1]), \"sparse\")'],\n  seealso: ['cos', 'sin']\n};", "map": {"version": 3, "names": ["rotationMatrixDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["D:/work/joyserver/client/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/matrix/rotationMatrix.js"], "sourcesContent": ["export var rotationMatrixDocs = {\n  name: 'rotationMatrix',\n  category: 'Matrix',\n  syntax: ['rotationMatrix(theta)', 'rotationMatrix(theta, v)', 'rotationMatrix(theta, v, format)'],\n  description: 'Returns a 2-D rotation matrix (2x2) for a given angle (in radians). ' + 'Returns a 2-D rotation matrix (3x3) of a given angle (in radians) around given axis.',\n  examples: ['rotationMatrix(pi / 2)', 'rotationMatrix(unit(\"45deg\"), [0, 0, 1])', 'rotationMatrix(1, matrix([0, 0, 1]), \"sparse\")'],\n  seealso: ['cos', 'sin']\n};"], "mappings": "AAAA,OAAO,IAAIA,kBAAkB,GAAG;EAC9BC,IAAI,EAAE,gBADwB;EAE9BC,QAAQ,EAAE,QAFoB;EAG9BC,MAAM,EAAE,CAAC,uBAAD,EAA0B,0BAA1B,EAAsD,kCAAtD,CAHsB;EAI9BC,WAAW,EAAE,yEAAyE,sFAJxD;EAK9BC,QAAQ,EAAE,CAAC,wBAAD,EAA2B,0CAA3B,EAAuE,gDAAvE,CALoB;EAM9BC,OAAO,EAAE,CAAC,KAAD,EAAQ,KAAR;AANqB,CAAzB"}, "metadata": {}, "sourceType": "module"}