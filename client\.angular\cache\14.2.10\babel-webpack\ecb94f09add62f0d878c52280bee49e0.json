{"ast": null, "code": "import _asyncToGenerator from \"D:/work/joyserver/client/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { lastValueFrom } from \"rxjs\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ngx-translate/core\";\nimport * as i2 from \"@core/modal/custom-alert/custom-alert.service\";\nexport class CheckEnvService {\n  constructor(translate, _alertSvc) {\n    var _this = this;\n\n    this.translate = translate;\n    this._alertSvc = _alertSvc;\n    this.isInstalledTWLanguage = true;\n\n    if (window.joyshell) {\n      _asyncToGenerator(function* () {\n        _this.inputMethods = yield joyshell.IME.availables();\n        _this.isInstalledTWLanguage = yield joyshell.isInstalledTWLanguagePacks();\n      })();\n    }\n  }\n\n  getIMLack(IMToCheckByPinYin) {\n    const IMLack = [];\n    const IMsInPc = this.inputMethods; // 本机安装输入法\n\n    /*const IMsInPc = [\r\n      {\"name\": \"中文(简体)\"},\r\n      {\"name\": \"微软拼音新体验输入风格\"},\r\n      {\"name\": \"王码五笔型输入法98版\"},\r\n      {\"name\": \"万能五笔输入法\"},\r\n      {\"name\": \"极品输入法\"},\r\n      {\"name\": \"谷歌拼音输入法\"},\r\n      {\"name\": \"搜狗五笔输入法\"},\r\n      {\"name\": \"搜狗拼音输入法\"},\r\n      {\"name\": \"王码五笔型输入法86版\"},\r\n      {\"name\": \"微软拼音 ABC 输入风格\"},\r\n      {\"name\": \"QQ拼音输入法\"},\r\n      {\"name\": \"简体全拼\"},\r\n      {\"name\": \"极点五笔\"},\r\n      {\"name\": \"讯飞输入法\"},\r\n      {\"name\": \"百度\"},\r\n      {\"name\": \"华宇拼音输入法\"}\r\n    ];*/\n\n    const IMsToCheck = this.getIMToCheck(IMToCheckByPinYin); // 云端需要检测输入法\n    // console.log(IMsNeededChar);\n\n    for (const IMToCheckKey in IMsToCheck) {\n      // 如果不存在push进去\n      if (!this.checkIM(IMToCheckKey, IMsInPc, IMsToCheck)) {\n        IMLack.push(IMToCheckKey);\n      } else {\n        // 如果存在且是繁体输入法，则需检查语言包是否安装\n        const fanTi = [\"microsoftfanzhong\", \"microsoftchangjie\", \"microsoftquick\", \"microsoftbopomofo\"];\n\n        if (fanTi.includes(IMToCheckKey)) {\n          console.log(\"isInstalledTWLanguage: \", this.isInstalledTWLanguage, IMToCheckKey);\n\n          if (!this.isInstalledTWLanguage) {\n            // 加\"_language\"，用于管理机区分显示未安装语言包\n            IMLack.push(IMToCheckKey + \"_language\");\n          }\n        }\n      }\n    }\n\n    console.log(\"- IMs lacked：\" + IMLack.join(\"、\"));\n    return IMLack;\n  } // IMToCheckKey: \"sougoushurufa\"\n  // IMsToCheck: { \"jipin\": [\"极品\"], \"sougoushurufa\": [\"搜狗拼音\", \"搜狗\"]}\n  // IMsInPc: [{\"name\": \"微软拼音 ABC 输入风格\"}, {\"name\": \"微软拼音 ABC 输入风格\"}]\n\n\n  checkIM(IMToCheckKey, IMsInPc, IMsToCheck) {\n    let available = false;\n    const matchList = IMsToCheck[IMToCheckKey]; // 需要检查的关键字\n\n    matchList.forEach(matchKey => {\n      // console.log(item);\n      if (!IMsInPc || !IMsInPc.length) {\n        return;\n      }\n\n      for (const IMInPc of IMsInPc) {\n        switch (IMToCheckKey) {\n          case \"wangma86\":\n          case \"wangma98\":\n            {\n              // 王码（输入法名称和版本分开）: 王码五笔型输入法98版 / 王码五笔型输入法86版\n              const version = IMToCheckKey.substring(6);\n\n              if (IMInPc.name.includes(\"王码\") && IMInPc.name.includes(version)) {\n                available = true;\n              }\n\n              break;\n            }\n\n          case \"microsoftjianzhong\":\n            {\n              // 注：微软拼音 ABC 不属于 微软拼音，属于 ABC\n              if (IMInPc.name.includes(matchKey) && !IMInPc.name.includes(\"ABC\")) {\n                available = true;\n              }\n\n              break;\n            }\n\n          case \"sougoushurufa\":\n            {\n              // sougoushurufa检查：搜狗拼音 | 搜狗；搜狗五笔检查：搜狗五笔\n              const isMatched = IMInPc.name.includes(matchKey);\n\n              if (isMatched && (matchKey == \"搜狗拼音\" || matchKey == \"搜狗\" && !IMInPc.name.includes(\"五笔\"))) {\n                available = true;\n              }\n\n              break;\n            }\n\n          default:\n            {\n              if (IMInPc.name.includes(matchKey)) {\n                available = true;\n              }\n\n              break;\n            }\n        }\n      }\n    }); // console.log(IMToCheckKey + \":\" + available);\n\n    return available;\n  }\n\n  getIMToCheck(IMToCheckByPinYin) {\n    const IMpreset = {\n      jipin: [\"极品\"],\n      sougoushurufa: [\"搜狗拼音\", \"搜狗\"],\n      microsoftchangjie: [\"仓颉\", \"倉頡\", \"ChangJie\"],\n      microsoftquick: [\"速成\", \"Quick\"],\n      microsoftbopomofo: [\"注音\", \"Phonetic\", \"Bopomofo\"],\n      sougouwubi: [\"搜狗五笔\"],\n      ABC: [\"ABC\"],\n      quanpin: [\"全拼\", \"QuanPin\"],\n      wangma86: [\"王码86\"],\n      wangma98: [\"王码98\"],\n      google: [\"谷歌\"],\n      wanneng: [\"万能\"],\n      microsoftjianzhong: [\"微软拼音\", \"Microsoft Pinyin\"],\n      microsoftfanzhong: [\"微軟拼音\"],\n      qqpinyin: [\"QQ拼音输入法\"],\n      jidianwubi: [\"极点五笔\"],\n      xunfeipinyin: [\"讯飞输入法\"],\n      baidupinyin: [\"百度\"],\n      ziguangpinyin: [\"华宇拼音输入法\"]\n    };\n    const IMsNeededChar = {};\n    IMToCheckByPinYin.forEach(item => {\n      if (item in IMpreset) {\n        IMsNeededChar[item] = IMpreset[item];\n      } else {\n        // 自定义输入法检查:  标准名称,可能名称1,可能名称2,...\n        IMsNeededChar[item] = item.split(\",\");\n      }\n    });\n    return IMsNeededChar;\n  }\n\n  getAllIM() {\n    const IMsInPc = this.inputMethods; // console.log(this.inputMethods);\n\n    /*const IMsInPc = [\r\n      {\"name\": \"中文(简体)\"},\r\n      {\"name\": \"微软拼音新体验输入风格\"},\r\n      {\"name\": \"王码五笔型输入法98版\"},\r\n      {\"name\": \"万能五笔输入法\"},\r\n      {\"name\": \"极品输入法\"},\r\n      {\"name\": \"谷歌拼音输入法\"},\r\n      {\"name\": \"搜狗五笔输入法\"},\r\n      {\"name\": \"搜狗拼音输入法\"},\r\n      {\"name\": \"王码五笔型输入法86版\"},\r\n      {\"name\": \"微软拼音 ABC 输入风格\"},\r\n      {\"name\": \"QQ拼音输入法\"},\r\n      {\"name\": \"简体全拼\"}\r\n    ];*/\n\n    const newIMs = [];\n    IMsInPc.forEach(function (v) {\n      newIMs.push(v.name);\n    });\n    console.log(\"- IMs exist:\" + newIMs.join(\"、\"));\n    return newIMs;\n  }\n\n  checkDiskFree() {\n    var _this2 = this;\n\n    return _asyncToGenerator(function* () {\n      if (window.joyshell) {\n        const minFreeDisk = joyshell.Settings.REQ_FREE_DISK || 100; // 要求剩余磁盘空间(MB)\n\n        if (minFreeDisk <= 0) {\n          return true;\n        }\n\n        const disk = yield window.joyshell.CheckDiskFree();\n\n        if (disk.free < minFreeDisk) {\n          const transVariable = {\n            freeSize: disk.free,\n            checkNum: minFreeDisk\n          };\n          const content = yield lastValueFrom(_this2.translate.get(\"diskCheck.diskCheckHint\", transVariable));\n          console.log(`* Disk check: free (${transVariable.freeSize}M), require (${minFreeDisk}M)`);\n\n          _this2._alertSvc.setValue({\n            status: true,\n            info: {\n              bodyText: content\n            }\n          });\n\n          return false;\n        }\n      }\n\n      return true;\n    })();\n  }\n\n}\n\nCheckEnvService.ɵfac = function CheckEnvService_Factory(t) {\n  return new (t || CheckEnvService)(i0.ɵɵinject(i1.TranslateService), i0.ɵɵinject(i2.CustomAlertService));\n};\n\nCheckEnvService.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n  token: CheckEnvService,\n  factory: CheckEnvService.ɵfac,\n  providedIn: \"root\"\n});", "map": null, "metadata": {}, "sourceType": "module"}