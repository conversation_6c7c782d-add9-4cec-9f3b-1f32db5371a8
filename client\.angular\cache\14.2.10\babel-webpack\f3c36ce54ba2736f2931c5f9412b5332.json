{"ast": null, "code": "/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createForEach } from '../../factoriesAny.js';\nexport var forEachDependencies = {\n  typedDependencies,\n  createForEach\n};", "map": {"version": 3, "names": ["typedDependencies", "createForEach", "forEachDependencies"], "sources": ["D:/work/joyserver/client/node_modules/mathjs/lib/esm/entry/dependenciesAny/dependenciesForEach.generated.js"], "sourcesContent": ["/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createForEach } from '../../factoriesAny.js';\nexport var forEachDependencies = {\n  typedDependencies,\n  createForEach\n};"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,iBAAT,QAAkC,kCAAlC;AACA,SAASC,aAAT,QAA8B,uBAA9B;AACA,OAAO,IAAIC,mBAAmB,GAAG;EAC/BF,iBAD+B;EAE/BC;AAF+B,CAA1B"}, "metadata": {}, "sourceType": "module"}