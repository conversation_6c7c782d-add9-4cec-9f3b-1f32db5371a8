{"ast": null, "code": "import { Subscriber } from '../Subscriber';\nimport { Notification } from '../Notification';\nexport function materialize() {\n  return function materializeOperatorFunction(source) {\n    return source.lift(new MaterializeOperator());\n  };\n}\n\nclass MaterializeOperator {\n  call(subscriber, source) {\n    return source.subscribe(new MaterializeSubscriber(subscriber));\n  }\n\n}\n\nclass MaterializeSubscriber extends Subscriber {\n  constructor(destination) {\n    super(destination);\n  }\n\n  _next(value) {\n    this.destination.next(Notification.createNext(value));\n  }\n\n  _error(err) {\n    const destination = this.destination;\n    destination.next(Notification.createError(err));\n    destination.complete();\n  }\n\n  _complete() {\n    const destination = this.destination;\n    destination.next(Notification.createComplete());\n    destination.complete();\n  }\n\n}", "map": {"version": 3, "names": ["Subscriber", "Notification", "materialize", "materializeOperatorFunction", "source", "lift", "MaterializeOperator", "call", "subscriber", "subscribe", "MaterializeSubscriber", "constructor", "destination", "_next", "value", "next", "createNext", "_error", "err", "createError", "complete", "_complete", "createComplete"], "sources": ["D:/work/joyserver/client/node_modules/@angular-slider/ngx-slider/node_modules/rxjs/_esm2015/internal/operators/materialize.js"], "sourcesContent": ["import { Subscriber } from '../Subscriber';\nimport { Notification } from '../Notification';\nexport function materialize() {\n    return function materializeOperatorFunction(source) {\n        return source.lift(new MaterializeOperator());\n    };\n}\nclass MaterializeOperator {\n    call(subscriber, source) {\n        return source.subscribe(new MaterializeSubscriber(subscriber));\n    }\n}\nclass MaterializeSubscriber extends Subscriber {\n    constructor(destination) {\n        super(destination);\n    }\n    _next(value) {\n        this.destination.next(Notification.createNext(value));\n    }\n    _error(err) {\n        const destination = this.destination;\n        destination.next(Notification.createError(err));\n        destination.complete();\n    }\n    _complete() {\n        const destination = this.destination;\n        destination.next(Notification.createComplete());\n        destination.complete();\n    }\n}\n"], "mappings": "AAAA,SAASA,UAAT,QAA2B,eAA3B;AACA,SAASC,YAAT,QAA6B,iBAA7B;AACA,OAAO,SAASC,WAAT,GAAuB;EAC1B,OAAO,SAASC,2BAAT,CAAqCC,MAArC,EAA6C;IAChD,OAAOA,MAAM,CAACC,IAAP,CAAY,IAAIC,mBAAJ,EAAZ,CAAP;EACH,CAFD;AAGH;;AACD,MAAMA,mBAAN,CAA0B;EACtBC,IAAI,CAACC,UAAD,EAAaJ,MAAb,EAAqB;IACrB,OAAOA,MAAM,CAACK,SAAP,CAAiB,IAAIC,qBAAJ,CAA0BF,UAA1B,CAAjB,CAAP;EACH;;AAHqB;;AAK1B,MAAME,qBAAN,SAAoCV,UAApC,CAA+C;EAC3CW,WAAW,CAACC,WAAD,EAAc;IACrB,MAAMA,WAAN;EACH;;EACDC,KAAK,CAACC,KAAD,EAAQ;IACT,KAAKF,WAAL,CAAiBG,IAAjB,CAAsBd,YAAY,CAACe,UAAb,CAAwBF,KAAxB,CAAtB;EACH;;EACDG,MAAM,CAACC,GAAD,EAAM;IACR,MAAMN,WAAW,GAAG,KAAKA,WAAzB;IACAA,WAAW,CAACG,IAAZ,CAAiBd,YAAY,CAACkB,WAAb,CAAyBD,GAAzB,CAAjB;IACAN,WAAW,CAACQ,QAAZ;EACH;;EACDC,SAAS,GAAG;IACR,MAAMT,WAAW,GAAG,KAAKA,WAAzB;IACAA,WAAW,CAACG,IAAZ,CAAiBd,YAAY,CAACqB,cAAb,EAAjB;IACAV,WAAW,CAACQ,QAAZ;EACH;;AAhB0C"}, "metadata": {}, "sourceType": "module"}