{"ast": null, "code": "export var cumSumDocs = {\n  name: 'cumsum',\n  category: 'Statistics',\n  syntax: ['cumsum(a, b, c, ...)', 'cumsum(A)'],\n  description: 'Compute the cumulative sum of all values.',\n  examples: ['cumsum(2, 3, 4, 1)', 'cumsum([2, 3, 4, 1])', 'cumsum([1, 2; 3, 4])', 'cumsum([1, 2; 3, 4], 1)', 'cumsum([1, 2; 3, 4], 2)'],\n  seealso: ['max', 'mean', 'median', 'min', 'prod', 'std', 'sum', 'variance']\n};", "map": null, "metadata": {}, "sourceType": "module"}