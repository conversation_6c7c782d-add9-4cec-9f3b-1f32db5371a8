{"ast": null, "code": "ace.define(\"ace/mode/doc_comment_highlight_rules\", [\"require\", \"exports\", \"module\", \"ace/lib/oop\", \"ace/mode/text_highlight_rules\"], function (acequire, exports, module) {\n  \"use strict\";\n\n  var oop = acequire(\"../lib/oop\");\n  var TextHighlightRules = acequire(\"./text_highlight_rules\").TextHighlightRules;\n\n  var DocCommentHighlightRules = function () {\n    this.$rules = {\n      \"start\": [{\n        token: \"comment.doc.tag\",\n        regex: \"@[\\\\w\\\\d_]+\" // TODO: fix email addresses\n\n      }, DocCommentHighlightRules.getTagRule(), {\n        defaultToken: \"comment.doc\",\n        caseInsensitive: true\n      }]\n    };\n  };\n\n  oop.inherits(DocCommentHighlightRules, TextHighlightRules);\n\n  DocCommentHighlightRules.getTagRule = function (start) {\n    return {\n      token: \"comment.doc.tag.storage.type\",\n      regex: \"\\\\b(?:TODO|FIXME|XXX|HACK)\\\\b\"\n    };\n  };\n\n  DocCommentHighlightRules.getStartRule = function (start) {\n    return {\n      token: \"comment.doc\",\n      // doc comment\n      regex: \"\\\\/\\\\*(?=\\\\*)\",\n      next: start\n    };\n  };\n\n  DocCommentHighlightRules.getEndRule = function (start) {\n    return {\n      token: \"comment.doc\",\n      // closing comment\n      regex: \"\\\\*\\\\/\",\n      next: start\n    };\n  };\n\n  exports.DocCommentHighlightRules = DocCommentHighlightRules;\n});\nace.define(\"ace/mode/golang_highlight_rules\", [\"require\", \"exports\", \"module\", \"ace/lib/oop\", \"ace/mode/doc_comment_highlight_rules\", \"ace/mode/text_highlight_rules\"], function (acequire, exports, module) {\n  var oop = acequire(\"../lib/oop\");\n  var DocCommentHighlightRules = acequire(\"./doc_comment_highlight_rules\").DocCommentHighlightRules;\n  var TextHighlightRules = acequire(\"./text_highlight_rules\").TextHighlightRules;\n\n  var GolangHighlightRules = function () {\n    var keywords = \"else|break|case|return|goto|if|const|select|\" + \"continue|struct|default|switch|for|range|\" + \"func|import|package|chan|defer|fallthrough|go|interface|map|range|\" + \"select|type|var\";\n    var builtinTypes = \"string|uint8|uint16|uint32|uint64|int8|int16|int32|int64|float32|\" + \"float64|complex64|complex128|byte|rune|uint|int|uintptr|bool|error\";\n    var builtinFunctions = \"new|close|cap|copy|panic|panicln|print|println|len|make|delete|real|recover|imag|append\";\n    var builtinConstants = \"nil|true|false|iota\";\n    var keywordMapper = this.createKeywordMapper({\n      \"keyword\": keywords,\n      \"constant.language\": builtinConstants,\n      \"support.function\": builtinFunctions,\n      \"support.type\": builtinTypes\n    }, \"\");\n    var stringEscapeRe = \"\\\\\\\\(?:[0-7]{3}|x\\\\h{2}|u{4}|U\\\\h{6}|[abfnrtv'\\\"\\\\\\\\])\".replace(/\\\\h/g, \"[a-fA-F\\\\d]\");\n    this.$rules = {\n      \"start\": [{\n        token: \"comment\",\n        regex: \"\\\\/\\\\/.*$\"\n      }, DocCommentHighlightRules.getStartRule(\"doc-start\"), {\n        token: \"comment.start\",\n        // multi line comment\n        regex: \"\\\\/\\\\*\",\n        next: \"comment\"\n      }, {\n        token: \"string\",\n        // single line\n        regex: /\"(?:[^\"\\\\]|\\\\.)*?\"/\n      }, {\n        token: \"string\",\n        // raw\n        regex: '`',\n        next: \"bqstring\"\n      }, {\n        token: \"constant.numeric\",\n        // rune\n        regex: \"'(?:[^\\\\'\\uD800-\\uDBFF]|[\\uD800-\\uDBFF][\\uDC00-\\uDFFF]|\" + stringEscapeRe.replace('\"', '') + \")'\"\n      }, {\n        token: \"constant.numeric\",\n        // hex\n        regex: \"0[xX][0-9a-fA-F]+\\\\b\"\n      }, {\n        token: \"constant.numeric\",\n        // float\n        regex: \"[+-]?\\\\d+(?:(?:\\\\.\\\\d*)?(?:[eE][+-]?\\\\d+)?)?\\\\b\"\n      }, {\n        token: [\"keyword\", \"text\", \"entity.name.function\"],\n        regex: \"(func)(\\\\s+)([a-zA-Z_$][a-zA-Z0-9_$]*)\\\\b\"\n      }, {\n        token: function (val) {\n          if (val[val.length - 1] == \"(\") {\n            return [{\n              type: keywordMapper(val.slice(0, -1)) || \"support.function\",\n              value: val.slice(0, -1)\n            }, {\n              type: \"paren.lparen\",\n              value: val.slice(-1)\n            }];\n          }\n\n          return keywordMapper(val) || \"identifier\";\n        },\n        regex: \"[a-zA-Z_$][a-zA-Z0-9_$]*\\\\b\\\\(?\"\n      }, {\n        token: \"keyword.operator\",\n        regex: \"!|\\\\$|%|&|\\\\*|\\\\-\\\\-|\\\\-|\\\\+\\\\+|\\\\+|~|==|=|!=|<=|>=|<<=|>>=|>>>=|<>|<|>|!|&&|\\\\|\\\\||\\\\?\\\\:|\\\\*=|%=|\\\\+=|\\\\-=|&=|\\\\^=\"\n      }, {\n        token: \"punctuation.operator\",\n        regex: \"\\\\?|\\\\:|\\\\,|\\\\;|\\\\.\"\n      }, {\n        token: \"paren.lparen\",\n        regex: \"[[({]\"\n      }, {\n        token: \"paren.rparen\",\n        regex: \"[\\\\])}]\"\n      }, {\n        token: \"text\",\n        regex: \"\\\\s+\"\n      }],\n      \"comment\": [{\n        token: \"comment.end\",\n        regex: \"\\\\*\\\\/\",\n        next: \"start\"\n      }, {\n        defaultToken: \"comment\"\n      }],\n      \"bqstring\": [{\n        token: \"string\",\n        regex: '`',\n        next: \"start\"\n      }, {\n        defaultToken: \"string\"\n      }]\n    };\n    this.embedRules(DocCommentHighlightRules, \"doc-\", [DocCommentHighlightRules.getEndRule(\"start\")]);\n  };\n\n  oop.inherits(GolangHighlightRules, TextHighlightRules);\n  exports.GolangHighlightRules = GolangHighlightRules;\n});\nace.define(\"ace/mode/matching_brace_outdent\", [\"require\", \"exports\", \"module\", \"ace/range\"], function (acequire, exports, module) {\n  \"use strict\";\n\n  var Range = acequire(\"../range\").Range;\n\n  var MatchingBraceOutdent = function () {};\n\n  (function () {\n    this.checkOutdent = function (line, input) {\n      if (!/^\\s+$/.test(line)) return false;\n      return /^\\s*\\}/.test(input);\n    };\n\n    this.autoOutdent = function (doc, row) {\n      var line = doc.getLine(row);\n      var match = line.match(/^(\\s*\\})/);\n      if (!match) return 0;\n      var column = match[1].length;\n      var openBracePos = doc.findMatchingBracket({\n        row: row,\n        column: column\n      });\n      if (!openBracePos || openBracePos.row == row) return 0;\n      var indent = this.$getIndent(doc.getLine(openBracePos.row));\n      doc.replace(new Range(row, 0, row, column - 1), indent);\n    };\n\n    this.$getIndent = function (line) {\n      return line.match(/^\\s*/)[0];\n    };\n  }).call(MatchingBraceOutdent.prototype);\n  exports.MatchingBraceOutdent = MatchingBraceOutdent;\n});\nace.define(\"ace/mode/folding/cstyle\", [\"require\", \"exports\", \"module\", \"ace/lib/oop\", \"ace/range\", \"ace/mode/folding/fold_mode\"], function (acequire, exports, module) {\n  \"use strict\";\n\n  var oop = acequire(\"../../lib/oop\");\n  var Range = acequire(\"../../range\").Range;\n  var BaseFoldMode = acequire(\"./fold_mode\").FoldMode;\n\n  var FoldMode = exports.FoldMode = function (commentRegex) {\n    if (commentRegex) {\n      this.foldingStartMarker = new RegExp(this.foldingStartMarker.source.replace(/\\|[^|]*?$/, \"|\" + commentRegex.start));\n      this.foldingStopMarker = new RegExp(this.foldingStopMarker.source.replace(/\\|[^|]*?$/, \"|\" + commentRegex.end));\n    }\n  };\n\n  oop.inherits(FoldMode, BaseFoldMode);\n  (function () {\n    this.foldingStartMarker = /([\\{\\[\\(])[^\\}\\]\\)]*$|^\\s*(\\/\\*)/;\n    this.foldingStopMarker = /^[^\\[\\{\\(]*([\\}\\]\\)])|^[\\s\\*]*(\\*\\/)/;\n    this.singleLineBlockCommentRe = /^\\s*(\\/\\*).*\\*\\/\\s*$/;\n    this.tripleStarBlockCommentRe = /^\\s*(\\/\\*\\*\\*).*\\*\\/\\s*$/;\n    this.startRegionRe = /^\\s*(\\/\\*|\\/\\/)#?region\\b/;\n    this._getFoldWidgetBase = this.getFoldWidget;\n\n    this.getFoldWidget = function (session, foldStyle, row) {\n      var line = session.getLine(row);\n\n      if (this.singleLineBlockCommentRe.test(line)) {\n        if (!this.startRegionRe.test(line) && !this.tripleStarBlockCommentRe.test(line)) return \"\";\n      }\n\n      var fw = this._getFoldWidgetBase(session, foldStyle, row);\n\n      if (!fw && this.startRegionRe.test(line)) return \"start\"; // lineCommentRegionStart\n\n      return fw;\n    };\n\n    this.getFoldWidgetRange = function (session, foldStyle, row, forceMultiline) {\n      var line = session.getLine(row);\n      if (this.startRegionRe.test(line)) return this.getCommentRegionBlock(session, line, row);\n      var match = line.match(this.foldingStartMarker);\n\n      if (match) {\n        var i = match.index;\n        if (match[1]) return this.openingBracketBlock(session, match[1], row, i);\n        var range = session.getCommentFoldRange(row, i + match[0].length, 1);\n\n        if (range && !range.isMultiLine()) {\n          if (forceMultiline) {\n            range = this.getSectionRange(session, row);\n          } else if (foldStyle != \"all\") range = null;\n        }\n\n        return range;\n      }\n\n      if (foldStyle === \"markbegin\") return;\n      var match = line.match(this.foldingStopMarker);\n\n      if (match) {\n        var i = match.index + match[0].length;\n        if (match[1]) return this.closingBracketBlock(session, match[1], row, i);\n        return session.getCommentFoldRange(row, i, -1);\n      }\n    };\n\n    this.getSectionRange = function (session, row) {\n      var line = session.getLine(row);\n      var startIndent = line.search(/\\S/);\n      var startRow = row;\n      var startColumn = line.length;\n      row = row + 1;\n      var endRow = row;\n      var maxRow = session.getLength();\n\n      while (++row < maxRow) {\n        line = session.getLine(row);\n        var indent = line.search(/\\S/);\n        if (indent === -1) continue;\n        if (startIndent > indent) break;\n        var subRange = this.getFoldWidgetRange(session, \"all\", row);\n\n        if (subRange) {\n          if (subRange.start.row <= startRow) {\n            break;\n          } else if (subRange.isMultiLine()) {\n            row = subRange.end.row;\n          } else if (startIndent == indent) {\n            break;\n          }\n        }\n\n        endRow = row;\n      }\n\n      return new Range(startRow, startColumn, endRow, session.getLine(endRow).length);\n    };\n\n    this.getCommentRegionBlock = function (session, line, row) {\n      var startColumn = line.search(/\\s*$/);\n      var maxRow = session.getLength();\n      var startRow = row;\n      var re = /^\\s*(?:\\/\\*|\\/\\/|--)#?(end)?region\\b/;\n      var depth = 1;\n\n      while (++row < maxRow) {\n        line = session.getLine(row);\n        var m = re.exec(line);\n        if (!m) continue;\n        if (m[1]) depth--;else depth++;\n        if (!depth) break;\n      }\n\n      var endRow = row;\n\n      if (endRow > startRow) {\n        return new Range(startRow, startColumn, endRow, line.length);\n      }\n    };\n  }).call(FoldMode.prototype);\n});\nace.define(\"ace/mode/golang\", [\"require\", \"exports\", \"module\", \"ace/lib/oop\", \"ace/mode/text\", \"ace/mode/golang_highlight_rules\", \"ace/mode/matching_brace_outdent\", \"ace/mode/behaviour/cstyle\", \"ace/mode/folding/cstyle\"], function (acequire, exports, module) {\n  var oop = acequire(\"../lib/oop\");\n  var TextMode = acequire(\"./text\").Mode;\n  var GolangHighlightRules = acequire(\"./golang_highlight_rules\").GolangHighlightRules;\n  var MatchingBraceOutdent = acequire(\"./matching_brace_outdent\").MatchingBraceOutdent;\n  var CstyleBehaviour = acequire(\"./behaviour/cstyle\").CstyleBehaviour;\n  var CStyleFoldMode = acequire(\"./folding/cstyle\").FoldMode;\n\n  var Mode = function () {\n    this.HighlightRules = GolangHighlightRules;\n    this.$outdent = new MatchingBraceOutdent();\n    this.foldingRules = new CStyleFoldMode();\n    this.$behaviour = new CstyleBehaviour();\n  };\n\n  oop.inherits(Mode, TextMode);\n  (function () {\n    this.lineCommentStart = \"//\";\n    this.blockComment = {\n      start: \"/*\",\n      end: \"*/\"\n    };\n\n    this.getNextLineIndent = function (state, line, tab) {\n      var indent = this.$getIndent(line);\n      var tokenizedLine = this.getTokenizer().getLineTokens(line, state);\n      var tokens = tokenizedLine.tokens;\n      var endState = tokenizedLine.state;\n\n      if (tokens.length && tokens[tokens.length - 1].type == \"comment\") {\n        return indent;\n      }\n\n      if (state == \"start\") {\n        var match = line.match(/^.*[\\{\\(\\[]\\s*$/);\n\n        if (match) {\n          indent += tab;\n        }\n      }\n\n      return indent;\n    }; //end getNextLineIndent\n\n\n    this.checkOutdent = function (state, line, input) {\n      return this.$outdent.checkOutdent(line, input);\n    };\n\n    this.autoOutdent = function (state, doc, row) {\n      this.$outdent.autoOutdent(doc, row);\n    };\n\n    this.$id = \"ace/mode/golang\";\n  }).call(Mode.prototype);\n  exports.Mode = Mode;\n});", "map": {"version": 3, "names": ["ace", "define", "acequire", "exports", "module", "oop", "TextHighlightRules", "DocCommentHighlightRules", "$rules", "token", "regex", "getTagRule", "defaultToken", "caseInsensitive", "inherits", "start", "getStartRule", "next", "getEndRule", "GolangHighlightRules", "keywords", "builtinTypes", "builtinFunctions", "builtinConstants", "keywordMapper", "createKeywordMapper", "stringEscapeRe", "replace", "val", "length", "type", "slice", "value", "embedRules", "Range", "MatchingBraceOutdent", "checkOutdent", "line", "input", "test", "autoOutdent", "doc", "row", "getLine", "match", "column", "openBracePos", "findMatchingBracket", "indent", "$getIndent", "call", "prototype", "BaseFoldMode", "FoldMode", "commentRegex", "foldingStartMarker", "RegExp", "source", "foldingStopMarker", "end", "singleLineBlockCommentRe", "tripleStarBlockCommentRe", "startRegionRe", "_getFoldWidgetBase", "getFoldWidget", "session", "foldStyle", "fw", "getFoldWidgetRange", "forceMultiline", "getCommentRegionBlock", "i", "index", "openingBracketBlock", "range", "getCommentFoldRange", "isMultiLine", "getSectionRange", "closingBracketBlock", "startIndent", "search", "startRow", "startColumn", "endRow", "maxRow", "<PERSON><PERSON><PERSON><PERSON>", "subRange", "re", "depth", "m", "exec", "TextMode", "Mode", "CstyleBehaviour", "CStyleFoldMode", "HighlightRules", "$outdent", "foldingRules", "$behaviour", "lineCommentStart", "blockComment", "getNextLineIndent", "state", "tab", "tokenizedLine", "getTokenizer", "getLineTokens", "tokens", "endState", "$id"], "sources": ["D:/work/joyserver/client/node_modules/brace/mode/golang.js"], "sourcesContent": ["ace.define(\"ace/mode/doc_comment_highlight_rules\",[\"require\",\"exports\",\"module\",\"ace/lib/oop\",\"ace/mode/text_highlight_rules\"], function(acequire, exports, module) {\n\"use strict\";\n\nvar oop = acequire(\"../lib/oop\");\nvar TextHighlightRules = acequire(\"./text_highlight_rules\").TextHighlightRules;\n\nvar DocCommentHighlightRules = function() {\n    this.$rules = {\n        \"start\" : [ {\n            token : \"comment.doc.tag\",\n            regex : \"@[\\\\w\\\\d_]+\" // TODO: fix email addresses\n        }, \n        DocCommentHighlightRules.getTagRule(),\n        {\n            defaultToken : \"comment.doc\",\n            caseInsensitive: true\n        }]\n    };\n};\n\noop.inherits(DocCommentHighlightRules, TextHighlightRules);\n\nDocCommentHighlightRules.getTagRule = function(start) {\n    return {\n        token : \"comment.doc.tag.storage.type\",\n        regex : \"\\\\b(?:TODO|FIXME|XXX|HACK)\\\\b\"\n    };\n};\n\nDocCommentHighlightRules.getStartRule = function(start) {\n    return {\n        token : \"comment.doc\", // doc comment\n        regex : \"\\\\/\\\\*(?=\\\\*)\",\n        next  : start\n    };\n};\n\nDocCommentHighlightRules.getEndRule = function (start) {\n    return {\n        token : \"comment.doc\", // closing comment\n        regex : \"\\\\*\\\\/\",\n        next  : start\n    };\n};\n\n\nexports.DocCommentHighlightRules = DocCommentHighlightRules;\n\n});\n\nace.define(\"ace/mode/golang_highlight_rules\",[\"require\",\"exports\",\"module\",\"ace/lib/oop\",\"ace/mode/doc_comment_highlight_rules\",\"ace/mode/text_highlight_rules\"], function(acequire, exports, module) {\n    var oop = acequire(\"../lib/oop\");\n    var DocCommentHighlightRules = acequire(\"./doc_comment_highlight_rules\").DocCommentHighlightRules;\n    var TextHighlightRules = acequire(\"./text_highlight_rules\").TextHighlightRules;\n\n    var GolangHighlightRules = function() {\n        var keywords = (\n            \"else|break|case|return|goto|if|const|select|\" +\n            \"continue|struct|default|switch|for|range|\" +\n            \"func|import|package|chan|defer|fallthrough|go|interface|map|range|\" +\n            \"select|type|var\"\n        );\n        var builtinTypes = (\n            \"string|uint8|uint16|uint32|uint64|int8|int16|int32|int64|float32|\" +\n            \"float64|complex64|complex128|byte|rune|uint|int|uintptr|bool|error\"\n        );\n        var builtinFunctions = (\n            \"new|close|cap|copy|panic|panicln|print|println|len|make|delete|real|recover|imag|append\"\n        );\n        var builtinConstants = (\"nil|true|false|iota\");\n\n        var keywordMapper = this.createKeywordMapper({\n            \"keyword\": keywords,\n            \"constant.language\": builtinConstants,\n            \"support.function\": builtinFunctions,\n            \"support.type\": builtinTypes\n        }, \"\");\n        \n        var stringEscapeRe = \"\\\\\\\\(?:[0-7]{3}|x\\\\h{2}|u{4}|U\\\\h{6}|[abfnrtv'\\\"\\\\\\\\])\".replace(/\\\\h/g, \"[a-fA-F\\\\d]\");\n\n        this.$rules = {\n            \"start\" : [\n                {\n                    token : \"comment\",\n                    regex : \"\\\\/\\\\/.*$\"\n                },\n                DocCommentHighlightRules.getStartRule(\"doc-start\"),\n                {\n                    token : \"comment.start\", // multi line comment\n                    regex : \"\\\\/\\\\*\",\n                    next : \"comment\"\n                }, {\n                    token : \"string\", // single line\n                    regex : /\"(?:[^\"\\\\]|\\\\.)*?\"/\n                }, {\n                    token : \"string\", // raw\n                    regex : '`',\n                    next : \"bqstring\"\n                }, {\n                    token : \"constant.numeric\", // rune\n                    regex : \"'(?:[^\\\\'\\uD800-\\uDBFF]|[\\uD800-\\uDBFF][\\uDC00-\\uDFFF]|\" + stringEscapeRe.replace('\"', '')  + \")'\"\n                }, {\n                    token : \"constant.numeric\", // hex\n                    regex : \"0[xX][0-9a-fA-F]+\\\\b\" \n                }, {\n                    token : \"constant.numeric\", // float\n                    regex : \"[+-]?\\\\d+(?:(?:\\\\.\\\\d*)?(?:[eE][+-]?\\\\d+)?)?\\\\b\"\n                }, {\n                    token : [\"keyword\", \"text\", \"entity.name.function\"],\n                    regex : \"(func)(\\\\s+)([a-zA-Z_$][a-zA-Z0-9_$]*)\\\\b\"\n                }, {\n                    token : function(val) {\n                        if (val[val.length - 1] == \"(\") {\n                            return [{\n                                type: keywordMapper(val.slice(0, -1)) || \"support.function\",\n                                value: val.slice(0, -1)\n                            }, {\n                                type: \"paren.lparen\",\n                                value: val.slice(-1)\n                            }];\n                        }\n                        \n                        return keywordMapper(val) || \"identifier\";\n                    },\n                    regex : \"[a-zA-Z_$][a-zA-Z0-9_$]*\\\\b\\\\(?\"\n                }, {\n                    token : \"keyword.operator\",\n                    regex : \"!|\\\\$|%|&|\\\\*|\\\\-\\\\-|\\\\-|\\\\+\\\\+|\\\\+|~|==|=|!=|<=|>=|<<=|>>=|>>>=|<>|<|>|!|&&|\\\\|\\\\||\\\\?\\\\:|\\\\*=|%=|\\\\+=|\\\\-=|&=|\\\\^=\"\n                }, {\n                    token : \"punctuation.operator\",\n                    regex : \"\\\\?|\\\\:|\\\\,|\\\\;|\\\\.\"\n                }, {\n                    token : \"paren.lparen\",\n                    regex : \"[[({]\"\n                }, {\n                    token : \"paren.rparen\",\n                    regex : \"[\\\\])}]\"\n                }, {\n                    token : \"text\",\n                    regex : \"\\\\s+\"\n                }\n            ],\n            \"comment\" : [\n                {\n                    token : \"comment.end\",\n                    regex : \"\\\\*\\\\/\",\n                    next : \"start\"\n                }, {\n                    defaultToken : \"comment\"\n                }\n            ],\n            \"bqstring\" : [\n                {\n                    token : \"string\",\n                    regex : '`',\n                    next : \"start\"\n                }, {\n                    defaultToken : \"string\"\n                }\n            ]\n        };\n\n        this.embedRules(DocCommentHighlightRules, \"doc-\",\n            [ DocCommentHighlightRules.getEndRule(\"start\") ]);\n    };\n    oop.inherits(GolangHighlightRules, TextHighlightRules);\n\n    exports.GolangHighlightRules = GolangHighlightRules;\n});\n\nace.define(\"ace/mode/matching_brace_outdent\",[\"require\",\"exports\",\"module\",\"ace/range\"], function(acequire, exports, module) {\n\"use strict\";\n\nvar Range = acequire(\"../range\").Range;\n\nvar MatchingBraceOutdent = function() {};\n\n(function() {\n\n    this.checkOutdent = function(line, input) {\n        if (! /^\\s+$/.test(line))\n            return false;\n\n        return /^\\s*\\}/.test(input);\n    };\n\n    this.autoOutdent = function(doc, row) {\n        var line = doc.getLine(row);\n        var match = line.match(/^(\\s*\\})/);\n\n        if (!match) return 0;\n\n        var column = match[1].length;\n        var openBracePos = doc.findMatchingBracket({row: row, column: column});\n\n        if (!openBracePos || openBracePos.row == row) return 0;\n\n        var indent = this.$getIndent(doc.getLine(openBracePos.row));\n        doc.replace(new Range(row, 0, row, column-1), indent);\n    };\n\n    this.$getIndent = function(line) {\n        return line.match(/^\\s*/)[0];\n    };\n\n}).call(MatchingBraceOutdent.prototype);\n\nexports.MatchingBraceOutdent = MatchingBraceOutdent;\n});\n\nace.define(\"ace/mode/folding/cstyle\",[\"require\",\"exports\",\"module\",\"ace/lib/oop\",\"ace/range\",\"ace/mode/folding/fold_mode\"], function(acequire, exports, module) {\n\"use strict\";\n\nvar oop = acequire(\"../../lib/oop\");\nvar Range = acequire(\"../../range\").Range;\nvar BaseFoldMode = acequire(\"./fold_mode\").FoldMode;\n\nvar FoldMode = exports.FoldMode = function(commentRegex) {\n    if (commentRegex) {\n        this.foldingStartMarker = new RegExp(\n            this.foldingStartMarker.source.replace(/\\|[^|]*?$/, \"|\" + commentRegex.start)\n        );\n        this.foldingStopMarker = new RegExp(\n            this.foldingStopMarker.source.replace(/\\|[^|]*?$/, \"|\" + commentRegex.end)\n        );\n    }\n};\noop.inherits(FoldMode, BaseFoldMode);\n\n(function() {\n    \n    this.foldingStartMarker = /([\\{\\[\\(])[^\\}\\]\\)]*$|^\\s*(\\/\\*)/;\n    this.foldingStopMarker = /^[^\\[\\{\\(]*([\\}\\]\\)])|^[\\s\\*]*(\\*\\/)/;\n    this.singleLineBlockCommentRe= /^\\s*(\\/\\*).*\\*\\/\\s*$/;\n    this.tripleStarBlockCommentRe = /^\\s*(\\/\\*\\*\\*).*\\*\\/\\s*$/;\n    this.startRegionRe = /^\\s*(\\/\\*|\\/\\/)#?region\\b/;\n    this._getFoldWidgetBase = this.getFoldWidget;\n    this.getFoldWidget = function(session, foldStyle, row) {\n        var line = session.getLine(row);\n    \n        if (this.singleLineBlockCommentRe.test(line)) {\n            if (!this.startRegionRe.test(line) && !this.tripleStarBlockCommentRe.test(line))\n                return \"\";\n        }\n    \n        var fw = this._getFoldWidgetBase(session, foldStyle, row);\n    \n        if (!fw && this.startRegionRe.test(line))\n            return \"start\"; // lineCommentRegionStart\n    \n        return fw;\n    };\n\n    this.getFoldWidgetRange = function(session, foldStyle, row, forceMultiline) {\n        var line = session.getLine(row);\n        \n        if (this.startRegionRe.test(line))\n            return this.getCommentRegionBlock(session, line, row);\n        \n        var match = line.match(this.foldingStartMarker);\n        if (match) {\n            var i = match.index;\n\n            if (match[1])\n                return this.openingBracketBlock(session, match[1], row, i);\n                \n            var range = session.getCommentFoldRange(row, i + match[0].length, 1);\n            \n            if (range && !range.isMultiLine()) {\n                if (forceMultiline) {\n                    range = this.getSectionRange(session, row);\n                } else if (foldStyle != \"all\")\n                    range = null;\n            }\n            \n            return range;\n        }\n\n        if (foldStyle === \"markbegin\")\n            return;\n\n        var match = line.match(this.foldingStopMarker);\n        if (match) {\n            var i = match.index + match[0].length;\n\n            if (match[1])\n                return this.closingBracketBlock(session, match[1], row, i);\n\n            return session.getCommentFoldRange(row, i, -1);\n        }\n    };\n    \n    this.getSectionRange = function(session, row) {\n        var line = session.getLine(row);\n        var startIndent = line.search(/\\S/);\n        var startRow = row;\n        var startColumn = line.length;\n        row = row + 1;\n        var endRow = row;\n        var maxRow = session.getLength();\n        while (++row < maxRow) {\n            line = session.getLine(row);\n            var indent = line.search(/\\S/);\n            if (indent === -1)\n                continue;\n            if  (startIndent > indent)\n                break;\n            var subRange = this.getFoldWidgetRange(session, \"all\", row);\n            \n            if (subRange) {\n                if (subRange.start.row <= startRow) {\n                    break;\n                } else if (subRange.isMultiLine()) {\n                    row = subRange.end.row;\n                } else if (startIndent == indent) {\n                    break;\n                }\n            }\n            endRow = row;\n        }\n        \n        return new Range(startRow, startColumn, endRow, session.getLine(endRow).length);\n    };\n    this.getCommentRegionBlock = function(session, line, row) {\n        var startColumn = line.search(/\\s*$/);\n        var maxRow = session.getLength();\n        var startRow = row;\n        \n        var re = /^\\s*(?:\\/\\*|\\/\\/|--)#?(end)?region\\b/;\n        var depth = 1;\n        while (++row < maxRow) {\n            line = session.getLine(row);\n            var m = re.exec(line);\n            if (!m) continue;\n            if (m[1]) depth--;\n            else depth++;\n\n            if (!depth) break;\n        }\n\n        var endRow = row;\n        if (endRow > startRow) {\n            return new Range(startRow, startColumn, endRow, line.length);\n        }\n    };\n\n}).call(FoldMode.prototype);\n\n});\n\nace.define(\"ace/mode/golang\",[\"require\",\"exports\",\"module\",\"ace/lib/oop\",\"ace/mode/text\",\"ace/mode/golang_highlight_rules\",\"ace/mode/matching_brace_outdent\",\"ace/mode/behaviour/cstyle\",\"ace/mode/folding/cstyle\"], function(acequire, exports, module) {\n\nvar oop = acequire(\"../lib/oop\");\nvar TextMode = acequire(\"./text\").Mode;\nvar GolangHighlightRules = acequire(\"./golang_highlight_rules\").GolangHighlightRules;\nvar MatchingBraceOutdent = acequire(\"./matching_brace_outdent\").MatchingBraceOutdent;\nvar CstyleBehaviour = acequire(\"./behaviour/cstyle\").CstyleBehaviour;\nvar CStyleFoldMode = acequire(\"./folding/cstyle\").FoldMode;\n\nvar Mode = function() {\n    this.HighlightRules = GolangHighlightRules;\n    this.$outdent = new MatchingBraceOutdent();\n    this.foldingRules = new CStyleFoldMode();\n    this.$behaviour = new CstyleBehaviour();\n};\noop.inherits(Mode, TextMode);\n\n(function() {\n    \n    this.lineCommentStart = \"//\";\n    this.blockComment = {start: \"/*\", end: \"*/\"};\n\n    this.getNextLineIndent = function(state, line, tab) {\n        var indent = this.$getIndent(line);\n\n        var tokenizedLine = this.getTokenizer().getLineTokens(line, state);\n        var tokens = tokenizedLine.tokens;\n        var endState = tokenizedLine.state;\n\n        if (tokens.length && tokens[tokens.length-1].type == \"comment\") {\n            return indent;\n        }\n        \n        if (state == \"start\") {\n            var match = line.match(/^.*[\\{\\(\\[]\\s*$/);\n            if (match) {\n                indent += tab;\n            }\n        }\n\n        return indent;\n    };//end getNextLineIndent\n\n    this.checkOutdent = function(state, line, input) {\n        return this.$outdent.checkOutdent(line, input);\n    };\n\n    this.autoOutdent = function(state, doc, row) {\n        this.$outdent.autoOutdent(doc, row);\n    };\n\n    this.$id = \"ace/mode/golang\";\n}).call(Mode.prototype);\n\nexports.Mode = Mode;\n});\n"], "mappings": "AAAAA,GAAG,CAACC,MAAJ,CAAW,sCAAX,EAAkD,CAAC,SAAD,EAAW,SAAX,EAAqB,QAArB,EAA8B,aAA9B,EAA4C,+BAA5C,CAAlD,EAAgI,UAASC,QAAT,EAAmBC,OAAnB,EAA4BC,MAA5B,EAAoC;EACpK;;EAEA,IAAIC,GAAG,GAAGH,QAAQ,CAAC,YAAD,CAAlB;EACA,IAAII,kBAAkB,GAAGJ,QAAQ,CAAC,wBAAD,CAAR,CAAmCI,kBAA5D;;EAEA,IAAIC,wBAAwB,GAAG,YAAW;IACtC,KAAKC,MAAL,GAAc;MACV,SAAU,CAAE;QACRC,KAAK,EAAG,iBADA;QAERC,KAAK,EAAG,aAFA,CAEc;;MAFd,CAAF,EAIVH,wBAAwB,CAACI,UAAzB,EAJU,EAKV;QACIC,YAAY,EAAG,aADnB;QAEIC,eAAe,EAAE;MAFrB,CALU;IADA,CAAd;EAWH,CAZD;;EAcAR,GAAG,CAACS,QAAJ,CAAaP,wBAAb,EAAuCD,kBAAvC;;EAEAC,wBAAwB,CAACI,UAAzB,GAAsC,UAASI,KAAT,EAAgB;IAClD,OAAO;MACHN,KAAK,EAAG,8BADL;MAEHC,KAAK,EAAG;IAFL,CAAP;EAIH,CALD;;EAOAH,wBAAwB,CAACS,YAAzB,GAAwC,UAASD,KAAT,EAAgB;IACpD,OAAO;MACHN,KAAK,EAAG,aADL;MACoB;MACvBC,KAAK,EAAG,eAFL;MAGHO,IAAI,EAAIF;IAHL,CAAP;EAKH,CAND;;EAQAR,wBAAwB,CAACW,UAAzB,GAAsC,UAAUH,KAAV,EAAiB;IACnD,OAAO;MACHN,KAAK,EAAG,aADL;MACoB;MACvBC,KAAK,EAAG,QAFL;MAGHO,IAAI,EAAIF;IAHL,CAAP;EAKH,CAND;;EASAZ,OAAO,CAACI,wBAAR,GAAmCA,wBAAnC;AAEC,CAhDD;AAkDAP,GAAG,CAACC,MAAJ,CAAW,iCAAX,EAA6C,CAAC,SAAD,EAAW,SAAX,EAAqB,QAArB,EAA8B,aAA9B,EAA4C,sCAA5C,EAAmF,+BAAnF,CAA7C,EAAkK,UAASC,QAAT,EAAmBC,OAAnB,EAA4BC,MAA5B,EAAoC;EAClM,IAAIC,GAAG,GAAGH,QAAQ,CAAC,YAAD,CAAlB;EACA,IAAIK,wBAAwB,GAAGL,QAAQ,CAAC,+BAAD,CAAR,CAA0CK,wBAAzE;EACA,IAAID,kBAAkB,GAAGJ,QAAQ,CAAC,wBAAD,CAAR,CAAmCI,kBAA5D;;EAEA,IAAIa,oBAAoB,GAAG,YAAW;IAClC,IAAIC,QAAQ,GACR,iDACA,2CADA,GAEA,oEAFA,GAGA,iBAJJ;IAMA,IAAIC,YAAY,GACZ,sEACA,oEAFJ;IAIA,IAAIC,gBAAgB,GAChB,yFADJ;IAGA,IAAIC,gBAAgB,GAAI,qBAAxB;IAEA,IAAIC,aAAa,GAAG,KAAKC,mBAAL,CAAyB;MACzC,WAAWL,QAD8B;MAEzC,qBAAqBG,gBAFoB;MAGzC,oBAAoBD,gBAHqB;MAIzC,gBAAgBD;IAJyB,CAAzB,EAKjB,EALiB,CAApB;IAOA,IAAIK,cAAc,GAAG,yDAAyDC,OAAzD,CAAiE,MAAjE,EAAyE,aAAzE,CAArB;IAEA,KAAKnB,MAAL,GAAc;MACV,SAAU,CACN;QACIC,KAAK,EAAG,SADZ;QAEIC,KAAK,EAAG;MAFZ,CADM,EAKNH,wBAAwB,CAACS,YAAzB,CAAsC,WAAtC,CALM,EAMN;QACIP,KAAK,EAAG,eADZ;QAC6B;QACzBC,KAAK,EAAG,QAFZ;QAGIO,IAAI,EAAG;MAHX,CANM,EAUH;QACCR,KAAK,EAAG,QADT;QACmB;QAClBC,KAAK,EAAG;MAFT,CAVG,EAaH;QACCD,KAAK,EAAG,QADT;QACmB;QAClBC,KAAK,EAAG,GAFT;QAGCO,IAAI,EAAG;MAHR,CAbG,EAiBH;QACCR,KAAK,EAAG,kBADT;QAC6B;QAC5BC,KAAK,EAAG,4DAA4DgB,cAAc,CAACC,OAAf,CAAuB,GAAvB,EAA4B,EAA5B,CAA5D,GAA+F;MAFxG,CAjBG,EAoBH;QACClB,KAAK,EAAG,kBADT;QAC6B;QAC5BC,KAAK,EAAG;MAFT,CApBG,EAuBH;QACCD,KAAK,EAAG,kBADT;QAC6B;QAC5BC,KAAK,EAAG;MAFT,CAvBG,EA0BH;QACCD,KAAK,EAAG,CAAC,SAAD,EAAY,MAAZ,EAAoB,sBAApB,CADT;QAECC,KAAK,EAAG;MAFT,CA1BG,EA6BH;QACCD,KAAK,EAAG,UAASmB,GAAT,EAAc;UAClB,IAAIA,GAAG,CAACA,GAAG,CAACC,MAAJ,GAAa,CAAd,CAAH,IAAuB,GAA3B,EAAgC;YAC5B,OAAO,CAAC;cACJC,IAAI,EAAEN,aAAa,CAACI,GAAG,CAACG,KAAJ,CAAU,CAAV,EAAa,CAAC,CAAd,CAAD,CAAb,IAAmC,kBADrC;cAEJC,KAAK,EAAEJ,GAAG,CAACG,KAAJ,CAAU,CAAV,EAAa,CAAC,CAAd;YAFH,CAAD,EAGJ;cACCD,IAAI,EAAE,cADP;cAECE,KAAK,EAAEJ,GAAG,CAACG,KAAJ,CAAU,CAAC,CAAX;YAFR,CAHI,CAAP;UAOH;;UAED,OAAOP,aAAa,CAACI,GAAD,CAAb,IAAsB,YAA7B;QACH,CAbF;QAcClB,KAAK,EAAG;MAdT,CA7BG,EA4CH;QACCD,KAAK,EAAG,kBADT;QAECC,KAAK,EAAG;MAFT,CA5CG,EA+CH;QACCD,KAAK,EAAG,sBADT;QAECC,KAAK,EAAG;MAFT,CA/CG,EAkDH;QACCD,KAAK,EAAG,cADT;QAECC,KAAK,EAAG;MAFT,CAlDG,EAqDH;QACCD,KAAK,EAAG,cADT;QAECC,KAAK,EAAG;MAFT,CArDG,EAwDH;QACCD,KAAK,EAAG,MADT;QAECC,KAAK,EAAG;MAFT,CAxDG,CADA;MA8DV,WAAY,CACR;QACID,KAAK,EAAG,aADZ;QAEIC,KAAK,EAAG,QAFZ;QAGIO,IAAI,EAAG;MAHX,CADQ,EAKL;QACCL,YAAY,EAAG;MADhB,CALK,CA9DF;MAuEV,YAAa,CACT;QACIH,KAAK,EAAG,QADZ;QAEIC,KAAK,EAAG,GAFZ;QAGIO,IAAI,EAAG;MAHX,CADS,EAKN;QACCL,YAAY,EAAG;MADhB,CALM;IAvEH,CAAd;IAkFA,KAAKqB,UAAL,CAAgB1B,wBAAhB,EAA0C,MAA1C,EACI,CAAEA,wBAAwB,CAACW,UAAzB,CAAoC,OAApC,CAAF,CADJ;EAEH,CA7GD;;EA8GAb,GAAG,CAACS,QAAJ,CAAaK,oBAAb,EAAmCb,kBAAnC;EAEAH,OAAO,CAACgB,oBAAR,GAA+BA,oBAA/B;AACH,CAtHD;AAwHAnB,GAAG,CAACC,MAAJ,CAAW,iCAAX,EAA6C,CAAC,SAAD,EAAW,SAAX,EAAqB,QAArB,EAA8B,WAA9B,CAA7C,EAAyF,UAASC,QAAT,EAAmBC,OAAnB,EAA4BC,MAA5B,EAAoC;EAC7H;;EAEA,IAAI8B,KAAK,GAAGhC,QAAQ,CAAC,UAAD,CAAR,CAAqBgC,KAAjC;;EAEA,IAAIC,oBAAoB,GAAG,YAAW,CAAE,CAAxC;;EAEA,CAAC,YAAW;IAER,KAAKC,YAAL,GAAoB,UAASC,IAAT,EAAeC,KAAf,EAAsB;MACtC,IAAI,CAAE,QAAQC,IAAR,CAAaF,IAAb,CAAN,EACI,OAAO,KAAP;MAEJ,OAAO,SAASE,IAAT,CAAcD,KAAd,CAAP;IACH,CALD;;IAOA,KAAKE,WAAL,GAAmB,UAASC,GAAT,EAAcC,GAAd,EAAmB;MAClC,IAAIL,IAAI,GAAGI,GAAG,CAACE,OAAJ,CAAYD,GAAZ,CAAX;MACA,IAAIE,KAAK,GAAGP,IAAI,CAACO,KAAL,CAAW,UAAX,CAAZ;MAEA,IAAI,CAACA,KAAL,EAAY,OAAO,CAAP;MAEZ,IAAIC,MAAM,GAAGD,KAAK,CAAC,CAAD,CAAL,CAASf,MAAtB;MACA,IAAIiB,YAAY,GAAGL,GAAG,CAACM,mBAAJ,CAAwB;QAACL,GAAG,EAAEA,GAAN;QAAWG,MAAM,EAAEA;MAAnB,CAAxB,CAAnB;MAEA,IAAI,CAACC,YAAD,IAAiBA,YAAY,CAACJ,GAAb,IAAoBA,GAAzC,EAA8C,OAAO,CAAP;MAE9C,IAAIM,MAAM,GAAG,KAAKC,UAAL,CAAgBR,GAAG,CAACE,OAAJ,CAAYG,YAAY,CAACJ,GAAzB,CAAhB,CAAb;MACAD,GAAG,CAACd,OAAJ,CAAY,IAAIO,KAAJ,CAAUQ,GAAV,EAAe,CAAf,EAAkBA,GAAlB,EAAuBG,MAAM,GAAC,CAA9B,CAAZ,EAA8CG,MAA9C;IACH,CAbD;;IAeA,KAAKC,UAAL,GAAkB,UAASZ,IAAT,EAAe;MAC7B,OAAOA,IAAI,CAACO,KAAL,CAAW,MAAX,EAAmB,CAAnB,CAAP;IACH,CAFD;EAIH,CA5BD,EA4BGM,IA5BH,CA4BQf,oBAAoB,CAACgB,SA5B7B;EA8BAhD,OAAO,CAACgC,oBAAR,GAA+BA,oBAA/B;AACC,CAtCD;AAwCAnC,GAAG,CAACC,MAAJ,CAAW,yBAAX,EAAqC,CAAC,SAAD,EAAW,SAAX,EAAqB,QAArB,EAA8B,aAA9B,EAA4C,WAA5C,EAAwD,4BAAxD,CAArC,EAA4H,UAASC,QAAT,EAAmBC,OAAnB,EAA4BC,MAA5B,EAAoC;EAChK;;EAEA,IAAIC,GAAG,GAAGH,QAAQ,CAAC,eAAD,CAAlB;EACA,IAAIgC,KAAK,GAAGhC,QAAQ,CAAC,aAAD,CAAR,CAAwBgC,KAApC;EACA,IAAIkB,YAAY,GAAGlD,QAAQ,CAAC,aAAD,CAAR,CAAwBmD,QAA3C;;EAEA,IAAIA,QAAQ,GAAGlD,OAAO,CAACkD,QAAR,GAAmB,UAASC,YAAT,EAAuB;IACrD,IAAIA,YAAJ,EAAkB;MACd,KAAKC,kBAAL,GAA0B,IAAIC,MAAJ,CACtB,KAAKD,kBAAL,CAAwBE,MAAxB,CAA+B9B,OAA/B,CAAuC,WAAvC,EAAoD,MAAM2B,YAAY,CAACvC,KAAvE,CADsB,CAA1B;MAGA,KAAK2C,iBAAL,GAAyB,IAAIF,MAAJ,CACrB,KAAKE,iBAAL,CAAuBD,MAAvB,CAA8B9B,OAA9B,CAAsC,WAAtC,EAAmD,MAAM2B,YAAY,CAACK,GAAtE,CADqB,CAAzB;IAGH;EACJ,CATD;;EAUAtD,GAAG,CAACS,QAAJ,CAAauC,QAAb,EAAuBD,YAAvB;EAEA,CAAC,YAAW;IAER,KAAKG,kBAAL,GAA0B,kCAA1B;IACA,KAAKG,iBAAL,GAAyB,sCAAzB;IACA,KAAKE,wBAAL,GAA+B,sBAA/B;IACA,KAAKC,wBAAL,GAAgC,0BAAhC;IACA,KAAKC,aAAL,GAAqB,2BAArB;IACA,KAAKC,kBAAL,GAA0B,KAAKC,aAA/B;;IACA,KAAKA,aAAL,GAAqB,UAASC,OAAT,EAAkBC,SAAlB,EAA6BxB,GAA7B,EAAkC;MACnD,IAAIL,IAAI,GAAG4B,OAAO,CAACtB,OAAR,CAAgBD,GAAhB,CAAX;;MAEA,IAAI,KAAKkB,wBAAL,CAA8BrB,IAA9B,CAAmCF,IAAnC,CAAJ,EAA8C;QAC1C,IAAI,CAAC,KAAKyB,aAAL,CAAmBvB,IAAnB,CAAwBF,IAAxB,CAAD,IAAkC,CAAC,KAAKwB,wBAAL,CAA8BtB,IAA9B,CAAmCF,IAAnC,CAAvC,EACI,OAAO,EAAP;MACP;;MAED,IAAI8B,EAAE,GAAG,KAAKJ,kBAAL,CAAwBE,OAAxB,EAAiCC,SAAjC,EAA4CxB,GAA5C,CAAT;;MAEA,IAAI,CAACyB,EAAD,IAAO,KAAKL,aAAL,CAAmBvB,IAAnB,CAAwBF,IAAxB,CAAX,EACI,OAAO,OAAP,CAX+C,CAW/B;;MAEpB,OAAO8B,EAAP;IACH,CAdD;;IAgBA,KAAKC,kBAAL,GAA0B,UAASH,OAAT,EAAkBC,SAAlB,EAA6BxB,GAA7B,EAAkC2B,cAAlC,EAAkD;MACxE,IAAIhC,IAAI,GAAG4B,OAAO,CAACtB,OAAR,CAAgBD,GAAhB,CAAX;MAEA,IAAI,KAAKoB,aAAL,CAAmBvB,IAAnB,CAAwBF,IAAxB,CAAJ,EACI,OAAO,KAAKiC,qBAAL,CAA2BL,OAA3B,EAAoC5B,IAApC,EAA0CK,GAA1C,CAAP;MAEJ,IAAIE,KAAK,GAAGP,IAAI,CAACO,KAAL,CAAW,KAAKW,kBAAhB,CAAZ;;MACA,IAAIX,KAAJ,EAAW;QACP,IAAI2B,CAAC,GAAG3B,KAAK,CAAC4B,KAAd;QAEA,IAAI5B,KAAK,CAAC,CAAD,CAAT,EACI,OAAO,KAAK6B,mBAAL,CAAyBR,OAAzB,EAAkCrB,KAAK,CAAC,CAAD,CAAvC,EAA4CF,GAA5C,EAAiD6B,CAAjD,CAAP;QAEJ,IAAIG,KAAK,GAAGT,OAAO,CAACU,mBAAR,CAA4BjC,GAA5B,EAAiC6B,CAAC,GAAG3B,KAAK,CAAC,CAAD,CAAL,CAASf,MAA9C,EAAsD,CAAtD,CAAZ;;QAEA,IAAI6C,KAAK,IAAI,CAACA,KAAK,CAACE,WAAN,EAAd,EAAmC;UAC/B,IAAIP,cAAJ,EAAoB;YAChBK,KAAK,GAAG,KAAKG,eAAL,CAAqBZ,OAArB,EAA8BvB,GAA9B,CAAR;UACH,CAFD,MAEO,IAAIwB,SAAS,IAAI,KAAjB,EACHQ,KAAK,GAAG,IAAR;QACP;;QAED,OAAOA,KAAP;MACH;;MAED,IAAIR,SAAS,KAAK,WAAlB,EACI;MAEJ,IAAItB,KAAK,GAAGP,IAAI,CAACO,KAAL,CAAW,KAAKc,iBAAhB,CAAZ;;MACA,IAAId,KAAJ,EAAW;QACP,IAAI2B,CAAC,GAAG3B,KAAK,CAAC4B,KAAN,GAAc5B,KAAK,CAAC,CAAD,CAAL,CAASf,MAA/B;QAEA,IAAIe,KAAK,CAAC,CAAD,CAAT,EACI,OAAO,KAAKkC,mBAAL,CAAyBb,OAAzB,EAAkCrB,KAAK,CAAC,CAAD,CAAvC,EAA4CF,GAA5C,EAAiD6B,CAAjD,CAAP;QAEJ,OAAON,OAAO,CAACU,mBAAR,CAA4BjC,GAA5B,EAAiC6B,CAAjC,EAAoC,CAAC,CAArC,CAAP;MACH;IACJ,CArCD;;IAuCA,KAAKM,eAAL,GAAuB,UAASZ,OAAT,EAAkBvB,GAAlB,EAAuB;MAC1C,IAAIL,IAAI,GAAG4B,OAAO,CAACtB,OAAR,CAAgBD,GAAhB,CAAX;MACA,IAAIqC,WAAW,GAAG1C,IAAI,CAAC2C,MAAL,CAAY,IAAZ,CAAlB;MACA,IAAIC,QAAQ,GAAGvC,GAAf;MACA,IAAIwC,WAAW,GAAG7C,IAAI,CAACR,MAAvB;MACAa,GAAG,GAAGA,GAAG,GAAG,CAAZ;MACA,IAAIyC,MAAM,GAAGzC,GAAb;MACA,IAAI0C,MAAM,GAAGnB,OAAO,CAACoB,SAAR,EAAb;;MACA,OAAO,EAAE3C,GAAF,GAAQ0C,MAAf,EAAuB;QACnB/C,IAAI,GAAG4B,OAAO,CAACtB,OAAR,CAAgBD,GAAhB,CAAP;QACA,IAAIM,MAAM,GAAGX,IAAI,CAAC2C,MAAL,CAAY,IAAZ,CAAb;QACA,IAAIhC,MAAM,KAAK,CAAC,CAAhB,EACI;QACJ,IAAK+B,WAAW,GAAG/B,MAAnB,EACI;QACJ,IAAIsC,QAAQ,GAAG,KAAKlB,kBAAL,CAAwBH,OAAxB,EAAiC,KAAjC,EAAwCvB,GAAxC,CAAf;;QAEA,IAAI4C,QAAJ,EAAc;UACV,IAAIA,QAAQ,CAACvE,KAAT,CAAe2B,GAAf,IAAsBuC,QAA1B,EAAoC;YAChC;UACH,CAFD,MAEO,IAAIK,QAAQ,CAACV,WAAT,EAAJ,EAA4B;YAC/BlC,GAAG,GAAG4C,QAAQ,CAAC3B,GAAT,CAAajB,GAAnB;UACH,CAFM,MAEA,IAAIqC,WAAW,IAAI/B,MAAnB,EAA2B;YAC9B;UACH;QACJ;;QACDmC,MAAM,GAAGzC,GAAT;MACH;;MAED,OAAO,IAAIR,KAAJ,CAAU+C,QAAV,EAAoBC,WAApB,EAAiCC,MAAjC,EAAyClB,OAAO,CAACtB,OAAR,CAAgBwC,MAAhB,EAAwBtD,MAAjE,CAAP;IACH,CA9BD;;IA+BA,KAAKyC,qBAAL,GAA6B,UAASL,OAAT,EAAkB5B,IAAlB,EAAwBK,GAAxB,EAA6B;MACtD,IAAIwC,WAAW,GAAG7C,IAAI,CAAC2C,MAAL,CAAY,MAAZ,CAAlB;MACA,IAAII,MAAM,GAAGnB,OAAO,CAACoB,SAAR,EAAb;MACA,IAAIJ,QAAQ,GAAGvC,GAAf;MAEA,IAAI6C,EAAE,GAAG,sCAAT;MACA,IAAIC,KAAK,GAAG,CAAZ;;MACA,OAAO,EAAE9C,GAAF,GAAQ0C,MAAf,EAAuB;QACnB/C,IAAI,GAAG4B,OAAO,CAACtB,OAAR,CAAgBD,GAAhB,CAAP;QACA,IAAI+C,CAAC,GAAGF,EAAE,CAACG,IAAH,CAAQrD,IAAR,CAAR;QACA,IAAI,CAACoD,CAAL,EAAQ;QACR,IAAIA,CAAC,CAAC,CAAD,CAAL,EAAUD,KAAK,GAAf,KACKA,KAAK;QAEV,IAAI,CAACA,KAAL,EAAY;MACf;;MAED,IAAIL,MAAM,GAAGzC,GAAb;;MACA,IAAIyC,MAAM,GAAGF,QAAb,EAAuB;QACnB,OAAO,IAAI/C,KAAJ,CAAU+C,QAAV,EAAoBC,WAApB,EAAiCC,MAAjC,EAAyC9C,IAAI,CAACR,MAA9C,CAAP;MACH;IACJ,CArBD;EAuBH,CArHD,EAqHGqB,IArHH,CAqHQG,QAAQ,CAACF,SArHjB;AAuHC,CA1ID;AA4IAnD,GAAG,CAACC,MAAJ,CAAW,iBAAX,EAA6B,CAAC,SAAD,EAAW,SAAX,EAAqB,QAArB,EAA8B,aAA9B,EAA4C,eAA5C,EAA4D,iCAA5D,EAA8F,iCAA9F,EAAgI,2BAAhI,EAA4J,yBAA5J,CAA7B,EAAqN,UAASC,QAAT,EAAmBC,OAAnB,EAA4BC,MAA5B,EAAoC;EAEzP,IAAIC,GAAG,GAAGH,QAAQ,CAAC,YAAD,CAAlB;EACA,IAAIyF,QAAQ,GAAGzF,QAAQ,CAAC,QAAD,CAAR,CAAmB0F,IAAlC;EACA,IAAIzE,oBAAoB,GAAGjB,QAAQ,CAAC,0BAAD,CAAR,CAAqCiB,oBAAhE;EACA,IAAIgB,oBAAoB,GAAGjC,QAAQ,CAAC,0BAAD,CAAR,CAAqCiC,oBAAhE;EACA,IAAI0D,eAAe,GAAG3F,QAAQ,CAAC,oBAAD,CAAR,CAA+B2F,eAArD;EACA,IAAIC,cAAc,GAAG5F,QAAQ,CAAC,kBAAD,CAAR,CAA6BmD,QAAlD;;EAEA,IAAIuC,IAAI,GAAG,YAAW;IAClB,KAAKG,cAAL,GAAsB5E,oBAAtB;IACA,KAAK6E,QAAL,GAAgB,IAAI7D,oBAAJ,EAAhB;IACA,KAAK8D,YAAL,GAAoB,IAAIH,cAAJ,EAApB;IACA,KAAKI,UAAL,GAAkB,IAAIL,eAAJ,EAAlB;EACH,CALD;;EAMAxF,GAAG,CAACS,QAAJ,CAAa8E,IAAb,EAAmBD,QAAnB;EAEA,CAAC,YAAW;IAER,KAAKQ,gBAAL,GAAwB,IAAxB;IACA,KAAKC,YAAL,GAAoB;MAACrF,KAAK,EAAE,IAAR;MAAc4C,GAAG,EAAE;IAAnB,CAApB;;IAEA,KAAK0C,iBAAL,GAAyB,UAASC,KAAT,EAAgBjE,IAAhB,EAAsBkE,GAAtB,EAA2B;MAChD,IAAIvD,MAAM,GAAG,KAAKC,UAAL,CAAgBZ,IAAhB,CAAb;MAEA,IAAImE,aAAa,GAAG,KAAKC,YAAL,GAAoBC,aAApB,CAAkCrE,IAAlC,EAAwCiE,KAAxC,CAApB;MACA,IAAIK,MAAM,GAAGH,aAAa,CAACG,MAA3B;MACA,IAAIC,QAAQ,GAAGJ,aAAa,CAACF,KAA7B;;MAEA,IAAIK,MAAM,CAAC9E,MAAP,IAAiB8E,MAAM,CAACA,MAAM,CAAC9E,MAAP,GAAc,CAAf,CAAN,CAAwBC,IAAxB,IAAgC,SAArD,EAAgE;QAC5D,OAAOkB,MAAP;MACH;;MAED,IAAIsD,KAAK,IAAI,OAAb,EAAsB;QAClB,IAAI1D,KAAK,GAAGP,IAAI,CAACO,KAAL,CAAW,iBAAX,CAAZ;;QACA,IAAIA,KAAJ,EAAW;UACPI,MAAM,IAAIuD,GAAV;QACH;MACJ;;MAED,OAAOvD,MAAP;IACH,CAnBD,CALQ,CAwBN;;;IAEF,KAAKZ,YAAL,GAAoB,UAASkE,KAAT,EAAgBjE,IAAhB,EAAsBC,KAAtB,EAA6B;MAC7C,OAAO,KAAK0D,QAAL,CAAc5D,YAAd,CAA2BC,IAA3B,EAAiCC,KAAjC,CAAP;IACH,CAFD;;IAIA,KAAKE,WAAL,GAAmB,UAAS8D,KAAT,EAAgB7D,GAAhB,EAAqBC,GAArB,EAA0B;MACzC,KAAKsD,QAAL,CAAcxD,WAAd,CAA0BC,GAA1B,EAA+BC,GAA/B;IACH,CAFD;;IAIA,KAAKmE,GAAL,GAAW,iBAAX;EACH,CAnCD,EAmCG3D,IAnCH,CAmCQ0C,IAAI,CAACzC,SAnCb;EAqCAhD,OAAO,CAACyF,IAAR,GAAeA,IAAf;AACC,CAvDD"}, "metadata": {}, "sourceType": "script"}