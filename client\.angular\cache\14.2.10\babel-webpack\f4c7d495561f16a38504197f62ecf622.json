{"ast": null, "code": "import { flatten } from '../../utils/array.js';\nimport { factory } from '../../utils/factory.js';\nvar name = 'setDistinct';\nvar dependencies = ['typed', 'size', 'subset', 'compareNatural', 'Index', 'DenseMatrix'];\nexport var createSetDistinct = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed,\n    size,\n    subset,\n    compareNatural,\n    Index,\n    DenseMatrix\n  } = _ref;\n  /**\n   * Collect the distinct elements of a multiset.\n   * A multi-dimension array will be converted to a single-dimension array before the operation.\n   *\n   * Syntax:\n   *\n   *    math.setDistinct(set)\n   *\n   * Examples:\n   *\n   *    math.setDistinct([1, 1, 1, 2, 2, 3])        // returns [1, 2, 3]\n   *\n   * See also:\n   *\n   *    setMultiplicity\n   *\n   * @param {Array | Matrix}    a  A multiset\n   * @return {Array | Matrix}    A set containing the distinc elements of the multiset\n   */\n\n  return typed(name, {\n    'Array | Matrix': function Array__Matrix(a) {\n      var result;\n\n      if (subset(size(a), new Index(0)) === 0) {\n        // if empty, return empty\n        result = [];\n      } else {\n        var b = flatten(Array.isArray(a) ? a : a.toArray()).sort(compareNatural);\n        result = [];\n        result.push(b[0]);\n\n        for (var i = 1; i < b.length; i++) {\n          if (compareNatural(b[i], b[i - 1]) !== 0) {\n            result.push(b[i]);\n          }\n        }\n      } // return an array, if the input was an array\n\n\n      if (Array.isArray(a)) {\n        return result;\n      } // return a matrix otherwise\n\n\n      return new DenseMatrix(result);\n    }\n  });\n});", "map": {"version": 3, "names": ["flatten", "factory", "name", "dependencies", "createSetDistinct", "_ref", "typed", "size", "subset", "compareNatural", "Index", "DenseMatrix", "Array__Matrix", "a", "result", "b", "Array", "isArray", "toArray", "sort", "push", "i", "length"], "sources": ["D:/work/joyserver/client/node_modules/mathjs/lib/esm/function/set/setDistinct.js"], "sourcesContent": ["import { flatten } from '../../utils/array.js';\nimport { factory } from '../../utils/factory.js';\nvar name = 'setDistinct';\nvar dependencies = ['typed', 'size', 'subset', 'compareNatural', 'Index', 'DenseMatrix'];\nexport var createSetDistinct = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed,\n    size,\n    subset,\n    compareNatural,\n    Index,\n    DenseMatrix\n  } = _ref;\n  /**\n   * Collect the distinct elements of a multiset.\n   * A multi-dimension array will be converted to a single-dimension array before the operation.\n   *\n   * Syntax:\n   *\n   *    math.setDistinct(set)\n   *\n   * Examples:\n   *\n   *    math.setDistinct([1, 1, 1, 2, 2, 3])        // returns [1, 2, 3]\n   *\n   * See also:\n   *\n   *    setMultiplicity\n   *\n   * @param {Array | Matrix}    a  A multiset\n   * @return {Array | Matrix}    A set containing the distinc elements of the multiset\n   */\n  return typed(name, {\n    'Array | Matrix': function Array__Matrix(a) {\n      var result;\n      if (subset(size(a), new Index(0)) === 0) {\n        // if empty, return empty\n        result = [];\n      } else {\n        var b = flatten(Array.isArray(a) ? a : a.toArray()).sort(compareNatural);\n        result = [];\n        result.push(b[0]);\n        for (var i = 1; i < b.length; i++) {\n          if (compareNatural(b[i], b[i - 1]) !== 0) {\n            result.push(b[i]);\n          }\n        }\n      }\n      // return an array, if the input was an array\n      if (Array.isArray(a)) {\n        return result;\n      }\n      // return a matrix otherwise\n      return new DenseMatrix(result);\n    }\n  });\n});"], "mappings": "AAAA,SAASA,OAAT,QAAwB,sBAAxB;AACA,SAASC,OAAT,QAAwB,wBAAxB;AACA,IAAIC,IAAI,GAAG,aAAX;AACA,IAAIC,YAAY,GAAG,CAAC,OAAD,EAAU,MAAV,EAAkB,QAAlB,EAA4B,gBAA5B,EAA8C,OAA9C,EAAuD,aAAvD,CAAnB;AACA,OAAO,IAAIC,iBAAiB,GAAG,eAAeH,OAAO,CAACC,IAAD,EAAOC,YAAP,EAAqBE,IAAI,IAAI;EAChF,IAAI;IACFC,KADE;IAEFC,IAFE;IAGFC,MAHE;IAIFC,cAJE;IAKFC,KALE;IAMFC;EANE,IAOAN,IAPJ;EAQA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EACE,OAAOC,KAAK,CAACJ,IAAD,EAAO;IACjB,kBAAkB,SAASU,aAAT,CAAuBC,CAAvB,EAA0B;MAC1C,IAAIC,MAAJ;;MACA,IAAIN,MAAM,CAACD,IAAI,CAACM,CAAD,CAAL,EAAU,IAAIH,KAAJ,CAAU,CAAV,CAAV,CAAN,KAAkC,CAAtC,EAAyC;QACvC;QACAI,MAAM,GAAG,EAAT;MACD,CAHD,MAGO;QACL,IAAIC,CAAC,GAAGf,OAAO,CAACgB,KAAK,CAACC,OAAN,CAAcJ,CAAd,IAAmBA,CAAnB,GAAuBA,CAAC,CAACK,OAAF,EAAxB,CAAP,CAA4CC,IAA5C,CAAiDV,cAAjD,CAAR;QACAK,MAAM,GAAG,EAAT;QACAA,MAAM,CAACM,IAAP,CAAYL,CAAC,CAAC,CAAD,CAAb;;QACA,KAAK,IAAIM,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGN,CAAC,CAACO,MAAtB,EAA8BD,CAAC,EAA/B,EAAmC;UACjC,IAAIZ,cAAc,CAACM,CAAC,CAACM,CAAD,CAAF,EAAON,CAAC,CAACM,CAAC,GAAG,CAAL,CAAR,CAAd,KAAmC,CAAvC,EAA0C;YACxCP,MAAM,CAACM,IAAP,CAAYL,CAAC,CAACM,CAAD,CAAb;UACD;QACF;MACF,CAdyC,CAe1C;;;MACA,IAAIL,KAAK,CAACC,OAAN,CAAcJ,CAAd,CAAJ,EAAsB;QACpB,OAAOC,MAAP;MACD,CAlByC,CAmB1C;;;MACA,OAAO,IAAIH,WAAJ,CAAgBG,MAAhB,CAAP;IACD;EAtBgB,CAAP,CAAZ;AAwBD,CApDoD,CAA9C"}, "metadata": {}, "sourceType": "module"}