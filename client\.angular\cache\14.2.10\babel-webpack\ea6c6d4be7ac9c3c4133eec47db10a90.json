{"ast": null, "code": "import { factory } from '../../utils/factory.js';\nimport { expm1Number } from '../../plain/number/index.js';\nvar name = 'expm1';\nvar dependencies = ['typed', 'Complex'];\nexport var createExpm1 = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed,\n    Complex: _Complex\n  } = _ref;\n  /**\n   * Calculate the value of subtracting 1 from the exponential value.\n   * This function is more accurate than `math.exp(x)-1` when `x` is near 0\n   * To avoid ambiguity with the matrix exponential `expm`, this function\n   * does not operate on matrices; if you wish to apply it elementwise, see\n   * the examples.\n   *\n   * Syntax:\n   *\n   *    math.expm1(x)\n   *\n   * Examples:\n   *\n   *    math.expm1(2)                      // returns number 6.38905609893065\n   *    math.pow(math.e, 2) - 1            // returns number 6.3890560989306495\n   *    math.expm1(1e-8)                   // returns number 1.0000000050000001e-8\n   *    math.exp(1e-8) - 1                 // returns number 9.9999999392253e-9\n   *    math.log(math.expm1(2) + 1)        // returns number 2\n   *\n   *    math.map([1, 2, 3], math.expm1)\n   *    // returns Array [\n   *    //   1.718281828459045,\n   *    //   6.3890560989306495,\n   *    //   19.085536923187668\n   *    // ]\n   *\n   * See also:\n   *\n   *    exp, expm, log, pow\n   *\n   * @param {number | BigNumber | Complex} x  The number to exponentiate\n   * @return {number | BigNumber | Complex} Exponential of `x`, minus one\n   */\n\n  return typed(name, {\n    number: expm1Number,\n    Complex: function Complex(x) {\n      var r = Math.exp(x.re);\n      return new _Complex(r * Math.cos(x.im) - 1, r * Math.sin(x.im));\n    },\n    BigNumber: function BigNumber(x) {\n      return x.exp().minus(1);\n    }\n  });\n});", "map": {"version": 3, "names": ["factory", "expm1Number", "name", "dependencies", "createExpm1", "_ref", "typed", "Complex", "_Complex", "number", "x", "r", "Math", "exp", "re", "cos", "im", "sin", "BigNumber", "minus"], "sources": ["D:/work/joyserver/client/node_modules/mathjs/lib/esm/function/arithmetic/expm1.js"], "sourcesContent": ["import { factory } from '../../utils/factory.js';\nimport { expm1Number } from '../../plain/number/index.js';\nvar name = 'expm1';\nvar dependencies = ['typed', 'Complex'];\nexport var createExpm1 = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed,\n    Complex: _Complex\n  } = _ref;\n  /**\n   * Calculate the value of subtracting 1 from the exponential value.\n   * This function is more accurate than `math.exp(x)-1` when `x` is near 0\n   * To avoid ambiguity with the matrix exponential `expm`, this function\n   * does not operate on matrices; if you wish to apply it elementwise, see\n   * the examples.\n   *\n   * Syntax:\n   *\n   *    math.expm1(x)\n   *\n   * Examples:\n   *\n   *    math.expm1(2)                      // returns number 6.38905609893065\n   *    math.pow(math.e, 2) - 1            // returns number 6.3890560989306495\n   *    math.expm1(1e-8)                   // returns number 1.0000000050000001e-8\n   *    math.exp(1e-8) - 1                 // returns number 9.9999999392253e-9\n   *    math.log(math.expm1(2) + 1)        // returns number 2\n   *\n   *    math.map([1, 2, 3], math.expm1)\n   *    // returns Array [\n   *    //   1.718281828459045,\n   *    //   6.3890560989306495,\n   *    //   19.085536923187668\n   *    // ]\n   *\n   * See also:\n   *\n   *    exp, expm, log, pow\n   *\n   * @param {number | BigNumber | Complex} x  The number to exponentiate\n   * @return {number | BigNumber | Complex} Exponential of `x`, minus one\n   */\n  return typed(name, {\n    number: expm1Number,\n    Complex: function Complex(x) {\n      var r = Math.exp(x.re);\n      return new _Complex(r * Math.cos(x.im) - 1, r * Math.sin(x.im));\n    },\n    BigNumber: function BigNumber(x) {\n      return x.exp().minus(1);\n    }\n  });\n});"], "mappings": "AAAA,SAASA,OAAT,QAAwB,wBAAxB;AACA,SAASC,WAAT,QAA4B,6BAA5B;AACA,IAAIC,IAAI,GAAG,OAAX;AACA,IAAIC,YAAY,GAAG,CAAC,OAAD,EAAU,SAAV,CAAnB;AACA,OAAO,IAAIC,WAAW,GAAG,eAAeJ,OAAO,CAACE,IAAD,EAAOC,YAAP,EAAqBE,IAAI,IAAI;EAC1E,IAAI;IACFC,KADE;IAEFC,OAAO,EAAEC;EAFP,IAGAH,IAHJ;EAIA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EACE,OAAOC,KAAK,CAACJ,IAAD,EAAO;IACjBO,MAAM,EAAER,WADS;IAEjBM,OAAO,EAAE,SAASA,OAAT,CAAiBG,CAAjB,EAAoB;MAC3B,IAAIC,CAAC,GAAGC,IAAI,CAACC,GAAL,CAASH,CAAC,CAACI,EAAX,CAAR;MACA,OAAO,IAAIN,QAAJ,CAAaG,CAAC,GAAGC,IAAI,CAACG,GAAL,CAASL,CAAC,CAACM,EAAX,CAAJ,GAAqB,CAAlC,EAAqCL,CAAC,GAAGC,IAAI,CAACK,GAAL,CAASP,CAAC,CAACM,EAAX,CAAzC,CAAP;IACD,CALgB;IAMjBE,SAAS,EAAE,SAASA,SAAT,CAAmBR,CAAnB,EAAsB;MAC/B,OAAOA,CAAC,CAACG,GAAF,GAAQM,KAAR,CAAc,CAAd,CAAP;IACD;EARgB,CAAP,CAAZ;AAUD,CAhD8C,CAAxC"}, "metadata": {}, "sourceType": "module"}