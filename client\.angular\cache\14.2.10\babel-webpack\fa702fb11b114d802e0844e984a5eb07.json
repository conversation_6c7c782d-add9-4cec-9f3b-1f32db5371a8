{"ast": null, "code": "import { factory } from '../../utils/factory.js';\nimport { errorTransform } from './utils/errorTransform.js';\nimport { createMax } from '../../function/statistics/max.js';\nimport { lastDimToZeroBase } from './utils/lastDimToZeroBase.js';\nvar name = 'max';\nvar dependencies = ['typed', 'config', 'numeric', 'larger'];\nexport var createMaxTransform = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed,\n    config,\n    numeric,\n    larger\n  } = _ref;\n  var max = createMax({\n    typed,\n    config,\n    numeric,\n    larger\n  });\n  /**\n   * Attach a transform function to math.max\n   * Adds a property transform containing the transform function.\n   *\n   * This transform changed the last `dim` parameter of function max\n   * from one-based to zero based\n   */\n\n  return typed('max', {\n    '...any': function any(args) {\n      args = lastDimToZeroBase(args);\n\n      try {\n        return max.apply(null, args);\n      } catch (err) {\n        throw errorTransform(err);\n      }\n    }\n  });\n}, {\n  isTransformFunction: true\n});", "map": null, "metadata": {}, "sourceType": "module"}