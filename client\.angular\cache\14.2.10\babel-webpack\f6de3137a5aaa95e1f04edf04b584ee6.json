{"ast": null, "code": "/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { matrixDependencies } from './dependenciesMatrix.generated.js';\nimport { multiplyScalarDependencies } from './dependenciesMultiplyScalar.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createKron } from '../../factoriesAny.js';\nexport var kronDependencies = {\n  matrixDependencies,\n  multiplyScalarDependencies,\n  typedDependencies,\n  createKron\n};", "map": {"version": 3, "names": ["matrixDependencies", "multiplyScalarDependencies", "typedDependencies", "createKron", "kronDependencies"], "sources": ["D:/work/joyserver/client/node_modules/mathjs/lib/esm/entry/dependenciesAny/dependenciesKron.generated.js"], "sourcesContent": ["/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { matrixDependencies } from './dependenciesMatrix.generated.js';\nimport { multiplyScalarDependencies } from './dependenciesMultiplyScalar.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createKron } from '../../factoriesAny.js';\nexport var kronDependencies = {\n  matrixDependencies,\n  multiplyScalarDependencies,\n  typedDependencies,\n  createKron\n};"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,kBAAT,QAAmC,mCAAnC;AACA,SAASC,0BAAT,QAA2C,2CAA3C;AACA,SAASC,iBAAT,QAAkC,kCAAlC;AACA,SAASC,UAAT,QAA2B,uBAA3B;AACA,OAAO,IAAIC,gBAAgB,GAAG;EAC5BJ,kBAD4B;EAE5BC,0BAF4B;EAG5BC,iBAH4B;EAI5BC;AAJ4B,CAAvB"}, "metadata": {}, "sourceType": "module"}