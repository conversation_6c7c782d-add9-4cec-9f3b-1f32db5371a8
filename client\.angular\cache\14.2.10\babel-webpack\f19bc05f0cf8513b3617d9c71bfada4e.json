{"ast": null, "code": "export var isZeroDocs = {\n  name: 'isZero',\n  category: 'Utils',\n  syntax: ['isZero(x)'],\n  description: 'Test whether a value is zero.',\n  examples: ['isZero(2)', 'isZero(0)', 'isZero(-4)', 'isZero([3, 0, -2, 0])'],\n  seealso: ['isInteger', 'isNumeric', 'isNegative', 'isPositive']\n};", "map": {"version": 3, "names": ["isZeroDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["D:/work/joyserver/client/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/utils/isZero.js"], "sourcesContent": ["export var isZeroDocs = {\n  name: 'isZero',\n  category: 'Utils',\n  syntax: ['isZero(x)'],\n  description: 'Test whether a value is zero.',\n  examples: ['isZero(2)', 'isZero(0)', 'isZero(-4)', 'isZero([3, 0, -2, 0])'],\n  seealso: ['isInteger', 'isNumeric', 'isNegative', 'isPositive']\n};"], "mappings": "AAAA,OAAO,IAAIA,UAAU,GAAG;EACtBC,IAAI,EAAE,QADgB;EAEtBC,QAAQ,EAAE,OAFY;EAGtBC,MAAM,EAAE,CAAC,WAAD,CAHc;EAItBC,WAAW,EAAE,+BAJS;EAKtBC,QAAQ,EAAE,CAAC,WAAD,EAAc,WAAd,EAA2B,YAA3B,EAAyC,uBAAzC,CALY;EAMtBC,OAAO,EAAE,CAAC,WAAD,EAAc,WAAd,EAA2B,YAA3B,EAAyC,YAAzC;AANa,CAAjB"}, "metadata": {}, "sourceType": "module"}