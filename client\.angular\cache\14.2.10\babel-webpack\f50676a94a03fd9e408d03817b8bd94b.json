{"ast": null, "code": "import { isScheduler } from '../util/isScheduler';\nimport { fromArray } from './fromArray';\nimport { scheduleArray } from '../scheduled/scheduleArray';\nexport function of(...args) {\n  let scheduler = args[args.length - 1];\n\n  if (isScheduler(scheduler)) {\n    args.pop();\n    return scheduleArray(args, scheduler);\n  } else {\n    return fromArray(args);\n  }\n} //# sourceMappingURL=of.js.map", "map": null, "metadata": {}, "sourceType": "module"}