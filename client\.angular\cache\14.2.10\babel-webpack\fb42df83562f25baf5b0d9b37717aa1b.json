{"ast": null, "code": "import { scheduleObservable } from './scheduleObservable';\nimport { schedulePromise } from './schedulePromise';\nimport { scheduleArray } from './scheduleArray';\nimport { scheduleIterable } from './scheduleIterable';\nimport { isInteropObservable } from '../util/isInteropObservable';\nimport { isPromise } from '../util/isPromise';\nimport { isArrayLike } from '../util/isArrayLike';\nimport { isIterable } from '../util/isIterable';\nexport function scheduled(input, scheduler) {\n  if (input != null) {\n    if (isInteropObservable(input)) {\n      return scheduleObservable(input, scheduler);\n    } else if (isPromise(input)) {\n      return schedulePromise(input, scheduler);\n    } else if (isArrayLike(input)) {\n      return scheduleArray(input, scheduler);\n    } else if (isIterable(input) || typeof input === 'string') {\n      return scheduleIterable(input, scheduler);\n    }\n  }\n\n  throw new TypeError((input !== null && typeof input || input) + ' is not observable');\n} //# sourceMappingURL=scheduled.js.map", "map": null, "metadata": {}, "sourceType": "module"}