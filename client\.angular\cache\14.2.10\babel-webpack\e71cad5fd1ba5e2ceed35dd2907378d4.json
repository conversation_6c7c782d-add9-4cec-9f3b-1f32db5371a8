{"ast": null, "code": "import { factory } from '../../utils/factory.js';\nimport { deepMap } from '../../utils/collection.js';\nimport { log1p as _log1p } from '../../utils/number.js';\nvar name = 'log1p';\nvar dependencies = ['typed', 'config', 'divideScalar', 'log', 'Complex'];\nexport var createLog1p = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed,\n    config,\n    divideScalar,\n    log,\n    Complex\n  } = _ref;\n  /**\n   * Calculate the logarithm of a `value+1`.\n   *\n   * For matrices, the function is evaluated element wise.\n   *\n   * Syntax:\n   *\n   *    math.log1p(x)\n   *    math.log1p(x, base)\n   *\n   * Examples:\n   *\n   *    math.log1p(2.5)                 // returns 1.252762968495368\n   *    math.exp(math.log1p(1.4))       // returns 2.4\n   *\n   *    math.pow(10, 4)                 // returns 10000\n   *    math.log1p(9999, 10)            // returns 4\n   *    math.log1p(9999) / math.log(10) // returns 4\n   *\n   * See also:\n   *\n   *    exp, log, log2, log10\n   *\n   * @param {number | BigNumber | Complex | Array | Matrix} x\n   *            Value for which to calculate the logarithm of `x+1`.\n   * @param {number | BigNumber | Complex} [base=e]\n   *            Optional base for the logarithm. If not provided, the natural\n   *            logarithm of `x+1` is calculated.\n   * @return {number | BigNumber | Complex | Array | Matrix}\n   *            Returns the logarithm of `x+1`\n   */\n\n  return typed(name, {\n    number: function number(x) {\n      if (x >= -1 || config.predictable) {\n        return _log1p(x);\n      } else {\n        // negative value -> complex value computation\n        return _log1pComplex(new Complex(x, 0));\n      }\n    },\n    Complex: _log1pComplex,\n    BigNumber: function BigNumber(x) {\n      var y = x.plus(1);\n\n      if (!y.isNegative() || config.predictable) {\n        return y.ln();\n      } else {\n        // downgrade to number, return Complex valued result\n        return _log1pComplex(new Complex(x.toNumber(), 0));\n      }\n    },\n    'Array | Matrix': typed.referToSelf(self => x => deepMap(x, self)),\n    'any, any': typed.referToSelf(self => (x, base) => {\n      // calculate logarithm for a specified base, log1p(x, base)\n      return divideScalar(self(x), log(base));\n    })\n  });\n  /**\n   * Calculate the natural logarithm of a complex number + 1\n   * @param {Complex} x\n   * @returns {Complex}\n   * @private\n   */\n\n  function _log1pComplex(x) {\n    var xRe1p = x.re + 1;\n    return new Complex(Math.log(Math.sqrt(xRe1p * xRe1p + x.im * x.im)), Math.atan2(x.im, xRe1p));\n  }\n});", "map": {"version": 3, "names": ["factory", "deepMap", "log1p", "_log1p", "name", "dependencies", "createLog1p", "_ref", "typed", "config", "divideScalar", "log", "Complex", "number", "x", "predictable", "_log1pComplex", "BigNumber", "y", "plus", "isNegative", "ln", "toNumber", "referToSelf", "self", "base", "xRe1p", "re", "Math", "sqrt", "im", "atan2"], "sources": ["D:/work/joyserver/client/node_modules/mathjs/lib/esm/function/arithmetic/log1p.js"], "sourcesContent": ["import { factory } from '../../utils/factory.js';\nimport { deepMap } from '../../utils/collection.js';\nimport { log1p as _log1p } from '../../utils/number.js';\nvar name = 'log1p';\nvar dependencies = ['typed', 'config', 'divideScalar', 'log', 'Complex'];\nexport var createLog1p = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed,\n    config,\n    divideScalar,\n    log,\n    Complex\n  } = _ref;\n  /**\n   * Calculate the logarithm of a `value+1`.\n   *\n   * For matrices, the function is evaluated element wise.\n   *\n   * Syntax:\n   *\n   *    math.log1p(x)\n   *    math.log1p(x, base)\n   *\n   * Examples:\n   *\n   *    math.log1p(2.5)                 // returns 1.252762968495368\n   *    math.exp(math.log1p(1.4))       // returns 2.4\n   *\n   *    math.pow(10, 4)                 // returns 10000\n   *    math.log1p(9999, 10)            // returns 4\n   *    math.log1p(9999) / math.log(10) // returns 4\n   *\n   * See also:\n   *\n   *    exp, log, log2, log10\n   *\n   * @param {number | BigNumber | Complex | Array | Matrix} x\n   *            Value for which to calculate the logarithm of `x+1`.\n   * @param {number | BigNumber | Complex} [base=e]\n   *            Optional base for the logarithm. If not provided, the natural\n   *            logarithm of `x+1` is calculated.\n   * @return {number | BigNumber | Complex | Array | Matrix}\n   *            Returns the logarithm of `x+1`\n   */\n  return typed(name, {\n    number: function number(x) {\n      if (x >= -1 || config.predictable) {\n        return _log1p(x);\n      } else {\n        // negative value -> complex value computation\n        return _log1pComplex(new Complex(x, 0));\n      }\n    },\n    Complex: _log1pComplex,\n    BigNumber: function BigNumber(x) {\n      var y = x.plus(1);\n      if (!y.isNegative() || config.predictable) {\n        return y.ln();\n      } else {\n        // downgrade to number, return Complex valued result\n        return _log1pComplex(new Complex(x.toNumber(), 0));\n      }\n    },\n    'Array | Matrix': typed.referToSelf(self => x => deepMap(x, self)),\n    'any, any': typed.referToSelf(self => (x, base) => {\n      // calculate logarithm for a specified base, log1p(x, base)\n      return divideScalar(self(x), log(base));\n    })\n  });\n\n  /**\n   * Calculate the natural logarithm of a complex number + 1\n   * @param {Complex} x\n   * @returns {Complex}\n   * @private\n   */\n  function _log1pComplex(x) {\n    var xRe1p = x.re + 1;\n    return new Complex(Math.log(Math.sqrt(xRe1p * xRe1p + x.im * x.im)), Math.atan2(x.im, xRe1p));\n  }\n});"], "mappings": "AAAA,SAASA,OAAT,QAAwB,wBAAxB;AACA,SAASC,OAAT,QAAwB,2BAAxB;AACA,SAASC,KAAK,IAAIC,MAAlB,QAAgC,uBAAhC;AACA,IAAIC,IAAI,GAAG,OAAX;AACA,IAAIC,YAAY,GAAG,CAAC,OAAD,EAAU,QAAV,EAAoB,cAApB,EAAoC,KAApC,EAA2C,SAA3C,CAAnB;AACA,OAAO,IAAIC,WAAW,GAAG,eAAeN,OAAO,CAACI,IAAD,EAAOC,YAAP,EAAqBE,IAAI,IAAI;EAC1E,IAAI;IACFC,KADE;IAEFC,MAFE;IAGFC,YAHE;IAIFC,GAJE;IAKFC;EALE,IAMAL,IANJ;EAOA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EACE,OAAOC,KAAK,CAACJ,IAAD,EAAO;IACjBS,MAAM,EAAE,SAASA,MAAT,CAAgBC,CAAhB,EAAmB;MACzB,IAAIA,CAAC,IAAI,CAAC,CAAN,IAAWL,MAAM,CAACM,WAAtB,EAAmC;QACjC,OAAOZ,MAAM,CAACW,CAAD,CAAb;MACD,CAFD,MAEO;QACL;QACA,OAAOE,aAAa,CAAC,IAAIJ,OAAJ,CAAYE,CAAZ,EAAe,CAAf,CAAD,CAApB;MACD;IACF,CARgB;IASjBF,OAAO,EAAEI,aATQ;IAUjBC,SAAS,EAAE,SAASA,SAAT,CAAmBH,CAAnB,EAAsB;MAC/B,IAAII,CAAC,GAAGJ,CAAC,CAACK,IAAF,CAAO,CAAP,CAAR;;MACA,IAAI,CAACD,CAAC,CAACE,UAAF,EAAD,IAAmBX,MAAM,CAACM,WAA9B,EAA2C;QACzC,OAAOG,CAAC,CAACG,EAAF,EAAP;MACD,CAFD,MAEO;QACL;QACA,OAAOL,aAAa,CAAC,IAAIJ,OAAJ,CAAYE,CAAC,CAACQ,QAAF,EAAZ,EAA0B,CAA1B,CAAD,CAApB;MACD;IACF,CAlBgB;IAmBjB,kBAAkBd,KAAK,CAACe,WAAN,CAAkBC,IAAI,IAAIV,CAAC,IAAIb,OAAO,CAACa,CAAD,EAAIU,IAAJ,CAAtC,CAnBD;IAoBjB,YAAYhB,KAAK,CAACe,WAAN,CAAkBC,IAAI,IAAI,CAACV,CAAD,EAAIW,IAAJ,KAAa;MACjD;MACA,OAAOf,YAAY,CAACc,IAAI,CAACV,CAAD,CAAL,EAAUH,GAAG,CAACc,IAAD,CAAb,CAAnB;IACD,CAHW;EApBK,CAAP,CAAZ;EA0BA;AACF;AACA;AACA;AACA;AACA;;EACE,SAAST,aAAT,CAAuBF,CAAvB,EAA0B;IACxB,IAAIY,KAAK,GAAGZ,CAAC,CAACa,EAAF,GAAO,CAAnB;IACA,OAAO,IAAIf,OAAJ,CAAYgB,IAAI,CAACjB,GAAL,CAASiB,IAAI,CAACC,IAAL,CAAUH,KAAK,GAAGA,KAAR,GAAgBZ,CAAC,CAACgB,EAAF,GAAOhB,CAAC,CAACgB,EAAnC,CAAT,CAAZ,EAA8DF,IAAI,CAACG,KAAL,CAAWjB,CAAC,CAACgB,EAAb,EAAiBJ,KAAjB,CAA9D,CAAP;EACD;AACF,CA3E8C,CAAxC"}, "metadata": {}, "sourceType": "module"}