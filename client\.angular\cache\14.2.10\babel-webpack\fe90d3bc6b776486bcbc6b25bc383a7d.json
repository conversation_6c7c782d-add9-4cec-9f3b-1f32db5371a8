{"ast": null, "code": "import _asyncToGenerator from \"D:/work/joyserver/client/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { MSG_SENDTO } from \"@core/io/io.message.consts\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./socket/socket.service\";\nimport * as i2 from \"./seat.service\";\nexport class WebrtcService {\n  constructor(socketService, seatService) {\n    this.seatService = seatService;\n    this.socket = socketService;\n  }\n\n  init() {\n    var _this = this;\n\n    this.seatService.seatNumber.subscribe(seat => {\n      this.seat = seat;\n    });\n    this.socket.clientIO.onMsg(MSG_SENDTO, /*#__PURE__*/function () {\n      var _ref = _asyncToGenerator(function* (req) {\n        const {\n          type,\n          data\n        } = req.params.info;\n\n        if (type === \"offer\") {\n          const {\n            offer,\n            config\n          } = data;\n          console.log(\"**** WebrtcService: get offer\", req.params);\n          const is_connected = !!_this.pc;\n\n          const pc = _this.getConn();\n\n          if (is_connected) {\n            // const stream = await navigator.mediaDevices.getDisplayMedia({ video: true });\n            const stream = yield _this.getStream(config?.mediaConfig);\n            stream.getTracks().forEach(track => {\n              pc.addTrack(track, stream);\n            });\n\n            _this.negotiateDescription(pc, {\n              streamId: stream.id,\n              mediaType: config?.mediaType\n            });\n\n            return true;\n          }\n\n          try {\n            yield pc.setRemoteDescription(offer);\n            const stream = yield _this.getStream(config?.mediaConfig);\n            stream.getTracks().forEach(track => {\n              pc.addTrack(track, stream);\n            });\n            yield _this.sendAnswer(pc, {\n              streamId: stream.id,\n              mediaType: config?.mediaType\n            });\n          } catch (err) {\n            console.error(err);\n          }\n\n          return true;\n        }\n\n        if (type === \"new-ice-candidate\") {\n          _this.pc.addIceCandidate(new RTCIceCandidate(data.candidate));\n\n          return console.log(\"get new-ice-candidate\", data);\n        }\n\n        if (type === \"answer-offer\") {\n          _this.pc.setRemoteDescription(data.payload);\n        }\n\n        if (type === \"close\" && _this.pc) {\n          console.log(\"**** WebrtcService: close connection\");\n\n          _this.pc.getSenders().forEach(s => s.track.stop());\n\n          _this.pc.close();\n\n          _this.pc = null;\n        }\n      });\n\n      return function (_x) {\n        return _ref.apply(this, arguments);\n      };\n    }());\n  }\n\n  sendAnswer(pc, info) {\n    var _this2 = this;\n\n    return _asyncToGenerator(function* () {\n      // send answer\n      try {\n        yield pc.setLocalDescription(yield pc.createAnswer());\n\n        _this2.socket.clientIO.sendMsg(MSG_SENDTO, {\n          target: \"manager\",\n          info: {\n            seat: _this2.seat,\n            type: \"answer\",\n            data: {\n              answer: pc.localDescription,\n              stream: {\n                id: info.streamId,\n                mediaType: info.mediaType\n              }\n            }\n          }\n        });\n      } catch (err) {\n        console.error(\"**** WebrtcService: ERROR:\", err);\n      }\n    })();\n  } // 需要添加流时主动协商\n\n\n  negotiateDescription(pc, info) {\n    var _this3 = this;\n\n    return _asyncToGenerator(function* () {\n      // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n      // @ts-ignore\n      yield pc.setLocalDescription();\n\n      _this3.socket.clientIO.sendMsg(MSG_SENDTO, {\n        target: \"manager\",\n        info: {\n          seat: _this3.seat,\n          type: \"offer\",\n          data: {\n            payload: pc.localDescription,\n            stream: {\n              id: info.streamId,\n              mediaType: info.mediaType\n            }\n          }\n        }\n      });\n    })();\n  }\n\n  getConn(cfg) {\n    return this.pc || this.addConnection(cfg);\n  }\n\n  removeConn() {\n    this.pc = null;\n  }\n\n  addConnection(cfg) {\n    const pc = new RTCPeerConnection(cfg);\n\n    pc.onicecandidate = evt => {\n      // send any ice candidates to the other peer\n      if (evt.candidate) {\n        console.log(\"**** WebrtcService: sendAnswer: iceGatheringState:\", pc.iceGatheringState, evt.candidate);\n        this.socket.clientIO.sendMsg(MSG_SENDTO, {\n          target: \"manager\",\n          info: {\n            seat: this.seat,\n            type: \"new-ice-candidate\",\n            data: {\n              candidate: evt.candidate\n            }\n          }\n        });\n      } else {\n        console.log(\"**** WebrtcService: sendAnswer: iceGatheringState:\", pc.iceGatheringState);\n      }\n    };\n\n    pc.oniceconnectionstatechange = () => {\n      console.log(\"WebrtcService: iceconnection state:\", pc.iceConnectionState, \"GatheringState:\", pc.iceGatheringState);\n\n      if (pc.iceConnectionState === \"disconnected\" || pc.iceConnectionState === \"failed\" || pc.iceConnectionState === \"closed\") {\n        pc.getSenders().forEach(s => s.track.stop());\n\n        if (pc.signalingState !== \"closed\") {\n          pc.close();\n        }\n\n        this.removeConn();\n      }\n    };\n\n    pc.onconnectionstatechange = () => {\n      console.log(\"**** WebrtcService: Connection state changed: \", pc.connectionState);\n    };\n\n    pc.onsignalingstatechange = () => {\n      console.log(\"**** WebrtcService: signaling state:\", pc.signalingState, \"GatheringState:\", pc.iceGatheringState);\n    };\n\n    pc.onicegatheringstatechange = () => {\n      console.log(\"**** WebrtcService: gathering state:\", pc.iceGatheringState);\n    };\n\n    pc.addEventListener(\"error\", evt => {\n      console.log(\"**** WebrtcService: error\", evt);\n    });\n    this.pc = pc;\n    return pc;\n  }\n\n  getStream(opt) {\n    const constrains = {\n      audio: false,\n      video: {\n        width: {\n          min: 320,\n          ideal: 640\n        },\n        height: {\n          min: 240,\n          ideal: 480\n        }\n      },\n      frameRate: {\n        max: 30\n      }\n    };\n    return navigator.mediaDevices.getUserMedia(opt || constrains);\n  }\n\n}\n\nWebrtcService.ɵfac = function WebrtcService_Factory(t) {\n  return new (t || WebrtcService)(i0.ɵɵinject(i1.SocketService), i0.ɵɵinject(i2.SeatService));\n};\n\nWebrtcService.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n  token: WebrtcService,\n  factory: WebrtcService.ɵfac,\n  providedIn: \"root\"\n});", "map": null, "metadata": {}, "sourceType": "module"}