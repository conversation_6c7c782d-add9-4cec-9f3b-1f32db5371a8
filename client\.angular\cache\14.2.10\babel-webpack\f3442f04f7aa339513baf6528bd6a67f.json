{"ast": null, "code": "// function utils\nimport { lruQueue } from './lruQueue.js';\n/**\n * Memoize a given function by caching the computed result.\n * The cache of a memoized function can be cleared by deleting the `cache`\n * property of the function.\n *\n * @param {function} fn                     The function to be memoized.\n *                                          Must be a pure function.\n * @param {Object} [options]\n * @param {function(args: Array): string} [options.hasher]\n *    A custom hash builder. Is JSON.stringify by default.\n * @param {number | undefined} [options.limit]\n *    Maximum number of values that may be cached. Undefined indicates\n *    unlimited (default)\n * @return {function}                       Returns the memoized function\n */\n\nexport function memoize(fn) {\n  var {\n    hasher,\n    limit\n  } = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  limit = limit == null ? Number.POSITIVE_INFINITY : limit;\n  hasher = hasher == null ? JSON.stringify : hasher;\n  return function memoize() {\n    if (typeof memoize.cache !== 'object') {\n      memoize.cache = {\n        values: new Map(),\n        lru: lruQueue(limit || Number.POSITIVE_INFINITY)\n      };\n    }\n\n    var args = [];\n\n    for (var i = 0; i < arguments.length; i++) {\n      args[i] = arguments[i];\n    }\n\n    var hash = hasher(args);\n\n    if (memoize.cache.values.has(hash)) {\n      memoize.cache.lru.hit(hash);\n      return memoize.cache.values.get(hash);\n    }\n\n    var newVal = fn.apply(fn, args);\n    memoize.cache.values.set(hash, newVal);\n    memoize.cache.values.delete(memoize.cache.lru.hit(hash));\n    return newVal;\n  };\n}\n/**\n * Memoize a given function by caching all results and the arguments,\n * and comparing against the arguments of previous results before\n * executing again.\n * This is less performant than `memoize` which calculates a hash,\n * which is very fast to compare. Use `memoizeCompare` only when it is\n * not possible to create a unique serializable hash from the function\n * arguments.\n * The isEqual function must compare two sets of arguments\n * and return true when equal (can be a deep equality check for example).\n * @param {function} fn\n * @param {function(a: *, b: *) : boolean} isEqual\n * @returns {function}\n */\n\nexport function memoizeCompare(fn, isEqual) {\n  var memoize = function memoize() {\n    var args = [];\n\n    for (var i = 0; i < arguments.length; i++) {\n      args[i] = arguments[i];\n    }\n\n    for (var c = 0; c < memoize.cache.length; c++) {\n      var cached = memoize.cache[c];\n\n      if (isEqual(args, cached.args)) {\n        // TODO: move this cache entry to the top so recently used entries move up?\n        return cached.res;\n      }\n    }\n\n    var res = fn.apply(fn, args);\n    memoize.cache.unshift({\n      args,\n      res\n    });\n    return res;\n  };\n\n  memoize.cache = [];\n  return memoize;\n}", "map": {"version": 3, "names": ["lruQueue", "memoize", "fn", "hasher", "limit", "arguments", "length", "undefined", "Number", "POSITIVE_INFINITY", "JSON", "stringify", "cache", "values", "Map", "lru", "args", "i", "hash", "has", "hit", "get", "newVal", "apply", "set", "delete", "memoizeCompare", "isEqual", "c", "cached", "res", "unshift"], "sources": ["D:/work/joyserver/client/node_modules/mathjs/lib/esm/utils/function.js"], "sourcesContent": ["// function utils\n\nimport { lruQueue } from './lruQueue.js';\n\n/**\n * Memoize a given function by caching the computed result.\n * The cache of a memoized function can be cleared by deleting the `cache`\n * property of the function.\n *\n * @param {function} fn                     The function to be memoized.\n *                                          Must be a pure function.\n * @param {Object} [options]\n * @param {function(args: Array): string} [options.hasher]\n *    A custom hash builder. Is JSON.stringify by default.\n * @param {number | undefined} [options.limit]\n *    Maximum number of values that may be cached. Undefined indicates\n *    unlimited (default)\n * @return {function}                       Returns the memoized function\n */\nexport function memoize(fn) {\n  var {\n    hasher,\n    limit\n  } = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  limit = limit == null ? Number.POSITIVE_INFINITY : limit;\n  hasher = hasher == null ? JSON.stringify : hasher;\n  return function memoize() {\n    if (typeof memoize.cache !== 'object') {\n      memoize.cache = {\n        values: new Map(),\n        lru: lruQueue(limit || Number.POSITIVE_INFINITY)\n      };\n    }\n    var args = [];\n    for (var i = 0; i < arguments.length; i++) {\n      args[i] = arguments[i];\n    }\n    var hash = hasher(args);\n    if (memoize.cache.values.has(hash)) {\n      memoize.cache.lru.hit(hash);\n      return memoize.cache.values.get(hash);\n    }\n    var newVal = fn.apply(fn, args);\n    memoize.cache.values.set(hash, newVal);\n    memoize.cache.values.delete(memoize.cache.lru.hit(hash));\n    return newVal;\n  };\n}\n\n/**\n * Memoize a given function by caching all results and the arguments,\n * and comparing against the arguments of previous results before\n * executing again.\n * This is less performant than `memoize` which calculates a hash,\n * which is very fast to compare. Use `memoizeCompare` only when it is\n * not possible to create a unique serializable hash from the function\n * arguments.\n * The isEqual function must compare two sets of arguments\n * and return true when equal (can be a deep equality check for example).\n * @param {function} fn\n * @param {function(a: *, b: *) : boolean} isEqual\n * @returns {function}\n */\nexport function memoizeCompare(fn, isEqual) {\n  var memoize = function memoize() {\n    var args = [];\n    for (var i = 0; i < arguments.length; i++) {\n      args[i] = arguments[i];\n    }\n    for (var c = 0; c < memoize.cache.length; c++) {\n      var cached = memoize.cache[c];\n      if (isEqual(args, cached.args)) {\n        // TODO: move this cache entry to the top so recently used entries move up?\n        return cached.res;\n      }\n    }\n    var res = fn.apply(fn, args);\n    memoize.cache.unshift({\n      args,\n      res\n    });\n    return res;\n  };\n  memoize.cache = [];\n  return memoize;\n}"], "mappings": "AAAA;AAEA,SAASA,QAAT,QAAyB,eAAzB;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,OAAO,SAASC,OAAT,CAAiBC,EAAjB,EAAqB;EAC1B,IAAI;IACFC,MADE;IAEFC;EAFE,IAGAC,SAAS,CAACC,MAAV,GAAmB,CAAnB,IAAwBD,SAAS,CAAC,CAAD,CAAT,KAAiBE,SAAzC,GAAqDF,SAAS,CAAC,CAAD,CAA9D,GAAoE,EAHxE;EAIAD,KAAK,GAAGA,KAAK,IAAI,IAAT,GAAgBI,MAAM,CAACC,iBAAvB,GAA2CL,KAAnD;EACAD,MAAM,GAAGA,MAAM,IAAI,IAAV,GAAiBO,IAAI,CAACC,SAAtB,GAAkCR,MAA3C;EACA,OAAO,SAASF,OAAT,GAAmB;IACxB,IAAI,OAAOA,OAAO,CAACW,KAAf,KAAyB,QAA7B,EAAuC;MACrCX,OAAO,CAACW,KAAR,GAAgB;QACdC,MAAM,EAAE,IAAIC,GAAJ,EADM;QAEdC,GAAG,EAAEf,QAAQ,CAACI,KAAK,IAAII,MAAM,CAACC,iBAAjB;MAFC,CAAhB;IAID;;IACD,IAAIO,IAAI,GAAG,EAAX;;IACA,KAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGZ,SAAS,CAACC,MAA9B,EAAsCW,CAAC,EAAvC,EAA2C;MACzCD,IAAI,CAACC,CAAD,CAAJ,GAAUZ,SAAS,CAACY,CAAD,CAAnB;IACD;;IACD,IAAIC,IAAI,GAAGf,MAAM,CAACa,IAAD,CAAjB;;IACA,IAAIf,OAAO,CAACW,KAAR,CAAcC,MAAd,CAAqBM,GAArB,CAAyBD,IAAzB,CAAJ,EAAoC;MAClCjB,OAAO,CAACW,KAAR,CAAcG,GAAd,CAAkBK,GAAlB,CAAsBF,IAAtB;MACA,OAAOjB,OAAO,CAACW,KAAR,CAAcC,MAAd,CAAqBQ,GAArB,CAAyBH,IAAzB,CAAP;IACD;;IACD,IAAII,MAAM,GAAGpB,EAAE,CAACqB,KAAH,CAASrB,EAAT,EAAac,IAAb,CAAb;IACAf,OAAO,CAACW,KAAR,CAAcC,MAAd,CAAqBW,GAArB,CAAyBN,IAAzB,EAA+BI,MAA/B;IACArB,OAAO,CAACW,KAAR,CAAcC,MAAd,CAAqBY,MAArB,CAA4BxB,OAAO,CAACW,KAAR,CAAcG,GAAd,CAAkBK,GAAlB,CAAsBF,IAAtB,CAA5B;IACA,OAAOI,MAAP;EACD,CApBD;AAqBD;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,OAAO,SAASI,cAAT,CAAwBxB,EAAxB,EAA4ByB,OAA5B,EAAqC;EAC1C,IAAI1B,OAAO,GAAG,SAASA,OAAT,GAAmB;IAC/B,IAAIe,IAAI,GAAG,EAAX;;IACA,KAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGZ,SAAS,CAACC,MAA9B,EAAsCW,CAAC,EAAvC,EAA2C;MACzCD,IAAI,CAACC,CAAD,CAAJ,GAAUZ,SAAS,CAACY,CAAD,CAAnB;IACD;;IACD,KAAK,IAAIW,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG3B,OAAO,CAACW,KAAR,CAAcN,MAAlC,EAA0CsB,CAAC,EAA3C,EAA+C;MAC7C,IAAIC,MAAM,GAAG5B,OAAO,CAACW,KAAR,CAAcgB,CAAd,CAAb;;MACA,IAAID,OAAO,CAACX,IAAD,EAAOa,MAAM,CAACb,IAAd,CAAX,EAAgC;QAC9B;QACA,OAAOa,MAAM,CAACC,GAAd;MACD;IACF;;IACD,IAAIA,GAAG,GAAG5B,EAAE,CAACqB,KAAH,CAASrB,EAAT,EAAac,IAAb,CAAV;IACAf,OAAO,CAACW,KAAR,CAAcmB,OAAd,CAAsB;MACpBf,IADoB;MAEpBc;IAFoB,CAAtB;IAIA,OAAOA,GAAP;EACD,CAlBD;;EAmBA7B,OAAO,CAACW,KAAR,GAAgB,EAAhB;EACA,OAAOX,OAAP;AACD"}, "metadata": {}, "sourceType": "module"}