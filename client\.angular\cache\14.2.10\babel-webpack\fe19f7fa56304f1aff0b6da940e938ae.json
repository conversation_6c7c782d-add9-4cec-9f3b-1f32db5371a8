{"ast": null, "code": "/** @param {number} i\n *  @param {number} n\n *  @returns {number} product of i to n\n */\nexport function product(i, n) {\n  if (n < i) {\n    return 1;\n  }\n\n  if (n === i) {\n    return n;\n  }\n\n  var half = n + i >> 1; // divide (n + i) by 2 and truncate to integer\n\n  return product(i, half) * product(half + 1, n);\n}", "map": null, "metadata": {}, "sourceType": "module"}