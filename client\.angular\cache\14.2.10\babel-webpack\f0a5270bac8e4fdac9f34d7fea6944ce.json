{"ast": null, "code": "// (c) 2018, <PERSON><PERSON>\n// SPDX-License-Identifier: ISC\n// Derived from https://github.com/medikoo/lru-queue\nexport function lruQueue(limit) {\n  var size = 0;\n  var base = 1;\n  var queue = Object.create(null);\n  var map = Object.create(null);\n  var index = 0;\n\n  var del = function del(id) {\n    var oldIndex = map[id];\n    if (!oldIndex) return;\n    delete queue[oldIndex];\n    delete map[id];\n    --size;\n    if (base !== oldIndex) return;\n\n    if (!size) {\n      index = 0;\n      base = 1;\n      return;\n    }\n\n    while (!Object.prototype.hasOwnProperty.call(queue, ++base)) {\n      /* empty */\n    }\n  };\n\n  limit = Math.abs(limit);\n  return {\n    hit: function hit(id) {\n      var oldIndex = map[id];\n      var nuIndex = ++index;\n      queue[nuIndex] = id;\n      map[id] = nuIndex;\n\n      if (!oldIndex) {\n        ++size;\n        if (size <= limit) return undefined;\n        id = queue[base];\n        del(id);\n        return id;\n      }\n\n      delete queue[oldIndex];\n      if (base !== oldIndex) return undefined;\n\n      while (!Object.prototype.hasOwnProperty.call(queue, ++base)) {\n        /* empty */\n      }\n\n      return undefined;\n    },\n    delete: del,\n    clear: function clear() {\n      size = index = 0;\n      base = 1;\n      queue = Object.create(null);\n      map = Object.create(null);\n    }\n  };\n}", "map": {"version": 3, "names": ["lruQueue", "limit", "size", "base", "queue", "Object", "create", "map", "index", "del", "id", "oldIndex", "prototype", "hasOwnProperty", "call", "Math", "abs", "hit", "nuIndex", "undefined", "delete", "clear"], "sources": ["D:/work/joyserver/client/node_modules/mathjs/lib/esm/utils/lruQueue.js"], "sourcesContent": ["// (c) 2018, <PERSON><PERSON>\n// SPDX-License-Identifier: ISC\n// Derived from https://github.com/medikoo/lru-queue\nexport function lruQueue(limit) {\n  var size = 0;\n  var base = 1;\n  var queue = Object.create(null);\n  var map = Object.create(null);\n  var index = 0;\n  var del = function del(id) {\n    var oldIndex = map[id];\n    if (!oldIndex) return;\n    delete queue[oldIndex];\n    delete map[id];\n    --size;\n    if (base !== oldIndex) return;\n    if (!size) {\n      index = 0;\n      base = 1;\n      return;\n    }\n    while (!Object.prototype.hasOwnProperty.call(queue, ++base)) {/* empty */}\n  };\n  limit = Math.abs(limit);\n  return {\n    hit: function hit(id) {\n      var oldIndex = map[id];\n      var nuIndex = ++index;\n      queue[nuIndex] = id;\n      map[id] = nuIndex;\n      if (!oldIndex) {\n        ++size;\n        if (size <= limit) return undefined;\n        id = queue[base];\n        del(id);\n        return id;\n      }\n      delete queue[oldIndex];\n      if (base !== oldIndex) return undefined;\n      while (!Object.prototype.hasOwnProperty.call(queue, ++base)) {/* empty */}\n      return undefined;\n    },\n    delete: del,\n    clear: function clear() {\n      size = index = 0;\n      base = 1;\n      queue = Object.create(null);\n      map = Object.create(null);\n    }\n  };\n}"], "mappings": "AAAA;AACA;AACA;AACA,OAAO,SAASA,QAAT,CAAkBC,KAAlB,EAAyB;EAC9B,IAAIC,IAAI,GAAG,CAAX;EACA,IAAIC,IAAI,GAAG,CAAX;EACA,IAAIC,KAAK,GAAGC,MAAM,CAACC,MAAP,CAAc,IAAd,CAAZ;EACA,IAAIC,GAAG,GAAGF,MAAM,CAACC,MAAP,CAAc,IAAd,CAAV;EACA,IAAIE,KAAK,GAAG,CAAZ;;EACA,IAAIC,GAAG,GAAG,SAASA,GAAT,CAAaC,EAAb,EAAiB;IACzB,IAAIC,QAAQ,GAAGJ,GAAG,CAACG,EAAD,CAAlB;IACA,IAAI,CAACC,QAAL,EAAe;IACf,OAAOP,KAAK,CAACO,QAAD,CAAZ;IACA,OAAOJ,GAAG,CAACG,EAAD,CAAV;IACA,EAAER,IAAF;IACA,IAAIC,IAAI,KAAKQ,QAAb,EAAuB;;IACvB,IAAI,CAACT,IAAL,EAAW;MACTM,KAAK,GAAG,CAAR;MACAL,IAAI,GAAG,CAAP;MACA;IACD;;IACD,OAAO,CAACE,MAAM,CAACO,SAAP,CAAiBC,cAAjB,CAAgCC,IAAhC,CAAqCV,KAArC,EAA4C,EAAED,IAA9C,CAAR,EAA6D;MAAC;IAAY;EAC3E,CAbD;;EAcAF,KAAK,GAAGc,IAAI,CAACC,GAAL,CAASf,KAAT,CAAR;EACA,OAAO;IACLgB,GAAG,EAAE,SAASA,GAAT,CAAaP,EAAb,EAAiB;MACpB,IAAIC,QAAQ,GAAGJ,GAAG,CAACG,EAAD,CAAlB;MACA,IAAIQ,OAAO,GAAG,EAAEV,KAAhB;MACAJ,KAAK,CAACc,OAAD,CAAL,GAAiBR,EAAjB;MACAH,GAAG,CAACG,EAAD,CAAH,GAAUQ,OAAV;;MACA,IAAI,CAACP,QAAL,EAAe;QACb,EAAET,IAAF;QACA,IAAIA,IAAI,IAAID,KAAZ,EAAmB,OAAOkB,SAAP;QACnBT,EAAE,GAAGN,KAAK,CAACD,IAAD,CAAV;QACAM,GAAG,CAACC,EAAD,CAAH;QACA,OAAOA,EAAP;MACD;;MACD,OAAON,KAAK,CAACO,QAAD,CAAZ;MACA,IAAIR,IAAI,KAAKQ,QAAb,EAAuB,OAAOQ,SAAP;;MACvB,OAAO,CAACd,MAAM,CAACO,SAAP,CAAiBC,cAAjB,CAAgCC,IAAhC,CAAqCV,KAArC,EAA4C,EAAED,IAA9C,CAAR,EAA6D;QAAC;MAAY;;MAC1E,OAAOgB,SAAP;IACD,CAjBI;IAkBLC,MAAM,EAAEX,GAlBH;IAmBLY,KAAK,EAAE,SAASA,KAAT,GAAiB;MACtBnB,IAAI,GAAGM,KAAK,GAAG,CAAf;MACAL,IAAI,GAAG,CAAP;MACAC,KAAK,GAAGC,MAAM,CAACC,MAAP,CAAc,IAAd,CAAR;MACAC,GAAG,GAAGF,MAAM,CAACC,MAAP,CAAc,IAAd,CAAN;IACD;EAxBI,CAAP;AA0BD"}, "metadata": {}, "sourceType": "module"}