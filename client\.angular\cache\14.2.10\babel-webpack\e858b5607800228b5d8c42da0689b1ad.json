{"ast": null, "code": "/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { divideDependencies } from './dependenciesDivide.generated.js';\nimport { dotDivideDependencies } from './dependenciesDotDivide.generated.js';\nimport { isNumericDependencies } from './dependenciesIsNumeric.generated.js';\nimport { logDependencies } from './dependenciesLog.generated.js';\nimport { mapDependencies } from './dependenciesMap.generated.js';\nimport { matrixDependencies } from './dependenciesMatrix.generated.js';\nimport { multiplyDependencies } from './dependenciesMultiply.generated.js';\nimport { sumDependencies } from './dependenciesSum.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createKldivergence } from '../../factoriesAny.js';\nexport var kldivergenceDependencies = {\n  divideDependencies,\n  dotDivideDependencies,\n  isNumericDependencies,\n  logDependencies,\n  mapDependencies,\n  matrixDependencies,\n  multiplyDependencies,\n  sumDependencies,\n  typedDependencies,\n  createKldivergence\n};", "map": null, "metadata": {}, "sourceType": "module"}