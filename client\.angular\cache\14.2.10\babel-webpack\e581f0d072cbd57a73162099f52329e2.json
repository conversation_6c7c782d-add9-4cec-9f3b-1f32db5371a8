{"ast": null, "code": "import { factory } from '../../utils/factory.js';\nvar name = 'norm';\nvar dependencies = ['typed', 'abs', 'add', 'pow', 'conj', 'sqrt', 'multiply', 'equalScalar', 'larger', 'smaller', 'matrix', 'ctranspose', 'eigs'];\nexport var createNorm = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed,\n    abs,\n    add,\n    pow,\n    conj,\n    sqrt,\n    multiply,\n    equalScalar,\n    larger,\n    smaller,\n    matrix,\n    ctranspose,\n    eigs\n  } = _ref;\n  /**\n   * Calculate the norm of a number, vector or matrix.\n   *\n   * The second parameter p is optional. If not provided, it defaults to 2.\n   *\n   * Syntax:\n   *\n   *    math.norm(x)\n   *    math.norm(x, p)\n   *\n   * Examples:\n   *\n   *    math.abs(-3.5)                         // returns 3.5\n   *    math.norm(-3.5)                        // returns 3.5\n   *\n   *    math.norm(math.complex(3, -4))         // returns 5\n   *\n   *    math.norm([1, 2, -3], Infinity)        // returns 3\n   *    math.norm([1, 2, -3], -Infinity)       // returns 1\n   *\n   *    math.norm([3, 4], 2)                   // returns 5\n   *\n   *    math.norm([[1, 2], [3, 4]], 1)          // returns 6\n   *    math.norm([[1, 2], [3, 4]], 'inf')     // returns 7\n   *    math.norm([[1, 2], [3, 4]], 'fro')     // returns 5.477225575051661\n   *\n   * See also:\n   *\n   *    abs, hypot\n   *\n   * @param  {number | BigNumber | Complex | Array | Matrix} x\n   *            Value for which to calculate the norm\n   * @param  {number | BigNumber | string} [p=2]\n   *            Vector space.\n   *            Supported numbers include Infinity and -Infinity.\n   *            Supported strings are: 'inf', '-inf', and 'fro' (The Frobenius norm)\n   * @return {number | BigNumber} the p-norm\n   */\n\n  return typed(name, {\n    number: Math.abs,\n    Complex: function Complex(x) {\n      return x.abs();\n    },\n    BigNumber: function BigNumber(x) {\n      // norm(x) = abs(x)\n      return x.abs();\n    },\n    boolean: function boolean(x) {\n      // norm(x) = abs(x)\n      return Math.abs(x);\n    },\n    Array: function Array(x) {\n      return _norm(matrix(x), 2);\n    },\n    Matrix: function Matrix(x) {\n      return _norm(x, 2);\n    },\n    'Array, number | BigNumber | string': function Array_number__BigNumber__string(x, p) {\n      return _norm(matrix(x), p);\n    },\n    'Matrix, number | BigNumber | string': function Matrix_number__BigNumber__string(x, p) {\n      return _norm(x, p);\n    }\n  });\n  /**\n   * Calculate the plus infinity norm for a vector\n   * @param {Matrix} x\n   * @returns {number} Returns the norm\n   * @private\n   */\n\n  function _vectorNormPlusInfinity(x) {\n    // norm(x, Infinity) = max(abs(x))\n    var pinf = 0; // skip zeros since abs(0) === 0\n\n    x.forEach(function (value) {\n      var v = abs(value);\n\n      if (larger(v, pinf)) {\n        pinf = v;\n      }\n    }, true);\n    return pinf;\n  }\n  /**\n   * Calculate the minus infinity norm for a vector\n   * @param {Matrix} x\n   * @returns {number} Returns the norm\n   * @private\n   */\n\n\n  function _vectorNormMinusInfinity(x) {\n    // norm(x, -Infinity) = min(abs(x))\n    var ninf; // skip zeros since abs(0) === 0\n\n    x.forEach(function (value) {\n      var v = abs(value);\n\n      if (!ninf || smaller(v, ninf)) {\n        ninf = v;\n      }\n    }, true);\n    return ninf || 0;\n  }\n  /**\n   * Calculate the norm for a vector\n   * @param {Matrix} x\n   * @param {number | string} p\n   * @returns {number} Returns the norm\n   * @private\n   */\n\n\n  function _vectorNorm(x, p) {\n    // check p\n    if (p === Number.POSITIVE_INFINITY || p === 'inf') {\n      return _vectorNormPlusInfinity(x);\n    }\n\n    if (p === Number.NEGATIVE_INFINITY || p === '-inf') {\n      return _vectorNormMinusInfinity(x);\n    }\n\n    if (p === 'fro') {\n      return _norm(x, 2);\n    }\n\n    if (typeof p === 'number' && !isNaN(p)) {\n      // check p != 0\n      if (!equalScalar(p, 0)) {\n        // norm(x, p) = sum(abs(xi) ^ p) ^ 1/p\n        var n = 0; // skip zeros since abs(0) === 0\n\n        x.forEach(function (value) {\n          n = add(pow(abs(value), p), n);\n        }, true);\n        return pow(n, 1 / p);\n      }\n\n      return Number.POSITIVE_INFINITY;\n    } // invalid parameter value\n\n\n    throw new Error('Unsupported parameter value');\n  }\n  /**\n   * Calculate the Frobenius norm for a matrix\n   * @param {Matrix} x\n   * @returns {number} Returns the norm\n   * @private\n   */\n\n\n  function _matrixNormFrobenius(x) {\n    // norm(x) = sqrt(sum(diag(x'x)))\n    var fro = 0;\n    x.forEach(function (value, index) {\n      fro = add(fro, multiply(value, conj(value)));\n    });\n    return abs(sqrt(fro));\n  }\n  /**\n   * Calculate the norm L1 for a matrix\n   * @param {Matrix} x\n   * @returns {number} Returns the norm\n   * @private\n   */\n\n\n  function _matrixNormOne(x) {\n    // norm(x) = the largest column sum\n    var c = []; // result\n\n    var maxc = 0; // skip zeros since abs(0) == 0\n\n    x.forEach(function (value, index) {\n      var j = index[1];\n      var cj = add(c[j] || 0, abs(value));\n\n      if (larger(cj, maxc)) {\n        maxc = cj;\n      }\n\n      c[j] = cj;\n    }, true);\n    return maxc;\n  }\n  /**\n   * Calculate the norm L2 for a matrix\n   * @param {Matrix} x\n   * @returns {number} Returns the norm\n   * @private\n   */\n\n\n  function _matrixNormTwo(x) {\n    // norm(x) = sqrt( max eigenvalue of A*.A)\n    var sizeX = x.size();\n\n    if (sizeX[0] !== sizeX[1]) {\n      throw new RangeError('Invalid matrix dimensions');\n    }\n\n    var tx = ctranspose(x);\n    var squaredX = multiply(tx, x);\n    var eigenVals = eigs(squaredX).values.toArray();\n    var rho = eigenVals[eigenVals.length - 1];\n    return abs(sqrt(rho));\n  }\n  /**\n   * Calculate the infinity norm for a matrix\n   * @param {Matrix} x\n   * @returns {number} Returns the norm\n   * @private\n   */\n\n\n  function _matrixNormInfinity(x) {\n    // norm(x) = the largest row sum\n    var r = []; // result\n\n    var maxr = 0; // skip zeros since abs(0) == 0\n\n    x.forEach(function (value, index) {\n      var i = index[0];\n      var ri = add(r[i] || 0, abs(value));\n\n      if (larger(ri, maxr)) {\n        maxr = ri;\n      }\n\n      r[i] = ri;\n    }, true);\n    return maxr;\n  }\n  /**\n   * Calculate the norm for a 2D Matrix (M*N)\n   * @param {Matrix} x\n   * @param {number | string} p\n   * @returns {number} Returns the norm\n   * @private\n   */\n\n\n  function _matrixNorm(x, p) {\n    // check p\n    if (p === 1) {\n      return _matrixNormOne(x);\n    }\n\n    if (p === Number.POSITIVE_INFINITY || p === 'inf') {\n      return _matrixNormInfinity(x);\n    }\n\n    if (p === 'fro') {\n      return _matrixNormFrobenius(x);\n    }\n\n    if (p === 2) {\n      return _matrixNormTwo(x);\n    } // invalid parameter value\n\n\n    throw new Error('Unsupported parameter value ' + p);\n  }\n  /**\n   * Calculate the norm for an array\n   * @param {Matrix} x\n   * @param {number | string} p\n   * @returns {number} Returns the norm\n   * @private\n   */\n\n\n  function _norm(x, p) {\n    // size\n    var sizeX = x.size(); // check if it is a vector\n\n    if (sizeX.length === 1) {\n      return _vectorNorm(x, p);\n    } // MxN matrix\n\n\n    if (sizeX.length === 2) {\n      if (sizeX[0] && sizeX[1]) {\n        return _matrixNorm(x, p);\n      } else {\n        throw new RangeError('Invalid matrix dimensions');\n      }\n    }\n  }\n});", "map": {"version": 3, "names": ["factory", "name", "dependencies", "createNorm", "_ref", "typed", "abs", "add", "pow", "conj", "sqrt", "multiply", "equalScalar", "larger", "smaller", "matrix", "ctranspose", "eigs", "number", "Math", "Complex", "x", "BigNumber", "boolean", "Array", "_norm", "Matrix", "Array_number__BigNumber__string", "p", "Matrix_number__BigNumber__string", "_vectorNormPlusInfinity", "pinf", "for<PERSON>ach", "value", "v", "_vectorNormMinusInfinity", "ninf", "_vectorNorm", "Number", "POSITIVE_INFINITY", "NEGATIVE_INFINITY", "isNaN", "n", "Error", "_matrixNorm<PERSON><PERSON>nius", "fro", "index", "_matrixNormOne", "c", "maxc", "j", "cj", "_matrixNormTwo", "sizeX", "size", "RangeError", "tx", "squaredX", "eigenVals", "values", "toArray", "rho", "length", "_matrixNormInfinity", "r", "maxr", "i", "ri", "_matrixNorm"], "sources": ["D:/work/joyserver/client/node_modules/mathjs/lib/esm/function/arithmetic/norm.js"], "sourcesContent": ["import { factory } from '../../utils/factory.js';\nvar name = 'norm';\nvar dependencies = ['typed', 'abs', 'add', 'pow', 'conj', 'sqrt', 'multiply', 'equalScalar', 'larger', 'smaller', 'matrix', 'ctranspose', 'eigs'];\nexport var createNorm = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed,\n    abs,\n    add,\n    pow,\n    conj,\n    sqrt,\n    multiply,\n    equalScalar,\n    larger,\n    smaller,\n    matrix,\n    ctranspose,\n    eigs\n  } = _ref;\n  /**\n   * Calculate the norm of a number, vector or matrix.\n   *\n   * The second parameter p is optional. If not provided, it defaults to 2.\n   *\n   * Syntax:\n   *\n   *    math.norm(x)\n   *    math.norm(x, p)\n   *\n   * Examples:\n   *\n   *    math.abs(-3.5)                         // returns 3.5\n   *    math.norm(-3.5)                        // returns 3.5\n   *\n   *    math.norm(math.complex(3, -4))         // returns 5\n   *\n   *    math.norm([1, 2, -3], Infinity)        // returns 3\n   *    math.norm([1, 2, -3], -Infinity)       // returns 1\n   *\n   *    math.norm([3, 4], 2)                   // returns 5\n   *\n   *    math.norm([[1, 2], [3, 4]], 1)          // returns 6\n   *    math.norm([[1, 2], [3, 4]], 'inf')     // returns 7\n   *    math.norm([[1, 2], [3, 4]], 'fro')     // returns 5.477225575051661\n   *\n   * See also:\n   *\n   *    abs, hypot\n   *\n   * @param  {number | BigNumber | Complex | Array | Matrix} x\n   *            Value for which to calculate the norm\n   * @param  {number | BigNumber | string} [p=2]\n   *            Vector space.\n   *            Supported numbers include Infinity and -Infinity.\n   *            Supported strings are: 'inf', '-inf', and 'fro' (The Frobenius norm)\n   * @return {number | BigNumber} the p-norm\n   */\n  return typed(name, {\n    number: Math.abs,\n    Complex: function Complex(x) {\n      return x.abs();\n    },\n    BigNumber: function BigNumber(x) {\n      // norm(x) = abs(x)\n      return x.abs();\n    },\n    boolean: function boolean(x) {\n      // norm(x) = abs(x)\n      return Math.abs(x);\n    },\n    Array: function Array(x) {\n      return _norm(matrix(x), 2);\n    },\n    Matrix: function Matrix(x) {\n      return _norm(x, 2);\n    },\n    'Array, number | BigNumber | string': function Array_number__BigNumber__string(x, p) {\n      return _norm(matrix(x), p);\n    },\n    'Matrix, number | BigNumber | string': function Matrix_number__BigNumber__string(x, p) {\n      return _norm(x, p);\n    }\n  });\n\n  /**\n   * Calculate the plus infinity norm for a vector\n   * @param {Matrix} x\n   * @returns {number} Returns the norm\n   * @private\n   */\n  function _vectorNormPlusInfinity(x) {\n    // norm(x, Infinity) = max(abs(x))\n    var pinf = 0;\n    // skip zeros since abs(0) === 0\n    x.forEach(function (value) {\n      var v = abs(value);\n      if (larger(v, pinf)) {\n        pinf = v;\n      }\n    }, true);\n    return pinf;\n  }\n\n  /**\n   * Calculate the minus infinity norm for a vector\n   * @param {Matrix} x\n   * @returns {number} Returns the norm\n   * @private\n   */\n  function _vectorNormMinusInfinity(x) {\n    // norm(x, -Infinity) = min(abs(x))\n    var ninf;\n    // skip zeros since abs(0) === 0\n    x.forEach(function (value) {\n      var v = abs(value);\n      if (!ninf || smaller(v, ninf)) {\n        ninf = v;\n      }\n    }, true);\n    return ninf || 0;\n  }\n\n  /**\n   * Calculate the norm for a vector\n   * @param {Matrix} x\n   * @param {number | string} p\n   * @returns {number} Returns the norm\n   * @private\n   */\n  function _vectorNorm(x, p) {\n    // check p\n    if (p === Number.POSITIVE_INFINITY || p === 'inf') {\n      return _vectorNormPlusInfinity(x);\n    }\n    if (p === Number.NEGATIVE_INFINITY || p === '-inf') {\n      return _vectorNormMinusInfinity(x);\n    }\n    if (p === 'fro') {\n      return _norm(x, 2);\n    }\n    if (typeof p === 'number' && !isNaN(p)) {\n      // check p != 0\n      if (!equalScalar(p, 0)) {\n        // norm(x, p) = sum(abs(xi) ^ p) ^ 1/p\n        var n = 0;\n        // skip zeros since abs(0) === 0\n        x.forEach(function (value) {\n          n = add(pow(abs(value), p), n);\n        }, true);\n        return pow(n, 1 / p);\n      }\n      return Number.POSITIVE_INFINITY;\n    }\n    // invalid parameter value\n    throw new Error('Unsupported parameter value');\n  }\n\n  /**\n   * Calculate the Frobenius norm for a matrix\n   * @param {Matrix} x\n   * @returns {number} Returns the norm\n   * @private\n   */\n  function _matrixNormFrobenius(x) {\n    // norm(x) = sqrt(sum(diag(x'x)))\n    var fro = 0;\n    x.forEach(function (value, index) {\n      fro = add(fro, multiply(value, conj(value)));\n    });\n    return abs(sqrt(fro));\n  }\n\n  /**\n   * Calculate the norm L1 for a matrix\n   * @param {Matrix} x\n   * @returns {number} Returns the norm\n   * @private\n   */\n  function _matrixNormOne(x) {\n    // norm(x) = the largest column sum\n    var c = [];\n    // result\n    var maxc = 0;\n    // skip zeros since abs(0) == 0\n    x.forEach(function (value, index) {\n      var j = index[1];\n      var cj = add(c[j] || 0, abs(value));\n      if (larger(cj, maxc)) {\n        maxc = cj;\n      }\n      c[j] = cj;\n    }, true);\n    return maxc;\n  }\n\n  /**\n   * Calculate the norm L2 for a matrix\n   * @param {Matrix} x\n   * @returns {number} Returns the norm\n   * @private\n   */\n  function _matrixNormTwo(x) {\n    // norm(x) = sqrt( max eigenvalue of A*.A)\n    var sizeX = x.size();\n    if (sizeX[0] !== sizeX[1]) {\n      throw new RangeError('Invalid matrix dimensions');\n    }\n    var tx = ctranspose(x);\n    var squaredX = multiply(tx, x);\n    var eigenVals = eigs(squaredX).values.toArray();\n    var rho = eigenVals[eigenVals.length - 1];\n    return abs(sqrt(rho));\n  }\n\n  /**\n   * Calculate the infinity norm for a matrix\n   * @param {Matrix} x\n   * @returns {number} Returns the norm\n   * @private\n   */\n  function _matrixNormInfinity(x) {\n    // norm(x) = the largest row sum\n    var r = [];\n    // result\n    var maxr = 0;\n    // skip zeros since abs(0) == 0\n    x.forEach(function (value, index) {\n      var i = index[0];\n      var ri = add(r[i] || 0, abs(value));\n      if (larger(ri, maxr)) {\n        maxr = ri;\n      }\n      r[i] = ri;\n    }, true);\n    return maxr;\n  }\n\n  /**\n   * Calculate the norm for a 2D Matrix (M*N)\n   * @param {Matrix} x\n   * @param {number | string} p\n   * @returns {number} Returns the norm\n   * @private\n   */\n  function _matrixNorm(x, p) {\n    // check p\n    if (p === 1) {\n      return _matrixNormOne(x);\n    }\n    if (p === Number.POSITIVE_INFINITY || p === 'inf') {\n      return _matrixNormInfinity(x);\n    }\n    if (p === 'fro') {\n      return _matrixNormFrobenius(x);\n    }\n    if (p === 2) {\n      return _matrixNormTwo(x);\n    } // invalid parameter value\n\n    throw new Error('Unsupported parameter value ' + p);\n  }\n\n  /**\n   * Calculate the norm for an array\n   * @param {Matrix} x\n   * @param {number | string} p\n   * @returns {number} Returns the norm\n   * @private\n   */\n  function _norm(x, p) {\n    // size\n    var sizeX = x.size();\n\n    // check if it is a vector\n    if (sizeX.length === 1) {\n      return _vectorNorm(x, p);\n    }\n    // MxN matrix\n    if (sizeX.length === 2) {\n      if (sizeX[0] && sizeX[1]) {\n        return _matrixNorm(x, p);\n      } else {\n        throw new RangeError('Invalid matrix dimensions');\n      }\n    }\n  }\n});"], "mappings": "AAAA,SAASA,OAAT,QAAwB,wBAAxB;AACA,IAAIC,IAAI,GAAG,MAAX;AACA,IAAIC,YAAY,GAAG,CAAC,OAAD,EAAU,KAAV,EAAiB,KAAjB,EAAwB,KAAxB,EAA+B,MAA/B,EAAuC,MAAvC,EAA+C,UAA/C,EAA2D,aAA3D,EAA0E,QAA1E,EAAoF,SAApF,EAA+F,QAA/F,EAAyG,YAAzG,EAAuH,MAAvH,CAAnB;AACA,OAAO,IAAIC,UAAU,GAAG,eAAeH,OAAO,CAACC,IAAD,EAAOC,YAAP,EAAqBE,IAAI,IAAI;EACzE,IAAI;IACFC,KADE;IAEFC,GAFE;IAGFC,GAHE;IAIFC,GAJE;IAKFC,IALE;IAMFC,IANE;IAOFC,QAPE;IAQFC,WARE;IASFC,MATE;IAUFC,OAVE;IAWFC,MAXE;IAYFC,UAZE;IAaFC;EAbE,IAcAb,IAdJ;EAeA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EACE,OAAOC,KAAK,CAACJ,IAAD,EAAO;IACjBiB,MAAM,EAAEC,IAAI,CAACb,GADI;IAEjBc,OAAO,EAAE,SAASA,OAAT,CAAiBC,CAAjB,EAAoB;MAC3B,OAAOA,CAAC,CAACf,GAAF,EAAP;IACD,CAJgB;IAKjBgB,SAAS,EAAE,SAASA,SAAT,CAAmBD,CAAnB,EAAsB;MAC/B;MACA,OAAOA,CAAC,CAACf,GAAF,EAAP;IACD,CARgB;IASjBiB,OAAO,EAAE,SAASA,OAAT,CAAiBF,CAAjB,EAAoB;MAC3B;MACA,OAAOF,IAAI,CAACb,GAAL,CAASe,CAAT,CAAP;IACD,CAZgB;IAajBG,KAAK,EAAE,SAASA,KAAT,CAAeH,CAAf,EAAkB;MACvB,OAAOI,KAAK,CAACV,MAAM,CAACM,CAAD,CAAP,EAAY,CAAZ,CAAZ;IACD,CAfgB;IAgBjBK,MAAM,EAAE,SAASA,MAAT,CAAgBL,CAAhB,EAAmB;MACzB,OAAOI,KAAK,CAACJ,CAAD,EAAI,CAAJ,CAAZ;IACD,CAlBgB;IAmBjB,sCAAsC,SAASM,+BAAT,CAAyCN,CAAzC,EAA4CO,CAA5C,EAA+C;MACnF,OAAOH,KAAK,CAACV,MAAM,CAACM,CAAD,CAAP,EAAYO,CAAZ,CAAZ;IACD,CArBgB;IAsBjB,uCAAuC,SAASC,gCAAT,CAA0CR,CAA1C,EAA6CO,CAA7C,EAAgD;MACrF,OAAOH,KAAK,CAACJ,CAAD,EAAIO,CAAJ,CAAZ;IACD;EAxBgB,CAAP,CAAZ;EA2BA;AACF;AACA;AACA;AACA;AACA;;EACE,SAASE,uBAAT,CAAiCT,CAAjC,EAAoC;IAClC;IACA,IAAIU,IAAI,GAAG,CAAX,CAFkC,CAGlC;;IACAV,CAAC,CAACW,OAAF,CAAU,UAAUC,KAAV,EAAiB;MACzB,IAAIC,CAAC,GAAG5B,GAAG,CAAC2B,KAAD,CAAX;;MACA,IAAIpB,MAAM,CAACqB,CAAD,EAAIH,IAAJ,CAAV,EAAqB;QACnBA,IAAI,GAAGG,CAAP;MACD;IACF,CALD,EAKG,IALH;IAMA,OAAOH,IAAP;EACD;EAED;AACF;AACA;AACA;AACA;AACA;;;EACE,SAASI,wBAAT,CAAkCd,CAAlC,EAAqC;IACnC;IACA,IAAIe,IAAJ,CAFmC,CAGnC;;IACAf,CAAC,CAACW,OAAF,CAAU,UAAUC,KAAV,EAAiB;MACzB,IAAIC,CAAC,GAAG5B,GAAG,CAAC2B,KAAD,CAAX;;MACA,IAAI,CAACG,IAAD,IAAStB,OAAO,CAACoB,CAAD,EAAIE,IAAJ,CAApB,EAA+B;QAC7BA,IAAI,GAAGF,CAAP;MACD;IACF,CALD,EAKG,IALH;IAMA,OAAOE,IAAI,IAAI,CAAf;EACD;EAED;AACF;AACA;AACA;AACA;AACA;AACA;;;EACE,SAASC,WAAT,CAAqBhB,CAArB,EAAwBO,CAAxB,EAA2B;IACzB;IACA,IAAIA,CAAC,KAAKU,MAAM,CAACC,iBAAb,IAAkCX,CAAC,KAAK,KAA5C,EAAmD;MACjD,OAAOE,uBAAuB,CAACT,CAAD,CAA9B;IACD;;IACD,IAAIO,CAAC,KAAKU,MAAM,CAACE,iBAAb,IAAkCZ,CAAC,KAAK,MAA5C,EAAoD;MAClD,OAAOO,wBAAwB,CAACd,CAAD,CAA/B;IACD;;IACD,IAAIO,CAAC,KAAK,KAAV,EAAiB;MACf,OAAOH,KAAK,CAACJ,CAAD,EAAI,CAAJ,CAAZ;IACD;;IACD,IAAI,OAAOO,CAAP,KAAa,QAAb,IAAyB,CAACa,KAAK,CAACb,CAAD,CAAnC,EAAwC;MACtC;MACA,IAAI,CAAChB,WAAW,CAACgB,CAAD,EAAI,CAAJ,CAAhB,EAAwB;QACtB;QACA,IAAIc,CAAC,GAAG,CAAR,CAFsB,CAGtB;;QACArB,CAAC,CAACW,OAAF,CAAU,UAAUC,KAAV,EAAiB;UACzBS,CAAC,GAAGnC,GAAG,CAACC,GAAG,CAACF,GAAG,CAAC2B,KAAD,CAAJ,EAAaL,CAAb,CAAJ,EAAqBc,CAArB,CAAP;QACD,CAFD,EAEG,IAFH;QAGA,OAAOlC,GAAG,CAACkC,CAAD,EAAI,IAAId,CAAR,CAAV;MACD;;MACD,OAAOU,MAAM,CAACC,iBAAd;IACD,CAvBwB,CAwBzB;;;IACA,MAAM,IAAII,KAAJ,CAAU,6BAAV,CAAN;EACD;EAED;AACF;AACA;AACA;AACA;AACA;;;EACE,SAASC,oBAAT,CAA8BvB,CAA9B,EAAiC;IAC/B;IACA,IAAIwB,GAAG,GAAG,CAAV;IACAxB,CAAC,CAACW,OAAF,CAAU,UAAUC,KAAV,EAAiBa,KAAjB,EAAwB;MAChCD,GAAG,GAAGtC,GAAG,CAACsC,GAAD,EAAMlC,QAAQ,CAACsB,KAAD,EAAQxB,IAAI,CAACwB,KAAD,CAAZ,CAAd,CAAT;IACD,CAFD;IAGA,OAAO3B,GAAG,CAACI,IAAI,CAACmC,GAAD,CAAL,CAAV;EACD;EAED;AACF;AACA;AACA;AACA;AACA;;;EACE,SAASE,cAAT,CAAwB1B,CAAxB,EAA2B;IACzB;IACA,IAAI2B,CAAC,GAAG,EAAR,CAFyB,CAGzB;;IACA,IAAIC,IAAI,GAAG,CAAX,CAJyB,CAKzB;;IACA5B,CAAC,CAACW,OAAF,CAAU,UAAUC,KAAV,EAAiBa,KAAjB,EAAwB;MAChC,IAAII,CAAC,GAAGJ,KAAK,CAAC,CAAD,CAAb;MACA,IAAIK,EAAE,GAAG5C,GAAG,CAACyC,CAAC,CAACE,CAAD,CAAD,IAAQ,CAAT,EAAY5C,GAAG,CAAC2B,KAAD,CAAf,CAAZ;;MACA,IAAIpB,MAAM,CAACsC,EAAD,EAAKF,IAAL,CAAV,EAAsB;QACpBA,IAAI,GAAGE,EAAP;MACD;;MACDH,CAAC,CAACE,CAAD,CAAD,GAAOC,EAAP;IACD,CAPD,EAOG,IAPH;IAQA,OAAOF,IAAP;EACD;EAED;AACF;AACA;AACA;AACA;AACA;;;EACE,SAASG,cAAT,CAAwB/B,CAAxB,EAA2B;IACzB;IACA,IAAIgC,KAAK,GAAGhC,CAAC,CAACiC,IAAF,EAAZ;;IACA,IAAID,KAAK,CAAC,CAAD,CAAL,KAAaA,KAAK,CAAC,CAAD,CAAtB,EAA2B;MACzB,MAAM,IAAIE,UAAJ,CAAe,2BAAf,CAAN;IACD;;IACD,IAAIC,EAAE,GAAGxC,UAAU,CAACK,CAAD,CAAnB;IACA,IAAIoC,QAAQ,GAAG9C,QAAQ,CAAC6C,EAAD,EAAKnC,CAAL,CAAvB;IACA,IAAIqC,SAAS,GAAGzC,IAAI,CAACwC,QAAD,CAAJ,CAAeE,MAAf,CAAsBC,OAAtB,EAAhB;IACA,IAAIC,GAAG,GAAGH,SAAS,CAACA,SAAS,CAACI,MAAV,GAAmB,CAApB,CAAnB;IACA,OAAOxD,GAAG,CAACI,IAAI,CAACmD,GAAD,CAAL,CAAV;EACD;EAED;AACF;AACA;AACA;AACA;AACA;;;EACE,SAASE,mBAAT,CAA6B1C,CAA7B,EAAgC;IAC9B;IACA,IAAI2C,CAAC,GAAG,EAAR,CAF8B,CAG9B;;IACA,IAAIC,IAAI,GAAG,CAAX,CAJ8B,CAK9B;;IACA5C,CAAC,CAACW,OAAF,CAAU,UAAUC,KAAV,EAAiBa,KAAjB,EAAwB;MAChC,IAAIoB,CAAC,GAAGpB,KAAK,CAAC,CAAD,CAAb;MACA,IAAIqB,EAAE,GAAG5D,GAAG,CAACyD,CAAC,CAACE,CAAD,CAAD,IAAQ,CAAT,EAAY5D,GAAG,CAAC2B,KAAD,CAAf,CAAZ;;MACA,IAAIpB,MAAM,CAACsD,EAAD,EAAKF,IAAL,CAAV,EAAsB;QACpBA,IAAI,GAAGE,EAAP;MACD;;MACDH,CAAC,CAACE,CAAD,CAAD,GAAOC,EAAP;IACD,CAPD,EAOG,IAPH;IAQA,OAAOF,IAAP;EACD;EAED;AACF;AACA;AACA;AACA;AACA;AACA;;;EACE,SAASG,WAAT,CAAqB/C,CAArB,EAAwBO,CAAxB,EAA2B;IACzB;IACA,IAAIA,CAAC,KAAK,CAAV,EAAa;MACX,OAAOmB,cAAc,CAAC1B,CAAD,CAArB;IACD;;IACD,IAAIO,CAAC,KAAKU,MAAM,CAACC,iBAAb,IAAkCX,CAAC,KAAK,KAA5C,EAAmD;MACjD,OAAOmC,mBAAmB,CAAC1C,CAAD,CAA1B;IACD;;IACD,IAAIO,CAAC,KAAK,KAAV,EAAiB;MACf,OAAOgB,oBAAoB,CAACvB,CAAD,CAA3B;IACD;;IACD,IAAIO,CAAC,KAAK,CAAV,EAAa;MACX,OAAOwB,cAAc,CAAC/B,CAAD,CAArB;IACD,CAbwB,CAavB;;;IAEF,MAAM,IAAIsB,KAAJ,CAAU,iCAAiCf,CAA3C,CAAN;EACD;EAED;AACF;AACA;AACA;AACA;AACA;AACA;;;EACE,SAASH,KAAT,CAAeJ,CAAf,EAAkBO,CAAlB,EAAqB;IACnB;IACA,IAAIyB,KAAK,GAAGhC,CAAC,CAACiC,IAAF,EAAZ,CAFmB,CAInB;;IACA,IAAID,KAAK,CAACS,MAAN,KAAiB,CAArB,EAAwB;MACtB,OAAOzB,WAAW,CAAChB,CAAD,EAAIO,CAAJ,CAAlB;IACD,CAPkB,CAQnB;;;IACA,IAAIyB,KAAK,CAACS,MAAN,KAAiB,CAArB,EAAwB;MACtB,IAAIT,KAAK,CAAC,CAAD,CAAL,IAAYA,KAAK,CAAC,CAAD,CAArB,EAA0B;QACxB,OAAOe,WAAW,CAAC/C,CAAD,EAAIO,CAAJ,CAAlB;MACD,CAFD,MAEO;QACL,MAAM,IAAI2B,UAAJ,CAAe,2BAAf,CAAN;MACD;IACF;EACF;AACF,CA3R6C,CAAvC"}, "metadata": {}, "sourceType": "module"}