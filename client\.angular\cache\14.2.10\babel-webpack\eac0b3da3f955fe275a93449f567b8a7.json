{"ast": null, "code": "/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { BigNumberDependencies } from './dependenciesBigNumberClass.generated.js';\nimport { UnitDependencies } from './dependenciesUnitClass.generated.js';\nimport { createConductanceQuantum } from '../../factoriesAny.js';\nexport var conductanceQuantumDependencies = {\n  BigNumberDependencies,\n  UnitDependencies,\n  createConductanceQuantum\n};", "map": {"version": 3, "names": ["BigNumberDependencies", "UnitDependencies", "createConductanceQuantum", "conductanceQuantumDependencies"], "sources": ["D:/work/joyserver/client/node_modules/mathjs/lib/esm/entry/dependenciesAny/dependenciesConductanceQuantum.generated.js"], "sourcesContent": ["/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { BigNumberDependencies } from './dependenciesBigNumberClass.generated.js';\nimport { UnitDependencies } from './dependenciesUnitClass.generated.js';\nimport { createConductanceQuantum } from '../../factoriesAny.js';\nexport var conductanceQuantumDependencies = {\n  BigNumberDependencies,\n  UnitDependencies,\n  createConductanceQuantum\n};"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,qBAAT,QAAsC,2CAAtC;AACA,SAASC,gBAAT,QAAiC,sCAAjC;AACA,SAASC,wBAAT,QAAyC,uBAAzC;AACA,OAAO,IAAIC,8BAA8B,GAAG;EAC1CH,qBAD0C;EAE1CC,gBAF0C;EAG1CC;AAH0C,CAArC"}, "metadata": {}, "sourceType": "module"}