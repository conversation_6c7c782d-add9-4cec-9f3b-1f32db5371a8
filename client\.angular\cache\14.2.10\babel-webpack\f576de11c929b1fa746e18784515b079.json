{"ast": null, "code": "/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { bignumberDependencies } from './dependenciesBignumber.generated.js';\nimport { addScalarDependencies } from './dependenciesAddScalar.generated.js';\nimport { combinationsDependencies } from './dependenciesCombinations.generated.js';\nimport { divideScalarDependencies } from './dependenciesDivideScalar.generated.js';\nimport { factorialDependencies } from './dependenciesFactorial.generated.js';\nimport { isIntegerDependencies } from './dependenciesIsInteger.generated.js';\nimport { isNegativeDependencies } from './dependenciesIsNegative.generated.js';\nimport { largerDependencies } from './dependenciesLarger.generated.js';\nimport { multiplyScalarDependencies } from './dependenciesMultiplyScalar.generated.js';\nimport { numberDependencies } from './dependenciesNumber.generated.js';\nimport { powDependencies } from './dependenciesPow.generated.js';\nimport { subtractScalarDependencies } from './dependenciesSubtractScalar.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createStirlingS2 } from '../../factoriesAny.js';\nexport var stirlingS2Dependencies = {\n  bignumberDependencies,\n  addScalarDependencies,\n  combinationsDependencies,\n  divideScalarDependencies,\n  factorialDependencies,\n  isIntegerDependencies,\n  isNegativeDependencies,\n  largerDependencies,\n  multiplyScalarDependencies,\n  numberDependencies,\n  powDependencies,\n  subtractScalarDependencies,\n  typedDependencies,\n  createStirlingS2\n};", "map": null, "metadata": {}, "sourceType": "module"}