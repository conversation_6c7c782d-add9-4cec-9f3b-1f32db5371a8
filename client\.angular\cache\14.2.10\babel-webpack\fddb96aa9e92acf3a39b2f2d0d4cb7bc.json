{"ast": null, "code": "/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { BigNumberDependencies } from './dependenciesBigNumberClass.generated.js';\nimport { DenseMatrixDependencies } from './dependenciesDenseMatrixClass.generated.js';\nimport { concatDependencies } from './dependenciesConcat.generated.js';\nimport { equalScalarDependencies } from './dependenciesEqualScalar.generated.js';\nimport { matrixDependencies } from './dependenciesMatrix.generated.js';\nimport { roundDependencies } from './dependenciesRound.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { zerosDependencies } from './dependenciesZeros.generated.js';\nimport { createGcd } from '../../factoriesAny.js';\nexport var gcdDependencies = {\n  BigNumberDependencies,\n  DenseMatrixDependencies,\n  concatDependencies,\n  equalScalarDependencies,\n  matrixDependencies,\n  roundDependencies,\n  typedDependencies,\n  zerosDependencies,\n  createGcd\n};", "map": {"version": 3, "names": ["BigNumberDependencies", "DenseMatrixDependencies", "concatDependencies", "equalScalarDependencies", "matrixDependencies", "roundDependencies", "typedDependencies", "zerosDependencies", "createGcd", "gcdDependencies"], "sources": ["D:/work/joyserver/client/node_modules/mathjs/lib/esm/entry/dependenciesAny/dependenciesGcd.generated.js"], "sourcesContent": ["/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { BigNumberDependencies } from './dependenciesBigNumberClass.generated.js';\nimport { DenseMatrixDependencies } from './dependenciesDenseMatrixClass.generated.js';\nimport { concatDependencies } from './dependenciesConcat.generated.js';\nimport { equalScalarDependencies } from './dependenciesEqualScalar.generated.js';\nimport { matrixDependencies } from './dependenciesMatrix.generated.js';\nimport { roundDependencies } from './dependenciesRound.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { zerosDependencies } from './dependenciesZeros.generated.js';\nimport { createGcd } from '../../factoriesAny.js';\nexport var gcdDependencies = {\n  BigNumberDependencies,\n  DenseMatrixDependencies,\n  concatDependencies,\n  equalScalarDependencies,\n  matrixDependencies,\n  roundDependencies,\n  typedDependencies,\n  zerosDependencies,\n  createGcd\n};"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,qBAAT,QAAsC,2CAAtC;AACA,SAASC,uBAAT,QAAwC,6CAAxC;AACA,SAASC,kBAAT,QAAmC,mCAAnC;AACA,SAASC,uBAAT,QAAwC,wCAAxC;AACA,SAASC,kBAAT,QAAmC,mCAAnC;AACA,SAASC,iBAAT,QAAkC,kCAAlC;AACA,SAASC,iBAAT,QAAkC,kCAAlC;AACA,SAASC,iBAAT,QAAkC,kCAAlC;AACA,SAASC,SAAT,QAA0B,uBAA1B;AACA,OAAO,IAAIC,eAAe,GAAG;EAC3BT,qBAD2B;EAE3BC,uBAF2B;EAG3BC,kBAH2B;EAI3BC,uBAJ2B;EAK3BC,kBAL2B;EAM3BC,iBAN2B;EAO3BC,iBAP2B;EAQ3BC,iBAR2B;EAS3BC;AAT2B,CAAtB"}, "metadata": {}, "sourceType": "module"}