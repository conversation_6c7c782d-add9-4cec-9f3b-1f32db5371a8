{"ast": null, "code": "import { factory } from '../../utils/factory.js';\nimport { expNumber } from '../../plain/number/index.js';\nvar name = 'exp';\nvar dependencies = ['typed'];\nexport var createExp = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed\n  } = _ref;\n  /**\n   * Calculate the exponential of a value.\n   * For matrices, if you want the matrix exponential of square matrix, use\n   * the `expm` function; if you want to take the exponential of each element,\n   * see the examples.\n   *\n   * Syntax:\n   *\n   *    math.exp(x)\n   *\n   * Examples:\n   *\n   *    math.exp(2)                  // returns number 7.3890560989306495\n   *    math.pow(math.e, 2)          // returns number 7.3890560989306495\n   *    math.log(math.exp(2))        // returns number 2\n   *\n   *    math.map([1, 2, 3], math.exp)\n   *    // returns Array [\n   *    //   2.718281828459045,\n   *    //   7.3890560989306495,\n   *    //   20.085536923187668\n   *    // ]\n   *\n   * See also:\n   *\n   *    expm1, expm, log, pow\n   *\n   * @param {number | BigNumber | Complex} x  A number to exponentiate\n   * @return {number | BigNumber | Complex} Exponential of `x`\n   */\n\n  return typed(name, {\n    number: expNumber,\n    Complex: function Complex(x) {\n      return x.exp();\n    },\n    BigNumber: function BigNumber(x) {\n      return x.exp();\n    }\n  });\n});", "map": null, "metadata": {}, "sourceType": "module"}