{"ast": null, "code": "export var minDocs = {\n  name: 'min',\n  category: 'Statistics',\n  syntax: ['min(a, b, c, ...)', 'min(A)', 'min(A, dimension)'],\n  description: 'Compute the minimum value of a list of values.',\n  examples: ['min(2, 3, 4, 1)', 'min([2, 3, 4, 1])', 'min([2, 5; 4, 3])', 'min([2, 5; 4, 3], 1)', 'min([2, 5; 4, 3], 2)', 'min(2.7, 7.1, -4.5, 2.0, 4.1)', 'max(2.7, 7.1, -4.5, 2.0, 4.1)'],\n  seealso: ['max', 'mean', 'median', 'prod', 'std', 'sum', 'variance']\n};", "map": null, "metadata": {}, "sourceType": "module"}