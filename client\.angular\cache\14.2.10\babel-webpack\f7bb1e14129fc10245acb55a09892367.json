{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nimport { isNode } from '../../utils/is.js';\nimport { forEach, map } from '../../utils/array.js';\nimport { factory } from '../../utils/factory.js';\nvar name = 'BlockNode';\nvar dependencies = ['ResultSet', 'Node'];\nexport var createBlockNode = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    ResultSet,\n    Node\n  } = _ref;\n\n  class BlockNode extends Node {\n    /**\n     * @constructor BlockNode\n     * @extends {Node}\n     * Holds a set with blocks\n     * @param {Array.<{node: Node} | {node: Node, visible: boolean}>} blocks\n     *            An array with blocks, where a block is constructed as an\n     *            Object with properties block, which is a Node, and visible,\n     *            which is a boolean. The property visible is optional and\n     *            is true by default\n     */\n    constructor(blocks) {\n      super(); // validate input, copy blocks\n\n      if (!Array.isArray(blocks)) throw new Error('Array expected');\n      this.blocks = blocks.map(function (block) {\n        var node = block && block.node;\n        var visible = block && block.visible !== undefined ? block.visible : true;\n        if (!isNode(node)) throw new TypeError('Property \"node\" must be a Node');\n\n        if (typeof visible !== 'boolean') {\n          throw new TypeError('Property \"visible\" must be a boolean');\n        }\n\n        return {\n          node,\n          visible\n        };\n      });\n    }\n\n    get type() {\n      return name;\n    }\n\n    get isBlockNode() {\n      return true;\n    }\n    /**\n     * Compile a node into a JavaScript function.\n     * This basically pre-calculates as much as possible and only leaves open\n     * calculations which depend on a dynamic scope with variables.\n     * @param {Object} math     Math.js namespace with functions and constants.\n     * @param {Object} argNames An object with argument names as key and `true`\n     *                          as value. Used in the SymbolNode to optimize\n     *                          for arguments from user assigned functions\n     *                          (see FunctionAssignmentNode) or special symbols\n     *                          like `end` (see IndexNode).\n     * @return {function} Returns a function which can be called like:\n     *                        evalNode(scope: Object, args: Object, context: *)\n     */\n\n\n    _compile(math, argNames) {\n      var evalBlocks = map(this.blocks, function (block) {\n        return {\n          evaluate: block.node._compile(math, argNames),\n          visible: block.visible\n        };\n      });\n      return function evalBlockNodes(scope, args, context) {\n        var results = [];\n        forEach(evalBlocks, function evalBlockNode(block) {\n          var result = block.evaluate(scope, args, context);\n\n          if (block.visible) {\n            results.push(result);\n          }\n        });\n        return new ResultSet(results);\n      };\n    }\n    /**\n     * Execute a callback for each of the child blocks of this node\n     * @param {function(child: Node, path: string, parent: Node)} callback\n     */\n\n\n    forEach(callback) {\n      for (var i = 0; i < this.blocks.length; i++) {\n        callback(this.blocks[i].node, 'blocks[' + i + '].node', this);\n      }\n    }\n    /**\n     * Create a new BlockNode whose children are the results of calling\n     * the provided callback function for each child of the original node.\n     * @param {function(child: Node, path: string, parent: Node): Node} callback\n     * @returns {BlockNode} Returns a transformed copy of the node\n     */\n\n\n    map(callback) {\n      var blocks = [];\n\n      for (var i = 0; i < this.blocks.length; i++) {\n        var block = this.blocks[i];\n\n        var node = this._ifNode(callback(block.node, 'blocks[' + i + '].node', this));\n\n        blocks[i] = {\n          node,\n          visible: block.visible\n        };\n      }\n\n      return new BlockNode(blocks);\n    }\n    /**\n     * Create a clone of this node, a shallow copy\n     * @return {BlockNode}\n     */\n\n\n    clone() {\n      var blocks = this.blocks.map(function (block) {\n        return {\n          node: block.node,\n          visible: block.visible\n        };\n      });\n      return new BlockNode(blocks);\n    }\n    /**\n     * Get string representation\n     * @param {Object} options\n     * @return {string} str\n     * @override\n     */\n\n\n    _toString(options) {\n      return this.blocks.map(function (param) {\n        return param.node.toString(options) + (param.visible ? '' : ';');\n      }).join('\\n');\n    }\n    /**\n     * Get a JSON representation of the node\n     * @returns {Object}\n     */\n\n\n    toJSON() {\n      return {\n        mathjs: name,\n        blocks: this.blocks\n      };\n    }\n    /**\n     * Instantiate an BlockNode from its JSON representation\n     * @param {Object} json\n     *     An object structured like\n     *     `{\"mathjs\": \"BlockNode\", blocks: [{node: ..., visible: false}, ...]}`,\n     *     where mathjs is optional\n     * @returns {BlockNode}\n     */\n\n\n    static fromJSON(json) {\n      return new BlockNode(json.blocks);\n    }\n    /**\n     * Get HTML representation\n     * @param {Object} options\n     * @return {string} str\n     * @override\n     */\n\n\n    _toHTML(options) {\n      return this.blocks.map(function (param) {\n        return param.node.toHTML(options) + (param.visible ? '' : '<span class=\"math-separator\">;</span>');\n      }).join('<span class=\"math-separator\"><br /></span>');\n    }\n    /**\n     * Get LaTeX representation\n     * @param {Object} options\n     * @return {string} str\n     */\n\n\n    _toTex(options) {\n      return this.blocks.map(function (param) {\n        return param.node.toTex(options) + (param.visible ? '' : ';');\n      }).join('\\\\;\\\\;\\n');\n    }\n\n  }\n\n  _defineProperty(BlockNode, \"name\", name);\n\n  return BlockNode;\n}, {\n  isClass: true,\n  isNode: true\n});", "map": null, "metadata": {}, "sourceType": "module"}