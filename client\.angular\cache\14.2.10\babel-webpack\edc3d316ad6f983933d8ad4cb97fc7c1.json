{"ast": null, "code": "import { factory } from '../../utils/factory.js';\nvar name = 'catalan';\nvar dependencies = ['typed', 'addScalar', 'divideScalar', 'multiplyScalar', 'combinations', 'isNegative', 'isInteger'];\nexport var createCatalan = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed,\n    addScalar,\n    divideScalar,\n    multiplyScalar,\n    combinations,\n    isNegative,\n    isInteger\n  } = _ref;\n  /**\n   * The Catalan Numbers enumerate combinatorial structures of many different types.\n   * catalan only takes integer arguments.\n   * The following condition must be enforced: n >= 0\n   *\n   * Syntax:\n   *\n   *   math.catalan(n)\n   *\n   * Examples:\n   *\n   *    math.catalan(3) // returns 5\n   *    math.catalan(8) // returns 1430\n   *\n   * See also:\n   *\n   *    bellNumbers\n   *\n   * @param {Number | BigNumber} n    nth Catalan number\n   * @return {Number | BigNumber}     Cn(n)\n   */\n\n  return typed(name, {\n    'number | BigNumber': function number__BigNumber(n) {\n      if (!isInteger(n) || isNegative(n)) {\n        throw new TypeError('Non-negative integer value expected in function catalan');\n      }\n\n      return divideScalar(combinations(multiplyScalar(n, 2), n), addScalar(n, 1));\n    }\n  });\n});", "map": null, "metadata": {}, "sourceType": "module"}