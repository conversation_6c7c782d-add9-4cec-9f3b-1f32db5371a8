{"ast": null, "code": "/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { DenseMatrixDependencies } from './dependenciesDenseMatrixClass.generated.js';\nimport { concatDependencies } from './dependenciesConcat.generated.js';\nimport { equalScalarDependencies } from './dependenciesEqualScalar.generated.js';\nimport { matrixDependencies } from './dependenciesMatrix.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createBitOrTransform } from '../../factoriesAny.js';\nexport var bitOrTransformDependencies = {\n  DenseMatrixDependencies,\n  concatDependencies,\n  equalScalarDependencies,\n  matrixDependencies,\n  typedDependencies,\n  createBitOrTransform\n};", "map": {"version": 3, "names": ["DenseMatrixDependencies", "concatDependencies", "equalScalarDependencies", "matrixDependencies", "typedDependencies", "createBitOrTransform", "bitOrTransformDependencies"], "sources": ["D:/work/joyserver/client/node_modules/mathjs/lib/esm/entry/dependenciesAny/dependenciesBitOrTransform.generated.js"], "sourcesContent": ["/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { DenseMatrixDependencies } from './dependenciesDenseMatrixClass.generated.js';\nimport { concatDependencies } from './dependenciesConcat.generated.js';\nimport { equalScalarDependencies } from './dependenciesEqualScalar.generated.js';\nimport { matrixDependencies } from './dependenciesMatrix.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createBitOrTransform } from '../../factoriesAny.js';\nexport var bitOrTransformDependencies = {\n  DenseMatrixDependencies,\n  concatDependencies,\n  equalScalarDependencies,\n  matrixDependencies,\n  typedDependencies,\n  createBitOrTransform\n};"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,uBAAT,QAAwC,6CAAxC;AACA,SAASC,kBAAT,QAAmC,mCAAnC;AACA,SAASC,uBAAT,QAAwC,wCAAxC;AACA,SAASC,kBAAT,QAAmC,mCAAnC;AACA,SAASC,iBAAT,QAAkC,kCAAlC;AACA,SAASC,oBAAT,QAAqC,uBAArC;AACA,OAAO,IAAIC,0BAA0B,GAAG;EACtCN,uBADsC;EAEtCC,kBAFsC;EAGtCC,uBAHsC;EAItCC,kBAJsC;EAKtCC,iBALsC;EAMtCC;AANsC,CAAjC"}, "metadata": {}, "sourceType": "module"}