{"ast": null, "code": "// Copyright (c) 2006-2024, <PERSON>, All Rights Reserved.\n// SPDX-License-Identifier: LGPL-2.1+\n// https://github.com/DrTimothyAldenDavis/SuiteSparse/tree/dev/CSparse/Source\nimport { csPermute } from './csPermute.js';\nimport { csPost } from './csPost.js';\nimport { csEtree } from './csEtree.js';\nimport { createCsAmd } from './csAmd.js';\nimport { createCsCounts } from './csCounts.js';\nimport { factory } from '../../../utils/factory.js';\nvar name = 'csSqr';\nvar dependencies = ['add', 'multiply', 'transpose'];\nexport var createCsSqr = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    add,\n    multiply,\n    transpose\n  } = _ref;\n  var csAmd = createCsAmd({\n    add,\n    multiply,\n    transpose\n  });\n  var csCounts = createCsCounts({\n    transpose\n  });\n  /**\n   * Symbolic ordering and analysis for QR and LU decompositions.\n   *\n   * @param {Number}  order           The ordering strategy (see csAmd for more details)\n   * @param {Matrix}  a               The A matrix\n   * @param {boolean} qr              Symbolic ordering and analysis for QR decomposition (true) or\n   *                                  symbolic ordering and analysis for LU decomposition (false)\n   *\n   * @return {Object}                 The Symbolic ordering and analysis for matrix A\n   */\n\n  return function csSqr(order, a, qr) {\n    // a arrays\n    var aptr = a._ptr;\n    var asize = a._size; // columns\n\n    var n = asize[1]; // vars\n\n    var k; // symbolic analysis result\n\n    var s = {}; // fill-reducing ordering\n\n    s.q = csAmd(order, a); // validate results\n\n    if (order && !s.q) {\n      return null;\n    } // QR symbolic analysis\n\n\n    if (qr) {\n      // apply permutations if needed\n      var c = order ? csPermute(a, null, s.q, 0) : a; // etree of C'*C, where C=A(:,q)\n\n      s.parent = csEtree(c, 1); // post order elimination tree\n\n      var post = csPost(s.parent, n); // col counts chol(C'*C)\n\n      s.cp = csCounts(c, s.parent, post, 1); // check we have everything needed to calculate number of nonzero elements\n\n      if (c && s.parent && s.cp && _vcount(c, s)) {\n        // calculate number of nonzero elements\n        for (s.unz = 0, k = 0; k < n; k++) {\n          s.unz += s.cp[k];\n        }\n      }\n    } else {\n      // for LU factorization only, guess nnz(L) and nnz(U)\n      s.unz = 4 * aptr[n] + n;\n      s.lnz = s.unz;\n    } // return result S\n\n\n    return s;\n  };\n  /**\n   * Compute nnz(V) = s.lnz, s.pinv, s.leftmost, s.m2 from A and s.parent\n   */\n\n  function _vcount(a, s) {\n    // a arrays\n    var aptr = a._ptr;\n    var aindex = a._index;\n    var asize = a._size; // rows & columns\n\n    var m = asize[0];\n    var n = asize[1]; // initialize s arrays\n\n    s.pinv = []; // (m + n)\n\n    s.leftmost = []; // (m)\n    // vars\n\n    var parent = s.parent;\n    var pinv = s.pinv;\n    var leftmost = s.leftmost; // workspace, next: first m entries, head: next n entries, tail: next n entries, nque: next n entries\n\n    var w = []; // (m + 3 * n)\n\n    var next = 0;\n    var head = m;\n    var tail = m + n;\n    var nque = m + 2 * n; // vars\n\n    var i, k, p, p0, p1; // initialize w\n\n    for (k = 0; k < n; k++) {\n      // queue k is empty\n      w[head + k] = -1;\n      w[tail + k] = -1;\n      w[nque + k] = 0;\n    } // initialize row arrays\n\n\n    for (i = 0; i < m; i++) {\n      leftmost[i] = -1;\n    } // loop columns backwards\n\n\n    for (k = n - 1; k >= 0; k--) {\n      // values & index for column k\n      for (p0 = aptr[k], p1 = aptr[k + 1], p = p0; p < p1; p++) {\n        // leftmost[i] = min(find(A(i,:)))\n        leftmost[aindex[p]] = k;\n      }\n    } // scan rows in reverse order\n\n\n    for (i = m - 1; i >= 0; i--) {\n      // row i is not yet ordered\n      pinv[i] = -1;\n      k = leftmost[i]; // check row i is empty\n\n      if (k === -1) {\n        continue;\n      } // first row in queue k\n\n\n      if (w[nque + k]++ === 0) {\n        w[tail + k] = i;\n      } // put i at head of queue k\n\n\n      w[next + i] = w[head + k];\n      w[head + k] = i;\n    }\n\n    s.lnz = 0;\n    s.m2 = m; // find row permutation and nnz(V)\n\n    for (k = 0; k < n; k++) {\n      // remove row i from queue k\n      i = w[head + k]; // count V(k,k) as nonzero\n\n      s.lnz++; // add a fictitious row\n\n      if (i < 0) {\n        i = s.m2++;\n      } // associate row i with V(:,k)\n\n\n      pinv[i] = k; // skip if V(k+1:m,k) is empty\n\n      if (--nque[k] <= 0) {\n        continue;\n      } // nque[k] is nnz (V(k+1:m,k))\n\n\n      s.lnz += w[nque + k]; // move all rows to parent of k\n\n      var pa = parent[k];\n\n      if (pa !== -1) {\n        if (w[nque + pa] === 0) {\n          w[tail + pa] = w[tail + k];\n        }\n\n        w[next + w[tail + k]] = w[head + pa];\n        w[head + pa] = w[next + i];\n        w[nque + pa] += w[nque + k];\n      }\n    }\n\n    for (i = 0; i < m; i++) {\n      if (pinv[i] < 0) {\n        pinv[i] = k++;\n      }\n    }\n\n    return true;\n  }\n});", "map": {"version": 3, "names": ["csPermute", "csPost", "csEtree", "createCsAmd", "createCsCounts", "factory", "name", "dependencies", "createCsSqr", "_ref", "add", "multiply", "transpose", "csAmd", "csCounts", "csSqr", "order", "a", "qr", "aptr", "_ptr", "asize", "_size", "n", "k", "s", "q", "c", "parent", "post", "cp", "_vcount", "unz", "lnz", "aindex", "_index", "m", "pinv", "leftmost", "w", "next", "head", "tail", "nque", "i", "p", "p0", "p1", "m2", "pa"], "sources": ["D:/work/joyserver/client/node_modules/mathjs/lib/esm/function/algebra/sparse/csSqr.js"], "sourcesContent": ["// Copyright (c) 2006-2024, <PERSON>, All Rights Reserved.\n// SPDX-License-Identifier: LGPL-2.1+\n// https://github.com/DrTimothyAldenDavis/SuiteSparse/tree/dev/CSparse/Source\nimport { csPermute } from './csPermute.js';\nimport { csPost } from './csPost.js';\nimport { csEtree } from './csEtree.js';\nimport { createCsAmd } from './csAmd.js';\nimport { createCsCounts } from './csCounts.js';\nimport { factory } from '../../../utils/factory.js';\nvar name = 'csSqr';\nvar dependencies = ['add', 'multiply', 'transpose'];\nexport var createCsSqr = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    add,\n    multiply,\n    transpose\n  } = _ref;\n  var csAmd = createCsAmd({\n    add,\n    multiply,\n    transpose\n  });\n  var csCounts = createCsCounts({\n    transpose\n  });\n\n  /**\n   * Symbolic ordering and analysis for QR and LU decompositions.\n   *\n   * @param {Number}  order           The ordering strategy (see csAmd for more details)\n   * @param {Matrix}  a               The A matrix\n   * @param {boolean} qr              Symbolic ordering and analysis for QR decomposition (true) or\n   *                                  symbolic ordering and analysis for LU decomposition (false)\n   *\n   * @return {Object}                 The Symbolic ordering and analysis for matrix A\n   */\n  return function csSqr(order, a, qr) {\n    // a arrays\n    var aptr = a._ptr;\n    var asize = a._size;\n    // columns\n    var n = asize[1];\n    // vars\n    var k;\n    // symbolic analysis result\n    var s = {};\n    // fill-reducing ordering\n    s.q = csAmd(order, a);\n    // validate results\n    if (order && !s.q) {\n      return null;\n    }\n    // QR symbolic analysis\n    if (qr) {\n      // apply permutations if needed\n      var c = order ? csPermute(a, null, s.q, 0) : a;\n      // etree of C'*C, where C=A(:,q)\n      s.parent = csEtree(c, 1);\n      // post order elimination tree\n      var post = csPost(s.parent, n);\n      // col counts chol(C'*C)\n      s.cp = csCounts(c, s.parent, post, 1);\n      // check we have everything needed to calculate number of nonzero elements\n      if (c && s.parent && s.cp && _vcount(c, s)) {\n        // calculate number of nonzero elements\n        for (s.unz = 0, k = 0; k < n; k++) {\n          s.unz += s.cp[k];\n        }\n      }\n    } else {\n      // for LU factorization only, guess nnz(L) and nnz(U)\n      s.unz = 4 * aptr[n] + n;\n      s.lnz = s.unz;\n    }\n    // return result S\n    return s;\n  };\n\n  /**\n   * Compute nnz(V) = s.lnz, s.pinv, s.leftmost, s.m2 from A and s.parent\n   */\n  function _vcount(a, s) {\n    // a arrays\n    var aptr = a._ptr;\n    var aindex = a._index;\n    var asize = a._size;\n    // rows & columns\n    var m = asize[0];\n    var n = asize[1];\n    // initialize s arrays\n    s.pinv = []; // (m + n)\n    s.leftmost = []; // (m)\n    // vars\n    var parent = s.parent;\n    var pinv = s.pinv;\n    var leftmost = s.leftmost;\n    // workspace, next: first m entries, head: next n entries, tail: next n entries, nque: next n entries\n    var w = []; // (m + 3 * n)\n    var next = 0;\n    var head = m;\n    var tail = m + n;\n    var nque = m + 2 * n;\n    // vars\n    var i, k, p, p0, p1;\n    // initialize w\n    for (k = 0; k < n; k++) {\n      // queue k is empty\n      w[head + k] = -1;\n      w[tail + k] = -1;\n      w[nque + k] = 0;\n    }\n    // initialize row arrays\n    for (i = 0; i < m; i++) {\n      leftmost[i] = -1;\n    }\n    // loop columns backwards\n    for (k = n - 1; k >= 0; k--) {\n      // values & index for column k\n      for (p0 = aptr[k], p1 = aptr[k + 1], p = p0; p < p1; p++) {\n        // leftmost[i] = min(find(A(i,:)))\n        leftmost[aindex[p]] = k;\n      }\n    }\n    // scan rows in reverse order\n    for (i = m - 1; i >= 0; i--) {\n      // row i is not yet ordered\n      pinv[i] = -1;\n      k = leftmost[i];\n      // check row i is empty\n      if (k === -1) {\n        continue;\n      }\n      // first row in queue k\n      if (w[nque + k]++ === 0) {\n        w[tail + k] = i;\n      }\n      // put i at head of queue k\n      w[next + i] = w[head + k];\n      w[head + k] = i;\n    }\n    s.lnz = 0;\n    s.m2 = m;\n    // find row permutation and nnz(V)\n    for (k = 0; k < n; k++) {\n      // remove row i from queue k\n      i = w[head + k];\n      // count V(k,k) as nonzero\n      s.lnz++;\n      // add a fictitious row\n      if (i < 0) {\n        i = s.m2++;\n      }\n      // associate row i with V(:,k)\n      pinv[i] = k;\n      // skip if V(k+1:m,k) is empty\n      if (--nque[k] <= 0) {\n        continue;\n      }\n      // nque[k] is nnz (V(k+1:m,k))\n      s.lnz += w[nque + k];\n      // move all rows to parent of k\n      var pa = parent[k];\n      if (pa !== -1) {\n        if (w[nque + pa] === 0) {\n          w[tail + pa] = w[tail + k];\n        }\n        w[next + w[tail + k]] = w[head + pa];\n        w[head + pa] = w[next + i];\n        w[nque + pa] += w[nque + k];\n      }\n    }\n    for (i = 0; i < m; i++) {\n      if (pinv[i] < 0) {\n        pinv[i] = k++;\n      }\n    }\n    return true;\n  }\n});"], "mappings": "AAAA;AACA;AACA;AACA,SAASA,SAAT,QAA0B,gBAA1B;AACA,SAASC,MAAT,QAAuB,aAAvB;AACA,SAASC,OAAT,QAAwB,cAAxB;AACA,SAASC,WAAT,QAA4B,YAA5B;AACA,SAASC,cAAT,QAA+B,eAA/B;AACA,SAASC,OAAT,QAAwB,2BAAxB;AACA,IAAIC,IAAI,GAAG,OAAX;AACA,IAAIC,YAAY,GAAG,CAAC,KAAD,EAAQ,UAAR,EAAoB,WAApB,CAAnB;AACA,OAAO,IAAIC,WAAW,GAAG,eAAeH,OAAO,CAACC,IAAD,EAAOC,YAAP,EAAqBE,IAAI,IAAI;EAC1E,IAAI;IACFC,GADE;IAEFC,QAFE;IAGFC;EAHE,IAIAH,IAJJ;EAKA,IAAII,KAAK,GAAGV,WAAW,CAAC;IACtBO,GADsB;IAEtBC,QAFsB;IAGtBC;EAHsB,CAAD,CAAvB;EAKA,IAAIE,QAAQ,GAAGV,cAAc,CAAC;IAC5BQ;EAD4B,CAAD,CAA7B;EAIA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EACE,OAAO,SAASG,KAAT,CAAeC,KAAf,EAAsBC,CAAtB,EAAyBC,EAAzB,EAA6B;IAClC;IACA,IAAIC,IAAI,GAAGF,CAAC,CAACG,IAAb;IACA,IAAIC,KAAK,GAAGJ,CAAC,CAACK,KAAd,CAHkC,CAIlC;;IACA,IAAIC,CAAC,GAAGF,KAAK,CAAC,CAAD,CAAb,CALkC,CAMlC;;IACA,IAAIG,CAAJ,CAPkC,CAQlC;;IACA,IAAIC,CAAC,GAAG,EAAR,CATkC,CAUlC;;IACAA,CAAC,CAACC,CAAF,GAAMb,KAAK,CAACG,KAAD,EAAQC,CAAR,CAAX,CAXkC,CAYlC;;IACA,IAAID,KAAK,IAAI,CAACS,CAAC,CAACC,CAAhB,EAAmB;MACjB,OAAO,IAAP;IACD,CAfiC,CAgBlC;;;IACA,IAAIR,EAAJ,EAAQ;MACN;MACA,IAAIS,CAAC,GAAGX,KAAK,GAAGhB,SAAS,CAACiB,CAAD,EAAI,IAAJ,EAAUQ,CAAC,CAACC,CAAZ,EAAe,CAAf,CAAZ,GAAgCT,CAA7C,CAFM,CAGN;;MACAQ,CAAC,CAACG,MAAF,GAAW1B,OAAO,CAACyB,CAAD,EAAI,CAAJ,CAAlB,CAJM,CAKN;;MACA,IAAIE,IAAI,GAAG5B,MAAM,CAACwB,CAAC,CAACG,MAAH,EAAWL,CAAX,CAAjB,CANM,CAON;;MACAE,CAAC,CAACK,EAAF,GAAOhB,QAAQ,CAACa,CAAD,EAAIF,CAAC,CAACG,MAAN,EAAcC,IAAd,EAAoB,CAApB,CAAf,CARM,CASN;;MACA,IAAIF,CAAC,IAAIF,CAAC,CAACG,MAAP,IAAiBH,CAAC,CAACK,EAAnB,IAAyBC,OAAO,CAACJ,CAAD,EAAIF,CAAJ,CAApC,EAA4C;QAC1C;QACA,KAAKA,CAAC,CAACO,GAAF,GAAQ,CAAR,EAAWR,CAAC,GAAG,CAApB,EAAuBA,CAAC,GAAGD,CAA3B,EAA8BC,CAAC,EAA/B,EAAmC;UACjCC,CAAC,CAACO,GAAF,IAASP,CAAC,CAACK,EAAF,CAAKN,CAAL,CAAT;QACD;MACF;IACF,CAhBD,MAgBO;MACL;MACAC,CAAC,CAACO,GAAF,GAAQ,IAAIb,IAAI,CAACI,CAAD,CAAR,GAAcA,CAAtB;MACAE,CAAC,CAACQ,GAAF,GAAQR,CAAC,CAACO,GAAV;IACD,CArCiC,CAsClC;;;IACA,OAAOP,CAAP;EACD,CAxCD;EA0CA;AACF;AACA;;EACE,SAASM,OAAT,CAAiBd,CAAjB,EAAoBQ,CAApB,EAAuB;IACrB;IACA,IAAIN,IAAI,GAAGF,CAAC,CAACG,IAAb;IACA,IAAIc,MAAM,GAAGjB,CAAC,CAACkB,MAAf;IACA,IAAId,KAAK,GAAGJ,CAAC,CAACK,KAAd,CAJqB,CAKrB;;IACA,IAAIc,CAAC,GAAGf,KAAK,CAAC,CAAD,CAAb;IACA,IAAIE,CAAC,GAAGF,KAAK,CAAC,CAAD,CAAb,CAPqB,CAQrB;;IACAI,CAAC,CAACY,IAAF,GAAS,EAAT,CATqB,CASR;;IACbZ,CAAC,CAACa,QAAF,GAAa,EAAb,CAVqB,CAUJ;IACjB;;IACA,IAAIV,MAAM,GAAGH,CAAC,CAACG,MAAf;IACA,IAAIS,IAAI,GAAGZ,CAAC,CAACY,IAAb;IACA,IAAIC,QAAQ,GAAGb,CAAC,CAACa,QAAjB,CAdqB,CAerB;;IACA,IAAIC,CAAC,GAAG,EAAR,CAhBqB,CAgBT;;IACZ,IAAIC,IAAI,GAAG,CAAX;IACA,IAAIC,IAAI,GAAGL,CAAX;IACA,IAAIM,IAAI,GAAGN,CAAC,GAAGb,CAAf;IACA,IAAIoB,IAAI,GAAGP,CAAC,GAAG,IAAIb,CAAnB,CApBqB,CAqBrB;;IACA,IAAIqB,CAAJ,EAAOpB,CAAP,EAAUqB,CAAV,EAAaC,EAAb,EAAiBC,EAAjB,CAtBqB,CAuBrB;;IACA,KAAKvB,CAAC,GAAG,CAAT,EAAYA,CAAC,GAAGD,CAAhB,EAAmBC,CAAC,EAApB,EAAwB;MACtB;MACAe,CAAC,CAACE,IAAI,GAAGjB,CAAR,CAAD,GAAc,CAAC,CAAf;MACAe,CAAC,CAACG,IAAI,GAAGlB,CAAR,CAAD,GAAc,CAAC,CAAf;MACAe,CAAC,CAACI,IAAI,GAAGnB,CAAR,CAAD,GAAc,CAAd;IACD,CA7BoB,CA8BrB;;;IACA,KAAKoB,CAAC,GAAG,CAAT,EAAYA,CAAC,GAAGR,CAAhB,EAAmBQ,CAAC,EAApB,EAAwB;MACtBN,QAAQ,CAACM,CAAD,CAAR,GAAc,CAAC,CAAf;IACD,CAjCoB,CAkCrB;;;IACA,KAAKpB,CAAC,GAAGD,CAAC,GAAG,CAAb,EAAgBC,CAAC,IAAI,CAArB,EAAwBA,CAAC,EAAzB,EAA6B;MAC3B;MACA,KAAKsB,EAAE,GAAG3B,IAAI,CAACK,CAAD,CAAT,EAAcuB,EAAE,GAAG5B,IAAI,CAACK,CAAC,GAAG,CAAL,CAAvB,EAAgCqB,CAAC,GAAGC,EAAzC,EAA6CD,CAAC,GAAGE,EAAjD,EAAqDF,CAAC,EAAtD,EAA0D;QACxD;QACAP,QAAQ,CAACJ,MAAM,CAACW,CAAD,CAAP,CAAR,GAAsBrB,CAAtB;MACD;IACF,CAzCoB,CA0CrB;;;IACA,KAAKoB,CAAC,GAAGR,CAAC,GAAG,CAAb,EAAgBQ,CAAC,IAAI,CAArB,EAAwBA,CAAC,EAAzB,EAA6B;MAC3B;MACAP,IAAI,CAACO,CAAD,CAAJ,GAAU,CAAC,CAAX;MACApB,CAAC,GAAGc,QAAQ,CAACM,CAAD,CAAZ,CAH2B,CAI3B;;MACA,IAAIpB,CAAC,KAAK,CAAC,CAAX,EAAc;QACZ;MACD,CAP0B,CAQ3B;;;MACA,IAAIe,CAAC,CAACI,IAAI,GAAGnB,CAAR,CAAD,OAAkB,CAAtB,EAAyB;QACvBe,CAAC,CAACG,IAAI,GAAGlB,CAAR,CAAD,GAAcoB,CAAd;MACD,CAX0B,CAY3B;;;MACAL,CAAC,CAACC,IAAI,GAAGI,CAAR,CAAD,GAAcL,CAAC,CAACE,IAAI,GAAGjB,CAAR,CAAf;MACAe,CAAC,CAACE,IAAI,GAAGjB,CAAR,CAAD,GAAcoB,CAAd;IACD;;IACDnB,CAAC,CAACQ,GAAF,GAAQ,CAAR;IACAR,CAAC,CAACuB,EAAF,GAAOZ,CAAP,CA5DqB,CA6DrB;;IACA,KAAKZ,CAAC,GAAG,CAAT,EAAYA,CAAC,GAAGD,CAAhB,EAAmBC,CAAC,EAApB,EAAwB;MACtB;MACAoB,CAAC,GAAGL,CAAC,CAACE,IAAI,GAAGjB,CAAR,CAAL,CAFsB,CAGtB;;MACAC,CAAC,CAACQ,GAAF,GAJsB,CAKtB;;MACA,IAAIW,CAAC,GAAG,CAAR,EAAW;QACTA,CAAC,GAAGnB,CAAC,CAACuB,EAAF,EAAJ;MACD,CARqB,CAStB;;;MACAX,IAAI,CAACO,CAAD,CAAJ,GAAUpB,CAAV,CAVsB,CAWtB;;MACA,IAAI,EAAEmB,IAAI,CAACnB,CAAD,CAAN,IAAa,CAAjB,EAAoB;QAClB;MACD,CAdqB,CAetB;;;MACAC,CAAC,CAACQ,GAAF,IAASM,CAAC,CAACI,IAAI,GAAGnB,CAAR,CAAV,CAhBsB,CAiBtB;;MACA,IAAIyB,EAAE,GAAGrB,MAAM,CAACJ,CAAD,CAAf;;MACA,IAAIyB,EAAE,KAAK,CAAC,CAAZ,EAAe;QACb,IAAIV,CAAC,CAACI,IAAI,GAAGM,EAAR,CAAD,KAAiB,CAArB,EAAwB;UACtBV,CAAC,CAACG,IAAI,GAAGO,EAAR,CAAD,GAAeV,CAAC,CAACG,IAAI,GAAGlB,CAAR,CAAhB;QACD;;QACDe,CAAC,CAACC,IAAI,GAAGD,CAAC,CAACG,IAAI,GAAGlB,CAAR,CAAT,CAAD,GAAwBe,CAAC,CAACE,IAAI,GAAGQ,EAAR,CAAzB;QACAV,CAAC,CAACE,IAAI,GAAGQ,EAAR,CAAD,GAAeV,CAAC,CAACC,IAAI,GAAGI,CAAR,CAAhB;QACAL,CAAC,CAACI,IAAI,GAAGM,EAAR,CAAD,IAAgBV,CAAC,CAACI,IAAI,GAAGnB,CAAR,CAAjB;MACD;IACF;;IACD,KAAKoB,CAAC,GAAG,CAAT,EAAYA,CAAC,GAAGR,CAAhB,EAAmBQ,CAAC,EAApB,EAAwB;MACtB,IAAIP,IAAI,CAACO,CAAD,CAAJ,GAAU,CAAd,EAAiB;QACfP,IAAI,CAACO,CAAD,CAAJ,GAAUpB,CAAC,EAAX;MACD;IACF;;IACD,OAAO,IAAP;EACD;AACF,CAvK8C,CAAxC"}, "metadata": {}, "sourceType": "module"}