{"ast": null, "code": "import { log10Number } from '../../plain/number/index.js';\nimport { promoteLogarithm } from '../../utils/bigint.js';\nimport { deepMap } from '../../utils/collection.js';\nimport { factory } from '../../utils/factory.js';\nvar name = 'log10';\nvar dependencies = ['typed', 'config', 'Complex'];\nvar log16 = log10Number(16);\nexport var createLog10 = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed,\n    config,\n    Complex\n  } = _ref;\n  /**\n   * Calculate the 10-base logarithm of a value. This is the same as calculating `log(x, 10)`.\n   *\n   * For matrices, the function is evaluated element wise.\n   *\n   * Syntax:\n   *\n   *    math.log10(x)\n   *\n   * Examples:\n   *\n   *    math.log10(0.00001)            // returns -5\n   *    math.log10(10000)              // returns 4\n   *    math.log(10000) / math.log(10) // returns 4\n   *    math.pow(10, 4)                // returns 10000\n   *\n   * See also:\n   *\n   *    exp, log, log1p, log2\n   *\n   * @param {number | BigNumber | Complex | Array | Matrix} x\n   *            Value for which to calculate the logarithm.\n   * @return {number | BigNumber | Complex | Array | Matrix}\n   *            Returns the 10-base logarithm of `x`\n   */\n\n  function complexLog(c) {\n    return c.log().div(Math.LN10);\n  }\n\n  function complexLogNumber(x) {\n    return complexLog(new Complex(x, 0));\n  }\n\n  return typed(name, {\n    number: function number(x) {\n      if (x >= 0 || config.predictable) {\n        return log10Number(x);\n      } else {\n        // negative value -> complex value computation\n        return complexLogNumber(x);\n      }\n    },\n    bigint: promoteLogarithm(log16, log10Number, config, complexLogNumber),\n    Complex: complexLog,\n    BigNumber: function BigNumber(x) {\n      if (!x.isNegative() || config.predictable) {\n        return x.log();\n      } else {\n        // downgrade to number, return Complex valued result\n        return complexLogNumber(x.toNumber());\n      }\n    },\n    'Array | Matrix': typed.referToSelf(self => x => deepMap(x, self))\n  });\n});", "map": null, "metadata": {}, "sourceType": "module"}