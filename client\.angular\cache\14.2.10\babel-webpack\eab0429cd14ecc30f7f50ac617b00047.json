{"ast": null, "code": "/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { BigNumberDependencies } from './dependenciesBigNumberClass.generated.js';\nimport { UnitDependencies } from './dependenciesUnitClass.generated.js';\nimport { createAvogadro } from '../../factoriesAny.js';\nexport var avogadroDependencies = {\n  BigNumberDependencies,\n  UnitDependencies,\n  createAvogadro\n};", "map": {"version": 3, "names": ["BigNumberDependencies", "UnitDependencies", "createAvogadro", "avogadroDependencies"], "sources": ["D:/work/joyserver/client/node_modules/mathjs/lib/esm/entry/dependenciesAny/dependenciesAvogadro.generated.js"], "sourcesContent": ["/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { BigNumberDependencies } from './dependenciesBigNumberClass.generated.js';\nimport { UnitDependencies } from './dependenciesUnitClass.generated.js';\nimport { createAvogadro } from '../../factoriesAny.js';\nexport var avogadroDependencies = {\n  BigNumberDependencies,\n  UnitDependencies,\n  createAvogadro\n};"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,qBAAT,QAAsC,2CAAtC;AACA,SAASC,gBAAT,QAAiC,sCAAjC;AACA,SAASC,cAAT,QAA+B,uBAA/B;AACA,OAAO,IAAIC,oBAAoB,GAAG;EAChCH,qBADgC;EAEhCC,gBAFgC;EAGhCC;AAHgC,CAA3B"}, "metadata": {}, "sourceType": "module"}