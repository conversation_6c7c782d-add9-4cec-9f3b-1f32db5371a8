{"ast": null, "code": "import { errorTransform } from '../../transform/utils/errorTransform.js';\nimport { getSafeProperty } from '../../../utils/customs.js';\nexport function accessFactory(_ref) {\n  var {\n    subset\n  } = _ref;\n  /**\n   * Retrieve part of an object:\n   *\n   * - Retrieve a property from an object\n   * - Retrieve a part of a string\n   * - Retrieve a matrix subset\n   *\n   * @param {Object | Array | Matrix | string} object\n   * @param {Index} index\n   * @return {Object | Array | Matrix | string} Returns the subset\n   */\n\n  return function access(object, index) {\n    try {\n      if (Array.isArray(object)) {\n        return subset(object, index);\n      } else if (object && typeof object.subset === 'function') {\n        // Matrix\n        return object.subset(index);\n      } else if (typeof object === 'string') {\n        // TODO: move getStringSubset into a separate util file, use that\n        return subset(object, index);\n      } else if (typeof object === 'object') {\n        if (!index.isObjectProperty()) {\n          throw new TypeError('Cannot apply a numeric index as object property');\n        }\n\n        return getSafeProperty(object, index.getObjectProperty());\n      } else {\n        throw new TypeError('Cannot apply index: unsupported type of object');\n      }\n    } catch (err) {\n      throw errorTransform(err);\n    }\n  };\n}", "map": {"version": 3, "names": ["errorTransform", "getSafeProperty", "accessFactory", "_ref", "subset", "access", "object", "index", "Array", "isArray", "isObjectProperty", "TypeError", "getObjectProperty", "err"], "sources": ["D:/work/joyserver/client/node_modules/mathjs/lib/esm/expression/node/utils/access.js"], "sourcesContent": ["import { errorTransform } from '../../transform/utils/errorTransform.js';\nimport { getSafeProperty } from '../../../utils/customs.js';\nexport function accessFactory(_ref) {\n  var {\n    subset\n  } = _ref;\n  /**\n   * Retrieve part of an object:\n   *\n   * - Retrieve a property from an object\n   * - Retrieve a part of a string\n   * - Retrieve a matrix subset\n   *\n   * @param {Object | Array | Matrix | string} object\n   * @param {Index} index\n   * @return {Object | Array | Matrix | string} Returns the subset\n   */\n  return function access(object, index) {\n    try {\n      if (Array.isArray(object)) {\n        return subset(object, index);\n      } else if (object && typeof object.subset === 'function') {\n        // Matrix\n        return object.subset(index);\n      } else if (typeof object === 'string') {\n        // TODO: move getStringSubset into a separate util file, use that\n        return subset(object, index);\n      } else if (typeof object === 'object') {\n        if (!index.isObjectProperty()) {\n          throw new TypeError('Cannot apply a numeric index as object property');\n        }\n        return getSafeProperty(object, index.getObjectProperty());\n      } else {\n        throw new TypeError('Cannot apply index: unsupported type of object');\n      }\n    } catch (err) {\n      throw errorTransform(err);\n    }\n  };\n}"], "mappings": "AAAA,SAASA,cAAT,QAA+B,yCAA/B;AACA,SAASC,eAAT,QAAgC,2BAAhC;AACA,OAAO,SAASC,aAAT,CAAuBC,IAAvB,EAA6B;EAClC,IAAI;IACFC;EADE,IAEAD,IAFJ;EAGA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EACE,OAAO,SAASE,MAAT,CAAgBC,MAAhB,EAAwBC,KAAxB,EAA+B;IACpC,IAAI;MACF,IAAIC,KAAK,CAACC,OAAN,CAAcH,MAAd,CAAJ,EAA2B;QACzB,OAAOF,MAAM,CAACE,MAAD,EAASC,KAAT,CAAb;MACD,CAFD,MAEO,IAAID,MAAM,IAAI,OAAOA,MAAM,CAACF,MAAd,KAAyB,UAAvC,EAAmD;QACxD;QACA,OAAOE,MAAM,CAACF,MAAP,CAAcG,KAAd,CAAP;MACD,CAHM,MAGA,IAAI,OAAOD,MAAP,KAAkB,QAAtB,EAAgC;QACrC;QACA,OAAOF,MAAM,CAACE,MAAD,EAASC,KAAT,CAAb;MACD,CAHM,MAGA,IAAI,OAAOD,MAAP,KAAkB,QAAtB,EAAgC;QACrC,IAAI,CAACC,KAAK,CAACG,gBAAN,EAAL,EAA+B;UAC7B,MAAM,IAAIC,SAAJ,CAAc,iDAAd,CAAN;QACD;;QACD,OAAOV,eAAe,CAACK,MAAD,EAASC,KAAK,CAACK,iBAAN,EAAT,CAAtB;MACD,CALM,MAKA;QACL,MAAM,IAAID,SAAJ,CAAc,gDAAd,CAAN;MACD;IACF,CAjBD,CAiBE,OAAOE,GAAP,EAAY;MACZ,MAAMb,cAAc,CAACa,GAAD,CAApB;IACD;EACF,CArBD;AAsBD"}, "metadata": {}, "sourceType": "module"}