{"ast": null, "code": "import { containsCollections, deepForEach, reduce } from '../../utils/collection.js';\nimport { factory } from '../../utils/factory.js';\nimport { safeNumberType } from '../../utils/number.js';\nimport { improveErrorMessage } from './utils/improveErrorMessage.js';\nvar name = 'min';\nvar dependencies = ['typed', 'config', 'numeric', 'smaller'];\nexport var createMin = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed,\n    config,\n    numeric,\n    smaller\n  } = _ref;\n  /**\n   * Compute the minimum value of a matrix or a  list of values.\n   * In case of a multidimensional array, the minimum of the flattened array\n   * will be calculated. When `dim` is provided, the minimum over the selected\n   * dimension will be calculated. Parameter `dim` is zero-based.\n   *\n   * Syntax:\n   *\n   *     math.min(a, b, c, ...)\n   *     math.min(A)\n   *     math.min(A, dimension)\n   *\n   * Examples:\n   *\n   *     math.min(2, 1, 4, 3)                  // returns 1\n   *     math.min([2, 1, 4, 3])                // returns 1\n   *\n   *     // minimum over a specified dimension (zero-based)\n   *     math.min([[2, 5], [4, 3], [1, 7]], 0) // returns [1, 3]\n   *     math.min([[2, 5], [4, 3], [1, 7]], 1) // returns [2, 3, 1]\n   *\n   *     math.max(2.7, 7.1, -4.5, 2.0, 4.1)    // returns 7.1\n   *     math.min(2.7, 7.1, -4.5, 2.0, 4.1)    // returns -4.5\n   *\n   * See also:\n   *\n   *    mean, median, max, prod, std, sum, variance\n   *\n   * @param {... *} args  A single matrix or or multiple scalar values\n   * @return {*} The minimum value\n   */\n\n  return typed(name, {\n    // min([a, b, c, d, ...])\n    'Array | Matrix': _min,\n    // min([a, b, c, d, ...], dim)\n    'Array | Matrix, number | BigNumber': function Array__Matrix_number__BigNumber(array, dim) {\n      return reduce(array, dim.valueOf(), _smallest);\n    },\n    // min(a, b, c, d, ...)\n    '...': function _(args) {\n      if (containsCollections(args)) {\n        throw new TypeError('Scalar values expected in function min');\n      }\n\n      return _min(args);\n    }\n  });\n  /**\n   * Return the smallest of two values\n   * @param {*} x\n   * @param {*} y\n   * @returns {*} Returns x when x is smallest, or y when y is smallest\n   * @private\n   */\n\n  function _smallest(x, y) {\n    try {\n      return smaller(x, y) ? x : y;\n    } catch (err) {\n      throw improveErrorMessage(err, 'min', y);\n    }\n  }\n  /**\n   * Recursively calculate the minimum value in an n-dimensional array\n   * @param {Array} array\n   * @return {number} min\n   * @private\n   */\n\n\n  function _min(array) {\n    var min;\n    deepForEach(array, function (value) {\n      try {\n        if (typeof value === 'number' && isNaN(value)) {\n          min = NaN;\n        } else if (min === undefined || smaller(value, min)) {\n          min = value;\n        }\n      } catch (err) {\n        throw improveErrorMessage(err, 'min', value);\n      }\n    });\n\n    if (min === undefined) {\n      throw new Error('Cannot calculate min of an empty array');\n    } // make sure returning numeric value: parse a string into a numeric value\n\n\n    if (typeof min === 'string') {\n      min = numeric(min, safeNumberType(min, config));\n    }\n\n    return min;\n  }\n});", "map": {"version": 3, "names": ["containsCollections", "deepForEach", "reduce", "factory", "safeNumberType", "improveErrorMessage", "name", "dependencies", "createMin", "_ref", "typed", "config", "numeric", "smaller", "_min", "Array__Matrix_number__BigNumber", "array", "dim", "valueOf", "_smallest", "_", "args", "TypeError", "x", "y", "err", "min", "value", "isNaN", "NaN", "undefined", "Error"], "sources": ["D:/work/joyserver/client/node_modules/mathjs/lib/esm/function/statistics/min.js"], "sourcesContent": ["import { containsCollections, deepForEach, reduce } from '../../utils/collection.js';\nimport { factory } from '../../utils/factory.js';\nimport { safeNumberType } from '../../utils/number.js';\nimport { improveErrorMessage } from './utils/improveErrorMessage.js';\nvar name = 'min';\nvar dependencies = ['typed', 'config', 'numeric', 'smaller'];\nexport var createMin = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed,\n    config,\n    numeric,\n    smaller\n  } = _ref;\n  /**\n   * Compute the minimum value of a matrix or a  list of values.\n   * In case of a multidimensional array, the minimum of the flattened array\n   * will be calculated. When `dim` is provided, the minimum over the selected\n   * dimension will be calculated. Parameter `dim` is zero-based.\n   *\n   * Syntax:\n   *\n   *     math.min(a, b, c, ...)\n   *     math.min(A)\n   *     math.min(A, dimension)\n   *\n   * Examples:\n   *\n   *     math.min(2, 1, 4, 3)                  // returns 1\n   *     math.min([2, 1, 4, 3])                // returns 1\n   *\n   *     // minimum over a specified dimension (zero-based)\n   *     math.min([[2, 5], [4, 3], [1, 7]], 0) // returns [1, 3]\n   *     math.min([[2, 5], [4, 3], [1, 7]], 1) // returns [2, 3, 1]\n   *\n   *     math.max(2.7, 7.1, -4.5, 2.0, 4.1)    // returns 7.1\n   *     math.min(2.7, 7.1, -4.5, 2.0, 4.1)    // returns -4.5\n   *\n   * See also:\n   *\n   *    mean, median, max, prod, std, sum, variance\n   *\n   * @param {... *} args  A single matrix or or multiple scalar values\n   * @return {*} The minimum value\n   */\n  return typed(name, {\n    // min([a, b, c, d, ...])\n    'Array | Matrix': _min,\n    // min([a, b, c, d, ...], dim)\n    'Array | Matrix, number | BigNumber': function Array__Matrix_number__BigNumber(array, dim) {\n      return reduce(array, dim.valueOf(), _smallest);\n    },\n    // min(a, b, c, d, ...)\n    '...': function _(args) {\n      if (containsCollections(args)) {\n        throw new TypeError('Scalar values expected in function min');\n      }\n      return _min(args);\n    }\n  });\n\n  /**\n   * Return the smallest of two values\n   * @param {*} x\n   * @param {*} y\n   * @returns {*} Returns x when x is smallest, or y when y is smallest\n   * @private\n   */\n  function _smallest(x, y) {\n    try {\n      return smaller(x, y) ? x : y;\n    } catch (err) {\n      throw improveErrorMessage(err, 'min', y);\n    }\n  }\n\n  /**\n   * Recursively calculate the minimum value in an n-dimensional array\n   * @param {Array} array\n   * @return {number} min\n   * @private\n   */\n  function _min(array) {\n    var min;\n    deepForEach(array, function (value) {\n      try {\n        if (typeof value === 'number' && isNaN(value)) {\n          min = NaN;\n        } else if (min === undefined || smaller(value, min)) {\n          min = value;\n        }\n      } catch (err) {\n        throw improveErrorMessage(err, 'min', value);\n      }\n    });\n    if (min === undefined) {\n      throw new Error('Cannot calculate min of an empty array');\n    }\n\n    // make sure returning numeric value: parse a string into a numeric value\n    if (typeof min === 'string') {\n      min = numeric(min, safeNumberType(min, config));\n    }\n    return min;\n  }\n});"], "mappings": "AAAA,SAASA,mBAAT,EAA8BC,WAA9B,EAA2CC,MAA3C,QAAyD,2BAAzD;AACA,SAASC,OAAT,QAAwB,wBAAxB;AACA,SAASC,cAAT,QAA+B,uBAA/B;AACA,SAASC,mBAAT,QAAoC,gCAApC;AACA,IAAIC,IAAI,GAAG,KAAX;AACA,IAAIC,YAAY,GAAG,CAAC,OAAD,EAAU,QAAV,EAAoB,SAApB,EAA+B,SAA/B,CAAnB;AACA,OAAO,IAAIC,SAAS,GAAG,eAAeL,OAAO,CAACG,IAAD,EAAOC,YAAP,EAAqBE,IAAI,IAAI;EACxE,IAAI;IACFC,KADE;IAEFC,MAFE;IAGFC,OAHE;IAIFC;EAJE,IAKAJ,IALJ;EAMA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EACE,OAAOC,KAAK,CAACJ,IAAD,EAAO;IACjB;IACA,kBAAkBQ,IAFD;IAGjB;IACA,sCAAsC,SAASC,+BAAT,CAAyCC,KAAzC,EAAgDC,GAAhD,EAAqD;MACzF,OAAOf,MAAM,CAACc,KAAD,EAAQC,GAAG,CAACC,OAAJ,EAAR,EAAuBC,SAAvB,CAAb;IACD,CANgB;IAOjB;IACA,OAAO,SAASC,CAAT,CAAWC,IAAX,EAAiB;MACtB,IAAIrB,mBAAmB,CAACqB,IAAD,CAAvB,EAA+B;QAC7B,MAAM,IAAIC,SAAJ,CAAc,wCAAd,CAAN;MACD;;MACD,OAAOR,IAAI,CAACO,IAAD,CAAX;IACD;EAbgB,CAAP,CAAZ;EAgBA;AACF;AACA;AACA;AACA;AACA;AACA;;EACE,SAASF,SAAT,CAAmBI,CAAnB,EAAsBC,CAAtB,EAAyB;IACvB,IAAI;MACF,OAAOX,OAAO,CAACU,CAAD,EAAIC,CAAJ,CAAP,GAAgBD,CAAhB,GAAoBC,CAA3B;IACD,CAFD,CAEE,OAAOC,GAAP,EAAY;MACZ,MAAMpB,mBAAmB,CAACoB,GAAD,EAAM,KAAN,EAAaD,CAAb,CAAzB;IACD;EACF;EAED;AACF;AACA;AACA;AACA;AACA;;;EACE,SAASV,IAAT,CAAcE,KAAd,EAAqB;IACnB,IAAIU,GAAJ;IACAzB,WAAW,CAACe,KAAD,EAAQ,UAAUW,KAAV,EAAiB;MAClC,IAAI;QACF,IAAI,OAAOA,KAAP,KAAiB,QAAjB,IAA6BC,KAAK,CAACD,KAAD,CAAtC,EAA+C;UAC7CD,GAAG,GAAGG,GAAN;QACD,CAFD,MAEO,IAAIH,GAAG,KAAKI,SAAR,IAAqBjB,OAAO,CAACc,KAAD,EAAQD,GAAR,CAAhC,EAA8C;UACnDA,GAAG,GAAGC,KAAN;QACD;MACF,CAND,CAME,OAAOF,GAAP,EAAY;QACZ,MAAMpB,mBAAmB,CAACoB,GAAD,EAAM,KAAN,EAAaE,KAAb,CAAzB;MACD;IACF,CAVU,CAAX;;IAWA,IAAID,GAAG,KAAKI,SAAZ,EAAuB;MACrB,MAAM,IAAIC,KAAJ,CAAU,wCAAV,CAAN;IACD,CAfkB,CAiBnB;;;IACA,IAAI,OAAOL,GAAP,KAAe,QAAnB,EAA6B;MAC3BA,GAAG,GAAGd,OAAO,CAACc,GAAD,EAAMtB,cAAc,CAACsB,GAAD,EAAMf,MAAN,CAApB,CAAb;IACD;;IACD,OAAOe,GAAP;EACD;AACF,CAlG4C,CAAtC"}, "metadata": {}, "sourceType": "module"}