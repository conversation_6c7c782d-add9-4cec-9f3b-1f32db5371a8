{"ast": null, "code": "/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { addScalarDependencies } from './dependenciesAddScalar.generated.js';\nimport { combinationsDependencies } from './dependenciesCombinations.generated.js';\nimport { divideScalarDependencies } from './dependenciesDivideScalar.generated.js';\nimport { isIntegerDependencies } from './dependenciesIsInteger.generated.js';\nimport { isNegativeDependencies } from './dependenciesIsNegative.generated.js';\nimport { multiplyScalarDependencies } from './dependenciesMultiplyScalar.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createCatalan } from '../../factoriesAny.js';\nexport var catalanDependencies = {\n  addScalarDependencies,\n  combinationsDependencies,\n  divideScalarDependencies,\n  isIntegerDependencies,\n  isNegativeDependencies,\n  multiplyScalarDependencies,\n  typedDependencies,\n  createCatalan\n};", "map": {"version": 3, "names": ["addScalarDependencies", "combinationsDependencies", "divideScalarDependencies", "isIntegerDependencies", "isNegativeDependencies", "multiplyScalarDependencies", "typedDependencies", "createCatalan", "catalanDependencies"], "sources": ["D:/work/joyserver/client/node_modules/mathjs/lib/esm/entry/dependenciesAny/dependenciesCatalan.generated.js"], "sourcesContent": ["/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { addScalarDependencies } from './dependenciesAddScalar.generated.js';\nimport { combinationsDependencies } from './dependenciesCombinations.generated.js';\nimport { divideScalarDependencies } from './dependenciesDivideScalar.generated.js';\nimport { isIntegerDependencies } from './dependenciesIsInteger.generated.js';\nimport { isNegativeDependencies } from './dependenciesIsNegative.generated.js';\nimport { multiplyScalarDependencies } from './dependenciesMultiplyScalar.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createCatalan } from '../../factoriesAny.js';\nexport var catalanDependencies = {\n  addScalarDependencies,\n  combinationsDependencies,\n  divideScalarDependencies,\n  isIntegerDependencies,\n  isNegativeDependencies,\n  multiplyScalarDependencies,\n  typedDependencies,\n  createCatalan\n};"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,qBAAT,QAAsC,sCAAtC;AACA,SAASC,wBAAT,QAAyC,yCAAzC;AACA,SAASC,wBAAT,QAAyC,yCAAzC;AACA,SAASC,qBAAT,QAAsC,sCAAtC;AACA,SAASC,sBAAT,QAAuC,uCAAvC;AACA,SAASC,0BAAT,QAA2C,2CAA3C;AACA,SAASC,iBAAT,QAAkC,kCAAlC;AACA,SAASC,aAAT,QAA8B,uBAA9B;AACA,OAAO,IAAIC,mBAAmB,GAAG;EAC/BR,qBAD+B;EAE/BC,wBAF+B;EAG/BC,wBAH+B;EAI/BC,qBAJ+B;EAK/BC,sBAL+B;EAM/BC,0BAN+B;EAO/BC,iBAP+B;EAQ/BC;AAR+B,CAA1B"}, "metadata": {}, "sourceType": "module"}