{"ast": null, "code": "import { factory } from '../../utils/factory.js';\nvar name = 'sylvester';\nvar dependencies = ['typed', 'schur', 'matrixFromColumns', 'matrix', 'multiply', 'range', 'concat', 'transpose', 'index', 'subset', 'add', 'subtract', 'identity', 'lusolve', 'abs'];\nexport var createSylvester = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed,\n    schur,\n    matrixFromColumns,\n    matrix,\n    multiply,\n    range,\n    concat,\n    transpose,\n    index,\n    subset,\n    add,\n    subtract,\n    identity,\n    lusolve,\n    abs\n  } = _ref;\n  /**\n   *\n   * Solves the real-valued Sylvester equation AX+XB=C for X, where A, B and C are\n   * matrices of appropriate dimensions, being A and B squared. Notice that other\n   * equivalent definitions for the Sylvester equation exist and this function\n   * assumes the one presented in the original publication of the the Bartels-\n   * Stewart algorithm, which is implemented by this function.\n   * https://en.wikipedia.org/wiki/Sylvester_equation\n   *\n   * Syntax:\n   *\n   *     math.sylvester(A, B, C)\n   *\n   * Examples:\n   *\n   *     const A = [[-1, -2], [1, 1]]\n   *     const B = [[2, -1], [1, -2]]\n   *     const C = [[-3, 2], [3, 0]]\n   *     math.sylvester(A, B, C)      // returns DenseMatrix [[-0.25, 0.25], [1.5, -1.25]]\n   *\n   * See also:\n   *\n   *     schur, lyap\n   *\n   * @param {Matrix | Array} A  Matrix A\n   * @param {Matrix | Array} B  Matrix B\n   * @param {Matrix | Array} C  Matrix C\n   * @return {Matrix | Array}   Matrix X, solving the Sylvester equation\n   */\n\n  return typed(name, {\n    'Matrix, Matrix, Matrix': _sylvester,\n    'Array, Matrix, Matrix': function Array_Matrix_Matrix(A, B, C) {\n      return _sylvester(matrix(A), B, C);\n    },\n    'Array, Array, Matrix': function Array_Array_Matrix(A, B, C) {\n      return _sylvester(matrix(A), matrix(B), C);\n    },\n    'Array, Matrix, Array': function Array_Matrix_Array(A, B, C) {\n      return _sylvester(matrix(A), B, matrix(C));\n    },\n    'Matrix, Array, Matrix': function Matrix_Array_Matrix(A, B, C) {\n      return _sylvester(A, matrix(B), C);\n    },\n    'Matrix, Array, Array': function Matrix_Array_Array(A, B, C) {\n      return _sylvester(A, matrix(B), matrix(C));\n    },\n    'Matrix, Matrix, Array': function Matrix_Matrix_Array(A, B, C) {\n      return _sylvester(A, B, matrix(C));\n    },\n    'Array, Array, Array': function Array_Array_Array(A, B, C) {\n      return _sylvester(matrix(A), matrix(B), matrix(C)).toArray();\n    }\n  });\n\n  function _sylvester(A, B, C) {\n    var n = B.size()[0];\n    var m = A.size()[0];\n    var sA = schur(A);\n    var F = sA.T;\n    var U = sA.U;\n    var sB = schur(multiply(-1, B));\n    var G = sB.T;\n    var V = sB.U;\n    var D = multiply(multiply(transpose(U), C), V);\n    var all = range(0, m);\n    var y = [];\n\n    var hc = (a, b) => concat(a, b, 1);\n\n    var vc = (a, b) => concat(a, b, 0);\n\n    for (var k = 0; k < n; k++) {\n      if (k < n - 1 && abs(subset(G, index(k + 1, k))) > 1e-5) {\n        var RHS = vc(subset(D, index(all, k)), subset(D, index(all, k + 1)));\n\n        for (var j = 0; j < k; j++) {\n          RHS = add(RHS, vc(multiply(y[j], subset(G, index(j, k))), multiply(y[j], subset(G, index(j, k + 1)))));\n        }\n\n        var gkk = multiply(identity(m), multiply(-1, subset(G, index(k, k))));\n        var gmk = multiply(identity(m), multiply(-1, subset(G, index(k + 1, k))));\n        var gkm = multiply(identity(m), multiply(-1, subset(G, index(k, k + 1))));\n        var gmm = multiply(identity(m), multiply(-1, subset(G, index(k + 1, k + 1))));\n        var LHS = vc(hc(add(F, gkk), gmk), hc(gkm, add(F, gmm)));\n        var yAux = lusolve(LHS, RHS);\n        y[k] = yAux.subset(index(range(0, m), 0));\n        y[k + 1] = yAux.subset(index(range(m, 2 * m), 0));\n        k++;\n      } else {\n        var _RHS = subset(D, index(all, k));\n\n        for (var _j = 0; _j < k; _j++) {\n          _RHS = add(_RHS, multiply(y[_j], subset(G, index(_j, k))));\n        }\n\n        var _gkk = subset(G, index(k, k));\n\n        var _LHS = subtract(F, multiply(_gkk, identity(m)));\n\n        y[k] = lusolve(_LHS, _RHS);\n      }\n    }\n\n    var Y = matrix(matrixFromColumns(...y));\n    var X = multiply(U, multiply(Y, transpose(V)));\n    return X;\n  }\n});", "map": null, "metadata": {}, "sourceType": "module"}