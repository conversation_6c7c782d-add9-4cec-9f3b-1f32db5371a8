{"ast": null, "code": "import { isHelp } from '../utils/is.js';\nimport { clone } from '../utils/object.js';\nimport { format } from '../utils/string.js';\nimport { factory } from '../utils/factory.js';\nvar name = 'Help';\nvar dependencies = ['evaluate'];\nexport var createHelpClass = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    evaluate\n  } = _ref;\n  /**\n   * Documentation object\n   * @param {Object} doc  Object containing properties:\n   *                      {string} name\n   *                      {string} category\n   *                      {string} description\n   *                      {string[]} syntax\n   *                      {string[]} examples\n   *                      {string[]} seealso\n   * @constructor\n   */\n\n  function Help(doc) {\n    if (!(this instanceof Help)) {\n      throw new SyntaxError('Constructor must be called with the new operator');\n    }\n\n    if (!doc) throw new Error('Argument \"doc\" missing');\n    this.doc = doc;\n  }\n  /**\n   * Attach type information\n   */\n\n\n  Help.prototype.type = 'Help';\n  Help.prototype.isHelp = true;\n  /**\n   * Generate a string representation of the Help object\n   * @return {string} Returns a string\n   * @private\n   */\n\n  Help.prototype.toString = function () {\n    var doc = this.doc || {};\n    var desc = '\\n';\n\n    if (doc.name) {\n      desc += 'Name: ' + doc.name + '\\n\\n';\n    }\n\n    if (doc.category) {\n      desc += 'Category: ' + doc.category + '\\n\\n';\n    }\n\n    if (doc.description) {\n      desc += 'Description:\\n    ' + doc.description + '\\n\\n';\n    }\n\n    if (doc.syntax) {\n      desc += 'Syntax:\\n    ' + doc.syntax.join('\\n    ') + '\\n\\n';\n    }\n\n    if (doc.examples) {\n      desc += 'Examples:\\n'; // after evaluating the examples, we restore config in case the examples\n      // did change the config.\n\n      var configChanged = false;\n      var originalConfig = evaluate('config()');\n      var scope = {\n        config: newConfig => {\n          configChanged = true;\n          return evaluate('config(newConfig)', {\n            newConfig\n          });\n        }\n      };\n\n      for (var i = 0; i < doc.examples.length; i++) {\n        var expr = doc.examples[i];\n        desc += '    ' + expr + '\\n';\n        var res = void 0;\n\n        try {\n          // note: res can be undefined when `expr` is an empty string\n          res = evaluate(expr, scope);\n        } catch (e) {\n          res = e;\n        }\n\n        if (res !== undefined && !isHelp(res)) {\n          desc += '        ' + format(res, {\n            precision: 14\n          }) + '\\n';\n        }\n      }\n\n      desc += '\\n';\n\n      if (configChanged) {\n        evaluate('config(originalConfig)', {\n          originalConfig\n        });\n      }\n    }\n\n    if (doc.mayThrow && doc.mayThrow.length) {\n      desc += 'Throws: ' + doc.mayThrow.join(', ') + '\\n\\n';\n    }\n\n    if (doc.seealso && doc.seealso.length) {\n      desc += 'See also: ' + doc.seealso.join(', ') + '\\n';\n    }\n\n    return desc;\n  };\n  /**\n   * Export the help object to JSON\n   */\n\n\n  Help.prototype.toJSON = function () {\n    var obj = clone(this.doc);\n    obj.mathjs = 'Help';\n    return obj;\n  };\n  /**\n   * Instantiate a Help object from a JSON object\n   * @param {Object} json\n   * @returns {Help} Returns a new Help object\n   */\n\n\n  Help.fromJSON = function (json) {\n    var doc = {};\n    Object.keys(json).filter(prop => prop !== 'mathjs').forEach(prop => {\n      doc[prop] = json[prop];\n    });\n    return new Help(doc);\n  };\n  /**\n   * Returns a string representation of the Help object\n   */\n\n\n  Help.prototype.valueOf = Help.prototype.toString;\n  return Help;\n}, {\n  isClass: true\n});", "map": {"version": 3, "names": ["isHelp", "clone", "format", "factory", "name", "dependencies", "createHelpClass", "_ref", "evaluate", "Help", "doc", "SyntaxError", "Error", "prototype", "type", "toString", "desc", "category", "description", "syntax", "join", "examples", "config<PERSON><PERSON><PERSON>", "originalConfig", "scope", "config", "newConfig", "i", "length", "expr", "res", "e", "undefined", "precision", "mayThrow", "<PERSON>also", "toJSON", "obj", "mathjs", "fromJSON", "json", "Object", "keys", "filter", "prop", "for<PERSON>ach", "valueOf", "isClass"], "sources": ["D:/work/joyserver/client/node_modules/mathjs/lib/esm/expression/Help.js"], "sourcesContent": ["import { isHelp } from '../utils/is.js';\nimport { clone } from '../utils/object.js';\nimport { format } from '../utils/string.js';\nimport { factory } from '../utils/factory.js';\nvar name = 'Help';\nvar dependencies = ['evaluate'];\nexport var createHelpClass = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    evaluate\n  } = _ref;\n  /**\n   * Documentation object\n   * @param {Object} doc  Object containing properties:\n   *                      {string} name\n   *                      {string} category\n   *                      {string} description\n   *                      {string[]} syntax\n   *                      {string[]} examples\n   *                      {string[]} seealso\n   * @constructor\n   */\n  function Help(doc) {\n    if (!(this instanceof Help)) {\n      throw new SyntaxError('Constructor must be called with the new operator');\n    }\n    if (!doc) throw new Error('Argument \"doc\" missing');\n    this.doc = doc;\n  }\n\n  /**\n   * Attach type information\n   */\n  Help.prototype.type = 'Help';\n  Help.prototype.isHelp = true;\n\n  /**\n   * Generate a string representation of the Help object\n   * @return {string} Returns a string\n   * @private\n   */\n  Help.prototype.toString = function () {\n    var doc = this.doc || {};\n    var desc = '\\n';\n    if (doc.name) {\n      desc += 'Name: ' + doc.name + '\\n\\n';\n    }\n    if (doc.category) {\n      desc += 'Category: ' + doc.category + '\\n\\n';\n    }\n    if (doc.description) {\n      desc += 'Description:\\n    ' + doc.description + '\\n\\n';\n    }\n    if (doc.syntax) {\n      desc += 'Syntax:\\n    ' + doc.syntax.join('\\n    ') + '\\n\\n';\n    }\n    if (doc.examples) {\n      desc += 'Examples:\\n';\n\n      // after evaluating the examples, we restore config in case the examples\n      // did change the config.\n      var configChanged = false;\n      var originalConfig = evaluate('config()');\n      var scope = {\n        config: newConfig => {\n          configChanged = true;\n          return evaluate('config(newConfig)', {\n            newConfig\n          });\n        }\n      };\n      for (var i = 0; i < doc.examples.length; i++) {\n        var expr = doc.examples[i];\n        desc += '    ' + expr + '\\n';\n        var res = void 0;\n        try {\n          // note: res can be undefined when `expr` is an empty string\n          res = evaluate(expr, scope);\n        } catch (e) {\n          res = e;\n        }\n        if (res !== undefined && !isHelp(res)) {\n          desc += '        ' + format(res, {\n            precision: 14\n          }) + '\\n';\n        }\n      }\n      desc += '\\n';\n      if (configChanged) {\n        evaluate('config(originalConfig)', {\n          originalConfig\n        });\n      }\n    }\n    if (doc.mayThrow && doc.mayThrow.length) {\n      desc += 'Throws: ' + doc.mayThrow.join(', ') + '\\n\\n';\n    }\n    if (doc.seealso && doc.seealso.length) {\n      desc += 'See also: ' + doc.seealso.join(', ') + '\\n';\n    }\n    return desc;\n  };\n\n  /**\n   * Export the help object to JSON\n   */\n  Help.prototype.toJSON = function () {\n    var obj = clone(this.doc);\n    obj.mathjs = 'Help';\n    return obj;\n  };\n\n  /**\n   * Instantiate a Help object from a JSON object\n   * @param {Object} json\n   * @returns {Help} Returns a new Help object\n   */\n  Help.fromJSON = function (json) {\n    var doc = {};\n    Object.keys(json).filter(prop => prop !== 'mathjs').forEach(prop => {\n      doc[prop] = json[prop];\n    });\n    return new Help(doc);\n  };\n\n  /**\n   * Returns a string representation of the Help object\n   */\n  Help.prototype.valueOf = Help.prototype.toString;\n  return Help;\n}, {\n  isClass: true\n});"], "mappings": "AAAA,SAASA,MAAT,QAAuB,gBAAvB;AACA,SAASC,KAAT,QAAsB,oBAAtB;AACA,SAASC,MAAT,QAAuB,oBAAvB;AACA,SAASC,OAAT,QAAwB,qBAAxB;AACA,IAAIC,IAAI,GAAG,MAAX;AACA,IAAIC,YAAY,GAAG,CAAC,UAAD,CAAnB;AACA,OAAO,IAAIC,eAAe,GAAG,eAAeH,OAAO,CAACC,IAAD,EAAOC,YAAP,EAAqBE,IAAI,IAAI;EAC9E,IAAI;IACFC;EADE,IAEAD,IAFJ;EAGA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EACE,SAASE,IAAT,CAAcC,GAAd,EAAmB;IACjB,IAAI,EAAE,gBAAgBD,IAAlB,CAAJ,EAA6B;MAC3B,MAAM,IAAIE,WAAJ,CAAgB,kDAAhB,CAAN;IACD;;IACD,IAAI,CAACD,GAAL,EAAU,MAAM,IAAIE,KAAJ,CAAU,wBAAV,CAAN;IACV,KAAKF,GAAL,GAAWA,GAAX;EACD;EAED;AACF;AACA;;;EACED,IAAI,CAACI,SAAL,CAAeC,IAAf,GAAsB,MAAtB;EACAL,IAAI,CAACI,SAAL,CAAeb,MAAf,GAAwB,IAAxB;EAEA;AACF;AACA;AACA;AACA;;EACES,IAAI,CAACI,SAAL,CAAeE,QAAf,GAA0B,YAAY;IACpC,IAAIL,GAAG,GAAG,KAAKA,GAAL,IAAY,EAAtB;IACA,IAAIM,IAAI,GAAG,IAAX;;IACA,IAAIN,GAAG,CAACN,IAAR,EAAc;MACZY,IAAI,IAAI,WAAWN,GAAG,CAACN,IAAf,GAAsB,MAA9B;IACD;;IACD,IAAIM,GAAG,CAACO,QAAR,EAAkB;MAChBD,IAAI,IAAI,eAAeN,GAAG,CAACO,QAAnB,GAA8B,MAAtC;IACD;;IACD,IAAIP,GAAG,CAACQ,WAAR,EAAqB;MACnBF,IAAI,IAAI,uBAAuBN,GAAG,CAACQ,WAA3B,GAAyC,MAAjD;IACD;;IACD,IAAIR,GAAG,CAACS,MAAR,EAAgB;MACdH,IAAI,IAAI,kBAAkBN,GAAG,CAACS,MAAJ,CAAWC,IAAX,CAAgB,QAAhB,CAAlB,GAA8C,MAAtD;IACD;;IACD,IAAIV,GAAG,CAACW,QAAR,EAAkB;MAChBL,IAAI,IAAI,aAAR,CADgB,CAGhB;MACA;;MACA,IAAIM,aAAa,GAAG,KAApB;MACA,IAAIC,cAAc,GAAGf,QAAQ,CAAC,UAAD,CAA7B;MACA,IAAIgB,KAAK,GAAG;QACVC,MAAM,EAAEC,SAAS,IAAI;UACnBJ,aAAa,GAAG,IAAhB;UACA,OAAOd,QAAQ,CAAC,mBAAD,EAAsB;YACnCkB;UADmC,CAAtB,CAAf;QAGD;MANS,CAAZ;;MAQA,KAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGjB,GAAG,CAACW,QAAJ,CAAaO,MAAjC,EAAyCD,CAAC,EAA1C,EAA8C;QAC5C,IAAIE,IAAI,GAAGnB,GAAG,CAACW,QAAJ,CAAaM,CAAb,CAAX;QACAX,IAAI,IAAI,SAASa,IAAT,GAAgB,IAAxB;QACA,IAAIC,GAAG,GAAG,KAAK,CAAf;;QACA,IAAI;UACF;UACAA,GAAG,GAAGtB,QAAQ,CAACqB,IAAD,EAAOL,KAAP,CAAd;QACD,CAHD,CAGE,OAAOO,CAAP,EAAU;UACVD,GAAG,GAAGC,CAAN;QACD;;QACD,IAAID,GAAG,KAAKE,SAAR,IAAqB,CAAChC,MAAM,CAAC8B,GAAD,CAAhC,EAAuC;UACrCd,IAAI,IAAI,aAAad,MAAM,CAAC4B,GAAD,EAAM;YAC/BG,SAAS,EAAE;UADoB,CAAN,CAAnB,GAEH,IAFL;QAGD;MACF;;MACDjB,IAAI,IAAI,IAAR;;MACA,IAAIM,aAAJ,EAAmB;QACjBd,QAAQ,CAAC,wBAAD,EAA2B;UACjCe;QADiC,CAA3B,CAAR;MAGD;IACF;;IACD,IAAIb,GAAG,CAACwB,QAAJ,IAAgBxB,GAAG,CAACwB,QAAJ,CAAaN,MAAjC,EAAyC;MACvCZ,IAAI,IAAI,aAAaN,GAAG,CAACwB,QAAJ,CAAad,IAAb,CAAkB,IAAlB,CAAb,GAAuC,MAA/C;IACD;;IACD,IAAIV,GAAG,CAACyB,OAAJ,IAAezB,GAAG,CAACyB,OAAJ,CAAYP,MAA/B,EAAuC;MACrCZ,IAAI,IAAI,eAAeN,GAAG,CAACyB,OAAJ,CAAYf,IAAZ,CAAiB,IAAjB,CAAf,GAAwC,IAAhD;IACD;;IACD,OAAOJ,IAAP;EACD,CA5DD;EA8DA;AACF;AACA;;;EACEP,IAAI,CAACI,SAAL,CAAeuB,MAAf,GAAwB,YAAY;IAClC,IAAIC,GAAG,GAAGpC,KAAK,CAAC,KAAKS,GAAN,CAAf;IACA2B,GAAG,CAACC,MAAJ,GAAa,MAAb;IACA,OAAOD,GAAP;EACD,CAJD;EAMA;AACF;AACA;AACA;AACA;;;EACE5B,IAAI,CAAC8B,QAAL,GAAgB,UAAUC,IAAV,EAAgB;IAC9B,IAAI9B,GAAG,GAAG,EAAV;IACA+B,MAAM,CAACC,IAAP,CAAYF,IAAZ,EAAkBG,MAAlB,CAAyBC,IAAI,IAAIA,IAAI,KAAK,QAA1C,EAAoDC,OAApD,CAA4DD,IAAI,IAAI;MAClElC,GAAG,CAACkC,IAAD,CAAH,GAAYJ,IAAI,CAACI,IAAD,CAAhB;IACD,CAFD;IAGA,OAAO,IAAInC,IAAJ,CAASC,GAAT,CAAP;EACD,CAND;EAQA;AACF;AACA;;;EACED,IAAI,CAACI,SAAL,CAAeiC,OAAf,GAAyBrC,IAAI,CAACI,SAAL,CAAeE,QAAxC;EACA,OAAON,IAAP;AACD,CA3HkD,EA2HhD;EACDsC,OAAO,EAAE;AADR,CA3HgD,CAA5C"}, "metadata": {}, "sourceType": "module"}