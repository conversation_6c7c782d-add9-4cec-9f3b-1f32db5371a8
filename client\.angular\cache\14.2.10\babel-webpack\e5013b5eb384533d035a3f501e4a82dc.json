{"ast": null, "code": "/**\n * THIS FILE IS AUTO-GENERATED\n * DON'T MAKE CHANGES HERE\n */\nimport { addDependencies } from './dependenciesAdd.generated.js';\nimport { divideDependencies } from './dependenciesDivide.generated.js';\nimport { matrixDependencies } from './dependenciesMatrix.generated.js';\nimport { meanDependencies } from './dependenciesMean.generated.js';\nimport { multiplyDependencies } from './dependenciesMultiply.generated.js';\nimport { powDependencies } from './dependenciesPow.generated.js';\nimport { sqrtDependencies } from './dependenciesSqrt.generated.js';\nimport { subtractDependencies } from './dependenciesSubtract.generated.js';\nimport { sumDependencies } from './dependenciesSum.generated.js';\nimport { typedDependencies } from './dependenciesTyped.generated.js';\nimport { createCorr } from '../../factoriesAny.js';\nexport var corrDependencies = {\n  addDependencies,\n  divideDependencies,\n  matrixDependencies,\n  meanDependencies,\n  multiplyDependencies,\n  powDependencies,\n  sqrtDependencies,\n  subtractDependencies,\n  sumDependencies,\n  typedDependencies,\n  createCorr\n};", "map": null, "metadata": {}, "sourceType": "module"}