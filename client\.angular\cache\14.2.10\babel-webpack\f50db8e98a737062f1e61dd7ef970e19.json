{"ast": null, "code": "import naturalSort from 'javascript-natural-sort';\nimport { isDenseMatrix, isSparseMatrix, typeOf } from '../../utils/is.js';\nimport { factory } from '../../utils/factory.js';\nvar name = 'compareNatural';\nvar dependencies = ['typed', 'compare'];\nexport var createCompareNatural = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed,\n    compare\n  } = _ref;\n  var compareBooleans = compare.signatures['boolean,boolean'];\n  /**\n   * Compare two values of any type in a deterministic, natural way.\n   *\n   * For numeric values, the function works the same as `math.compare`.\n   * For types of values that can't be compared mathematically,\n   * the function compares in a natural way.\n   *\n   * For numeric values, x and y are considered equal when the relative\n   * difference between x and y is smaller than the configured relTol and absTol.\n   * The function cannot be used to compare values smaller than\n   * approximately 2.22e-16.\n   *\n   * For Complex numbers, first the real parts are compared. If equal,\n   * the imaginary parts are compared.\n   *\n   * Strings are compared with a natural sorting algorithm, which\n   * orders strings in a \"logic\" way following some heuristics.\n   * This differs from the function `compare`, which converts the string\n   * into a numeric value and compares that. The function `compareText`\n   * on the other hand compares text lexically.\n   *\n   * Arrays and Matrices are compared value by value until there is an\n   * unequal pair of values encountered. Objects are compared by sorted\n   * keys until the keys or their values are unequal.\n   *\n   * Syntax:\n   *\n   *    math.compareNatural(x, y)\n   *\n   * Examples:\n   *\n   *    math.compareNatural(6, 1)              // returns 1\n   *    math.compareNatural(2, 3)              // returns -1\n   *    math.compareNatural(7, 7)              // returns 0\n   *\n   *    math.compareNatural('10', '2')         // returns 1\n   *    math.compareText('10', '2')            // returns -1\n   *    math.compare('10', '2')                // returns 1\n   *\n   *    math.compareNatural('Answer: 10', 'Answer: 2') // returns 1\n   *    math.compareText('Answer: 10', 'Answer: 2')    // returns -1\n   *    math.compare('Answer: 10', 'Answer: 2')\n   *        // Error: Cannot convert \"Answer: 10\" to a number\n   *\n   *    const a = math.unit('5 cm')\n   *    const b = math.unit('40 mm')\n   *    math.compareNatural(a, b)              // returns 1\n   *\n   *    const c = math.complex('2 + 3i')\n   *    const d = math.complex('2 + 4i')\n   *    math.compareNatural(c, d)              // returns -1\n   *\n   *    math.compareNatural([1, 2, 4], [1, 2, 3]) // returns 1\n   *    math.compareNatural([1, 2, 3], [1, 2])    // returns 1\n   *    math.compareNatural([1, 5], [1, 2, 3])    // returns 1\n   *    math.compareNatural([1, 2], [1, 2])       // returns 0\n   *\n   *    math.compareNatural({a: 2}, {a: 4})       // returns -1\n   *\n   * See also:\n   *\n   *    compare, compareText\n   *\n   * @param  {*} x First value to compare\n   * @param  {*} y Second value to compare\n   * @return {number} Returns the result of the comparison:\n   *                  1 when x > y, -1 when x < y, and 0 when x == y.\n   */\n\n  return typed(name, {\n    'any, any': _compareNatural\n  }); // just to check # args\n\n  function _compareNatural(x, y) {\n    var typeX = typeOf(x);\n    var typeY = typeOf(y);\n    var c; // numeric types\n\n    if ((typeX === 'number' || typeX === 'BigNumber' || typeX === 'Fraction') && (typeY === 'number' || typeY === 'BigNumber' || typeY === 'Fraction')) {\n      c = compare(x, y);\n\n      if (c.toString() !== '0') {\n        // c can be number, BigNumber, or Fraction\n        return c > 0 ? 1 : -1; // return a number\n      } else {\n        return naturalSort(typeX, typeY);\n      }\n    } // matrix types\n\n\n    var matTypes = ['Array', 'DenseMatrix', 'SparseMatrix'];\n\n    if (matTypes.includes(typeX) || matTypes.includes(typeY)) {\n      c = compareMatricesAndArrays(_compareNatural, x, y);\n\n      if (c !== 0) {\n        return c;\n      } else {\n        return naturalSort(typeX, typeY);\n      }\n    } // in case of different types, order by name of type, i.e. 'BigNumber' < 'Complex'\n\n\n    if (typeX !== typeY) {\n      return naturalSort(typeX, typeY);\n    }\n\n    if (typeX === 'Complex') {\n      return compareComplexNumbers(x, y);\n    }\n\n    if (typeX === 'Unit') {\n      if (x.equalBase(y)) {\n        return _compareNatural(x.value, y.value);\n      } // compare by units\n\n\n      return compareArrays(_compareNatural, x.formatUnits(), y.formatUnits());\n    }\n\n    if (typeX === 'boolean') {\n      return compareBooleans(x, y);\n    }\n\n    if (typeX === 'string') {\n      return naturalSort(x, y);\n    }\n\n    if (typeX === 'Object') {\n      return compareObjects(_compareNatural, x, y);\n    }\n\n    if (typeX === 'null') {\n      return 0;\n    }\n\n    if (typeX === 'undefined') {\n      return 0;\n    } // this should not occur...\n\n\n    throw new TypeError('Unsupported type of value \"' + typeX + '\"');\n  }\n  /**\n   * Compare mixed matrix/array types, by converting to same-shaped array.\n   * This comparator is non-deterministic regarding input types.\n   * @param {Array | SparseMatrix | DenseMatrix | *} x\n   * @param {Array | SparseMatrix | DenseMatrix | *} y\n   * @returns {number} Returns the comparison result: -1, 0, or 1\n   */\n\n\n  function compareMatricesAndArrays(compareNatural, x, y) {\n    if (isSparseMatrix(x) && isSparseMatrix(y)) {\n      return compareArrays(compareNatural, x.toJSON().values, y.toJSON().values);\n    }\n\n    if (isSparseMatrix(x)) {\n      // note: convert to array is expensive\n      return compareMatricesAndArrays(compareNatural, x.toArray(), y);\n    }\n\n    if (isSparseMatrix(y)) {\n      // note: convert to array is expensive\n      return compareMatricesAndArrays(compareNatural, x, y.toArray());\n    } // convert DenseArray into Array\n\n\n    if (isDenseMatrix(x)) {\n      return compareMatricesAndArrays(compareNatural, x.toJSON().data, y);\n    }\n\n    if (isDenseMatrix(y)) {\n      return compareMatricesAndArrays(compareNatural, x, y.toJSON().data);\n    } // convert scalars to array\n\n\n    if (!Array.isArray(x)) {\n      return compareMatricesAndArrays(compareNatural, [x], y);\n    }\n\n    if (!Array.isArray(y)) {\n      return compareMatricesAndArrays(compareNatural, x, [y]);\n    }\n\n    return compareArrays(compareNatural, x, y);\n  }\n  /**\n   * Compare two Arrays\n   *\n   * - First, compares value by value\n   * - Next, if all corresponding values are equal,\n   *   look at the length: longest array will be considered largest\n   *\n   * @param {Array} x\n   * @param {Array} y\n   * @returns {number} Returns the comparison result: -1, 0, or 1\n   */\n\n\n  function compareArrays(compareNatural, x, y) {\n    // compare each value\n    for (var i = 0, ii = Math.min(x.length, y.length); i < ii; i++) {\n      var v = compareNatural(x[i], y[i]);\n\n      if (v !== 0) {\n        return v;\n      }\n    } // compare the size of the arrays\n\n\n    if (x.length > y.length) {\n      return 1;\n    }\n\n    if (x.length < y.length) {\n      return -1;\n    } // both Arrays have equal size and content\n\n\n    return 0;\n  }\n  /**\n   * Compare two objects\n   *\n   * - First, compare sorted property names\n   * - Next, compare the property values\n   *\n   * @param {Object} x\n   * @param {Object} y\n   * @returns {number} Returns the comparison result: -1, 0, or 1\n   */\n\n\n  function compareObjects(compareNatural, x, y) {\n    var keysX = Object.keys(x);\n    var keysY = Object.keys(y); // compare keys\n\n    keysX.sort(naturalSort);\n    keysY.sort(naturalSort);\n    var c = compareArrays(compareNatural, keysX, keysY);\n\n    if (c !== 0) {\n      return c;\n    } // compare values\n\n\n    for (var i = 0; i < keysX.length; i++) {\n      var v = compareNatural(x[keysX[i]], y[keysY[i]]);\n\n      if (v !== 0) {\n        return v;\n      }\n    }\n\n    return 0;\n  }\n});\n/**\n * Compare two complex numbers, `x` and `y`:\n *\n * - First, compare the real values of `x` and `y`\n * - If equal, compare the imaginary values of `x` and `y`\n *\n * @params {Complex} x\n * @params {Complex} y\n * @returns {number} Returns the comparison result: -1, 0, or 1\n */\n\nfunction compareComplexNumbers(x, y) {\n  if (x.re > y.re) {\n    return 1;\n  }\n\n  if (x.re < y.re) {\n    return -1;\n  }\n\n  if (x.im > y.im) {\n    return 1;\n  }\n\n  if (x.im < y.im) {\n    return -1;\n  }\n\n  return 0;\n}", "map": null, "metadata": {}, "sourceType": "module"}