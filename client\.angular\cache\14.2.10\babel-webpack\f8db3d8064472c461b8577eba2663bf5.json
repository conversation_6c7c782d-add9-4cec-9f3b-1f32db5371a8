{"ast": null, "code": "import { isArray, isMatrix, isDenseMatrix, isSparseMatrix } from '../../../../utils/is.js';\nimport { arraySize } from '../../../../utils/array.js';\nimport { format } from '../../../../utils/string.js';\nexport function createSolveValidation(_ref) {\n  var {\n    DenseMatrix\n  } = _ref;\n  /**\n   * Validates matrix and column vector b for backward/forward substitution algorithms.\n   *\n   * @param {Matrix} m            An N x N matrix\n   * @param {Array | Matrix} b    A column vector\n   * @param {Boolean} copy        Return a copy of vector b\n   *\n   * @return {DenseMatrix}        Dense column vector b\n   */\n\n  return function solveValidation(m, b, copy) {\n    var mSize = m.size();\n\n    if (mSize.length !== 2) {\n      throw new RangeError('Matrix must be two dimensional (size: ' + format(mSize) + ')');\n    }\n\n    var rows = mSize[0];\n    var columns = mSize[1];\n\n    if (rows !== columns) {\n      throw new RangeError('Matrix must be square (size: ' + format(mSize) + ')');\n    }\n\n    var data = [];\n\n    if (isMatrix(b)) {\n      var bSize = b.size();\n      var bdata = b._data; // 1-dim vector\n\n      if (bSize.length === 1) {\n        if (bSize[0] !== rows) {\n          throw new RangeError('Dimension mismatch. Matrix columns must match vector length.');\n        }\n\n        for (var i = 0; i < rows; i++) {\n          data[i] = [bdata[i]];\n        }\n\n        return new DenseMatrix({\n          data,\n          size: [rows, 1],\n          datatype: b._datatype\n        });\n      } // 2-dim column\n\n\n      if (bSize.length === 2) {\n        if (bSize[0] !== rows || bSize[1] !== 1) {\n          throw new RangeError('Dimension mismatch. Matrix columns must match vector length.');\n        }\n\n        if (isDenseMatrix(b)) {\n          if (copy) {\n            data = [];\n\n            for (var _i = 0; _i < rows; _i++) {\n              data[_i] = [bdata[_i][0]];\n            }\n\n            return new DenseMatrix({\n              data,\n              size: [rows, 1],\n              datatype: b._datatype\n            });\n          }\n\n          return b;\n        }\n\n        if (isSparseMatrix(b)) {\n          for (var _i2 = 0; _i2 < rows; _i2++) {\n            data[_i2] = [0];\n          }\n\n          var values = b._values;\n          var index = b._index;\n          var ptr = b._ptr;\n\n          for (var k1 = ptr[1], k = ptr[0]; k < k1; k++) {\n            var _i3 = index[k];\n            data[_i3][0] = values[k];\n          }\n\n          return new DenseMatrix({\n            data,\n            size: [rows, 1],\n            datatype: b._datatype\n          });\n        }\n      }\n\n      throw new RangeError('Dimension mismatch. The right side has to be either 1- or 2-dimensional vector.');\n    }\n\n    if (isArray(b)) {\n      var bsize = arraySize(b);\n\n      if (bsize.length === 1) {\n        if (bsize[0] !== rows) {\n          throw new RangeError('Dimension mismatch. Matrix columns must match vector length.');\n        }\n\n        for (var _i4 = 0; _i4 < rows; _i4++) {\n          data[_i4] = [b[_i4]];\n        }\n\n        return new DenseMatrix({\n          data,\n          size: [rows, 1]\n        });\n      }\n\n      if (bsize.length === 2) {\n        if (bsize[0] !== rows || bsize[1] !== 1) {\n          throw new RangeError('Dimension mismatch. Matrix columns must match vector length.');\n        }\n\n        for (var _i5 = 0; _i5 < rows; _i5++) {\n          data[_i5] = [b[_i5][0]];\n        }\n\n        return new DenseMatrix({\n          data,\n          size: [rows, 1]\n        });\n      }\n\n      throw new RangeError('Dimension mismatch. The right side has to be either 1- or 2-dimensional vector.');\n    }\n  };\n}", "map": null, "metadata": {}, "sourceType": "module"}