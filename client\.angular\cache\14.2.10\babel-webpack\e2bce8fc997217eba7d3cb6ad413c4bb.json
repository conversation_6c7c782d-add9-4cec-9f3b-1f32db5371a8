{"ast": null, "code": "export var fftDocs = {\n  name: 'fft',\n  category: 'Matrix',\n  syntax: ['fft(x)'],\n  description: 'Calculate N-dimensional Fourier transform',\n  examples: ['fft([[1, 0], [1, 0]])'],\n  seealso: ['ifft']\n};", "map": {"version": 3, "names": ["fftDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["D:/work/joyserver/client/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/matrix/fft.js"], "sourcesContent": ["export var fftDocs = {\n  name: 'fft',\n  category: 'Matrix',\n  syntax: ['fft(x)'],\n  description: 'Calculate N-dimensional Fourier transform',\n  examples: ['fft([[1, 0], [1, 0]])'],\n  seealso: ['ifft']\n};"], "mappings": "AAAA,OAAO,IAAIA,OAAO,GAAG;EACnBC,IAAI,EAAE,KADa;EAEnBC,QAAQ,EAAE,QAFS;EAGnBC,MAAM,EAAE,CAAC,QAAD,CAHW;EAInBC,WAAW,EAAE,2CAJM;EAKnBC,QAAQ,EAAE,CAAC,uBAAD,CALS;EAMnBC,OAAO,EAAE,CAAC,MAAD;AANU,CAAd"}, "metadata": {}, "sourceType": "module"}