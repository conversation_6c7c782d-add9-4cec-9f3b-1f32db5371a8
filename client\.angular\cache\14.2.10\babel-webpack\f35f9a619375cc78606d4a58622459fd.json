{"ast": null, "code": "/* eslint-disable no-loss-of-precision */\nimport { isInteger } from '../../utils/number.js';\nimport { product } from '../../utils/product.js';\nexport function gammaNumber(n) {\n  var x;\n\n  if (isInteger(n)) {\n    if (n <= 0) {\n      return isFinite(n) ? Infinity : NaN;\n    }\n\n    if (n > 171) {\n      return Infinity; // Will overflow\n    }\n\n    return product(1, n - 1);\n  }\n\n  if (n < 0.5) {\n    return Math.PI / (Math.sin(Math.PI * n) * gammaNumber(1 - n));\n  }\n\n  if (n >= 171.35) {\n    return Infinity; // will overflow\n  }\n\n  if (n > 85.0) {\n    // Extended Stirling Approx\n    var twoN = n * n;\n    var threeN = twoN * n;\n    var fourN = threeN * n;\n    var fiveN = fourN * n;\n    return Math.sqrt(2 * Math.PI / n) * Math.pow(n / Math.E, n) * (1 + 1 / (12 * n) + 1 / (288 * twoN) - 139 / (51840 * threeN) - 571 / (2488320 * fourN) + 163879 / (209018880 * fiveN) + 5246819 / (75246796800 * fiveN * n));\n  }\n\n  --n;\n  x = gammaP[0];\n\n  for (var i = 1; i < gammaP.length; ++i) {\n    x += gammaP[i] / (n + i);\n  }\n\n  var t = n + gammaG + 0.5;\n  return Math.sqrt(2 * Math.PI) * Math.pow(t, n + 0.5) * Math.exp(-t) * x;\n}\ngammaNumber.signature = 'number'; // TODO: comment on the variables g and p\n\nexport var gammaG = 4.7421875;\nexport var gammaP = [0.99999999999999709182, 57.156235665862923517, -59.597960355475491248, 14.136097974741747174, -0.49191381609762019978, 0.33994649984811888699e-4, 0.46523628927048575665e-4, -0.98374475304879564677e-4, 0.15808870322491248884e-3, -0.21026444172410488319e-3, 0.21743961811521264320e-3, -0.16431810653676389022e-3, 0.84418223983852743293e-4, -0.26190838401581408670e-4, 0.36899182659531622704e-5]; // lgamma implementation ref: https://mrob.com/pub/ries/lanczos-gamma.html#code\n// log(2 * pi) / 2\n\nexport var lnSqrt2PI = 0.91893853320467274178;\nexport var lgammaG = 5; // Lanczos parameter \"g\"\n\nexport var lgammaN = 7; // Range of coefficients \"n\"\n\nexport var lgammaSeries = [1.000000000190015, 76.18009172947146, -86.50532032941677, 24.01409824083091, -1.231739572450155, 0.1208650973866179e-2, -0.5395239384953e-5];\nexport function lgammaNumber(n) {\n  if (n < 0) return NaN;\n  if (n === 0) return Infinity;\n  if (!isFinite(n)) return n;\n\n  if (n < 0.5) {\n    // Use Euler's reflection formula:\n    // gamma(z) = PI / (sin(PI * z) * gamma(1 - z))\n    return Math.log(Math.PI / Math.sin(Math.PI * n)) - lgammaNumber(1 - n);\n  } // Compute the logarithm of the Gamma function using the Lanczos method\n\n\n  n = n - 1;\n  var base = n + lgammaG + 0.5; // Base of the Lanczos exponential\n\n  var sum = lgammaSeries[0]; // We start with the terms that have the smallest coefficients and largest denominator\n\n  for (var i = lgammaN - 1; i >= 1; i--) {\n    sum += lgammaSeries[i] / (n + i);\n  }\n\n  return lnSqrt2PI + (n + 0.5) * Math.log(base) - base + Math.log(sum);\n}\nlgammaNumber.signature = 'number';", "map": {"version": 3, "names": ["isInteger", "product", "gammaNumber", "n", "x", "isFinite", "Infinity", "NaN", "Math", "PI", "sin", "twoN", "threeN", "fourN", "fiveN", "sqrt", "pow", "E", "gammaP", "i", "length", "t", "gammaG", "exp", "signature", "lnSqrt2PI", "lgammaG", "lgammaN", "lgammaSeries", "lgammaNumber", "log", "base", "sum"], "sources": ["D:/work/joyserver/client/node_modules/mathjs/lib/esm/plain/number/probability.js"], "sourcesContent": ["/* eslint-disable no-loss-of-precision */\n\nimport { isInteger } from '../../utils/number.js';\nimport { product } from '../../utils/product.js';\nexport function gammaNumber(n) {\n  var x;\n  if (isInteger(n)) {\n    if (n <= 0) {\n      return isFinite(n) ? Infinity : NaN;\n    }\n    if (n > 171) {\n      return Infinity; // Will overflow\n    }\n    return product(1, n - 1);\n  }\n  if (n < 0.5) {\n    return Math.PI / (Math.sin(Math.PI * n) * gammaNumber(1 - n));\n  }\n  if (n >= 171.35) {\n    return Infinity; // will overflow\n  }\n  if (n > 85.0) {\n    // Extended Stirling Approx\n    var twoN = n * n;\n    var threeN = twoN * n;\n    var fourN = threeN * n;\n    var fiveN = fourN * n;\n    return Math.sqrt(2 * Math.PI / n) * Math.pow(n / Math.E, n) * (1 + 1 / (12 * n) + 1 / (288 * twoN) - 139 / (51840 * threeN) - 571 / (2488320 * fourN) + 163879 / (209018880 * fiveN) + 5246819 / (75246796800 * fiveN * n));\n  }\n  --n;\n  x = gammaP[0];\n  for (var i = 1; i < gammaP.length; ++i) {\n    x += gammaP[i] / (n + i);\n  }\n  var t = n + gammaG + 0.5;\n  return Math.sqrt(2 * Math.PI) * Math.pow(t, n + 0.5) * Math.exp(-t) * x;\n}\ngammaNumber.signature = 'number';\n\n// TODO: comment on the variables g and p\n\nexport var gammaG = 4.7421875;\nexport var gammaP = [0.99999999999999709182, 57.156235665862923517, -59.597960355475491248, 14.136097974741747174, -0.49191381609762019978, 0.33994649984811888699e-4, 0.46523628927048575665e-4, -0.98374475304879564677e-4, 0.15808870322491248884e-3, -0.21026444172410488319e-3, 0.21743961811521264320e-3, -0.16431810653676389022e-3, 0.84418223983852743293e-4, -0.26190838401581408670e-4, 0.36899182659531622704e-5];\n\n// lgamma implementation ref: https://mrob.com/pub/ries/lanczos-gamma.html#code\n\n// log(2 * pi) / 2\nexport var lnSqrt2PI = 0.91893853320467274178;\nexport var lgammaG = 5; // Lanczos parameter \"g\"\nexport var lgammaN = 7; // Range of coefficients \"n\"\n\nexport var lgammaSeries = [1.000000000190015, 76.18009172947146, -86.50532032941677, 24.01409824083091, -1.231739572450155, 0.1208650973866179e-2, -0.5395239384953e-5];\nexport function lgammaNumber(n) {\n  if (n < 0) return NaN;\n  if (n === 0) return Infinity;\n  if (!isFinite(n)) return n;\n  if (n < 0.5) {\n    // Use Euler's reflection formula:\n    // gamma(z) = PI / (sin(PI * z) * gamma(1 - z))\n    return Math.log(Math.PI / Math.sin(Math.PI * n)) - lgammaNumber(1 - n);\n  }\n\n  // Compute the logarithm of the Gamma function using the Lanczos method\n\n  n = n - 1;\n  var base = n + lgammaG + 0.5; // Base of the Lanczos exponential\n  var sum = lgammaSeries[0];\n\n  // We start with the terms that have the smallest coefficients and largest denominator\n  for (var i = lgammaN - 1; i >= 1; i--) {\n    sum += lgammaSeries[i] / (n + i);\n  }\n  return lnSqrt2PI + (n + 0.5) * Math.log(base) - base + Math.log(sum);\n}\nlgammaNumber.signature = 'number';"], "mappings": "AAAA;AAEA,SAASA,SAAT,QAA0B,uBAA1B;AACA,SAASC,OAAT,QAAwB,wBAAxB;AACA,OAAO,SAASC,WAAT,CAAqBC,CAArB,EAAwB;EAC7B,IAAIC,CAAJ;;EACA,IAAIJ,SAAS,CAACG,CAAD,CAAb,EAAkB;IAChB,IAAIA,CAAC,IAAI,CAAT,EAAY;MACV,OAAOE,QAAQ,CAACF,CAAD,CAAR,GAAcG,QAAd,GAAyBC,GAAhC;IACD;;IACD,IAAIJ,CAAC,GAAG,GAAR,EAAa;MACX,OAAOG,QAAP,CADW,CACM;IAClB;;IACD,OAAOL,OAAO,CAAC,CAAD,EAAIE,CAAC,GAAG,CAAR,CAAd;EACD;;EACD,IAAIA,CAAC,GAAG,GAAR,EAAa;IACX,OAAOK,IAAI,CAACC,EAAL,IAAWD,IAAI,CAACE,GAAL,CAASF,IAAI,CAACC,EAAL,GAAUN,CAAnB,IAAwBD,WAAW,CAAC,IAAIC,CAAL,CAA9C,CAAP;EACD;;EACD,IAAIA,CAAC,IAAI,MAAT,EAAiB;IACf,OAAOG,QAAP,CADe,CACE;EAClB;;EACD,IAAIH,CAAC,GAAG,IAAR,EAAc;IACZ;IACA,IAAIQ,IAAI,GAAGR,CAAC,GAAGA,CAAf;IACA,IAAIS,MAAM,GAAGD,IAAI,GAAGR,CAApB;IACA,IAAIU,KAAK,GAAGD,MAAM,GAAGT,CAArB;IACA,IAAIW,KAAK,GAAGD,KAAK,GAAGV,CAApB;IACA,OAAOK,IAAI,CAACO,IAAL,CAAU,IAAIP,IAAI,CAACC,EAAT,GAAcN,CAAxB,IAA6BK,IAAI,CAACQ,GAAL,CAASb,CAAC,GAAGK,IAAI,CAACS,CAAlB,EAAqBd,CAArB,CAA7B,IAAwD,IAAI,KAAK,KAAKA,CAAV,CAAJ,GAAmB,KAAK,MAAMQ,IAAX,CAAnB,GAAsC,OAAO,QAAQC,MAAf,CAAtC,GAA+D,OAAO,UAAUC,KAAjB,CAA/D,GAAyF,UAAU,YAAYC,KAAtB,CAAzF,GAAwH,WAAW,cAAcA,KAAd,GAAsBX,CAAjC,CAAhL,CAAP;EACD;;EACD,EAAEA,CAAF;EACAC,CAAC,GAAGc,MAAM,CAAC,CAAD,CAAV;;EACA,KAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGD,MAAM,CAACE,MAA3B,EAAmC,EAAED,CAArC,EAAwC;IACtCf,CAAC,IAAIc,MAAM,CAACC,CAAD,CAAN,IAAahB,CAAC,GAAGgB,CAAjB,CAAL;EACD;;EACD,IAAIE,CAAC,GAAGlB,CAAC,GAAGmB,MAAJ,GAAa,GAArB;EACA,OAAOd,IAAI,CAACO,IAAL,CAAU,IAAIP,IAAI,CAACC,EAAnB,IAAyBD,IAAI,CAACQ,GAAL,CAASK,CAAT,EAAYlB,CAAC,GAAG,GAAhB,CAAzB,GAAgDK,IAAI,CAACe,GAAL,CAAS,CAACF,CAAV,CAAhD,GAA+DjB,CAAtE;AACD;AACDF,WAAW,CAACsB,SAAZ,GAAwB,QAAxB,C,CAEA;;AAEA,OAAO,IAAIF,MAAM,GAAG,SAAb;AACP,OAAO,IAAIJ,MAAM,GAAG,CAAC,sBAAD,EAAyB,qBAAzB,EAAgD,CAAC,qBAAjD,EAAwE,qBAAxE,EAA+F,CAAC,sBAAhG,EAAwH,yBAAxH,EAAmJ,yBAAnJ,EAA8K,CAAC,yBAA/K,EAA0M,yBAA1M,EAAqO,CAAC,yBAAtO,EAAiQ,yBAAjQ,EAA4R,CAAC,yBAA7R,EAAwT,yBAAxT,EAAmV,CAAC,yBAApV,EAA+W,yBAA/W,CAAb,C,CAEP;AAEA;;AACA,OAAO,IAAIO,SAAS,GAAG,sBAAhB;AACP,OAAO,IAAIC,OAAO,GAAG,CAAd,C,CAAiB;;AACxB,OAAO,IAAIC,OAAO,GAAG,CAAd,C,CAAiB;;AAExB,OAAO,IAAIC,YAAY,GAAG,CAAC,iBAAD,EAAoB,iBAApB,EAAuC,CAAC,iBAAxC,EAA2D,iBAA3D,EAA8E,CAAC,iBAA/E,EAAkG,qBAAlG,EAAyH,CAAC,kBAA1H,CAAnB;AACP,OAAO,SAASC,YAAT,CAAsB1B,CAAtB,EAAyB;EAC9B,IAAIA,CAAC,GAAG,CAAR,EAAW,OAAOI,GAAP;EACX,IAAIJ,CAAC,KAAK,CAAV,EAAa,OAAOG,QAAP;EACb,IAAI,CAACD,QAAQ,CAACF,CAAD,CAAb,EAAkB,OAAOA,CAAP;;EAClB,IAAIA,CAAC,GAAG,GAAR,EAAa;IACX;IACA;IACA,OAAOK,IAAI,CAACsB,GAAL,CAAStB,IAAI,CAACC,EAAL,GAAUD,IAAI,CAACE,GAAL,CAASF,IAAI,CAACC,EAAL,GAAUN,CAAnB,CAAnB,IAA4C0B,YAAY,CAAC,IAAI1B,CAAL,CAA/D;EACD,CAR6B,CAU9B;;;EAEAA,CAAC,GAAGA,CAAC,GAAG,CAAR;EACA,IAAI4B,IAAI,GAAG5B,CAAC,GAAGuB,OAAJ,GAAc,GAAzB,CAb8B,CAaA;;EAC9B,IAAIM,GAAG,GAAGJ,YAAY,CAAC,CAAD,CAAtB,CAd8B,CAgB9B;;EACA,KAAK,IAAIT,CAAC,GAAGQ,OAAO,GAAG,CAAvB,EAA0BR,CAAC,IAAI,CAA/B,EAAkCA,CAAC,EAAnC,EAAuC;IACrCa,GAAG,IAAIJ,YAAY,CAACT,CAAD,CAAZ,IAAmBhB,CAAC,GAAGgB,CAAvB,CAAP;EACD;;EACD,OAAOM,SAAS,GAAG,CAACtB,CAAC,GAAG,GAAL,IAAYK,IAAI,CAACsB,GAAL,CAASC,IAAT,CAAxB,GAAyCA,IAAzC,GAAgDvB,IAAI,CAACsB,GAAL,CAASE,GAAT,CAAvD;AACD;AACDH,YAAY,CAACL,SAAb,GAAyB,QAAzB"}, "metadata": {}, "sourceType": "module"}