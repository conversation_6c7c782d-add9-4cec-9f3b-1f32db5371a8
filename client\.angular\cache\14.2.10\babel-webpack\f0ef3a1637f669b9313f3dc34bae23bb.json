{"ast": null, "code": "export var xgcdDocs = {\n  name: 'xgcd',\n  category: 'Arithmetic',\n  syntax: ['xgcd(a, b)'],\n  description: 'Calculate the extended greatest common divisor for two values. The result is an array [d, x, y] with 3 entries, where d is the greatest common divisor, and d = x * a + y * b.',\n  examples: ['xgcd(8, 12)', 'gcd(8, 12)', 'xgcd(36163, 21199)'],\n  seealso: ['gcd', 'lcm']\n};", "map": {"version": 3, "names": ["xgcdDocs", "name", "category", "syntax", "description", "examples", "<PERSON>also"], "sources": ["D:/work/joyserver/client/node_modules/mathjs/lib/esm/expression/embeddedDocs/function/arithmetic/xgcd.js"], "sourcesContent": ["export var xgcdDocs = {\n  name: 'xgcd',\n  category: 'Arithmetic',\n  syntax: ['xgcd(a, b)'],\n  description: 'Calculate the extended greatest common divisor for two values. The result is an array [d, x, y] with 3 entries, where d is the greatest common divisor, and d = x * a + y * b.',\n  examples: ['xgcd(8, 12)', 'gcd(8, 12)', 'xgcd(36163, 21199)'],\n  seealso: ['gcd', 'lcm']\n};"], "mappings": "AAAA,OAAO,IAAIA,QAAQ,GAAG;EACpBC,IAAI,EAAE,MADc;EAEpBC,QAAQ,EAAE,YAFU;EAGpBC,MAAM,EAAE,CAAC,YAAD,CAHY;EAIpBC,WAAW,EAAE,gLAJO;EAKpBC,QAAQ,EAAE,CAAC,aAAD,EAAgB,YAAhB,EAA8B,oBAA9B,CALU;EAMpBC,OAAO,EAAE,CAAC,KAAD,EAAQ,KAAR;AANW,CAAf"}, "metadata": {}, "sourceType": "module"}