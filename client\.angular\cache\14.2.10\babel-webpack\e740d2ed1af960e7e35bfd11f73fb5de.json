{"ast": null, "code": "import { Observable } from '../Observable';\nimport { from } from './from';\nimport { empty } from './empty';\nexport function defer(observableFactory) {\n  return new Observable(subscriber => {\n    let input;\n\n    try {\n      input = observableFactory();\n    } catch (err) {\n      subscriber.error(err);\n      return undefined;\n    }\n\n    const source = input ? from(input) : empty();\n    return source.subscribe(subscriber);\n  });\n} //# sourceMappingURL=defer.js.map", "map": null, "metadata": {}, "sourceType": "module"}