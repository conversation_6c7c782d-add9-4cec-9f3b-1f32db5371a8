{"ast": null, "code": "import { deepMap } from '../../utils/collection.js';\nimport { isInteger as isIntegerNumber } from '../../utils/number.js';\nimport { factory } from '../../utils/factory.js';\nvar name = 'isInteger';\nvar dependencies = ['typed'];\nexport var createIsInteger = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed\n  } = _ref;\n  /**\n   * Test whether a value is an integer number.\n   * The function supports `number`, `BigNumber`, and `Fraction`.\n   *\n   * The function is evaluated element-wise in case of Array or Matrix input.\n   *\n   * Syntax:\n   *\n   *     math.isInteger(x)\n   *\n   * Examples:\n   *\n   *    math.isInteger(2)                     // returns true\n   *    math.isInteger(0)                     // returns true\n   *    math.isInteger(0.5)                   // returns false\n   *    math.isInteger(math.bignumber(500))   // returns true\n   *    math.isInteger(math.fraction(4))      // returns true\n   *    math.isInteger('3')                   // returns true\n   *    math.isInteger([3, 0.5, -2])          // returns [true, false, true]\n   *    math.isInteger(math.complex('2-4i'))  // throws TypeError\n   *\n   * See also:\n   *\n   *    isNumeric, isPositive, isNegative, isZero\n   *\n   * @param {number | BigNumber | bigint | Fraction | Array | Matrix} x   Value to be tested\n   * @return {boolean}  Returns true when `x` contains a numeric, integer value.\n   *                    Throws an error in case of an unknown data type.\n   */\n\n  return typed(name, {\n    number: isIntegerNumber,\n    // TODO: what to do with isInteger(add(0.1, 0.2))  ?\n    BigNumber: function BigNumber(x) {\n      return x.isInt();\n    },\n    bigint: function bigint(x) {\n      return true;\n    },\n    Fraction: function Fraction(x) {\n      return x.d === 1n;\n    },\n    'Array | Matrix': typed.referToSelf(self => x => deepMap(x, self))\n  });\n});", "map": {"version": 3, "names": ["deepMap", "isInteger", "isIntegerNumber", "factory", "name", "dependencies", "createIsInteger", "_ref", "typed", "number", "BigNumber", "x", "isInt", "bigint", "Fraction", "d", "referToSelf", "self"], "sources": ["D:/work/joyserver/client/node_modules/mathjs/lib/esm/function/utils/isInteger.js"], "sourcesContent": ["import { deepMap } from '../../utils/collection.js';\nimport { isInteger as isIntegerNumber } from '../../utils/number.js';\nimport { factory } from '../../utils/factory.js';\nvar name = 'isInteger';\nvar dependencies = ['typed'];\nexport var createIsInteger = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed\n  } = _ref;\n  /**\n   * Test whether a value is an integer number.\n   * The function supports `number`, `BigNumber`, and `Fraction`.\n   *\n   * The function is evaluated element-wise in case of Array or Matrix input.\n   *\n   * Syntax:\n   *\n   *     math.isInteger(x)\n   *\n   * Examples:\n   *\n   *    math.isInteger(2)                     // returns true\n   *    math.isInteger(0)                     // returns true\n   *    math.isInteger(0.5)                   // returns false\n   *    math.isInteger(math.bignumber(500))   // returns true\n   *    math.isInteger(math.fraction(4))      // returns true\n   *    math.isInteger('3')                   // returns true\n   *    math.isInteger([3, 0.5, -2])          // returns [true, false, true]\n   *    math.isInteger(math.complex('2-4i'))  // throws TypeError\n   *\n   * See also:\n   *\n   *    isNumeric, isPositive, isNegative, isZero\n   *\n   * @param {number | BigNumber | bigint | Fraction | Array | Matrix} x   Value to be tested\n   * @return {boolean}  Returns true when `x` contains a numeric, integer value.\n   *                    Throws an error in case of an unknown data type.\n   */\n  return typed(name, {\n    number: isIntegerNumber,\n    // TODO: what to do with isInteger(add(0.1, 0.2))  ?\n\n    BigNumber: function BigNumber(x) {\n      return x.isInt();\n    },\n    bigint: function bigint(x) {\n      return true;\n    },\n    Fraction: function Fraction(x) {\n      return x.d === 1n;\n    },\n    'Array | Matrix': typed.referToSelf(self => x => deepMap(x, self))\n  });\n});"], "mappings": "AAAA,SAASA,OAAT,QAAwB,2BAAxB;AACA,SAASC,SAAS,IAAIC,eAAtB,QAA6C,uBAA7C;AACA,SAASC,OAAT,QAAwB,wBAAxB;AACA,IAAIC,IAAI,GAAG,WAAX;AACA,IAAIC,YAAY,GAAG,CAAC,OAAD,CAAnB;AACA,OAAO,IAAIC,eAAe,GAAG,eAAeH,OAAO,CAACC,IAAD,EAAOC,YAAP,EAAqBE,IAAI,IAAI;EAC9E,IAAI;IACFC;EADE,IAEAD,IAFJ;EAGA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EACE,OAAOC,KAAK,CAACJ,IAAD,EAAO;IACjBK,MAAM,EAAEP,eADS;IAEjB;IAEAQ,SAAS,EAAE,SAASA,SAAT,CAAmBC,CAAnB,EAAsB;MAC/B,OAAOA,CAAC,CAACC,KAAF,EAAP;IACD,CANgB;IAOjBC,MAAM,EAAE,SAASA,MAAT,CAAgBF,CAAhB,EAAmB;MACzB,OAAO,IAAP;IACD,CATgB;IAUjBG,QAAQ,EAAE,SAASA,QAAT,CAAkBH,CAAlB,EAAqB;MAC7B,OAAOA,CAAC,CAACI,CAAF,KAAQ,EAAf;IACD,CAZgB;IAajB,kBAAkBP,KAAK,CAACQ,WAAN,CAAkBC,IAAI,IAAIN,CAAC,IAAIX,OAAO,CAACW,CAAD,EAAIM,IAAJ,CAAtC;EAbD,CAAP,CAAZ;AAeD,CAhDkD,CAA5C"}, "metadata": {}, "sourceType": "module"}