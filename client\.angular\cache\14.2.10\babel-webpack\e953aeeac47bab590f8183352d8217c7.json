{"ast": null, "code": "import { map } from './map';\nimport { from } from '../observable/from';\nimport { SimpleOuterSubscriber, SimpleInnerSubscriber, innerSubscribe } from '../innerSubscribe';\nexport function switchMap(project, resultSelector) {\n  if (typeof resultSelector === 'function') {\n    return source => source.pipe(switchMap((a, i) => from(project(a, i)).pipe(map((b, ii) => resultSelector(a, b, i, ii)))));\n  }\n\n  return source => source.lift(new SwitchMapOperator(project));\n}\n\nclass SwitchMapOperator {\n  constructor(project) {\n    this.project = project;\n  }\n\n  call(subscriber, source) {\n    return source.subscribe(new SwitchMapSubscriber(subscriber, this.project));\n  }\n\n}\n\nclass SwitchMapSubscriber extends SimpleOuterSubscriber {\n  constructor(destination, project) {\n    super(destination);\n    this.project = project;\n    this.index = 0;\n  }\n\n  _next(value) {\n    let result;\n    const index = this.index++;\n\n    try {\n      result = this.project(value, index);\n    } catch (error) {\n      this.destination.error(error);\n      return;\n    }\n\n    this._innerSub(result);\n  }\n\n  _innerSub(result) {\n    const innerSubscription = this.innerSubscription;\n\n    if (innerSubscription) {\n      innerSubscription.unsubscribe();\n    }\n\n    const innerSubscriber = new SimpleInnerSubscriber(this);\n    const destination = this.destination;\n    destination.add(innerSubscriber);\n    this.innerSubscription = innerSubscribe(result, innerSubscriber);\n\n    if (this.innerSubscription !== innerSubscriber) {\n      destination.add(this.innerSubscription);\n    }\n  }\n\n  _complete() {\n    const {\n      innerSubscription\n    } = this;\n\n    if (!innerSubscription || innerSubscription.closed) {\n      super._complete();\n    }\n\n    this.unsubscribe();\n  }\n\n  _unsubscribe() {\n    this.innerSubscription = undefined;\n  }\n\n  notifyComplete() {\n    this.innerSubscription = undefined;\n\n    if (this.isStopped) {\n      super._complete();\n    }\n  }\n\n  notifyNext(innerValue) {\n    this.destination.next(innerValue);\n  }\n\n} //# sourceMappingURL=switchMap.js.map", "map": null, "metadata": {}, "sourceType": "module"}