{"ast": null, "code": "import { createPrint } from '../../function/string/print.js';\nimport { factory } from '../../utils/factory.js';\nimport { printTemplate } from '../../utils/print.js';\nvar name = 'print';\nvar dependencies = ['typed', 'matrix', 'zeros', 'add'];\nexport var createPrintTransform = /* #__PURE__ */factory(name, dependencies, _ref => {\n  var {\n    typed,\n    matrix,\n    zeros,\n    add\n  } = _ref;\n  var print = createPrint({\n    typed,\n    matrix,\n    zeros,\n    add\n  });\n  return typed(name, {\n    'string, Object | Array': function string_Object__Array(template, values) {\n      return print(_convertTemplateToZeroBasedIndex(template), values);\n    },\n    'string, Object | Array, number | Object': function string_Object__Array_number__Object(template, values, options) {\n      return print(_convertTemplateToZeroBasedIndex(template), values, options);\n    }\n  });\n\n  function _convertTemplateToZeroBasedIndex(template) {\n    return template.replace(printTemplate, x => {\n      var parts = x.slice(1).split('.');\n      var result = parts.map(function (part) {\n        if (!isNaN(part) && part.length > 0) {\n          return parseInt(part) - 1;\n        } else {\n          return part;\n        }\n      });\n      return '$' + result.join('.');\n    });\n  }\n}, {\n  isTransformFunction: true\n});", "map": null, "metadata": {}, "sourceType": "module"}